// function wildcardToRegex(pattern: string): RegExp {
//   const escaped = pattern
//     .replace(/[-/\\^$+?.()|[\]{}]/g, '\\$&') // escape regex special characters
//     .replace(/\*/g, '.*');                  // convert * to match anything

//   return new RegExp(`^${escaped}$`);
// }

// export function matchPattern(pattern: string, value: string): boolean {
//   return wildcardToRegex(pattern).test(value);
// }


export function wildcardToRegex(pattern: string): RegExp {
  const escaped = pattern
    .replace(/[-/\\^$+?.()|[\]{}]/g, '\\$&') // Escape regex special characters
    .replace(/\*/g, '.*'); // Replace * with .*

  return new RegExp(`^${escaped}$`);
}

export function matchPattern(pattern: string, value: string): boolean {
  return wildcardToRegex(pattern).test(value);
}
