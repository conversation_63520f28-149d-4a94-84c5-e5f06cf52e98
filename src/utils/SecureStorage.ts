// secureStorage.ts

type StorageType = "local" | "cookie";

class StorageKey {
  constructor(public key: string, public storageType: StorageType) {}
}

// Define all supported keys with their target storage
export const StorageKeys = {
  UserToken: new StorageKey("UserToken", "local"),
  RefreshToken: new StorageKey("RefreshToken", "local"),
  CurrentOrganization: new StorageKey("CurrentOrganization", "local"),
  CookieToken: new StorageKey("token", "cookie"),
} as const;

export type StorageKeyType = (typeof StorageKeys)[keyof typeof StorageKeys];

class SecureStorage {
  private static instance: SecureStorage;

  private constructor() {}

  public static getInstance(): SecureStorage {
    if (!SecureStorage.instance) {
      SecureStorage.instance = new SecureStorage();
    }
    return SecureStorage.instance;
  }

  public setItem(key: StorageKeyType, value: string): void {
    try {
      if (key.storageType === "local") {
        localStorage.setItem(key.key, value);
      } else {
        document.cookie = `${key.key}=${encodeURIComponent(value)}; path=/`;
      }
    } catch (error) {
      console.error(`Error setting item for key "${key.key}"`, error);
      throw error;
    }
  }

  public getItem(key: StorageKeyType): string | null {
    try {
      if (key.storageType === "local") {
        return localStorage.getItem(key.key);
      } else {
        const match = document.cookie.match(
          new RegExp("(^| )" + key.key + "=([^;]+)")
        );
        return match ? decodeURIComponent(match[2]) : null;
      }
    } catch (error) {
      console.error(`Error getting item for key "${key.key}"`, error);
      throw error;
    }
  }

  public removeItem(key: StorageKeyType): void {
    try {
      if (key.storageType === "local") {
        localStorage.removeItem(key.key);
      } else {
        document.cookie = `${key.key}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
      }
    } catch (error) {
      console.error(`Error removing item for key "${key.key}"`, error);
      throw error;
    }
  }
}

export const secureStorage = SecureStorage.getInstance();
