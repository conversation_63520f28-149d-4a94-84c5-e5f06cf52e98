export const translations = {
  en: {
    register: {
      getStarted: "Get Started",
      haveAccount: "Already have an account?",
      login: "Sign in",
      register: "Sign up",
      placeholderName: "Enter your name",
      placeholderEmail: "Enter your email",
      placeholderPassword: "Enter your password",
      labelInputName: "Name",
      questions: "Questions",
      ask: "Ask",
      upgradeSkill: "Upgrade your skill",
      onWebsite: "on our website",
    },
    localeSwitcher: {
      en: "English",
      id: "Indonesia",
    },
  },
}

// Helper function to get translations
export function getTranslation(path: string): string {
  const keys = path.split(".")
  let result: any = translations.en

  for (const key of keys) {
    if (result[key] === undefined) {
      return path // Return the path if translation not found
    }
    result = result[key]
  }

  return result
}
