import { BaseAPI } from "../baseApi";

import sampleList from "./sampleList.json";
import samplePostResponse from "./samplePostResponse.json";
import sampleUpdateResponse from "./sampleUpdateResponse.json";
import sampleDeleteResponse from "./sampleDeleteResponse.json";

export class SampleCrudAPI extends BaseAPI {
    static ListItems = () =>
      new BaseAPI("/sample-items", undefined, "GET", false, {}, sampleList);
  
    static CreateItem = (body: any) =>
      new BaseAPI("/sample-items", body, "POST", false, {}, samplePostResponse);
  
    static UpdateItem = (id: string, body: any) =>
      new BaseAPI(`/sample-items/${id}`, body, "PUT", false, {}, sampleUpdateResponse);
  
    static DeleteItem = (id: string) =>
      new BaseAPI(`/sample-items/${id}`, undefined, "DELETE", false, {}, sampleDeleteResponse);
  }
  