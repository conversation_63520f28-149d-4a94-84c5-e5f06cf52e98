export interface PagingAndSearch<T extends Record<string, string | boolean | string[] | undefined>> {
  page: number
  per_page?: number
  search?: string
  sort_by?: string
  sort_order?: 'asc' | 'desc'
  filters?: T
}

export namespace PagingAndSearch {
  export function toRecordString<T extends Record<string, string | boolean | string[] | undefined>>(
    p: PagingAndSearch<T>
  ): Record<string, string> {
    const query: Record<string, string> = {
      page: p.page.toString(),
      per_page: (p.per_page ?? 20).toString(),
    }

    if (p.search) query.search = p.search
    if (p.sort_by) query.sort_by = p.sort_by
    if (p.sort_order) query.sort_order = p.sort_order

    if (p.filters) {
      Object.entries(p.filters).forEach(([key, value]) => {
        if (value) {
          query[key] = Array.isArray(value) ? value.join(',') : value.toString()
        }
      })
    }

    return query
  }
}
