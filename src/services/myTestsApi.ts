import { MyTests, MyTestDetail } from "@/src/lib/repositories/my-tests/MyTestsRepository";
import { BaseAPI } from "./baseApi";
import { PagingAndSearch } from "./types";

type MyTestPayload = Partial<MyTests>;

export class MyTestsAPI extends BaseAPI {
  static MyTests(
    params: PagingAndSearch<{
      category?: string;
      search?: string;
    }>
  ) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params)
    ).toString()}`;
    return new BaseAPI(`/my-tests${queryString}`).build<MyTests[]>();
  }

  static MyTestDetail = (testId: string) =>
    new BaseAPI(`/my-tests/${testId}`).build<MyTests>();

  static MyTestDetailFull = (testId: string) =>
    new BaseAPI(`/my-tests/${testId}/detail`).build<MyTestDetail>();

  static CreateMyTest = (body: MyTestPayload) =>
    new BaseAPI(`/my-tests`, body, "POST").build<MyTests>();

  static UpdateMyTest = (testId: string, body: MyTestPayload) =>
    new BaseAPI(`/my-tests/${testId}`, body, "PUT").build<MyTests>();

  static DeleteMyTest = (testId: string) =>
    new BaseAPI(`/my-tests/${testId}`, undefined, "DELETE").build<{
      success: boolean;
    }>();

  static BulkDeleteMyTests = (ids: string[]) =>
    new BaseAPI(`/my-tests/bulk-delete`, { ids }, "POST").build<{
      deleted: number;
    }>();
}
