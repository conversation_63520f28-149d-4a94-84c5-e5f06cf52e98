import { Course, CourseDetail, CourseComment, CourseResponse, CourseAnalyticsResponse } from "@/src/lib/repositories/courses/CoursesRepository";
import { BaseAPI } from "./baseApi";
import { PagingAndSearch } from "./types";

type CoursePayload = Partial<Course>;
type CourseCommentPayload = Partial<CourseComment>;

export class CoursesAPI extends BaseAPI {
  static Courses(
    params: PagingAndSearch<{
      search?: string;
    }>
  ) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params)
    ).toString()}`;
    return new BaseAPI(`/courses${queryString}`).build<CourseResponse>();
  }

  static CourseDetail = (courseId: string) =>
    new BaseAPI(`/courses/${courseId}`).build<CourseDetail>();

  static CreateCourse = (body: CoursePayload) =>
    new BaseAPI(`/courses`, body, "POST").build<Course>();

  static UpdateCourse = (courseId: string, body: CoursePayload) =>
    new BaseAPI(`/courses/${courseId}`, body, "PUT").build<Course>();

  static DeleteCourse = (courseId: string) =>
    new BaseAPI(`/courses/${courseId}`, undefined, "DELETE").build<{
      success: boolean;
    }>();

  static BulkDeleteCourses = (ids: string[]) =>
    new BaseAPI(`/courses/bulk-delete`, { ids }, "POST").build<{
      deleted: number;
    }>();

  // Course Comments
  static CourseComments(
    params: PagingAndSearch<{
      filter?: 'all' | 'unread' | 'flagged';
      search?: string;
    }>
  ) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params)
    ).toString()}`;
    return new BaseAPI(`/courses/comments${queryString}`).build<CourseComment[]>();
  }

  static UpdateCourseComment = (commentId: string, body: CourseCommentPayload) =>
    new BaseAPI(`/courses/comments/${commentId}`, body, "PUT").build<CourseComment>();

  static DeleteCourseComment = (commentId: string) =>
    new BaseAPI(`/courses/comments/${commentId}`, undefined, "DELETE").build<{
      success: boolean;
    }>();

  // Course Analytics
  static CourseAnalytics(
    params: PagingAndSearch<{}>
  ) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params)
    ).toString()}`;
    return new BaseAPI(`/courses/analytics${queryString}`).build<CourseAnalyticsResponse>();
  }
}
