import { StatsItem } from "@/src/lib/repositories/analytics/summary/AnalyticsSummaryRepository";
import { DetailedType } from "@/src/lib/repositories/analytics/detailed/DetailedRepository";
import { BaseAPI } from "./baseApi";
import { PagingAndSearch } from "./types";

export class AnalyticsAPI extends BaseAPI {
  // Summary Analytics
  static AnalyticsSummary(
    params: PagingAndSearch<{
      uniq_id?: string;
      date?: string;
    }>
  ) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params)
    ).toString()}`;
    return new BaseAPI(`/analytics/summary${queryString}`).build<StatsItem[]>();
  }

  // Detailed Analytics
  static AnalyticsDetailed(
    params: PagingAndSearch<{}>
  ) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params)
    ).toString()}`;
    return new BaseAPI(`/analytics/detailed${queryString}`).build<DetailedType>();
  }

  // Upgrade Overlay Analytics
  static AnalyticsUpgradeOverlay(
    params: PagingAndSearch<{}>
  ) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params)
    ).toString()}`;
    return new BaseAPI(`/analytics/upgrade-overlay${queryString}`).build<any>();
  }
}
