import { UserSettings } from "@/src/lib/repositories/settings/SettingsRepository";
import { BaseAPI } from "./baseApi";
import { PagingAndSearch } from "./types";

type UserSettingsPayload = Partial<UserSettings>;
type NotificationSettingsPayload = Partial<Pick<UserSettings, 'emailNotifications' | 'pushNotifications' | 'courseUpdates' | 'testReminders' | 'collaboratorActivity' | 'notificationFrequency'>>;
type AppearanceSettingsPayload = Partial<Pick<UserSettings, 'theme' | 'colorScheme' | 'fontSize' | 'compactMode' | 'animations'>>;
type LanguageSettingsPayload = Partial<Pick<UserSettings, 'language' | 'region' | 'dateFormat' | 'timeFormat'>>;
type SecuritySettingsPayload = Partial<Pick<UserSettings, 'twoFactorEnabled' | 'sessionTimeout'>>;

export class SettingsAPI extends BaseAPI {
  // Get user settings
  static UserSettings(userId?: string) {
    const queryString = userId ? `?userId=${userId}` : "";
    return new BaseAPI(`/settings${queryString}`).build<UserSettings>();
  }

  // Create user settings
  static CreateUserSettings = (body: UserSettingsPayload) =>
    new BaseAPI(`/settings`, body, "POST").build<UserSettings>();

  // Update user settings
  static UpdateUserSettings = (userId: string, body: UserSettingsPayload) => {
    const queryString = `?userId=${userId}`;
    return new BaseAPI(`/settings${queryString}`, body, "PUT").build<UserSettings>();
  }

  // Update notification settings
  static UpdateNotificationSettings = (userId: string, body: NotificationSettingsPayload) => {
    const queryString = `?userId=${userId}`;
    return new BaseAPI(`/settings/notifications${queryString}`, body, "PUT").build<UserSettings>();
  }

  // Update appearance settings
  static UpdateAppearanceSettings = (userId: string, body: AppearanceSettingsPayload) => {
    const queryString = `?userId=${userId}`;
    return new BaseAPI(`/settings/appearance${queryString}`, body, "PUT").build<UserSettings>();
  }

  // Update language settings
  static UpdateLanguageSettings = (userId: string, body: LanguageSettingsPayload) => {
    const queryString = `?userId=${userId}`;
    return new BaseAPI(`/settings/language${queryString}`, body, "PUT").build<UserSettings>();
  }

  // Update security settings
  static UpdateSecuritySettings = (userId: string, body: SecuritySettingsPayload) => {
    const queryString = `?userId=${userId}`;
    return new BaseAPI(`/settings/security${queryString}`, body, "PUT").build<UserSettings>();
  }
}
