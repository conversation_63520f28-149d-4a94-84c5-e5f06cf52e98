import { BaseAPI } from "./baseApi";

export class AuthAP<PERSON> extends BaseAPI {
  static Register = (body: { email: string; password: string; name?: string }) =>
    new BaseAPI(`/auth/register`, body, "POST").build<{ success: boolean; message: string }>();

  static Login = (body: { email: string; password: string }) =>
    new BaseAPI(`/auth/login`, body, "POST").build<{ token: string; refresh_token: string }>();

  static Logout = () =>
    new BaseAPI(`/auth/logout`, undefined, "POST").build<{ success: boolean }>();

  static RefreshToken = (body: { refresh_token: string; token: string }) =>
    new BaseAPI(`/auth/refresh_token`, body, "POST").build<{ token: string; refresh_token: string; }>();

  static ForgotPassword = (body: { email: string }) =>
    new BaseAPI(`/auth/forgot-password`, body, "POST").build<{ success: boolean }>();

  static ResetPassword = (body: { token: string; newPassword: string }) =>
    new BaseAPI(`/auth/reset-password`, body, "POST").build<{ success: boolean }>();

  static ChangePassword = (body: { currentPassword: string; newPassword: string }) =>
    new BaseAPI(`/auth/change-password`, body, "POST").build<{ success: boolean }>();

  static VerifyEmail = (body: { token: string }) =>
    new BaseAPI(`/auth/verify-email`, body, "POST").build<{ success: boolean }>();

  static ResendVerification = (body: { email: string }) =>
    new BaseAPI(`/auth/resend-verification`, body, "POST").build<{ success: boolean }>();

  static DeleteAccount = (body: { password: string }) =>
    new BaseAPI(`/auth/delete-account`, body, "DELETE").build<{ success: boolean }>();
}
