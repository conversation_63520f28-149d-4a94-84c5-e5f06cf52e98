import { CollaboratorUser, Invitation } from "@/src/lib/repositories/collaborators/CollaboratorsRepository";
import { BaseAPI } from "./baseApi";
import { PagingAndSearch } from "./types";

type CollaboratorPayload = Partial<CollaboratorUser>;
type InvitationPayload = Partial<Invitation>;

export class CollaboratorsAPI extends BaseAPI {
  static Collaborators(
    params: PagingAndSearch<{
      role?: string;
      status?: string;
      search?: string;
    }>
  ) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params)
    ).toString()}`;
    return new BaseAPI(`/collaborators${queryString}`).build<CollaboratorUser[]>();
  }

  static CollaboratorDetail = (collaboratorId: string) =>
    new BaseAPI(`/collaborators/${collaboratorId}`).build<CollaboratorUser>();

  static CreateCollaborator = (body: CollaboratorPayload) =>
    new BaseAPI(`/collaborators`, body, "POST").build<CollaboratorUser>();

  static UpdateCollaborator = (collaboratorId: string, body: CollaboratorPayload) =>
    new BaseAPI(`/collaborators/${collaboratorId}`, body, "PUT").build<CollaboratorUser>();

  static DeleteCollaborator = (collaboratorId: string) =>
    new BaseAPI(`/collaborators/${collaboratorId}`, undefined, "DELETE").build<{
      success: boolean;
    }>();

  static BulkDeleteCollaborators = (ids: string[]) =>
    new BaseAPI(`/collaborators/bulk-delete`, { ids }, "POST").build<{
      deleted: number;
    }>();

  // Invitations
  static Invitations(
    params: PagingAndSearch<{
      status?: string;
      search?: string;
    }>
  ) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params)
    ).toString()}`;
    return new BaseAPI(`/collaborators/invitations${queryString}`).build<Invitation[]>();
  }

  static InvitationDetail = (invitationId: string) =>
    new BaseAPI(`/collaborators/invitations/${invitationId}`).build<Invitation>();

  static CreateInvitation = (body: InvitationPayload) =>
    new BaseAPI(`/collaborators/invitations`, body, "POST").build<Invitation>();

  static UpdateInvitation = (invitationId: string, body: InvitationPayload) =>
    new BaseAPI(`/collaborators/invitations/${invitationId}`, body, "PUT").build<Invitation>();

  static DeleteInvitation = (invitationId: string) =>
    new BaseAPI(`/collaborators/invitations/${invitationId}`, undefined, "DELETE").build<{
      success: boolean;
    }>();
}
