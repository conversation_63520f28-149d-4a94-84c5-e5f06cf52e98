import { Test } from "@/src/lib/repositories/tests/TestsRepository";
import { BaseAPI } from "./baseApi";
import { PagingAndSearch } from "./types";

type TestPayload = Test;

export class TestsAPI extends BaseAPI {
  static Tests(
    params: PagingAndSearch<{
      status?: "active" | "inactive" | "archived" | "draft";
      search?: string;
    }>
  ) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params)
    ).toString()}`;
    return new BaseAPI(`/tests${queryString}`).build<Test[]>();
  }

  static TestDetail = (testId: string) =>
    new BaseAPI(`/tests/${testId}`).build<Test>();

  static CreateTest = (body: Omit<Test, "id" | "createdAt" | "updatedAt">) =>
    new BaseAPI(`/tests`, body, "POST").build<Test>();

  static UpdateTest = (testId: string, body: TestPayload) =>
    new BaseAPI(`/tests/${testId}`, body, "PUT").build<Test>();

  static DeleteTest = (testId: string) =>
    new BaseAPI(`/tests/${testId}`, undefined, "DELETE").build<{
      success: boolean;
    }>();

  static PublishTest = (testId: string) =>
    new BaseAPI(`/tests/${testId}/publish`, undefined, "PATCH").build<Test>();

  static RestoreTest = (testId: string) =>
    new BaseAPI(`/tests/${testId}/restore`, undefined, "PATCH").build<Test>();

  static BulkUpdateStatus = (ids: string[], status: "active" | "inactive" | "archived") =>
    new BaseAPI(`/tests/bulk-status`, { ids, status }, "PATCH").build<{
      success: boolean;
    }>();
}
