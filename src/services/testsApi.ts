import { Test, TestCreateInput, TestUpdateInput } from "@/src/lib/repositories/tests/interface";
import { BaseAPI } from "./baseApi";
import { PagingAndSearch } from "./types";

interface TestListResponse {
  items: Test[];
  page: number;
  total: number;
}

export class TestsAPI {
  static Tests(
    params: PagingAndSearch<{
      status?: "DRAFT" | "INPROGRESS" | "DONE" | "CANCELLED";
      search?: string;
      includeDeleted?: boolean;
    }>
  ) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params)
    ).toString()}`;
    return new BaseAPI(`/tests${queryString}`).build<TestListResponse>();
  }

  static TestDetail = (testId: string) =>
    new BaseAPI(`/tests/${testId}`).build<Test>();

  static CreateTest = (body: TestCreateInput) =>
    new BaseAPI(`/tests`, body, "POST").build<Test>();

  static UpdateTest = (testId: string, body: TestUpdateInput) =>
    new BaseAPI(`/tests/${testId}`, body, "PUT").build<Test>();

  static DeleteTest = (testId: string, hardDelete: boolean = false) => {
    const queryString = hardDelete ? '?hardDelete=true' : '';
    return new BaseAPI(`/tests/${testId}${queryString}`, undefined, "DELETE").build<{
      message: string;
    }>();
  }

  static RestoreTest = (testId: string) =>
    new BaseAPI(`/tests/${testId}/restore`, undefined, "PATCH").build<{
      restored: boolean;
    }>();

  // Bulk operations
  static BulkCreateTests = (data: TestCreateInput[]) =>
    new BaseAPI(`/tests/bulk`, data, "POST").build<Test[]>();

  static BulkUpdateTests = (updates: { id: string; data: TestUpdateInput }[]) =>
    new BaseAPI(`/tests/bulk`, updates, "PUT").build<{ updatedCount: number }>();

  static BulkDeleteTests = (ids: string[], hardDelete: boolean = false) => {
    const queryString = hardDelete ? '?hardDelete=true' : '';
    return new BaseAPI(`/tests/bulk${queryString}`, { ids }, "DELETE").build<{ deletedCount: number }>();
  }

  // Test Questions
  static GetTestQuestions = (testId: string) =>
    new BaseAPI(`/tests/${testId}/questions`).build<Array<{ id: string; question: string; type: string }>>();

  static UpdateTestQuestions = (testId: string, body: { added: string[]; removed: string[] }) =>
    new BaseAPI(`/tests/${testId}/questions`, body, "PUT").build<{ updated: boolean }>();

  // Categories
  static SearchCategories = (query: string) =>
    new BaseAPI(`/tests/categories?query=${query}`).build<{ items: { id: string; name: string }[] }>();
  static CreateCategory = (body: { name: string }) =>
    new BaseAPI(`/tests/categories`, body, "POST").build<{ id: string; name: string }>();
}
