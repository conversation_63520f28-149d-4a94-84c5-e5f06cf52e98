import { Question, QuestionCreateInput, QuestionUpdateInput } from "@/src/lib/repositories/questions/interface";
import { BaseAPI } from "./baseApi";
import { PagingAndSearch } from "./types";

interface QuestionListResponse {
  items: Question[];
  page: number;
  total: number;
}

export class QuestionsAPI {
  static Questions(
    params: PagingAndSearch<{
      type?: string;
      category?: string;
      difficulty?: string;
      search?: string;
      includeDeleted?: boolean;
    }>
  ) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params)
    ).toString()}`;
    return new BaseAPI(`/questions${queryString}`).build<QuestionListResponse>();
  }

  static QuestionDetail = (questionId: string) =>
    new BaseAPI(`/questions/${questionId}`).build<Question>();

  static CreateQuestion = (body: QuestionCreateInput) =>
    new BaseAPI(`/questions`, body, "POST").build<Question>();

  static UpdateQuestion = (questionId: string, body: QuestionUpdateInput) =>
    new BaseAPI(`/questions/${questionId}`, body, "PUT").build<Question>();

  static DeleteQuestion = (questionId: string, hardDelete: boolean = false) => {
    const queryString = hardDelete ? '?hardDelete=true' : '';
    return new BaseAPI(`/questions/${questionId}${queryString}`, undefined, "DELETE").build<{
      message: string;
    }>();
  }

  static RestoreQuestion = (questionId: string) =>
    new BaseAPI(`/questions/${questionId}/restore`, undefined, "PATCH").build<{
      restored: boolean;
    }>();

  // Bulk operations
  static BulkCreateQuestions = (data: QuestionCreateInput[]) =>
    new BaseAPI(`/questions/bulk`, data, "POST").build<Question[]>();

  static BulkUpdateQuestions = (updates: { id: string; data: QuestionUpdateInput }[]) =>
    new BaseAPI(`/questions/bulk`, updates, "PUT").build<{ updatedCount: number }>();

  static BulkDeleteQuestions = (ids: string[], hardDelete: boolean = false) => {
    const queryString = hardDelete ? '?hardDelete=true' : '';
    return new BaseAPI(`/questions/bulk${queryString}`, { ids }, "DELETE").build<{ deletedCount: number }>();
  }
}