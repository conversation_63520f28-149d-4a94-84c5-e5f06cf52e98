import { NotificationType, NotificationResponse } from "@/src/lib/repositories/notifications/NotificationsRepository";
import { BaseAPI } from "./baseApi";
import { PagingAndSearch } from "./types";

type NotificationPayload = Partial<NotificationType>;

export class NotificationsAPI extends BaseAPI {
  static Notifications(
    params: PagingAndSearch<{
      read?: boolean;
      type?: string;
      search?: string;
    }>
  ) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params)
    ).toString()}`;
    return new BaseAPI(`/notifications${queryString}`).build<NotificationResponse>();
  }

  static NotificationDetail = (notificationId: number) =>
    new BaseAPI(`/notifications/${notificationId}`).build<NotificationType>();

  static NotificationDetailByTestId = (testId: string) =>
    new BaseAPI(`/notifications/detail?test_id=${testId}`).build<NotificationType>();

  static CreateNotification = (body: NotificationPayload) =>
    new BaseAPI(`/notifications`, body, "POST").build<NotificationType>();

  static UpdateNotification = (notificationId: string, body: NotificationPayload) =>
    new BaseAPI(`/notifications/${notificationId}`, body, "PUT").build<NotificationType>();

  static DeleteNotification = (notificationId: string) =>
    new BaseAPI(`/notifications/${notificationId}`, undefined, "DELETE").build<{
      success: boolean;
    }>();

  static MarkAllAsRead = () =>
    new BaseAPI(`/notifications/mark-all-read`, undefined, "PATCH").build<NotificationType[]>();

  static DeleteAllRead = () =>
    new BaseAPI(`/notifications/delete-all-read`, undefined, "DELETE").build<NotificationType[]>();

  static BulkDeleteNotifications = (ids: string[]) =>
    new BaseAPI(`/notifications/bulk-delete`, { ids }, "POST").build<{
      deleted: number;
    }>();
}
