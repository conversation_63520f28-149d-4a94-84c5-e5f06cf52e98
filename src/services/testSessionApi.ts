import { TestSession, TestSessionCreateInput, TestSessionUpdateInput, SubmittedAnswer } from "@/src/lib/repositories/test-session/interface";
import { BaseAPI } from "./baseApi";
import { PagingAndSearch } from "./types";
import { ResponseWrapper } from "@/src/lib/types/responseWrapper";

interface TestSessionListResponse {
  items: TestSession[];
  page: number;
  total: number;
}

type SubmitAnswersPayload = {
  sessionId: string;
  answers: SubmittedAnswer[];
};

type ExitSessionPayload = {
  sessionId: string;
  exitReason: string;
  timeSpent: number;
};

export class TestSessionsAPI {
  static TestSessions(
    params: PagingAndSearch<{
      status?: "OPEN" | "COMPLETED" | "EXPIRED" | "CANCELLED";
      testId?: string;
      userId?: string;
      search?: string;
      includeDeleted?: boolean;
    }>
  ) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params)
    ).toString()}`;
    return new BaseAPI(`/test-sessions${queryString}`).build<TestSessionListResponse>();
  }

  static TestSessionDetail = (sessionId: string) =>
    new BaseAPI(`/test-sessions/${sessionId}`).build<ResponseWrapper<TestSession>>();

  static CreateTestSession = (body: TestSessionCreateInput) =>
    new BaseAPI(`/test-sessions`, body, "POST").build<ResponseWrapper<TestSession>>();

  static UpdateTestSession = (sessionId: string, body: TestSessionUpdateInput) =>
    new BaseAPI(`/test-sessions/${sessionId}`, body, "PUT").build<ResponseWrapper<TestSession>>();

  static DeleteTestSession = (sessionId: string, hardDelete: boolean = false) => {
    const queryString = hardDelete ? '?hardDelete=true' : '';
    return new BaseAPI(`/test-sessions/${sessionId}${queryString}`, undefined, "DELETE").build<{
      message: string;
    }>();
  }

  static RestoreTestSession = (sessionId: string) =>
    new BaseAPI(`/test-sessions/${sessionId}/restore`, undefined, "PATCH").build<{
      restored: boolean;
    }>();

  // Bulk operations
  static BulkCreateTestSessions = (data: TestSessionCreateInput[]) =>
    new BaseAPI(`/test-sessions/bulk`, data, "POST").build<TestSession[]>();

  static BulkUpdateTestSessions = (updates: { id: string; data: TestSessionUpdateInput }[]) =>
    new BaseAPI(`/test-sessions/bulk`, updates, "PUT").build<{ updatedCount: number }>();

  static BulkDeleteTestSessions = (ids: string[], hardDelete: boolean = false) => {
    const queryString = hardDelete ? '?hardDelete=true' : '';
    return new BaseAPI(`/test-sessions/bulk${queryString}`, { ids }, "DELETE").build<{ deletedCount: number }>();
  }

  // Test session specific methods
  static SubmitAnswers = (body: SubmitAnswersPayload) =>
    new BaseAPI(`/test-sessions/submit`, body, "POST").build<ResponseWrapper<any>>();

  static ExitSession = (body: ExitSessionPayload) =>
    new BaseAPI(`/test-sessions/exit`, body, "POST").build<ResponseWrapper<TestSession>>();

  // Get sessions by test or user
  static GetSessionsByTestId = (testId: string, params?: PagingAndSearch<{}>) => {
    const queryString = params ? `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params)
    ).toString()}` : '';
    return new BaseAPI(`/test-sessions?testId=${testId}${queryString}`).build<TestSessionListResponse>();
  }

  static GetSessionsByUserId = (userId: string, params?: PagingAndSearch<{}>) => {
    const queryString = params ? `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params)
    ).toString()}` : '';
    return new BaseAPI(`/test-sessions?userId=${userId}${queryString}`).build<TestSessionListResponse>();
  }
}
