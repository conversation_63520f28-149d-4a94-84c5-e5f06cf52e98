import { TestSession, SubmittedAnswer } from "@/src/lib/repositories/test-session/TestSessionRepository";
import { BaseAPI } from "./baseApi";
import { PagingAndSearch } from "./types";
import { TestResult } from "../lib/repositories/test-result/types";

type CreateTestSessionPayload = {
  testId: string;
  user: {
    id: string;
    name: string;
    email: string;
  };
};

type SubmitTestSessionPayload = {
  sessionId: string;
  answers: SubmittedAnswer[];
  timeSpent?: number;
  completedAt?: string;
};

type ExitTestSessionPayload = {
  sessionId: string;
  userId: string;
  timeSpent: number;
  exitReason: string;
  exitedAt: string;
};

export class TestSessionAPI extends BaseAPI {
  // Get Test Session Questions
  static TestSessionQuestions(
    params: PagingAndSearch<{}>
  ) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params)
    ).toString()}`;
    return new BaseAPI(`/test-session${queryString}`).build<any[]>();
  }

  // Create New Test Session
  static CreateTestSession = (body: CreateTestSessionPayload) =>
    new BaseAPI(`/test-session/new`, body, "POST").build<TestSession>();

  // Submit Test Session
  static SubmitTestSession = (body: SubmitTestSessionPayload) =>
    new BaseAPI(`/test-session/submit`, body, "POST").build<TestResult>();

  // Exit Test Session
  static ExitTestSession = (body: ExitTestSessionPayload) =>
    new BaseAPI(`/test-session/exit`, body, "POST").build<{
      success: boolean;
    }>();
}
