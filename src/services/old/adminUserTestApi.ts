import { BaseAPI } from "../baseApi";
import { PagingAndSearch } from "../types";

export class UserTest<PERSON>I extends BaseAPI {
  // 👤 User Test Management
  static UserTests = () => new BaseAPI(`/user-tests`);

  static UserTestDetail = (userTestId: string) =>
    new BaseAPI(`/user-tests/${userTestId}`);

  static CreateUserTest = (body: any) =>
    new BaseAPI(`/user-tests`, body, "POST");

  static UpdateUserTest = (userTestId: string, body: any) =>
    new BaseAPI(`/user-tests/${userTestId}`, body, "PUT");

  static DeleteUserTest = (userTestId: string) =>
    new BaseAPI(`/user-tests/${userTestId}`, undefined, "DELETE");
}
