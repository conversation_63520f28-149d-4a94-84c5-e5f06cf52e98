import { BaseAPI } from "../baseApi"

export class Test<PERSON>roupAPI extends BaseAPI {
  static CreateTestGroup = (body: {
    type: string
    title: string
    test_ids: string[]
    question_ids: string[]
  }) => new BaseAPI(`/test-groups`, body, "POST")

  static UpdateTestGroup = (
    id: string,
    body: {
      type: string
      title: string
      test_ids: string[]
      question_ids: string[]
    }
  ) => new BaseAPI(`/test-groups/${id}`, body, "PUT")

  static TestGroup = (params?: { search?: string }) =>
    new BaseAPI(`/test-groups`)

  static TestGroupQuestions = (id: string) =>
    new BaseAPI(`/test-groups/${id}/questions`)

  static TestGroupTests = (id: string) =>
    new BaseAPI(`/test-groups/${id}/tests`)

  static TestGroupInfo = (id: string) =>
    new BaseAPI(`/test-groups/${id}/info`)

  static TestGroupInit = (id: string) =>
    new BaseAPI(`/test-groups/${id}/start`)

  static TestGroupQuestion = (params: {
    testId: string
    questionId: string
  }) =>
    new BaseAPI(
      `/test-groups/${params.testId}/questions/${params.questionId}`
    )

  static TestGroupQuestionResponse = (
    params: { testId: string; questionId: string },
    body: {
      type: "SKIP" | "ANSWER"
      answer?: string[]
      time_to_answer: number
    }
  ) =>
    new BaseAPI(
      `/test-groups/sessions/${params.testId}/questions/${params.questionId}/response`,
      body,
      "POST"
    )

  static TestGroupResult = (id: string) =>
    new BaseAPI(`/test-groups/sessions/${id}/result`)
}
