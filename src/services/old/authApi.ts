import { BaseAPI } from '../baseApi'

export class AuthAPI extends BaseAPI {
  static Login = (body: { username: string; password: string }) => 
    new BaseAPI(`/auth/signin`, body, "POST", false)

  static Register = (body: {
    username: string
    password: string
    fullname: string
    email: string
    type: string
  }) => new BaseAPI(`/auth/signup`, body, "POST", false)

  static MyProfile = new BaseAPI(`/profile`)
}