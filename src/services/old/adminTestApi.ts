import { BaseAPI } from "../baseApi";
import { PagingAndSearch } from "../types";

export class AdminTestAPI extends BaseAPI {
  // 📁 Test Management
  static Tests = (params: PagingAndSearch<{}>) => {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params)
    ).toString()}`;
    return new BaseAPI(`/tests${queryString}`);
  };

  static TestDetail = (testId: string) => new BaseAPI(`/tests/${testId}`);

  static CreateTest = (body: any) => new BaseAPI(`/tests`, body, "POST");

  static UpdateTest = (testId: string, body: any) =>
    new BaseAPI(`/tests/${testId}`, body, "PUT");

  static DeleteTest = (testId: string) =>
    new BaseAPI(`/tests/${testId}`, undefined, "DELETE");

  // 🔁 Bulk Operations
  static BulkDeleteTests = (ids: string[]) =>
    new BaseAPI(`/tests/bulk-delete`, { ids }, "POST");

  static BulkUpdateStatus = (payload: {
    ids: string[];
    status: "active" | "inactive" | "archived";
  }) => new BaseAPI(`/tests/bulk-status`, payload, "PATCH");

  // 🔄 Lifecycle Methods
  static PublishTest = (testId: string) =>
    new BaseAPI(`/tests/${testId}/publish`, undefined, "PATCH");

  static ArchiveTest = (testId: string) =>
    new BaseAPI(`/tests/${testId}/archive`, undefined, "PATCH");

  static RestoreArchivedTest = (testId: string) =>
    new BaseAPI(`/tests/${testId}/restore`, undefined, "PATCH");
}
