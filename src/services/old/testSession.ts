import { TestSession } from "@/src/lib/repositories/test-session/TestSessionRepository";
import { BaseAPI } from "../baseApi";
import { PagingAndSearch } from "../types";

export class TestSessionAPI extends BaseAPI {
  // 🕒 Session Queries
  static TestSessions = (params: PagingAndSearch<{}>) => {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params)
    ).toString()}`;
    return new BaseAPI(`/test-session/me${queryString}`);
  };

  static New = (payload: {
    testId: string;
    user: { id: string; name: string; email: string };
  }) => {
    return new BaseAPI(
      `/test-session/new`,
      payload,
      "POST"
    ).build<TestSession>();
  };

  static Submit = (body: any) => {
    return new BaseAPI(`/test-session/submit`, body, "POST");
  };

  static Exit = (payload: {
    sessionId: string;
    userId: string;
    timeSpent: number;
    exitReason: string;
    exitedAt: string;
  }) => {
    return new BaseAPI(`/test-session/exit`, payload, "POST");
  };
}
