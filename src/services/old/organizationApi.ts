import { BaseAPI } from '../baseApi'
import { PagingAndSearch } from '../types'

export class OrganizationAPI extends BaseAPI {
  static Organizations = ({ search }: { search?: string }) => 
    new BaseAPI(`/organizations?search=${search || ""}`)
    
  static MyOrganizations = () => new BaseAPI(`/organizations/me`)
  static OrganizationDetail = (organizationId: string) => new BaseAPI(`/organizations/${organizationId}`)
  static CreateOrganization = (body: any) => new BaseAPI(`/organizations`, body, "POST")
  static UpdateOrganization = (organizationId: string, body: any) =>
    new BaseAPI(`/organizations/${organizationId}`, body, "PUT")
  static DeleteOrganization = (organizationId: string) =>
    new BaseAPI(`/organizations/${organizationId}`, undefined, "DELETE")

  // Organization Member methods
  static OrganizationMembers(organizationID: string, params: PagingAndSearch<{}>): BaseAPI {
    const queryString = `?${new URLSearchParams(PagingAndSearch.toRecordString(params)).toString()}`
    return new BaseAPI(`/organizations/${organizationID}/members${queryString}`)
  }
  
  static OrganizationMemberDetail = (organizationID: string, memberId: string) =>
    new BaseAPI(`/organizations/${organizationID}/members/${memberId}`)
    
  static CreateOrganizationMember = (organizationID: string, body: any) =>
    new BaseAPI(`/organizations/${organizationID}/members`, body, "POST")
    
  static UpdateOrganizationMember = (organizationID: string, memberId: string, body: any) =>
    new BaseAPI(`/organizations/${organizationID}/members/${memberId}`, body, "PUT")
    
  static DeleteOrganizationMember = (organizationID: string, memberId: string) =>
    new BaseAPI(`/organizations/${organizationID}/members/${memberId}`, undefined, "DELETE")
}