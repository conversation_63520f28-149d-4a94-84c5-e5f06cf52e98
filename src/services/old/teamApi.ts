import { BaseAPI } from '../baseApi'
import { PagingAndSearch } from '../types'

export class TeamAPI extends BaseAPI {
  static Teams = ({ search }: { search?: string }) => 
    new BaseAPI(`/teams?search=${search || ""}`)
    
  static TeamDetail = (teamId: string) => new BaseAPI(`/teams/${teamId}`)
  static CreateTeam = (body: any) => new BaseAPI(`/teams`, body, "POST")
  static UpdateTeam = (teamId: string, body: any) => new BaseAPI(`/teams/${teamId}`, body, "PUT")
  static DeleteTeam = (teamId: string) => new BaseAPI(`/teams/${teamId}`, undefined, "DELETE")

  // Team Member methods
  static TeamMembers(teamID: string, params: PagingAndSearch<{}>): BaseAPI {
    const queryString = `?${new URLSearchParams(PagingAndSearch.toRecordString(params)).toString()}`
    return new BaseAPI(`/teams/${teamID}/members${queryString}`)
  }
  
  static TeamMemberDetail = (teamID: string, memberId: string) => 
    new BaseAPI(`/teams/${teamID}/members/${memberId}`)
    
  static CreateTeamMember = (teamID: string, body: any) => 
    new BaseAPI(`/teams/${teamID}/members`, body, "POST")
    
  static UpdateTeamMember = (teamID: string, memberId: string, body: any) =>
    new BaseAPI(`/teams/${teamID}/members/${memberId}`, body, "PUT")
    
  static DeleteTeamMember = (teamID: string, memberId: string) =>
    new BaseAPI(`/teams/${teamID}/members/${memberId}`, undefined, "DELETE")
}