import { 
  MyCourse, 
  MyCourseDetail, 
  MyCourseResponse, 
  MyCoursesTotalLearningStats,
  VideoComment,
  MyCourseNote,
  MyCoursesMCQTest,
  MyCoursesFeedback
} from "@/src/lib/repositories/my-courses/MyCoursesRepository";
import { BaseAPI } from "./baseApi";
import { PagingAndSearch } from "./types";

type MyCoursePayload = Partial<MyCourse>;
type VideoCommentPayload = Partial<VideoComment>;
type MyCourseNotePayload = Partial<MyCourseNote>;
type MyCoursesFeedbackPayload = Partial<MyCoursesFeedback>;

export class MyCoursesAPI extends BaseAPI {
  static MyCourses(
    params: PagingAndSearch<{
      category?: string;
      search?: string;
    }>
  ) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params)
    ).toString()}`;
    return new BaseAPI(`/my-courses${queryString}`).build<MyCourseResponse>();
  }

  static MyCourseDetail = (courseId: string) =>
    new BaseAPI(`/my-courses/${courseId}`).build<MyCourse>();

  static CreateMyCourse = (body: MyCoursePayload) =>
    new BaseAPI(`/my-courses`, body, "POST").build<MyCourse>();

  static UpdateMyCourse = (courseId: string, body: MyCoursePayload) =>
    new BaseAPI(`/my-courses/${courseId}`, body, "PUT").build<MyCourse>();

  static DeleteMyCourse = (courseId: string) =>
    new BaseAPI(`/my-courses/${courseId}`, undefined, "DELETE").build<{
      success: boolean;
    }>();

  static TotalLearningStats = () =>
    new BaseAPI(`/my-courses/stats`).build<MyCoursesTotalLearningStats>();

  // Video Comments
  static VideoComments(
    params: PagingAndSearch<{
      id?: string;
      filter?: 'all' | 'my';
      currentUserName?: string;
    }>
  ) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params)
    ).toString()}`;
    return new BaseAPI(`/my-courses/video-comments${queryString}`).build<VideoComment[]>();
  }

  static CreateVideoComment = (body: VideoCommentPayload) =>
    new BaseAPI(`/my-courses/video-comments`, body, "POST").build<VideoComment>();

  // Course Notes
  static CourseNotes(
    params: PagingAndSearch<{
      id?: string;
      filter?: 'this-video' | 'this-section' | 'all';
    }>
  ) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params)
    ).toString()}`;
    return new BaseAPI(`/my-courses/notes${queryString}`).build<MyCourseNote[]>();
  }

  static CreateCourseNote = (body: MyCourseNotePayload) =>
    new BaseAPI(`/my-courses/notes`, body, "POST").build<MyCourseNote>();

  static UpdateCourseNote = (noteId: string, body: MyCourseNotePayload) =>
    new BaseAPI(`/my-courses/notes/${noteId}`, body, "PUT").build<MyCourseNote>();

  static DeleteCourseNote = (noteId: string) =>
    new BaseAPI(`/my-courses/notes/${noteId}`, undefined, "DELETE").build<{
      success: boolean;
    }>();

  // MCQ Tests
  static CourseMCQTests(
    params: PagingAndSearch<{
      course_id?: string;
    }>
  ) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params)
    ).toString()}`;
    return new BaseAPI(`/my-courses/mcq-tests${queryString}`).build<MyCoursesMCQTest[]>();
  }

  // Course Feedback
  static CreateCourseFeedback = (body: MyCoursesFeedbackPayload) =>
    new BaseAPI(`/my-courses/feedback`, body, "POST").build<MyCoursesFeedback>();
}
