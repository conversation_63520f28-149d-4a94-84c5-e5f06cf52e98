import { BaseAPI } from "./baseApi";

interface Creator {
  id: string;
  name: string;
  avatar?: string;
}

interface CreatorListResponse {
  items: Creator[];
  page: number;
  total: number;
}

interface CreatorCreateInput {
  name: string;
  avatar?: string;
}

export class CreatorsAPI {
  static SearchCreators = (query: string, page: number = 1) => {
    const queryString = `?q=${encodeURIComponent(query)}&page=${page}`;
    return new BaseAPI(`/creators${queryString}`).build<CreatorListResponse>();
  };

  static CreateCreator = (body: CreatorCreateInput) => {
    return new BaseAPI(`/creators`, body, "POST").build<Creator>();
  };
}
