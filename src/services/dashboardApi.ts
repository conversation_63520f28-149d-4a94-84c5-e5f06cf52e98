import { ActivityItem } from "@/src/lib/repositories/dashboard/overview/OverviewRepository";
import { UserStats } from "@/src/lib/repositories/dashboard/summary/DashboardSummaryRepository";
import { RecommendationsCourse } from "@/src/lib/repositories/dashboard/recommendations-courses/RecommendationsCoursesRepository";
import { BaseAPI } from "./baseApi";
import { PagingAndSearch } from "./types";
import { ProgressStats } from "../lib/repositories/dashboard/progress/ProgressRepository";

export class DashboardAPI extends BaseAPI {
  // Dashboard Overview
  static DashboardOverview(
    params: PagingAndSearch<{
      type?: string;
      search?: string;
    }>
  ) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params)
    ).toString()}`;
    return new BaseAPI(`/dashboard/overview${queryString}`).build<ActivityItem[]>();
  }

  // Dashboard Summary
  static DashboardSummary = (userId: string) =>
    new BaseAPI(`/dashboard/summary/${userId}`).build<UserStats>();

  // Dashboard Progress
  static DashboardProgress = (userId: string) =>
    new BaseAPI(`/dashboard/progress/${userId}`).build<ProgressStats>();

  // Dashboard Recommendations
  static DashboardRecommendations(
    params: PagingAndSearch<{
      level?: string;
      search?: string;
    }>
  ) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params)
    ).toString()}`;
    return new BaseAPI(`/dashboard/recommendations-courses${queryString}`).build<RecommendationsCourse[]>();
  }
}
