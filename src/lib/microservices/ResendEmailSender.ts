// src/services/ResendEmailSender.ts
import { Resend } from "resend";
import { EmailSender } from "./EmailSender";
import { locales } from "@/src/lib/repositories/email-locales";
import { localize } from "@/src/localization/functions/server";

const resend = new Resend(process.env.RESEND_API_KEY);
const APP_BASE_URL = process.env.NEXT_PUBLIC_APP_BASE_URL!;
const ACCOUNT_FROM_EMAIL = `SkillPintar Account <account@${process.env.EMAIL_DOMAIN_SEND!}>`;

export class ResendEmailSender implements EmailSender {
    async sendEmailVerification(email: string, token: string): Promise<void> {
        const { t } = await localize("email-sender", locales);
        const url = `${APP_BASE_URL}/auth/verify-email?token=${token}`;

        await resend.emails.send({
            from: ACCOUNT_FROM_EMAIL,
            to: email,
            subject: t("verify_subject"),
            html: `
        <p style="font-family: sans-serif; font-size: 16px;">
          ${t("verify_html", { url })}
        </p>
        <p>${t("verify_ignore")}</p>
      `,
            text: `${t("verify_text", { url })}\n\n${t("verify_ignore")}`,
        });
    }

    async sendPasswordReset(email: string, token: string): Promise<void> {
        const { t } = await localize("email-sender", locales);
        const url = `${APP_BASE_URL}/auth/reset-password?token=${token}`;

        await resend.emails.send({
            from: ACCOUNT_FROM_EMAIL,
            to: email,
            subject: t("reset_subject"),
            html: `
        <p style="font-family: sans-serif; font-size: 16px;">
          ${t("reset_html", { url })}
        </p>
        <p>${t("reset_ignore")}</p>
      `,
            text: `${t("reset_text", { url })}\n\n${t("reset_ignore")}`,
        });
    }
}
