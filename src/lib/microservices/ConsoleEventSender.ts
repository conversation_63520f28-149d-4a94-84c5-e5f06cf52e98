import { EventSender, UserEvent, TestEvent, TestSessionEvent, SystemEvent } from './EventSender';

export class ConsoleEventSender implements EventSender {
  async connect() {
    console.log('[ConsoleEventSender] Connected (no-op)');
  }

  async disconnect() {
    console.log('[ConsoleEventSender] Disconnected (no-op)');
  }

  async sendUserEvent(event: UserEvent): Promise<string> {
    const timestamp = new Date();
    const eventId = this.generateId();
    console.log(`[UserEvent] ${eventId}`, {
      type: event.type,
      timestamp,
      payload: event.payload,
    });
    return eventId;
  }

  async sendTestEvent(event: TestEvent): Promise<string> {
    const timestamp = new Date();
    const eventId = this.generateId();
    console.log(`[TestEvent] ${eventId}`, {
      type: event.type,
      timestamp,
      payload: event.payload,
    });
    return eventId;
  }

  async sendTestSessionEvent(event: TestSessionEvent): Promise<string> {
    const timestamp = new Date();
    const eventId = this.generateId();
    console.log(`[TestSessionEvent] ${eventId}`, {
      type: event.type,
      timestamp,
      payload: event.payload,
    });
    return eventId;
  }

  async sendSystemEvent(event: SystemEvent): Promise<string> {
    const timestamp = new Date();
    const eventId = this.generateId();
    console.log(`[SystemEvent] ${eventId}`, {
      type: event.type,
      timestamp,
      payload: event.payload,
    });
    return eventId;
  }

  private generateId(): string {
    return `${Date.now()}-${Math.floor(Math.random() * 1000)}`;
  }
}
