import { MongoDriver } from "../repositories/MongoDriver";
import { EventSender, SystemEvent, UserEvent } from "./EventSender";

export class SimpleEventSender implements EventSender {

    constructor(private db: MongoDriver) { }

    async sendUserEvent(notification: UserEvent): Promise<void> {
        console.log("User Event Sent:", notification);
        // Here you might publish to a queue or save to event store
    }

    async sendSystemEvent(notification: SystemEvent): Promise<void> {
        console.log("System Event Sent:", notification);
        // Here you might publish to a queue or save to event store
    }
}