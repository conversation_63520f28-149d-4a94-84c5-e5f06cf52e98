import { driver } from "../repositories/LiveMongoDriver";
import { KafkaEventSender } from "./KafkaEventSender";
import { EventSender } from "./EventSender";
import { ConsoleEventSender } from "./ConsoleEventSender";

const brokers = process.env.KAFKA_BROKERS ? process.env.KAFKA_BROKERS.split(",") : [];

let eventService: EventSender;

if (process.env.NODE_ENV === "development" || brokers.length === 0) {
    console.warn("[EventSender] Using ConsoleEventSender (development or no brokers configured)");
    const _eventService = new ConsoleEventSender();
    _eventService.connect();
    eventService = _eventService;
} else {
    const _eventService = new KafkaEventSender(driver, brokers);
    _eventService.connect();
    eventService = _eventService;
}

export { eventService };
