// Base event structure with snake_case type naming
export interface BaseEvent<TType extends string, TPayload = any> {
  type: TType;
  payload: TPayload;
}

// ✨ User test-related events
export type UserTestEvent =
  | BaseEvent<"test_started", { test_id: string; user_id: string }>
  | BaseEvent<"test_completed", { test_id: string; user_id: string }>;

// ✨ User authentication events
export type UserAuthEvent = 
  | BaseEvent<"password_changed", { user_id: string }>
  | BaseEvent<"email_verified", { user_id: string }>
  | BaseEvent<"account_login", { user_id: string }>
  | BaseEvent<"account_deleted", { user_id: string }>
  | BaseEvent<"user_registered", { user_id: string; email: string }>
  | BaseEvent<"password_reset_requested", { user_id: string; reset_token: string }>
  | BaseEvent<"password_reset_completed", { user_id: string }>
  | BaseEvent<"token_refreshed", { user_id: string; old_token_id: string }>
  ;

// 👥 All user-related events
export type UserEvent = UserTestEvent | UserAuthEvent;

// ⚙️ System-level events
export type SystemEvent =
  | BaseEvent<"system", { message: string }>
  | BaseEvent<"test_reminder", { test_id: string; user_id: string }>;

// 📦 Final union type for all events
export type EventEvent = UserEvent | SystemEvent;

// 🎯 Event sender interface
export interface EventSender {
  sendUserEvent(event: UserEvent): Promise<void>;
  sendSystemEvent(event: SystemEvent): Promise<void>;
}
