import { v4 as uuidv4 } from "uuid"

// Types
type Question = any
type QueryParams = {
  type?: string
  difficulty?: string
  tag?: string
  createdBy?: string
  limit: number
  skip: number
}

// In-memory data store
let questions: Question[] = []

// Initialize with some sample data
const initializeSampleData = () => {
  questions = [
    {
      _id: "sample1",
      type: "multiple_choice",
      question: "What is the capital of France?",
      options: ["London", "Berlin", "Paris", "Madrid"],
      correctAnswer: 2, // Paris
      difficulty: "easy",
      tags: ["geography", "europe"],
      createdBy: "system",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      _id: "sample2",
      type: "free_text",
      question: "Explain the concept of object-oriented programming.",
      sampleAnswer: "Object-oriented programming is a programming paradigm based on the concept of objects...",
      difficulty: "medium",
      tags: ["programming", "computer science"],
      createdBy: "system",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
  ]
}

// Initialize sample data
initializeSampleData()

// Mock data store functions
export const mockDataStore = {
  // Create a question
  createQuestion: async (question: Question) => {
    const _id = uuidv4()
    const now = new Date().toISOString()

    const newQuestion = {
      _id,
      ...question,
      createdAt: now,
      updatedAt: now,
    }

    questions.push(newQuestion)

    return {
      insertedId: _id,
      acknowledged: true,
    }
  },

  // Get questions with filtering
  getQuestions: async (params: QueryParams) => {
    const { type, difficulty, tag, createdBy, limit, skip } = params

    let filteredQuestions = [...questions]

    // Apply filters
    if (type) {
      filteredQuestions = filteredQuestions.filter((q) => q.type === type)
    }

    if (difficulty) {
      filteredQuestions = filteredQuestions.filter((q) => q.difficulty === difficulty)
    }

    if (tag) {
      filteredQuestions = filteredQuestions.filter((q) => q.tags && q.tags.includes(tag))
    }

    if (createdBy) {
      filteredQuestions = filteredQuestions.filter((q) => q.createdBy === createdBy)
    }

    // Sort by createdAt (newest first)
    filteredQuestions.sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    })

    // Apply pagination
    const paginatedQuestions = filteredQuestions.slice(skip, skip + limit)

    return {
      questions: paginatedQuestions,
      total: filteredQuestions.length,
    }
  },

  // Bulk create questions
  bulkCreateQuestions: async (questionsToCreate: Question[]) => {
    const now = new Date().toISOString()
    const insertedIds: Record<string, string> = {}

    questionsToCreate.forEach((question, index) => {
      const _id = uuidv4()
      insertedIds[index] = _id

      const newQuestion = {
        _id,
        ...question,
        createdAt: now,
        updatedAt: now,
      }

      questions.push(newQuestion)
    })

    return {
      insertedCount: questionsToCreate.length,
      insertedIds,
      acknowledged: true,
    }
  },

  // Get all questions (for debugging)
  getAllQuestions: async () => {
    return [...questions]
  },

  // Clear all questions
  clearQuestions: async () => {
    questions = []
    return { acknowledged: true, deletedCount: questions.length }
  },

  // Reset to sample data
  resetToSampleData: async () => {
    initializeSampleData()
    return { acknowledged: true }
  },
}
