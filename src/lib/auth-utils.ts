"use client";

import { useEffect, useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import { AuthAPI } from "../services/authApi";
import { secureStorage, StorageKeys } from "../utils/SecureStorage";

export function useAuth() {
  const router = useRouter();
  const [isChecked, setIsChecked] = useState(false);

  useEffect(() => {
    const checkAuth = () => {
      const user = localStorage.getItem("currentUser");
      if (!user) {
        router.push("/auth/login");
      }
      setIsChecked(true);
    };

    if (typeof window !== "undefined" && !isChecked) {
      checkAuth();
    }
  }, [router, isChecked]);

  const getCurrentUser = useCallback(() => {
    if (typeof window !== "undefined") {
      const user = localStorage.getItem("currentUser");
      return user ? JSON.parse(user) : null;
    }
    return null;
  }, []);

  const performLogout = useCallback(async () => {
    try {
      await AuthAPI.Logout().request();
    } catch (error) {
      console.error("Failed to logout:", error);
    } finally {
      secureStorage.removeItem(StorageKeys.UserToken);
      secureStorage.removeItem(StorageKeys.RefreshToken);
      secureStorage.removeItem(StorageKeys.CookieToken);
      router.push("/tests");
    }
  }, [router]);

  return {
    getCurrentUser,
    logout: performLogout,
    isChecked,
  };
}
