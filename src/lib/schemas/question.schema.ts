import { z } from "zod"

// Base question schema
const baseQuestionSchema = z.object({
  question: z.string().min(1, "Question is required"),
  difficulty: z.enum(["easy", "medium", "hard"]),
  tags: z.array(z.string()).optional(),
  timeLimit: z.number().optional(),
  points: z.number().optional().default(1),
})

// Multiple choice question schema
const multipleChoiceQuestionSchema = baseQuestionSchema.extend({
  type: z.literal("multipleChoice"),
  options: z.array(z.string()).min(2, "At least 2 options are required"),
  correctAnswer: z.number().min(0, "Correct answer index must be valid"),
})

// Free text question schema
const freeTextQuestionSchema = baseQuestionSchema.extend({
  type: z.literal("free_text"),
  sampleAnswer: z.string().optional(),
})

// Code input question schema
const codeInputQuestionSchema = baseQuestionSchema.extend({
  type: z.literal("code_input"),
  language: z.string().optional(),
  sampleSolution: z.string().optional(),
})

// File upload question schema
const fileUploadQuestionSchema = baseQuestionSchema.extend({
  type: z.literal("file_upload"),
  allowedFileTypes: z.array(z.string()).optional(),
  maxFileSize: z.number().optional(),
})

// Voice input question schema
const voiceInputQuestionSchema = baseQuestionSchema.extend({
  type: z.literal("voice_input"),
  maxDuration: z.number().optional(),
})

// Image-based question schema
const imageBasedQuestionSchema = baseQuestionSchema.extend({
  type: z.literal("image_based"),
  imageUrl: z.string().optional(),
})

// Audio-based question schema
const audioBasedQuestionSchema = baseQuestionSchema.extend({
  type: z.literal("audio_based"),
  audioUrl: z.string().optional(),
})

// Audio answer question schema
const audioAnswerQuestionSchema = baseQuestionSchema.extend({
  type: z.literal("audio_answer"),
  maxDuration: z.number().optional(),
})

// Union of all question types
export const questionSchema = z.discriminatedUnion("type", [
  multipleChoiceQuestionSchema,
  freeTextQuestionSchema,
  codeInputQuestionSchema,
  fileUploadQuestionSchema,
  voiceInputQuestionSchema,
  imageBasedQuestionSchema,
  audioBasedQuestionSchema,
  audioAnswerQuestionSchema,
])

// Bulk import schema
export const bulkImportSchema = z.object({
  questions: z.array(questionSchema),
})
