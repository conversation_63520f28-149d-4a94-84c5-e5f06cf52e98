export interface BaseOption {
  id: string
  text?: string
  audio?: string
  image?: string
  // isCorrect removed as requested
}

// Base question interface
export interface Question {
  id: number
  type: string // plugin-registered question type
  question: string
  image?: string
  audio?: string
  options?: BaseOption[]
  correctAnswers?: string[] // Array of option IDs that are correct
  assessmentCriteria?: string // How to assess open-ended answers
  rubric?: string // Detailed rubric for grading
  wordLimit?: number // Word limit for text responses
  timeLimit?: number // Time limit for audio/video responses
  allowedFileTypes?: string[] // Allowed file types for uploads
  fileSizeLimit?: number // File size limit in MB
  autoGrade?: boolean // Whether to auto-grade
  language?: string // Programming language for code questions
  sampleSolution?: string // Sample solution for code/text questions
  tags?: string[] // Question tags
  competency?: string // Competency level
  notes?: string // Internal notes
  metadata?: Record<string, any> // Extra data for plugins
}

// Plugin props interface
export interface PluginProps {
  question: Question
  onChange: (updatedQuestion: Partial<Question>) => void
}

// Answer interface for different question types
export interface Answer {
  questionId: number
  value: any // Could be string[] for multiple choice, string for text, etc.
  timestamp: number
}

// Student-facing plugin props
export interface StudentPluginProps {
  question: Question
  answer: Answer | undefined
  onAnswer: (answer: Answer) => void
  readonly?: boolean
}
