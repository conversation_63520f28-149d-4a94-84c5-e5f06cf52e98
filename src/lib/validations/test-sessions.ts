import { z } from "zod";

const testUserSchema = z.object({
  id: z.string().min(1, "User ID is required"),
  name: z.string().min(1, "User name is required"),
  email: z.string().email("Valid email is required"),
});

const submittedAnswerSchema = z.object({
  questionId: z.string().min(1, "Question ID is required"),
  answer: z.union([z.string(), z.array(z.string())]).refine(
    (value) => {
      if (typeof value === "string") return value.trim().length > 0;
      if (Array.isArray(value)) return value.length > 0 && value.every(v => v.trim().length > 0);
      return false;
    },
    "Answer cannot be empty"
  ),
  timeSpent: z.number().min(0).optional(),
  answeredAt: z.date().optional(),
});

export const TestSessionCreateSchema = z.object({
  testId: z.string().min(1, "Test ID is required"),
  user: testUserSchema,
  expiresAt: z.date().optional(),
  createdBy: z.string().min(1, "Created by is required"),
});

export const TestSessionUpdateSchema = z.object({
  status: z.enum([
    "OPEN",
    "COMPLETED",
    "EXPIRED",
    "CANCELLED",
  ]).optional(),
  completedAt: z.date().optional(),
  timeSpent: z.number().min(0).optional(),
  currentQuestionIndex: z.number().min(0).optional(),
  answers: z.array(submittedAnswerSchema).optional(),
  score: z.number().min(0).max(100).optional(),
  exitReason: z.string().optional(),
  updatedBy: z.string().min(1, "Updated by is required").optional(),
});

export const TestSessionIdSchema = z.object({
  id: z.string().min(1, "Test Session ID is required"),
});

// Specific schemas for test session actions
export const SubmitAnswersSchema = z.object({
  sessionId: z.string().min(1, "Session ID is required"),
  answers: z.array(submittedAnswerSchema).min(1, "At least one answer is required"),
});

export const ExitSessionSchema = z.object({
  sessionId: z.string().min(1, "Session ID is required"),
  exitReason: z.string().min(1, "Exit reason is required"),
  timeSpent: z.number().min(0, "Time spent cannot be negative"),
});

export const GetSessionsByTestIdSchema = z.object({
  testId: z.string().min(1, "Test ID is required"),
});

export const GetSessionsByUserIdSchema = z.object({
  userId: z.string().min(1, "User ID is required"),
});

export type TestSessionCreateInput = z.infer<typeof TestSessionCreateSchema>;
export type TestSessionUpdateInput = z.infer<typeof TestSessionUpdateSchema>;
export type TestSessionIdInput = z.infer<typeof TestSessionIdSchema>;
export type SubmitAnswersInput = z.infer<typeof SubmitAnswersSchema>;
export type ExitSessionInput = z.infer<typeof ExitSessionSchema>;
export type GetSessionsByTestIdInput = z.infer<typeof GetSessionsByTestIdSchema>;
export type GetSessionsByUserIdInput = z.infer<typeof GetSessionsByUserIdSchema>;