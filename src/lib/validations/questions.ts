import { z } from "zod";

export const QuestionCreateSchema = z.object({
  question: z.string().min(1, "Question text is required"),
  type: z.string().min(1, "Question type is required"),
  options: z.array(z.object({
    id: z.string().min(1, "Option ID is required"),
    text: z.string().min(1, "Option text is required"),
    correct: z.boolean().optional(), // Optional for multiple choice questions  
  })).optional(),
  correctAnswer: z.string().optional(),
  difficulty: z.string().min(1, "Difficulty is required"),
  tags: z.array(z.string()).optional().default([]),
  isActive: z.boolean().optional().default(true),
  createdBy: z.string().min(1, "Created by is required"),
});

export const QuestionUpdateSchema = z.object({
  question: z.string().min(1, "Question text is required").optional(),
  type: z.string().min(1, "Question type is required").optional(),
  options: z.array(z.object({
    id: z.string().min(1, "Option ID is required"),
    text: z.string().min(1, "Option text is required"),
    correct: z.boolean().optional(), // Optional for multiple choice questions  
  })).optional(),
  correctAnswer: z.string().optional(),
  difficulty: z.string().min(1, "Difficulty is required").optional(),
  tags: z.array(z.string()).optional(),
  isActive: z.boolean().optional(),
  updatedBy: z.string().min(1, "Updated by is required").optional(),
});

export const QuestionIdSchema = z.object({
  id: z.string().min(1, "Question ID is required"),
});

export type QuestionCreateInput = z.infer<typeof QuestionCreateSchema>;
export type QuestionUpdateInput = z.infer<typeof QuestionUpdateSchema>;
export type QuestionIdInput = z.infer<typeof QuestionIdSchema>;
