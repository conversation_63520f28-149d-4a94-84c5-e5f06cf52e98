import { z } from "zod";

const creatorSchema = z.object({
  id: z.string().min(1, "Creator ID is required"),
  name: z.string().min(1, "Creator name is required"),
  avatar: z.string().optional(),
});

const participantSchema = z.object({
  id: z.string().min(1, "Participant ID is required"),
  name: z.string().min(1, "Participant name is required"),
  avatar: z.string().optional(),
});

const availabilitySchema = z.object({
  isLimitedTime: z.boolean(),
  startTime: z.date(),
  endTime: z.date(),
});

const premiumSchema = z.object({
  isPremium: z.boolean(),
  price: z.string().optional(),
  offerText: z.string().optional(),
  badges: z.array(z.string()).optional(),
});

const subtopicSchema = z.object({
  title: z.string().min(1, "Subtopic title is required"),
  description: z.string().min(1, "Subtopic description is required"),
});

const topicSchema = z.object({
  title: z.string().min(1, "Topic title is required"),
  description: z.string().min(1, "Topic description is required"),
  subtopics: z.array(subtopicSchema).optional(),
});

export const TestCreateSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  access: z.enum([
    "PUBLIC",
    "PRIVATE",
  ]).optional().default("PRIVATE"),
  status: z.enum([
    "ACTIVE",
    "INACTIVE", 
    "ARCHIVED",
    "DRAFT",
    "inprogress",
    "done",
    "cancelled",
  ]).optional().default("DRAFT"),
  categories: z.array(z.string()).optional(),
  difficulties: z.array(z.string()).optional(),
  benefits: z.array(z.string()).optional(),
  // benefits: z.array(z.string().min(1, "Benefit cannot be empty")).min(1, "At least one benefit is required"),
  usersCount: z.number().min(0).optional().default(0),
  successRate: z.number().min(0).max(100).optional(),
  durationInSeconds: z.number().min(1).optional(),
  totalQuestions: z.number().min(0).optional().default(0),
  questionIds: z.array(z.string()).optional().default([]),
  image: z.string().optional(),
  cover: z.string().optional(),
  mediaUrl: z.string().optional(),
  reason: z.string().optional(),
  creator: creatorSchema,
  participants: z.array(participantSchema).optional(),
  availability: availabilitySchema.optional(),
  premium: premiumSchema.optional(),
  topics: z.array(topicSchema).optional(),
  createdBy: z.string().min(1, "Created by is required"),
});

export const TestUpdateSchema = z.object({
  title: z.string().min(1, "Title is required").optional(),
  description: z.string().optional(),
  access: z.enum([
    "PUBLIC",
    "PRIVATE",
  ]).optional().default("PRIVATE"),
  status: z.enum([
    "ACTIVE",
    "INACTIVE",
    "ARCHIVED", 
    "DRAFT",
    "inprogress",
    "done",
    "cancelled",
  ]).optional(),
  categories: z.array(z.string()).optional(),
  difficulties: z.array(z.string()).optional(),
  benefits: z.array(z.string().min(1, "Benefit cannot be empty")).optional(),
  usersCount: z.number().min(0).optional(),
  successRate: z.number().min(0).max(100).optional(),
  durationInSeconds: z.number().min(1).optional(),
  totalQuestions: z.number().min(0).optional(),
  questionIds: z.array(z.string()).optional(),
  image: z.string().optional(),
  cover: z.string().optional(),
  mediaUrl: z.string().optional(),
  reason: z.string().optional(),
  creator: creatorSchema.optional(),
  participants: z.array(participantSchema).optional(),
  availability: availabilitySchema.optional().nullable(),
  premium: premiumSchema.optional(),
  topics: z.array(topicSchema).optional(),
  updatedBy: z.string().min(1, "Updated by is required").optional(),
});

export const TestIdSchema = z.object({
  id: z.string().min(1, "Test ID is required"),
});

export type TestCreateInput = z.infer<typeof TestCreateSchema>;
export type TestUpdateInput = z.infer<typeof TestUpdateSchema>;
export type TestIdInput = z.infer<typeof TestIdSchema>; 