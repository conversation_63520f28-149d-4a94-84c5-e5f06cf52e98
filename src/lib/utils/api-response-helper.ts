/**
 * Helper functions to create standardized API responses with translation keys
 * instead of hardcoded strings
 */

import { ERROR_CODES } from "@/src/app/api/error_codes";
import MESSAGE_KEYS from "@/src/app/api/message_keys";
import { ResponseWrapper } from "../types/responseWrapper";

// Success response helpers
export function createSuccessResponse<T>(
  data: T,
  messageKey?: string,
  status: number = 200
): {
  status: number;
  body: ResponseWrapper<T>;
} {
  return {
    status,
    body: new ResponseWrapper(
      "success",
      data,
      undefined,
      undefined,
      messageKey ? [messageKey] : []
    )
  };
}

// Error response helpers
export function createErrorResponse(
  error: string[],
  errorCodes: string[],
  status: number = 400
): {
  status: number;
  body: ResponseWrapper<any>;
} {
  return {
    status,
    body: new ResponseWrapper(
      "failed",
      undefined,
      error,
      errorCodes
    )
  };
}

// Validation error response
export function createValidationErrorResponse(
  validationErrors: string[]
): {
  status: number;
  body: ResponseWrapper<any>;
} {
  return createErrorResponse(
    validationErrors,
    [ERROR_CODES.VALIDATION_FAILED],
    400
  );
}

// Not found error response
export function createNotFoundResponse(
  entityType: string = "resource"
): {
  status: number;
  body: ResponseWrapper<any>;
} {
  let messageKey: string = MESSAGE_KEYS.ERROR.NOT_FOUND;

  // Handle specific entity types
  if (entityType === "USER") {
    messageKey = MESSAGE_KEYS.USER.NOT_FOUND;
  }

  return createErrorResponse(
    [messageKey],
    [ERROR_CODES.NOT_FOUND],
    404
  );
}

// Duplicate resource error response
export function createDuplicateResourceResponse(
  field?: string
): {
  status: number;
  body: ResponseWrapper<any>;
} {
  let messageKey: string = MESSAGE_KEYS.ERROR.DUPLICATE_RESOURCE;

  if (field) {
    if (field === "phone") {
      messageKey = MESSAGE_KEYS.ERROR.DUPLICATE_PHONE;
    } else if (field === "email") {
      messageKey = MESSAGE_KEYS.ERROR.DUPLICATE_EMAIL;
    } else if (field === "name") {
      messageKey = MESSAGE_KEYS.ERROR.DUPLICATE_NAME;
    }
  }

  return createErrorResponse(
    [messageKey],
    [ERROR_CODES.DUPLICATE_RESOURCE],
    409
  );
}

// Internal server error response
export function createInternalServerErrorResponse(
  messageKey: string = MESSAGE_KEYS.ERROR.INTERNAL_SERVER_ERROR
): {
  status: number;
  body: ResponseWrapper<any>;
} {
  return createErrorResponse(
    [messageKey],
    [ERROR_CODES.INTERNAL_SERVER_ERROR],
    500
  );
}

export const UserResponses = {
  created: (data: any) => createSuccessResponse(data, MESSAGE_KEYS.USER.CREATED, 201),
  updated: (data: any) => createSuccessResponse(data, MESSAGE_KEYS.USER.UPDATED),
  deleted: () => createSuccessResponse({ message: MESSAGE_KEYS.USER.DELETED }, MESSAGE_KEYS.USER.DELETED),
  notFound: () => createNotFoundResponse("USER"),
  createFailed: () => createInternalServerErrorResponse(MESSAGE_KEYS.USER.CREATE_FAILED),
  updateFailed: () => createInternalServerErrorResponse(MESSAGE_KEYS.USER.UPDATE_FAILED),
  deleteFailed: () => createInternalServerErrorResponse(MESSAGE_KEYS.USER.DELETE_FAILED),
  fetchFailed: () => createInternalServerErrorResponse(MESSAGE_KEYS.USER.FETCH_FAILED),
  duplicateEmail: () => createDuplicateResourceResponse("email")
};

// Search and filter specific responses
export const SearchResponses = {
  emptyKeyword: () => createErrorResponse(
    [MESSAGE_KEYS.ERROR.SEARCH_KEYWORD_EMPTY],
    [ERROR_CODES.VALIDATION_FAILED],
    400
  ),
  emptyFilterField: () => createErrorResponse(
    [MESSAGE_KEYS.ERROR.FILTER_FIELD_EMPTY],
    [ERROR_CODES.VALIDATION_FAILED],
    400
  ),
  emptySortField: () => createErrorResponse(
    [MESSAGE_KEYS.ERROR.SORT_FIELD_EMPTY],
    [ERROR_CODES.VALIDATION_FAILED],
    400
  ),
  invalidSortDirection: () => createErrorResponse(
    [MESSAGE_KEYS.ERROR.INVALID_SORT_DIRECTION],
    [ERROR_CODES.VALIDATION_FAILED],
    400
  )
};

// Bulk operation responses
export const BulkResponses = {
  invalidDataFormat: () => createErrorResponse(
    [MESSAGE_KEYS.ERROR.INVALID_DATA_FORMAT],
    [ERROR_CODES.VALIDATION_FAILED],
    400
  ),
  noDataProvided: () => createErrorResponse(
    [MESSAGE_KEYS.ERROR.NO_DATA_PROVIDED],
    [ERROR_CODES.VALIDATION_FAILED],
    400
  ),
  importFailed: () => createInternalServerErrorResponse(MESSAGE_KEYS.ERROR.BULK_IMPORT_FAILED),
  updateFailed: () => createInternalServerErrorResponse(MESSAGE_KEYS.ERROR.BULK_UPDATE_FAILED),
  deleteFailed: () => createInternalServerErrorResponse(MESSAGE_KEYS.ERROR.BULK_DELETE_FAILED)
};
