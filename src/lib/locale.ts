"use client"

import { useEffect, useState } from "react"
import en from "@/locales/en.json"
import es from "@/locales/es.json"

// Define available locales
const locales = {
  en,
  es,
}

export type LocaleKey = keyof typeof locales

// Get browser language or default to English
const getBrowserLocale = (): LocaleKey => {
  if (typeof window === "undefined") return "en"

  const browserLang = navigator.language.split("-")[0]
  return (browserLang as LocaleKey) in locales ? (browserLang as LocaleKey) : "en"
}

export const useLocale = (initialLocale?: LocaleKey) => {
  const [locale, setLocale] = useState<LocaleKey>(initialLocale || "en")

  useEffect(() => {
    if (!initialLocale) {
      setLocale(getBrowserLocale())
    }
  }, [initialLocale])

  return {
    locale,
    setLocale,
    t: locales[locale],
  }
}
