import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import { useTranslation as translate } from "react-i18next";

i18n.use(initReactI18next).init({
  lng: "en", // default language
  fallbackLng: "en",
  interpolation: {
    escapeValue: false, // React already escapes
  },
  react: {
    useSuspense: false, // useful for SSR/Next.js
  },
});

export function registerTranslation(namespace: string, resources: { [lang: string]: any }) {
  Object.entries(resources).forEach(([lang, resource]) => {
    if (!i18n.hasResourceBundle(lang, namespace)) {
      i18n.addResourceBundle(lang, namespace, resource);
    }
  });
  return translate(namespace)
}

export { i18n, translate };
