import GitHubProvider from "next-auth/providers/github"

export const authOptions = {
  secret: process.env.NEXTAUTH_SECRET!,
  providers: [
    // GoogleProvider({
    //   clientId: process.env.GOOGLE_CLIENT_ID!,
    //   clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    // }),
    // FacebookProvider({
    //   clientId: process.env.FACEBOOK_CLIENT_ID!,
    //   clientSecret: process.env.FACEBOOK_CLIENT_SECRET!,
    // }),
    GitHubProvider({
      clientId: process.env.GITHUB_CLIENT_ID!,
      clientSecret: process.env.GITHUB_CLIENT_SECRET!,
    }),
  ],
  //   pages: {
  //     signIn: '/auth/signin',
  //   },
  //   callbacks: {
  //     async session({ session, token }) {
  //       return session;
  //     },
  //     async jwt({ token, user, account, profile, isNewUser }) {
  //       return token;
  //     },
  //   },
}
