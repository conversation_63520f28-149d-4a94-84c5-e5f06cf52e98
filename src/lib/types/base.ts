// Base interfaces for common patterns
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface PagingAndSearchParams {
  page?: number;
  pageSize?: number;
  search?: string;
  [key: string]: any; // Other possible filters
}

// Authentication Types
export interface User extends BaseEntity {
  email: string;
  password?: string;
  name?: string;
  emailVerified?: boolean;
  emailVerificationToken?: string;
  emailVerificationExpires?: Date;
  passwordResetToken?: string;
  passwordResetExpires?: Date;
  lastLoginAt?: Date;
  isActive?: boolean;
}

export interface UserToken {
  token: string;
  refresh_token: string;
  user: User;
}

export interface Login {
  email: string;
  password: string;
}

export interface UserRegister {
  email: string;
  password: string;
  name?: string;
}

export interface RefreshTokenInput {
  token: string;
  refresh_token: string;
}

export interface PasswordResetRequest {
  email: string;
  resetToken: string;
  expiresAt: Date;
}

export interface EmailVerificationRequest {
  email: string;
  verificationToken: string;
  expiresAt: Date;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  data?: any;
}

// Profile Types
export interface ProfileInput {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  [key: string]: any;
}

// Error Types
export interface Error {
  message: string;
  code?: string;
  details?: any;
}

// API Response Types
export interface ApiResponse<T = any> {
  status: "success" | "failed";
  data?: T;
  errors?: string[];
  errorCodes?: string[];
}

// Common Status Types
export type StatusType = "active" | "inactive" | "deleted";
export type ServiceType = "RANAP" | "RALAN" | "IGD";

// Date range filter
export interface DateRangeFilter {
  date_from?: string;
  date_to?: string;
}
