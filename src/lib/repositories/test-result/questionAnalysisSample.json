[{"id": "qa1", "testResultId": "tr123", "questionId": "q1", "questionText": "Which CSS property is used to control the space between elements?", "isCorrect": true, "correctAnswer": "margin", "selectedAnswer": "margin", "answerStats": [{"answer": "margin", "selectedPercentage": 48, "explanation": "<PERSON><PERSON> controls the space outside elements."}, {"answer": "padding", "selectedPercentage": 34, "explanation": "Padding is space inside the element's border.", "commonMistake": "Often confused with margin due to similar visual effects."}, {"answer": "border", "selectedPercentage": 14}, {"answer": "spacing", "selectedPercentage": 4}], "learningTip": "Margin = outer space, Padding = inner space."}, {"id": "qa2", "testResultId": "tr123", "questionId": "q2", "questionText": "What HTML tag is used to define an unordered list?", "isCorrect": true, "correctAnswer": "<ul>", "selectedAnswer": "<ul>", "answerStats": [{"answer": "<ul>", "selectedPercentage": 60}, {"answer": "<ol>", "selectedPercentage": 28, "commonMistake": "This is used for ordered lists."}, {"answer": "<li>", "selectedPercentage": 10, "explanation": "Used to define list items, not the list container."}, {"answer": "<list>", "selectedPercentage": 2}], "learningTip": "`<ul>` = unordered list, `<ol>` = ordered list."}, {"id": "qa3", "testResultId": "tr123", "questionId": "q3", "questionText": "Which value of the position property makes the element stay in place during scroll?", "isCorrect": false, "correctAnswer": "fixed", "selectedAnswer": "sticky", "answerStats": [{"answer": "fixed", "selectedPercentage": 40, "explanation": "Fixed elements are positioned relative to the viewport and do not move on scroll."}, {"answer": "sticky", "selectedPercentage": 35, "explanation": "Sticky only stays in place within a parent scroll container.", "commonMistake": "Sticky feels similar to fixed but is more limited."}, {"answer": "absolute", "selectedPercentage": 20}, {"answer": "relative", "selectedPercentage": 5}], "learningTip": "Use `position: fixed` for always-on-top elements like navbars or back-to-top buttons."}]