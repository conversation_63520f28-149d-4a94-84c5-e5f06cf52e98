[{"id": "qa1", "testResultId": "tr123", "questionId": "1", "questionText": "Which of the following are programming languages?", "type": "multipleChoice", "isCorrect": true, "correctAnswer": ["A", "D"], "selectedAnswer": ["A", "D"], "answerStats": [{"answer": "A", "selectedPercentage": 80, "isSelected": true, "isCorrect": true, "explanation": "Python is a high-level, general-purpose programming language widely used for web development, data science, and automation."}, {"answer": "B", "selectedPercentage": 30, "isSelected": false, "isCorrect": false, "explanation": "HTML is a markup language used for structuring content on the web, not a programming language."}, {"answer": "C", "selectedPercentage": 25, "isSelected": false, "isCorrect": false, "explanation": "CSS is a style sheet language used to describe the presentation of HTML, not a programming language."}, {"answer": "D", "selectedPercentage": 75, "isSelected": true, "isCorrect": true, "explanation": "JavaScript is a widely-used programming language, especially for interactive web applications."}], "explanation": "Python and JavaScript are programming languages, whereas HTML and CSS are markup and styling languages, respectively.", "reviewComment": "", "requiresManualReview": false}, {"id": "qa2", "testResultId": "tr123", "questionId": "2", "questionText": "What is the capital city of France?", "type": "singleChoice", "isCorrect": true, "correctAnswer": "B", "selectedAnswer": "B", "answerStats": [{"answer": "A", "selectedPercentage": 10, "isSelected": false, "isCorrect": false, "explanation": "Berlin is the capital of Germany, not France."}, {"answer": "B", "selectedPercentage": 85, "isSelected": true, "isCorrect": true, "explanation": "Paris is the capital city of France and a major European cultural and political center."}, {"answer": "C", "selectedPercentage": 3, "isSelected": false, "isCorrect": false, "explanation": "Madrid is the capital of Spain, not France."}, {"answer": "D", "selectedPercentage": 2, "isSelected": false, "isCorrect": false, "explanation": "Rome is the capital of Italy, not France."}], "explanation": "Paris is the capital city of France and a major European cultural and political center.", "reviewComment": "", "requiresManualReview": false}, {"id": "qa3", "testResultId": "tr123", "questionId": "3", "questionText": "Explain the concept of polymorphism in object-oriented programming.", "type": "textInput", "isCorrect": null, "selectedAnswer": "Polymorphism allows objects of different classes to be treated as objects of a common superclass.", "explanation": "", "reviewComment": "Accurate and concise. Accepted.", "requiresManualReview": true}, {"id": "qa4", "testResultId": "tr123", "questionId": "4", "questionText": "Write a function in JavaScript that returns the square of a number.", "type": "codeInput", "isCorrect": null, "selectedAnswer": "function square(n) { return n * n; }", "explanation": "", "reviewComment": "Correct syntax and logic. Accepted.", "requiresManualReview": true}, {"id": "qa5", "testResultId": "tr123", "questionId": "5", "questionText": "Identify the animal shown in the picture.", "type": "imageBased", "isCorrect": true, "correctAnswer": "B", "selectedAnswer": "B", "answerStats": [{"answer": "A", "selectedPercentage": 12, "isSelected": false, "isCorrect": false, "explanation": "This is likely a tiger, which has stripes and a different facial structure."}, {"answer": "B", "selectedPercentage": 70, "isSelected": true, "isCorrect": true, "explanation": "The lion is recognized by its distinctive mane and facial structure."}, {"answer": "C", "selectedPercentage": 10, "isSelected": false, "isCorrect": false, "explanation": "This may be a leopard, which is smaller and has a spotted coat."}, {"answer": "D", "selectedPercentage": 8, "isSelected": false, "isCorrect": false, "explanation": "This could refer to a cheetah, which has a more slender build and black tear markings."}], "explanation": "The lion is recognized by its distinctive mane and facial structure.", "reviewComment": "", "requiresManualReview": false}, {"id": "qa6", "testResultId": "tr123", "questionId": "6", "questionText": "Listen to the audio and select the language spoken.", "type": "audioBased", "isCorrect": true, "correctAnswer": "B", "selectedAnswer": "B", "answerStats": [{"answer": "A", "selectedPercentage": 15, "isSelected": false, "isCorrect": false, "explanation": "German has a more guttural and clipped tone, which differs from the clip."}, {"answer": "B", "selectedPercentage": 65, "isSelected": true, "isCorrect": true, "explanation": "The audio clip contains Spanish vocabulary and accent patterns."}, {"answer": "C", "selectedPercentage": 10, "isSelected": false, "isCorrect": false, "explanation": "French pronunciation and intonation differ significantly from the audio clip."}, {"answer": "D", "selectedPercentage": 10, "isSelected": false, "isCorrect": false, "explanation": "Italian has a more melodic flow and distinct phonetic structure than what was heard."}], "explanation": "The audio clip contains Spanish vocabulary and accent patterns.", "reviewComment": "", "requiresManualReview": false}, {"id": "qa7", "testResultId": "tr123", "questionId": "7", "questionText": "Which audio clip correctly pronounces the word 'entrepreneur'?", "type": "audioAnswer", "isCorrect": false, "correctAnswer": "C", "selectedAnswer": "B", "answerStats": [{"answer": "A", "selectedPercentage": 10, "isSelected": false, "isCorrect": false, "explanation": "Clip <PERSON> may have used an overly phonetic or non-standard pronunciation."}, {"answer": "B", "selectedPercentage": 30, "isSelected": true, "isCorrect": false, "explanation": "Clip <PERSON> used a mispronunciation or an unclear accent not aligned with standard English."}, {"answer": "C", "selectedPercentage": 50, "isSelected": false, "isCorrect": true, "explanation": "<PERSON><PERSON> uses the correct American English pronunciation of 'entrepreneur'."}, {"answer": "D", "selectedPercentage": 10, "isSelected": false, "isCorrect": false, "explanation": "Clip <PERSON> may have exaggerated or mispronounced the nasal and final syllables."}], "explanation": "<PERSON><PERSON> uses the correct American English pronunciation of 'entrepreneur'.", "reviewComment": "", "requiresManualReview": false}, {"id": "qa8", "testResultId": "tr123", "questionId": "8", "questionText": "Please record yourself reading the following sentence: 'The quick brown fox jumps over the lazy dog.'", "type": "voiceInput", "isCorrect": null, "selectedAnswer": "https://example.com/uploads/audio123.ogg", "explanation": "", "reviewComment": "", "requiresManualReview": true}, {"id": "qa9", "testResultId": "tr123", "questionId": "9", "questionText": "Upload your resume in PDF format.", "type": "fileUpload", "isCorrect": null, "selectedAnswer": "https://example.com/uploads/resume123.pdf", "explanation": "", "reviewComment": "", "requiresManualReview": true}]