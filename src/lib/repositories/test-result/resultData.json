{"id": "1", "verificationId": "verify-7890", "test": {"id": "test-001", "title": "HTML & CSS Fundamentals", "description": "A basic test to assess knowledge of HTML and CSS.", "categories": ["HTML", "CSS", "Web Development"], "difficulties": ["EASY", "INTERMEDIATE"], "benefits": ["Improve frontend skills", "Prepare for developer job interviews"], "durationInSeconds": 1800, "cover": "/images/html-css-cover.png", "mediaUrl": "https://example.com/media/intro-video.mp4", "creator": {"id": "creator-123", "name": "<PERSON>", "avatar": "/avatars/jane.png"}, "reason": "To validate foundational web design skills", "usersCount": 1523, "successRate": 75, "totalQuestions": 5}, "score": 85, "completedOn": "2025-05-15T00:00:00.000Z", "correctAnswers": 3, "incorrectAnswers": 2, "pendingReview": 2, "totalQuestions": 5, "successRate": 60, "percentile": 92, "timeTaken": 765, "badge": 1, "status": "partial", "submittedAnswers": [{"questionId": "q1", "answer": "A"}, {"questionId": "q2", "answer": "My motivation is to help people."}, {"questionId": "q3", "answer": "audio123.webm"}, {"questionId": "q4", "answer": "C"}, {"questionId": "q5", "answer": "file_upload.pdf"}], "questions": [{"id": "q1", "type": "MCQ", "correctAnswer": ["A"], "questionText": "What does HTML stand for?", "isCorrect": true, "selectedAnswer": "A", "answerStats": [{"answer": "A", "selectedPercentage": 70}, {"answer": "B", "selectedPercentage": 15}, {"answer": "C", "selectedPercentage": 10}, {"answer": "D", "selectedPercentage": 5}], "explanation": "HTML stands for HyperText Markup Language.", "reviewComment": "", "requiresManualReview": false, "testResultId": "session-123456", "questionId": "q1"}, {"id": "q2", "type": "FreeText", "correctAnswer": [], "questionText": "Why do you want to learn web development?", "isCorrect": null, "selectedAnswer": "My motivation is to help people.", "requiresManualReview": true, "testResultId": "session-123456", "questionId": "q2"}, {"id": "q3", "type": "AudioAnswer", "correctAnswer": [], "questionText": "Please record a short introduction about yourself.", "isCorrect": null, "selectedAnswer": "audio123.webm", "requiresManualReview": true, "testResultId": "session-123456", "questionId": "q3"}, {"id": "q4", "type": "MCQ", "correctAnswer": ["C"], "questionText": "Which CSS property controls the text size?", "isCorrect": true, "selectedAnswer": "C", "requiresManualReview": false, "testResultId": "session-123456", "questionId": "q4"}, {"id": "q5", "type": "FileInput", "correctAnswer": [], "questionText": "Upload your project documentation.", "isCorrect": null, "selectedAnswer": "file_upload.pdf", "requiresManualReview": true, "testResultId": "session-123456", "questionId": "q5"}], "performanceByCategory": [{"category": "HTML", "correct": 2, "total": 3, "successRate": 66.67}, {"category": "CSS", "correct": 1, "total": 2, "successRate": 50}], "ranking": {"rank": 15, "outOf": 150}}