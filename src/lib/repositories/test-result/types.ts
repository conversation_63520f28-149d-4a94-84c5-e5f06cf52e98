import { SubmittedAnswer } from "../test-session/TestSessionRepository";

export interface QuestionReview {
  id: string;
  type: string;
  sessionQuestionId: string;
  metadata: Record<string, any>;
}

export interface CategoryPerformance {
  category: string;
  correct: number;
  total: number;
  successRate: number;
}

export type TestResult = {
  id: string;
  sessionId: string;
  testTitle: string;
  user: {
    id: string;
    email: string;
    name: string;
  };
  verificationId: string;
  score: number;
  completedOn: Date;
  correctAnswers: number;
  incorrectAnswers: number;
  pendingReview: number;
  totalQuestions: number;
  successRate: number;
  percentile: number;
  timeTaken: number;
  badge: string;
  status: "partial" | "complete";
  submittedAnswers: SubmittedAnswer[];
  questionReviews: QuestionReview[];
  performanceByCategory: CategoryPerformance[];
  ranking?: {
    rank: number;
    outOf: number;
  };
};

export interface AnswerStat {
  answer: string;
  selectedPercentage: number;
  explanation?: string;
  commonMistake?: string;
}

export interface TestQuestionInsight {
  id: string;
  testResultId: string;
  questionId: string;
  questionText: string;
  type: string;
  isCorrect: boolean | null;
  correctAnswer?: string | string[];
  selectedAnswer: string | string[];
  answerStats?: {
    answer: string;
    selectedPercentage: number;
    isSelected: boolean;
    isCorrect: boolean;
    explanation?: string;
  }[];
  explanation: string;
  reviewComment: string;
  requiresManualReview: boolean;
}

export interface RecommendationStep {
  title: string;
  description: string;
  icon: string;
  link: string;
}
