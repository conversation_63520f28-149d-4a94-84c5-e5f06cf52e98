import { mongoDbInstance } from "@/src/lib/db/mongoClient";
import { MONGO_COLLECTIONS } from "@/src/lib/db/mongoCollections";
import { TestResultRepository } from "./TestResultRepository";
import { TestResult, TestQuestionInsight, RecommendationStep } from "./types";
import questionAnalysis from "./questionAnalysisForEachType.json";
import recommendationsJson from "./recommendation.json";

export class MongoTestResultRepository implements TestResultRepository {
  private analyses: TestQuestionInsight[] =
    questionAnalysis as unknown as TestQuestionInsight[];
  private recommendations: RecommendationStep[] =
    recommendationsJson as unknown as RecommendationStep[];

  private async getCollection() {
    const client = await mongoDbInstance;
    const db = client.db(); // DB name from URI
    return db.collection(MONGO_COLLECTIONS.TEST_RESULT);
  }

  async save(result: TestResult): Promise<void> {
    const collection = await this.getCollection();
    await collection.insertOne({
      ...result,
      completedOn: result.completedOn || new Date(),
    });
  }

  async getById(id: string): Promise<TestResult> {
    const collection = await this.getCollection();
    const doc = await collection.findOne({ id });

    if (!doc) {
      throw new Error(`TestResult with id "${id}" not found`);
    }

    return {
      id: doc.id,
      testTitle: doc.testTitle,
      sessionId: doc.sessionId,
      user: {
        id: doc.user.id,
        email: doc.user.email,
        name: doc.user.name,
      },
      verificationId: doc.verificationId,
      score: doc.score,
      completedOn: new Date(doc.completedOn),
      correctAnswers: doc.correctAnswers,
      incorrectAnswers: doc.incorrectAnswers,
      pendingReview: doc.pendingReview,
      totalQuestions: doc.totalQuestions,
      successRate: doc.successRate,
      percentile: doc.percentile,
      timeTaken: doc.timeTaken,
      badge: doc.badge,
      status: doc.status,
      submittedAnswers: doc.submittedAnswers,
      questionReviews: doc.questionReviews,
      performanceByCategory: doc.performanceByCategory,
      ranking: doc.ranking,
    };
  }

  async getQuestionAnalysisByTestResultId(
    testResultId: string
  ): Promise<TestQuestionInsight[]> {
    return this.analyses;
  }

  async getRecommendationsByTestResultId(
    testResultId: string
  ): Promise<RecommendationStep[]> {
    return this.recommendations;
  }
}
