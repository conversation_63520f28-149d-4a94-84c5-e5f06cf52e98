import { ITestRepository, PagingAndSearchParams, MyTests, MyTestDetail } from "./MyTestsRepository";
import { mongoDbInstance } from "@/src/lib/db/mongoClient";
import { MONGO_COLLECTIONS } from "../../db/mongoCollections";


function mapToMyTests(doc: any): MyTests {
  const { _id, ...rest } = doc;
  return {
    id: rest.id,
    title: rest.title,
    category: rest.category,
    badges: rest.badges || [],
    correctAnswers: rest.correctAnswers,
    score: rest.score,
    instructor: rest.instructor,
    totalQuestions: rest.totalQuestions,
    date: rest.date,
    duration: rest.duration,
    image: rest.image,
    createdAt: rest.createdAt,
    updatedAt: rest.updatedAt,
  };
}

function mapToMyTestDetail(doc: any): MyTestDetail {
  const { _id, ...rest } = doc;
  return {
    id: rest.id,
    tests_id: rest.tests_id,
    title: rest.title,
    description: rest.description,
    timeSpent: rest.timeSpent,
    category: rest.category,
    badges: rest.badges || [],
    correctAnswers: rest.correctAnswers,
    score: rest.score,
    instructor: rest.instructor,
    totalQuestions: rest.totalQuestions,
    date: rest.date,
    duration: rest.duration,
    image: rest.image,
    categoryBreakdown: rest.categoryBreakdown || [],
    questionBreakdown: rest.questionBreakdown || [],
    recommendedCourses: rest.recommendedCourses || [],
    createdAt: rest.createdAt,
    updatedAt: rest.updatedAt,
  };
}

export class MongoMyTestsRepository implements ITestRepository {
  private async getCollection() {
    const client = await mongoDbInstance;
    const db = client.db(process.env.MONGODB_DB!);
    return db.collection(MONGO_COLLECTIONS.MY_TESTS);
  }

  private generateId() {
    return Math.random().toString(36).substring(2, 11);
  }

  private buildQuery(filters?: PagingAndSearchParams): any {
    const query: any = {};
    if (!filters) return query;

    if (filters.search) {
      query.$or = [
        { title: { $regex: filters.search, $options: "i" } },
        { category: { $regex: filters.search, $options: "i" } }
      ];
    }

    if (filters.category) {
      query.category = filters.category;
    }

    return query;
  }

  async getAll(filters?: PagingAndSearchParams): Promise<MyTests[]> {
    const collection = await this.getCollection();
    const query = this.buildQuery(filters);

    const cursor = collection.find(query);

    if (filters?.page && filters?.pageSize) {
      cursor.skip((filters.page - 1) * filters.pageSize).limit(filters.pageSize);
    }

    const docs = await cursor.toArray();
    return docs.map(mapToMyTests);
  }

  async getById(id: string): Promise<MyTests | null> {
    const collection = await this.getCollection();
    const doc = await collection.findOne({ id });
    return doc ? mapToMyTests(doc) : null;
  }

  async getDetail(id: string): Promise<MyTestDetail | null> {
    const collection = await this.getCollection();
    const doc = await collection.findOne({ id });
    return doc ? mapToMyTestDetail(doc) : null;
  }

  async create(data: Partial<MyTests>): Promise<MyTests> {
    const collection = await this.getCollection();

    const newMyTest: MyTests = {
      ...data,
      id: this.generateId(),
      title: data.title || "Untitled Test",
      category: data.category || "General",
      badges: data.badges || [],
      correctAnswers: data.correctAnswers || 0,
      score: data.score || 0,
      instructor: data.instructor || { name: "Unknown", avatar: "" },
      totalQuestions: data.totalQuestions || 0,
      date: data.date || new Date(),
      duration: data.duration || "30 min",
      image: data.image || "",
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    await collection.insertOne(newMyTest);
    return newMyTest;
  }

  async update(id: string, data: Partial<MyTests>): Promise<MyTests | null> {
    const collection = await this.getCollection();

    const result = await collection.findOneAndUpdate(
      { id },
      {
        $set: {
          ...data,
          updatedAt: new Date(),
        },
      },
      { returnDocument: "after" }
    );

    return result ? mapToMyTests(result) : null;
  }

  async delete(id: string): Promise<boolean> {
    const collection = await this.getCollection();
    const result = await collection.deleteOne({ id });
    return result.deletedCount === 1;
  }

  async bulkDelete(ids: string[]): Promise<void> {
    const collection = await this.getCollection();
    await collection.deleteMany({ id: { $in: ids } });
  }
}
