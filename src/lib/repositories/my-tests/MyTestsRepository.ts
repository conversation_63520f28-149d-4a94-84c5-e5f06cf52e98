interface Instructor {
  name: string;
  avatar: string;
}

interface CategoryBreakdownItem {
  category: string;
  correct: number;
  total: number;
  percentage: number;
}

interface QuestionBreakdownItem {
  question: string;
  yourAnswer: string;
  correctAnswer: string;
  isCorrect: boolean;
  category: string;
}

interface RecommendedCourse {
  title: string;
  description: string;
  image: string;
  instructor: string;
  duration: string;
  level: string;
}

export interface MyTests {
  id: string;
  title: string;
  category: string;
  badges: string[];
  correctAnswers: number;
  score: number;
  instructor: Instructor;
  totalQuestions: number;
  date: Date; // You can change this to `Date` if you plan to parse it
  duration?: string;
  image?: string;

  createdAt: Date;
  updatedAt: Date;
}

export interface MyTestDetail extends MyTests {
  tests_id:string
  description:string
  timeSpent:string
  categoryBreakdown: CategoryBreakdownItem[];
  questionBreakdown: QuestionBreakdownItem[];
  recommendedCourses: RecommendedCourse[];
}

export interface PagingAndSearchParams {
  page?: number;
  pageSize?: number;
  search?: string;
  [key: string]: any; // Other possible filters
}

export interface ITestRepository {
  getAll(filters?: PagingAndSearchParams): Promise<MyTests[]>;
  getById(id: string): Promise<MyTests | null>;
  getDetail(id: string): Promise<MyTestDetail | null>;
  create(data: Partial<MyTests>): Promise<MyTests>;
  update(id: string, data: Partial<MyTests>): Promise<MyTests | null>;
  delete(id: string): Promise<boolean>;

  bulkDelete(ids: string[]): Promise<void>;
}
