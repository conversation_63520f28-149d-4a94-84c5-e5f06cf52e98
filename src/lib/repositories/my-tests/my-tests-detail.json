[{"id": "1", "tests_id": "1", "title": "Web Development Fundamentals", "category": "Technology", "date": "May 15, 2023", "score": 85, "totalQuestions": 20, "correctAnswers": 17, "duration": "15 min", "timeSpent": "12 min", "image": "https://ui.shadcn.com/placeholder.svg", "badges": ["Top 10%", "Fast Learner"], "instructor": {"name": "<PERSON>", "avatar": "https://ui.shadcn.com/placeholder.svg"}, "description": "Test your knowledge of HTML, CSS, and JavaScript basics", "categoryBreakdown": [{"category": "HTML", "correct": 7, "total": 8, "percentage": 88}, {"category": "CSS", "correct": 5, "total": 6, "percentage": 83}, {"category": "JavaScript", "correct": 5, "total": 6, "percentage": 83}], "questionBreakdown": [{"question": "Which HTML tag is used to create a hyperlink?", "yourAnswer": "<a>", "correctAnswer": "<a>", "isCorrect": true, "category": "HTML"}, {"question": "Which CSS property is used to change the text color?", "yourAnswer": "color", "correctAnswer": "color", "isCorrect": true, "category": "CSS"}, {"question": "Which of the following is NOT a JavaScript data type?", "yourAnswer": "character", "correctAnswer": "character", "isCorrect": true, "category": "JavaScript"}, {"question": "What does CSS stand for?", "yourAnswer": "Cascading Style Sheets", "correctAnswer": "Cascading Style Sheets", "isCorrect": true, "category": "CSS"}, {"question": "Which HTML tag is used to define an internal style sheet?", "yourAnswer": "<script>", "correctAnswer": "<style>", "isCorrect": false, "category": "HTML"}], "recommendedCourses": [{"title": "Advanced JavaScript Concepts", "description": "Deep dive into closures, prototypes, and async programming", "image": "https://ui.shadcn.com/placeholder.svg", "instructor": "<PERSON>", "duration": "4 hours", "level": "Intermediate"}, {"title": "CSS Grid & Flexbox Mastery", "description": "Master modern CSS layout techniques", "image": "https://ui.shadcn.com/placeholder.svg", "instructor": "<PERSON>", "duration": "3 hours", "level": "Intermediate"}, {"title": "Responsive Web Design", "description": "Learn to build websites that work on any device", "image": "https://ui.shadcn.com/placeholder.svg", "instructor": "<PERSON>", "duration": "5 hours", "level": "Intermediate"}]}, {"id": "2", "tests_id": "2", "title": "JavaScript Basics", "category": "Programming", "date": "April 28, 2023", "score": 72, "totalQuestions": 25, "correctAnswers": 18, "duration": "20 min", "timeSpent": "18 min", "image": "https://ui.shadcn.com/placeholder.svg", "badges": ["Persistent"], "instructor": {"name": "<PERSON>", "avatar": "https://ui.shadcn.com/placeholder.svg"}, "description": "Test your knowledge of JavaScript fundamentals", "categoryBreakdown": [{"category": "Variables", "correct": 5, "total": 6, "percentage": 83}, {"category": "Functions", "correct": 4, "total": 6, "percentage": 67}, {"category": "<PERSON><PERSON><PERSON>", "correct": 5, "total": 7, "percentage": 71}, {"category": "Objects", "correct": 4, "total": 6, "percentage": 67}], "questionBreakdown": [{"question": "Which keyword is used to declare a variable in JavaScript?", "yourAnswer": "var, let, const", "correctAnswer": "var, let, const", "isCorrect": true, "category": "Variables"}, {"question": "How do you create a function in JavaScript?", "yourAnswer": "function myFunction()", "correctAnswer": "function myFunction()", "isCorrect": true, "category": "Functions"}, {"question": "How do you access the first element of an array?", "yourAnswer": "array[0]", "correctAnswer": "array[0]", "isCorrect": true, "category": "<PERSON><PERSON><PERSON>"}, {"question": "What is the correct way to write a JavaScript object?", "yourAnswer": "{name:'<PERSON>', age:30}", "correctAnswer": "{name:'<PERSON>', age:30}", "isCorrect": true, "category": "Objects"}, {"question": "Which method adds a new element to the end of an array?", "yourAnswer": "append()", "correctAnswer": "push()", "isCorrect": false, "category": "<PERSON><PERSON><PERSON>"}], "recommendedCourses": [{"title": "JavaScript: The Complete Guide", "description": "Master JavaScript with the most comprehensive course", "image": "https://ui.shadcn.com/placeholder.svg", "instructor": "<PERSON>", "duration": "8 hours", "level": "Beginner to Advanced"}, {"title": "Modern JavaScript ES6+", "description": "Learn all the new features of modern JavaScript", "image": "https://ui.shadcn.com/placeholder.svg", "instructor": "<PERSON>", "duration": "4 hours", "level": "Intermediate"}, {"title": "JavaScript Algorithms and Data Structures", "description": "Improve your problem-solving skills with JavaScript", "image": "https://ui.shadcn.com/placeholder.svg", "instructor": "<PERSON>", "duration": "6 hours", "level": "Intermediate"}]}]