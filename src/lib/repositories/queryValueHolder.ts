
export interface QueryBuilder {
  addDateRange(field: string, start: Date, end: Date): void
  addStringFilter(field: string, value: string, operator?: string): void
  addBooleanFilter(field: string, value: boolean): void
  addMultipleSelectFilter(field: string, values: string[]): void
  addRegexFilter(field: string, regex: RegExp): void
  addNotRegexFilter(field: string, regex: RegExp): void
}

export interface QueryValueHolder {
  visit(queryBuilder: QueryBuilder): void
}
