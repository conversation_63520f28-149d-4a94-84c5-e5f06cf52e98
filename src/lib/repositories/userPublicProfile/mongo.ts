import { mongoDbInstance } from "@/src/lib/db/mongoClient";
import { MONGO_COLLECTIONS } from "@/src/lib/db/mongoCollections";
import { IProfileRepository, ProfileData } from "./interface";
import { TestResult } from "../test-result/types";
import { User } from "../../types/base";

function mapToUser(doc: any): Omit<User, "password"> {
  const { _id, password, ...rest } = doc;
  return {
    id: rest.id,
    email: rest.email,
    name: rest.name,
    createdAt: rest.createdAt,
    updatedAt: rest.updatedAt
  };
}

function mapToTestResult(doc: any): TestResult {
  const { _id, ...rest } = doc;
  return {
    id: rest.id,
    sessionId: rest.sessionId,
    testTitle: rest.testTitle,
    user: rest.user,
    verificationId: rest.verificationId,
    score: rest.score,
    completedOn: rest.completedOn,
    correctAnswers: rest.correctAnswers,
    incorrectAnswers: rest.incorrectAnswers,
    pendingReview: rest.pendingReview,
    totalQuestions: rest.totalQuestions,
    successRate: rest.successRate,
    percentile: rest.percentile,
    timeTaken: rest.timeTaken,
    badge: rest.badge,
    status: rest.status,
    submittedAnswers: rest.submittedAnswers || [],
    questionReviews: rest.questionReviews || [],
    performanceByCategory: rest.performanceByCategory || [],
    ranking: rest.ranking,
  };
}

export class MongoProfileRepository implements IProfileRepository {
  private async getUsersCollection() {
    const client = await mongoDbInstance;
    const db = client.db(process.env.MONGODB_DB!);
    return db.collection(MONGO_COLLECTIONS.USER_PROFILES);
  }

  private async getTestResultsCollection() {
    const client = await mongoDbInstance;
    const db = client.db(process.env.MONGODB_DB!);
    return db.collection(MONGO_COLLECTIONS.TEST_RESULT);
  }

  async getProfileByUserId(userId: string): Promise<ProfileData | null> {
    try {
      // Get user data
      const usersCollection = await this.getUsersCollection();
      const userDoc = await usersCollection.findOne({ id: userId });

      if (!userDoc) {
        return null;
      }

      // Get test history for the user
      const testResultsCollection = await this.getTestResultsCollection();
      const testDocs = await testResultsCollection
        .find({ "user.id": userId })
        .sort({ completedOn: -1 }) // Most recent first
        .toArray();

      const user = mapToUser(userDoc);
      const testHistory = testDocs.map(mapToTestResult);

      return {
        user,
        testHistory,
      };
    } catch (error) {
      console.error("Error fetching profile data:", error);
      return null;
    }
  }

  async createProfile(userData: Omit<User, "password">): Promise<ProfileData | null> {
    try {
      const usersCollection = await this.getUsersCollection();
      
      const newUser = {
        ...userData,
        createdAt: userData.createdAt || new Date(),
        updatedAt: new Date(),
      };

      await usersCollection.insertOne(newUser);
      
      return {
        user: mapToUser(newUser),
        testHistory: [],
      };
    } catch (error) {
      console.error("Error creating profile:", error);
      return null;
    }
  }

  async updateProfile(userId: string, userData: Partial<Omit<User, "password">>): Promise<ProfileData | null> {
    try {
      const usersCollection = await this.getUsersCollection();
      
      const result = await usersCollection.findOneAndUpdate(
        { id: userId },
        {
          $set: {
            ...userData,
            updatedAt: new Date(),
          },
        },
        { returnDocument: "after" }
      );

      if (!result) {
        return null;
      }

      // Get updated test history
      const testResultsCollection = await this.getTestResultsCollection();
      const testDocs = await testResultsCollection
        .find({ "user.id": userId })
        .sort({ completedOn: -1 })
        .toArray();

      return {
        user: mapToUser(result),
        testHistory: testDocs.map(mapToTestResult),
      };
    } catch (error) {
      console.error("Error updating profile:", error);
      return null;
    }
  }
}
