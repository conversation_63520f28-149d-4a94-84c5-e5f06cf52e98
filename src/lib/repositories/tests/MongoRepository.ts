import { ObjectId } from "mongodb";
import { mongoDbInstance } from "@/src/lib/db/mongoClient";
import { MONGO_COLLECTIONS } from "@/src/lib/db/mongoCollections";
import {
  Test,
  TestCreateInput,
  TestUpdateInput,
  TestQueryParams,
  PublicTestQuery,
} from "./interface";
import { TestDBRepository } from "./DBRepository";

export class TestMongoRepository implements TestDBRepository {
  private async getCollection() {
    const client = await mongoDbInstance;
    const db = client.db();
    return db.collection(MONGO_COLLECTIONS.TESTS);
  }

  private buildFilter(query: TestQueryParams = {}) {
    const filter: any = {};

    // Handle soft delete
    if (!query.includeDeleted) {
      filter.deletedAt = { $exists: false };
    }

    // Handle search
    if (query.search) {
      filter.$or = [
        { title: { $regex: query.search, $options: "i" } },
        { description: { $regex: query.search, $options: "i" } },
        { "creator.name": { $regex: query.search, $options: "i" } },
      ];
    }

    // Handle custom filters
    if (query.filters) {
      query.filters.forEach(({ field, value }) => {
        if (field === "categories" && Array.isArray(value)) {
          filter.categories = { $in: value };
        } else if (field === "difficulties" && Array.isArray(value)) {
          filter.difficulties = { $in: value };
        } else if (field === "status") {
          filter.status = value;
        } else if (field === "premium.isPremium") {
          filter["premium.isPremium"] = value;
        } else if (field === "creator.id") {
          filter["creator.id"] = value;
        } else {
          filter[field] = value;
        }
      });
    }

    return filter;
  }

  private buildSort(sorts?: TestQueryParams["sorts"]) {
    if (!sorts || sorts.length === 0) {
      return { createdAt: -1 };
    }

    const sortObj: any = {};
    sorts.forEach(({ field, direction }) => {
      sortObj[field] = direction === "desc" ? -1 : 1;
    });

    return sortObj;
  }

  async getById(id: string, includeDeleted = false): Promise<Test | null> {
    const collection = await this.getCollection();
    const filter: any = { id: id };

    if (!includeDeleted) {
      filter.deletedAt = { $exists: false };
    }

    const result = await collection.findOne(filter);
    return result ? this.mapFromMongo(result) : null;
  }

  async getAll(
    params?: TestQueryParams
  ): Promise<{ items: Test[]; total: number }> {
    const collection = await this.getCollection();
    const filter = this.buildFilter(params);
    const sort = this.buildSort(params?.sorts);

    const [items, total] = await Promise.all([
      collection
        .find(filter)
        .sort(sort)
        .skip(((params?.page || 1) - 1) * (params?.limit || 10))
        .limit(params?.limit || 10)
        .toArray(),
      collection.countDocuments(filter),
    ]);

    return {
      items: items.map((item) => this.mapFromMongo(item)),
      total,
    };
  }

  async getAllCategories(
    params?: TestQueryParams
  ): Promise<{ items: string[]; total: number }> {
    const collection = await this.getCollection();
    const filter = this.buildFilter(params);
    const sort = this.buildSort(params?.sorts);

    const docs = await collection
        .find(filter)
        .sort(sort)
        .skip(((params?.page || 1) - 1) * (params?.limit || 10))
        .limit(params?.limit || 10)
        .toArray()

    const categorySet = new Set<string>();
    docs.forEach((doc) => {
      (doc.categories || []).forEach((cat: string) => categorySet.add(cat));
    });

    return {
      items: ["All", ...Array.from(categorySet)],
      total: categorySet.size,
    };
  }

  async getSimilarTests(filters?: PublicTestQuery): Promise<{ items: Test[]; total: number }> {
    const collection = await this.getCollection();
    const query: any = {};

    if (filters?.query) {
      const keywords = filters.query
        .toLowerCase()
        .split(/[\s\-_,]+/)
        .filter((word) => word.length > 2); // filter out short/common words

      query.$or = keywords.flatMap((word) => [
        { title: { $regex: new RegExp(word, "i") } },
        { description: { $regex: new RegExp(word, "i") } },
        { reason: { $regex: new RegExp(word, "i") } },
      ]);
    }

    if (filters?.categories?.length) {
      query.categories = { $in: filters.categories };
    }

    const docs = await collection.find(query).limit(10).toArray();

    return {
      items: docs.map((item) => this.mapFromMongo(item)),
      total: await collection.find(query).count(),
    };
  }

  async create(data: TestCreateInput): Promise<Test> {
    const collection = await this.getCollection();
    const now = new Date();
    const testData = {
      ...data,
      id: new ObjectId().toString(),
      createdAt: now,
      updatedAt: now,
      usersCount: data.usersCount || 0,
      totalQuestions: data.totalQuestions || 0,
      questionIds: data.questionIds || [],
      status: data.status || "DRAFT",
    };
    console.log("Creating test with data:", testData);

    const result = await collection.insertOne(testData);
    const created = await collection.findOne({ id: testData.id });
    return this.mapFromMongo(created!);
  }

  async update(id: string, data: TestUpdateInput): Promise<Test | null> {
    const collection = await this.getCollection();
    const updateData = {
      ...data,
      updatedAt: new Date(),
    };

    const result = await collection.findOneAndUpdate(
      { id: id, deletedAt: { $exists: false } },
      { $set: updateData },
      { returnDocument: "after" }
    );

    return result ? this.mapFromMongo(result) : null;
  }

  async delete(id: string, hardDelete = false): Promise<boolean> {
    const collection = await this.getCollection();
    console.log("Deleting test with ID:", id, "hardDelete:", hardDelete);

    if (hardDelete) {
      const result = await collection.deleteOne({ id: id });
      return result.deletedCount > 0;
    } else {
      const result = await collection.updateOne(
        { id: id },
        { $set: { deletedAt: new Date() } }
      );
      return result.modifiedCount > 0;
    }
  }

  async restore(id: string): Promise<boolean> {
    const collection = await this.getCollection();
    const result = await collection.updateOne(
      { id: id },
      { $unset: { deletedAt: "" } }
    );
    return result.modifiedCount > 0;
  }

  async bulkCreate(data: TestCreateInput[]): Promise<Test[]> {
    const collection = await this.getCollection();
    const now = new Date();

    const testDataArray = data.map((item) => ({
      ...item,
      id: new ObjectId().toString(),
      createdAt: now,
      updatedAt: now,
      usersCount: item.usersCount || 0,
      totalQuestions: item.totalQuestions || 0,
      questionIds: item.questionIds || [],
      status: item.status || "DRAFT",
    }));

    const result = await collection.insertMany(testDataArray);
    const created = await collection
      .find({ id: { $in: testDataArray.map((item) => item.id) } })
      .toArray();
    return created.map((item) => this.mapFromMongo(item));
  }

  async bulkUpdate(
    updates: { id: string; data: TestUpdateInput }[]
  ): Promise<number> {
    const collection = await this.getCollection();
    const bulkOps = updates.map(({ id, data }) => ({
      updateOne: {
        filter: { id: id, deletedAt: { $exists: false } },
        update: { $set: { ...data, updatedAt: new Date() } },
      },
    }));

    const result = await collection.bulkWrite(bulkOps);
    return result.modifiedCount;
  }

  async bulkDelete(ids: string[], hardDelete = false): Promise<number> {
    const collection = await this.getCollection();

    if (hardDelete) {
      const result = await collection.deleteMany({
        id: { $in: ids },
      });
      return result.deletedCount;
    } else {
      const result = await collection.updateMany(
        { id: { $in: ids }},
        { $set: { deletedAt: new Date() } }
      );
      return result.modifiedCount;
    }
  }

  async bulkUpdateStatus(ids: string[], status: "ACTIVE" | "INACTIVE" | "ARCHIVED"): Promise<void> {
    const collection = await this.getCollection();
    await collection.updateMany(
      { id: { $in: ids } },
      { $set: { status, updatedAt: new Date() } }
    );
  }
  
  private mapFromMongo(doc: any): Test {
    return {
      id: doc.id,
      title: doc.title,
      description: doc.description,
      access: doc.access,
      status: doc.status,
      createdAt: doc.createdAt,
      updatedAt: doc.updatedAt,
      deletedAt: doc.deletedAt,
      categories: doc.categories || [],
      difficulties: doc.difficulties || [],
      benefits: doc.benefits || [],
      usersCount: doc.usersCount || 0,
      successRate: doc.successRate,
      durationInSeconds: doc.durationInSeconds,
      totalQuestions: doc.totalQuestions || 0,
      questionIds: doc.questionIds || [],
      image: doc.image,
      cover: doc.cover,
      mediaUrl: doc.mediaUrl,
      reason: doc.reason,
      creator: doc.creator,
      participants: doc.participants || [],
      availability: doc.availability,
      premium: doc.premium,
      topics: doc.topics || [],
      createdBy: doc.createdBy,
      updatedBy: doc.updatedBy,
    };
  }
}
