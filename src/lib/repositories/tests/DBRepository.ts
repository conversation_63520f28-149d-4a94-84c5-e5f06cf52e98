import { Test, TestCreateInput, TestUpdateInput, TestQueryParams, PublicTestQuery } from "./interface";

export interface TestDBRepository {
  getById(id: string, includeDeleted?: boolean): Promise<Test | null>;
  getAll(params?: TestQueryParams): Promise<{ items: Test[]; total: number }>;
  getAllCategories(params?: TestQueryParams): Promise<{ items: string[]; total: number }>;
  getSimilarTests(params?: PublicTestQuery): Promise<{
    items: Test[];
    total: number;
  }>;
  create(data: TestCreateInput): Promise<Test>;
  update(id: string, data: TestUpdateInput): Promise<Test | null>;
  delete(id: string, hardDelete?: boolean): Promise<boolean>;
  restore(id: string): Promise<boolean>;
  bulkCreate(data: TestCreateInput[]): Promise<Test[]>;
  bulkUpdate(updates: { id: string; data: TestUpdateInput }[]): Promise<number>;
  bulkDelete(ids: string[], hardDelete?: boolean): Promise<number>;
  bulkUpdateStatus(ids: string[], status: "ACTIVE" | "INACTIVE" | "ARCHIVED"): Promise<void>
} 