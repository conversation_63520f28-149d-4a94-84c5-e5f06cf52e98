import {
  Test,
  TestCreateInput,
  TestUpdateInput,
  TestQueryParams,
  TestBusinessLogicInterface,
  PublicTestQuery,
} from "./interface";
import { createError } from "@/src/lib/utils/common";
import { TestDBRepository } from "./DBRepository";

export class TestBusinessLogic implements TestBusinessLogicInterface {
  constructor(private readonly db: TestDBRepository) {}

  private validateId(id: string) {
    if (!id || !id.trim()) throw createError("Test ID is required", "INVALID_ID");
  }

  private trimCreateInput(data: TestCreateInput): TestCreateInput {
    return {
      ...data,
      title: data.title.trim(),
      description: data.description?.trim(),
      categories: data.categories?.map(c => c.trim()) ?? [],
      difficulties: data.difficulties?.map(d => d.trim()) ?? [],
      benefits: data.benefits?.map(b => b.trim()) ?? [],
      questionIds: data.questionIds ?? [],
      image: data.image?.trim(),
      cover: data.cover?.trim(),
      mediaUrl: data.mediaUrl?.trim(),
      reason: data.reason?.trim(),
      creator: {
        ...data.creator,
        name: data.creator.name.trim(),
        avatar: data.creator.avatar?.trim(),
      },
      participants: data.participants?.map(p => ({
        ...p,
        name: p.name.trim(),
        avatar: p.avatar?.trim(),
      })),
      topics: data.topics?.map(topic => ({
        ...topic,
        title: topic.title.trim(),
        description: topic.description.trim(),
        subtopics: topic.subtopics?.map(subtopic => ({
          ...subtopic,
          title: subtopic.title.trim(),
          description: subtopic.description.trim(),
        })),
      })),
      premium: data.premium ? {
        ...data.premium,
        price: data.premium.price?.trim(),
        offerText: data.premium.offerText?.trim(),
        badges: data.premium.badges?.map(b => b.trim()),
      } : undefined,
      usersCount: data.usersCount ?? 0,
      totalQuestions: data.totalQuestions ?? 0,
      status: data.status ?? "DRAFT",
    };
  }

  private trimUpdateInput(data: TestUpdateInput): TestUpdateInput {
    return {
      ...data,
      title: data.title?.trim(),
      description: data.description?.trim(),
      categories: data.categories?.map(c => c.trim()),
      difficulties: data.difficulties?.map(d => d.trim()),
      benefits: data.benefits?.map(b => b.trim()),
      questionIds: data.questionIds,
      image: data.image?.trim(),
      cover: data.cover?.trim(),
      mediaUrl: data.mediaUrl?.trim(),
      reason: data.reason?.trim(),
      creator: data.creator ? {
        ...data.creator,
        name: data.creator.name.trim(),
        avatar: data.creator.avatar?.trim(),
      } : undefined,
      participants: data.participants?.map(p => ({
        ...p,
        name: p.name.trim(),
        avatar: p.avatar?.trim(),
      })),
      topics: data.topics?.map(topic => ({
        ...topic,
        title: topic.title.trim(),
        description: topic.description.trim(),
        subtopics: topic.subtopics?.map(subtopic => ({
          ...subtopic,
          title: subtopic.title.trim(),
          description: subtopic.description.trim(),
        })),
      })),
      premium: data.premium ? {
        ...data.premium,
        price: data.premium.price?.trim(),
        offerText: data.premium.offerText?.trim(),
        badges: data.premium.badges?.map(b => b.trim()),
      } : undefined,
    };
  }

  async getById(id: string, includeDeleted = false): Promise<Test | null> {
    this.validateId(id);
    return this.db.getById(id, includeDeleted);
  }

  async getAll(params?: TestQueryParams): Promise<{ items: Test[]; total: number }> {
    return this.db.getAll(params);
  }

  async getAllCategories(params?: TestQueryParams): Promise<{ items: string[]; total: number }> {
    return this.db.getAllCategories(params);
  }
  
  async getSimilarTests(params?: PublicTestQuery): Promise<{ items: Test[]; total: number }> {
    return this.db.getSimilarTests(params);
  }
  
  async create(data: TestCreateInput): Promise<Test> {
    const trimmedData = this.trimCreateInput(data);

    // Check for duplicate title
    const existing = await this.db.getAll({ filters: [{ field: "title", value: trimmedData.title }] });
    if (existing.items.length > 0) {
      throw createError("Test with the same title already exists", "DUPLICATE_TITLE");
    }

    return this.db.create(trimmedData);
  }

  async update(id: string, data: TestUpdateInput): Promise<Test | null> {
    this.validateId(id);

    if (!data || Object.keys(data).length === 0) {
      throw createError("No data provided for update", "INVALID_UPDATE_DATA");
    }

    const existingTest = await this.db.getById(id);
    if (!existingTest) return null;

    if (data.title && data.title.trim() !== existingTest.title) {
      const duplicates = await this.db.getAll({ filters: [{ field: "title", value: data.title.trim() }] });
      if (duplicates.items.some(t => t.id !== id)) {
        throw createError("Another test with this title exists", "DUPLICATE_TITLE");
      }
    }

    const trimmedData = this.trimUpdateInput(data);
    return this.db.update(id, trimmedData);
  }

  async delete(id: string, hardDelete = false): Promise<boolean> {
    this.validateId(id);

    const existingTest = await this.db.getById(id);
    if (!existingTest) throw createError("Test not found", "NOT_FOUND");

    return this.db.delete(id, hardDelete);
  }

  async restore(id: string): Promise<boolean> {
    this.validateId(id);

    const test = await this.db.getById(id, true);
    if (!test || !test.deletedAt) return false;

    // Check for conflicts by title
    const conflict = await this.db.getAll({ filters: [{ field: "title", value: test.title }] });
    if (conflict.items.length > 0) {
      throw createError("Cannot restore: another test with this title exists", "DUPLICATE_TITLE");
    }

    return this.db.restore(id);
  }

  async bulkCreate(data: TestCreateInput[]): Promise<Test[]> {
    if (!data || data.length === 0) {
      throw createError("No data provided for bulk create", "INVALID_BULK_DATA");
    }

    const trimmedData = data.map(item => this.trimCreateInput(item));

    // Check for duplicate titles
    const titles = trimmedData.map(item => item.title);
    const existingTests = await this.db.getAll({ filters: [{ field: "title", value: { $in: titles } }] });
    
    if (existingTests.items.length > 0) {
      const existingTitles = existingTests.items.map(t => t.title);
      const duplicates = titles.filter(title => existingTitles.includes(title));
      throw createError(`Tests with these titles already exist: ${duplicates.join(", ")}`, "DUPLICATE_TITLES");
    }

    return this.db.bulkCreate(trimmedData);
  }

  async bulkUpdate(updates: { id: string; data: TestUpdateInput }[]): Promise<number> {
    if (!updates || updates.length === 0) {
      throw createError("No updates provided", "INVALID_BULK_UPDATE_DATA");
    }

    // Validate all IDs
    updates.forEach(update => this.validateId(update.id));

    // Check for duplicate titles in updates
    const titleUpdates = updates.filter(u => u.data.title);
    if (titleUpdates.length > 0) {
      const titles = titleUpdates.map(u => u.data.title!);
      const uniqueTitles = [...new Set(titles)];
      
      if (uniqueTitles.length !== titles.length) {
        throw createError("Duplicate titles found in bulk update", "DUPLICATE_TITLES");
      }

      // Check against existing tests
      const existingTests = await this.db.getAll({ filters: [{ field: "title", value: { $in: uniqueTitles } }] });
      const existingTitles = existingTests.items.map(t => t.title);
      const conflicts = uniqueTitles.filter(title => existingTitles.includes(title));
      
      if (conflicts.length > 0) {
        throw createError(`Tests with these titles already exist: ${conflicts.join(", ")}`, "DUPLICATE_TITLES");
      }
    }

    const trimmedUpdates = updates.map(update => ({
      id: update.id,
      data: this.trimUpdateInput(update.data)
    }));

    return this.db.bulkUpdate(trimmedUpdates);
  }

  async bulkDelete(ids: string[], hardDelete = false): Promise<number> {
    if (!ids || ids.length === 0) {
      throw createError("No IDs provided for bulk delete", "INVALID_BULK_DELETE_DATA");
    }

    // Validate all IDs
    ids.forEach(id => this.validateId(id));

    return this.db.bulkDelete(ids, hardDelete);
  }

  async bulkUpdateStatus(ids: string[], status: "ACTIVE" | "INACTIVE" | "ARCHIVED"): Promise<void> {
    return await this.db.bulkUpdateStatus(ids, status)
  }
} 