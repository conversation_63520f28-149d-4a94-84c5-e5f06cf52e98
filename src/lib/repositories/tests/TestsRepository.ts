export interface Creator {
  id: string;
  name: string;
  avatar?: string;
}

export interface Test {
  id: string;
  title: string;
  description?: string;
  status: "active" | "inactive" | "archived" | "draft" | "inprogress" | "done" | "cancelled";
  createdAt: Date;
  updatedAt: Date;

  categories?: string[];
  difficulties?: string[];
  benefits: string[];
  usersCount: number;
  successRate?: number;
  durationInSeconds?: number;
  totalQuestions: number;
  questionIds?: string[];
  image?: string;
  mediaUrl?: string;
  reason?: string;
  creator: Creator;

  participants?: {
    id: string;
    name: string;
    avatar?: string;
  }[];

  availability?: {
    isLimitedTime: boolean;
    startTime: Date;
    endTime: Date;
  };
}

export interface PagingAndSearchParams {
  page?: number;
  pageSize?: number;
  search?: string;
  [key: string]: any; // Other possible filters
}

export interface ITestRepository {
  getAll(filters?: PagingAndSearchParams): Promise<Test[]>;
  getById(id: string): Promise<Test | null>;
  create(data: Partial<Test>): Promise<Test>;
  update(id: string, data: Partial<Test>): Promise<Test | null>;
  delete(id: string): Promise<boolean>;

  bulkDelete(ids: string[]): Promise<void>;
  bulkUpdateStatus(
    ids: string[],
    status: "active" | "inactive" | "archived"
  ): Promise<void>;

  publish(id: string): Promise<Test | null>;
  archive(id: string): Promise<Test | null>;
  restore(id: string): Promise<Test | null>;
}
