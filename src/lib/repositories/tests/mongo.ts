import { ITestRepository, PagingAndSearchParams, Test } from "./TestsRepository";
import { mongoDbInstance } from "@/src/lib/db/mongoClient";
import { MONGO_COLLECTIONS } from "../../db/mongoCollections";
import { Question } from "../../question-types";



function mapToTest(doc: any): Test {
  const { _id, ...rest } = doc;
  return {
    id: rest.id,
    ...rest,
    title: rest.title,
    status: rest.status || "draft",
    createdAt: new Date(rest.createdAt),
    updatedAt: new Date(rest.updatedAt),
  };
}

export class MongoTestRepository implements ITestRepository {
  private async getCollection() {
    const client = await mongoDbInstance;
    const db = client.db();
    return db.collection(MONGO_COLLECTIONS.TESTS);
  }

  private generateId() {
    return Math.random().toString(36).substring(2, 11);
  }

  private buildQuery(filters?: PagingAndSearchParams): any {
    const query: any = {};
    if (!filters) return query;

    if (filters.search) {
      query.title = { $regex: filters.search, $options: "i" };
    }

    if (filters.status) {
      query.status = filters.status;
    }

    return query;
  }

  async getAll(filters?: PagingAndSearchParams): Promise<Test[]> {
    const collection = await this.getCollection();
    const query = this.buildQuery(filters);

    const cursor = collection.find(query);

    if (filters?.page && filters?.pageSize) {
      cursor.skip((filters.page - 1) * filters.pageSize).limit(filters.pageSize);
    }

    const docs = await cursor.toArray();
    return docs.map(mapToTest);
  }

  async getById(id: string): Promise<Test | null> {
    const collection = await this.getCollection();
    const doc = await collection.findOne({ id });
    return doc ? mapToTest(doc) : null;
  }

  async create(data: Omit<Test, 'id' | 'createdAt' | 'updatedAt'>): Promise<Test> {
    const collection = await this.getCollection();

    const newTest: Test = {
      ...data,
      id: this.generateId(),
      status: data.status || "draft",
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    await collection.insertOne(newTest);
    return newTest;
  }

  async update(id: string, data: Partial<Omit<Test, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Test | null> {
    const collection = await this.getCollection();

    const result = await collection.findOneAndUpdate(
      { id },
      {
        $set: {
          ...data,
          updatedAt: new Date(),
        },
      },
      { returnDocument: "after" }
    );

    return result ? mapToTest(result) : null;
  }

  async delete(id: string): Promise<boolean> {
    const collection = await this.getCollection();
    const result = await collection.deleteOne({ id });
    return result.deletedCount === 1;
  }

  async bulkDelete(ids: string[]): Promise<void> {
    const collection = await this.getCollection();
    await collection.deleteMany({ id: { $in: ids } });
  }

  async bulkUpdateStatus(ids: string[], status: "active" | "inactive" | "archived"): Promise<void> {
    const collection = await this.getCollection();
    await collection.updateMany(
      { id: { $in: ids } },
      { $set: { status, updatedAt: new Date() } }
    );
  }

  async publish(id: string): Promise<Test | null> {
    return this.update(id, { status: "active" });
  }

  async archive(id: string): Promise<Test | null> {
    return this.update(id, { status: "archived" });
  }

  async restore(id: string): Promise<Test | null> {
    const test = await this.getById(id);
    if (!test || test.status !== "archived") return null;
    return this.update(id, { status: "inactive" });
  }

  async updateQuestions(
    testId: string,
    {
      added,
      removed,
    }: {
      added: string[];
      removed: string[];
    }
  ): Promise<void> {
    const collection = await this.getCollection();

    const updateOps: any = {
      $set: {
        updatedAt: new Date(),
      },
    };

    if (added.length > 0) {
      updateOps.$addToSet = {
        questionIds: { $each: added },
      };
    }

    if (removed.length > 0) {
      updateOps.$pull = {
        questionIds: { $in: removed },
      };
    }

    await collection.updateOne({ id: testId }, updateOps);
  }

  async getQuestionsOfTestWithId(testId: string): Promise<Question[]> {
    const testCollection = await this.getCollection();
    const testDoc = await testCollection.findOne({ id: testId });

    if (!testDoc || !Array.isArray(testDoc.questionIds) || testDoc.questionIds.length === 0) {
      return [];
    }

    const client = await mongoDbInstance;
    const db = client.db();
    const questionsCollection = db.collection(MONGO_COLLECTIONS.QUESTIONS);

    const questions = await questionsCollection
      .find({ id: { $in: testDoc.questionIds } })
      .toArray();

    return questions.map((q: any) => ({
      id: q.id,
      question: q.question,
      type: q.type,
      // Include more fields if needed
    }));
  }

}
