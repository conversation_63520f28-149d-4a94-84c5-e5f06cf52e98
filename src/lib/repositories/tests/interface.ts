export interface Creator {
  id: string;
  name: string;
  avatar?: string;
}

export interface Participant {
  id: string;
  name: string;
  avatar?: string;
}

export interface Availability {
  isLimitedTime: boolean;
  startTime: Date;
  endTime: Date;
}

export interface Premium {
  isPremium: boolean;
  price?: string;
  offerText?: string;
  badges?: string[];
}

export interface Subtopic {
  title: string;
  description: string;
}

export interface Topic {
  title: string;
  description: string;
  subtopics?: Subtopic[];
}

export interface Test {
  id: string;
  title: string;
  description?: string;
  access: "PUBLIC" | "PRIVATE" ;
  status: "ACTIVE" | "INACTIVE" | "ARCHIVED" | "DRAFT" | "inprogress" | "done" | "cancelled";
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  categories?: string[];
  difficulties?: string[];
  benefits: string[];
  usersCount: number;
  successRate?: number;
  durationInSeconds?: number;
  totalQuestions: number;
  questionIds: string[];
  image?: string;
  cover?: string;
  mediaUrl?: string;
  reason?: string;
  creator: Creator;
  participants?: Participant[];
  availability?: Availability;
  premium?: Premium;
  topics?: Topic[];
  createdBy: string;
  updatedBy?: string;
}

export interface TestCreateInput {
  title: string;
  description?: string;
  access?: "PUBLIC" | "PRIVATE" ;
  status?: "ACTIVE" | "INACTIVE" | "ARCHIVED" | "DRAFT" | "inprogress" | "done" | "cancelled";
  categories?: string[];
  difficulties?: string[];
  benefits: string[];
  usersCount?: number;
  successRate?: number;
  durationInSeconds?: number;
  totalQuestions?: number;
  questionIds?: string[];
  image?: string;
  cover?: string;
  mediaUrl?: string;
  reason?: string;
  creator: Creator;
  participants?: Participant[];
  availability?: Availability;
  premium?: Premium;
  topics?: Topic[];
  createdBy: string;
}

export interface TestUpdateInput {
  title?: string;
  description?: string;
  access?: "PUBLIC" | "PRIVATE" ;
  status?: "ACTIVE" | "INACTIVE" | "ARCHIVED" | "DRAFT" | "inprogress" | "done" | "cancelled";
  categories?: string[];
  difficulties?: string[];
  benefits?: string[];
  usersCount?: number;
  successRate?: number;
  durationInSeconds?: number;
  totalQuestions?: number;
  questionIds?: string[];
  image?: string;
  cover?: string;
  mediaUrl?: string;
  reason?: string;
  creator?: Creator;
  participants?: Participant[];
  availability?: Availability | null;
  premium?: Premium;
  topics?: Topic[];
  updatedBy?: string;
}

export interface TestQueryParams {
  search?: string;
  filters?: { field: keyof Test | string; value: any }[];
  sorts?: { field: keyof Test | string; direction: "asc" | "desc" }[];
  page?: number;
  limit?: number;
  includeDeleted?: boolean;
}

export type TestDifficulty = "EASY" | "INTERMEDIATE" | "ADVANCED";

export interface PublicTestQuery {
  query?: string;
  categories?: string[];
}
export interface TestBusinessLogicInterface {
  getById(id: string, includeDeleted?: boolean): Promise<Test | null>;
  getAll(params?: TestQueryParams): Promise<{
    items: Test[];
    total: number;
  }>;
  getAllCategories(params?: TestQueryParams): Promise<{
    items: string[];
    total: number;
  }>;
  getSimilarTests(params?: PublicTestQuery): Promise<{
    items: Test[];
    total: number;
  }>;
  create(data: TestCreateInput): Promise<Test>;
  update(id: string, data: TestUpdateInput): Promise<Test | null>;
  delete(id: string, hardDelete?: boolean): Promise<boolean>;
  restore(id: string): Promise<boolean>;
  bulkCreate(data: TestCreateInput[]): Promise<Test[]>;
  bulkUpdate(updates: { id: string; data: TestUpdateInput }[]): Promise<number>;
  bulkDelete(ids: string[], hardDelete?: boolean): Promise<number>;
} 