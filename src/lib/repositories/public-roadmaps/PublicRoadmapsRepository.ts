import { Result } from "@/types/result";

export type RoadmapLevel = "BEGINNER" | "INTERMEDIATE" | "ADVANCED" | "FULL_JOURNEY";

export interface RoadmapData {
  id: string;
  title: string;
  description: string;
  cover: string;

  levels: RoadmapLevel[];
  tags?: string[]; // optional searchable/filterable tags like ["frontend", "react", "mobile"]

  creator: RoadmapCreator;
  reason: string;               // Why this roadmap matters
  usersCount: number;

  timeline: RoadmapStage[];

  premium?: PremiumInfo;
}

export interface RoadmapStage {
  level: RoadmapLevel;
  title: string;
  description: string;
  skills: string[];
  tests: {
    id: string;
    type: string;
    title: string;
    description?: string;
    questionsCount?: number;
    passScore?: number;
    course?: {
      provider?: string;
      url: string;
    };
  }[];
}

export interface PremiumInfo {
  isPremium?: boolean;
  price?: string;              // e.g. "$9.99"
  offerText?: string;          // e.g. "Get exclusive content + vouchers!"
  badges?: string[];           // e.g. ["1-on-1 Mentoring", "Certificate Included"]
}

export interface RoadmapCreator {
  id: string;
  name: string;
  avatar: string;
}

export interface PublicRoadmapQuery {
  query?: string;
  tags?: string[];
}

export interface PublicRoadmapsRepository {
  getAll(filters?: PublicRoadmapQuery): Promise<RoadmapData[]>;
  getById(id: string): Promise<Result<RoadmapData, string>>;
  getAllTags(): Promise<string[]>;
}
