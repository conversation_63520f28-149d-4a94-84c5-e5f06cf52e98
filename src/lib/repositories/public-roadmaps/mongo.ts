import { Result } from "@/types/result";
import {
  RoadmapData,
  PublicRoadmapQuery,
  PublicRoadmapsRepository,
} from "./PublicRoadmapsRepository";
import { mongoDbInstance } from "@/src/lib/db/mongoClient";
import { MONGO_COLLECTIONS } from "../../db/mongoCollections";

function mapToRoadmapData(doc: any): RoadmapData {
  return {
    id: doc.id,
    title: doc.title,
    description: doc.description,
    cover: doc.cover,
    levels: doc.levels,
    tags: doc.tags,
    creator: {
      id: doc.creator?.id,
      name: doc.creator?.name,
      avatar: doc.creator?.avatar,
    },
    reason: doc.reason,
    usersCount: doc.usersCount,
    premium: doc.premium,
    timeline: doc.timeline
  };
}

export class MongoPublicRoadmapsRepository implements PublicRoadmapsRepository {
  private async getCollection() {
    const client = await mongoDbInstance;
    const db = client.db(); // db name comes from URI
    return db.collection(MONGO_COLLECTIONS.PUBLIC_ROADMAPS);
  }

  async getAll(filters?: PublicRoadmapQuery): Promise<RoadmapData[]> {
    const collection = await this.getCollection();

    const query: any = {};

    if (filters?.query) {
      const regex = new RegExp(filters.query, "i");
      query.$or = [
        { title: { $regex: regex } },
        { description: { $regex: regex } },
      ];
    }

    if (filters?.tags?.length) {
      query.tags = { $in: filters.tags };
    }

    const docs = await collection.find(query).toArray();
    return docs.map(mapToRoadmapData);
  }

  async getAllTags(): Promise<string[]> {
    const collection = await this.getCollection();
    const docs = await collection.find({}).toArray();

    const tagSet = new Set<string>();
    docs.forEach((doc) => {
      (doc.tags || []).forEach((tag: string) => tagSet.add(tag));
    });

    return ["All", ...Array.from(tagSet)];
  }

  async getById(id: string): Promise<Result<RoadmapData, string>> {
    const collection = await this.getCollection();
    const doc = await collection.findOne({ id });

    if (!doc) {
      return { ok: false, error: `RoadmapData with id ${id} not found` };
    }

    return { ok: true, value: mapToRoadmapData(doc) };
  }
}
