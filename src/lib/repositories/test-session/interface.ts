export interface TestUser {
  id: string;
  name: string;
  email: string;
}

export interface TestSession {
  id: string;
  testId: string;
  user: TestUser;
  status: "OPEN" | "COMPLETED" | "EXPIRED" | "CANCELLED";
  startedAt: Date;
  completedAt?: Date;
  expiresAt?: Date;
  timeSpent: number;
  currentQuestionIndex: number;
  totalQuestions: number;
  answers: SubmittedAnswer[];
  score?: number;
  exitReason?: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  createdBy: string;
  updatedBy?: string;
}

export interface TestSessionCreateInput {
  testId: string;
  user: TestUser;
  expiresAt?: Date;
  createdBy: string;
}

export interface TestSessionUpdateInput {
  status?: "OPEN" | "COMPLETED" | "EXPIRED" | "CANCELLED";
  completedAt?: Date;
  timeSpent?: number;
  currentQuestionIndex?: number;
  answers?: SubmittedAnswer[];
  score?: number;
  exitReason?: string;
  updatedBy?: string;
}

export interface SubmittedAnswer {
  questionId: string;
  answer: string | string[];
  timeSpent?: number;
  answeredAt?: Date;
}

export interface TestSessionQueryParams {
  search?: string;
  filters?: { field: keyof TestSession | string; value: any }[];
  sorts?: { field: keyof TestSession | string; direction: "asc" | "desc" }[];
  page?: number;
  limit?: number;
  includeDeleted?: boolean;
}

export interface TestSessionBusinessLogicInterface {
  // Expose the db repository for advanced operations
  readonly db: any;
  
  getById(id: string, includeDeleted?: boolean): Promise<TestSession | null>;
  getAll(params?: TestSessionQueryParams): Promise<{
    items: TestSession[];
    total: number;
  }>;
  create(data: TestSessionCreateInput): Promise<TestSession>;
  update(id: string, data: TestSessionUpdateInput): Promise<TestSession | null>;
  delete(id: string, hardDelete?: boolean): Promise<boolean>;
  restore(id: string): Promise<boolean>;
  bulkCreate(data: TestSessionCreateInput[]): Promise<TestSession[]>;
  bulkUpdate(updates: { id: string; data: TestSessionUpdateInput }[]): Promise<number>;
  bulkDelete(ids: string[], hardDelete?: boolean): Promise<number>;
  
  // Test session specific methods
  submitAnswers(sessionId: string, answers: SubmittedAnswer[]): Promise<TestSession | null>;
  exitSession(sessionId: string, exitReason: string, timeSpent: number): Promise<TestSession | null>;
  getSessionsByTestId(testId: string, params?: TestSessionQueryParams): Promise<{
    items: TestSession[];
    total: number;
  }>;
  getSessionsByUserId(userId: string, params?: TestSessionQueryParams): Promise<{
    items: TestSession[];
    total: number;
  }>;
}