import { TestSessionQuestion } from "@/src/app/test-session/[id]/model";
import { QuestionReview } from "../test-result/types";
import { SubmittedAnswer } from "./TestSessionRepository";

export function scoreAnswer(
  answer: SubmittedAnswer,
  question: TestSessionQuestion
): {
  score: "correct" | "incorrect" | "review";
  review?: QuestionReview;
} {
  const internal = question.internal || {};
  const userAnswer = answer.answer;

  const needsManualReview = (): QuestionReview => ({
    id: Math.random().toString(36).substring(2, 11),
    type: question.type,
    sessionQuestionId: question.id,
    metadata: {
      userAnswer,
      questionText: question.question,
      correctAnswer: internal.correctAnswer,
      correctAnswers: internal.correctAnswers,
      correctPhrases: internal.correctPhrases,
      reviewGuide: internal.reviewGuide,
    },
  });

  // Case 1: Manual review explicitly required
  if (internal.skipAutomaticCheck) {
    return {
      score: "review",
      review: needsManualReview(),
    };
  }

  // Case 2: Exact match
  if (
    Array.isArray(internal.correctAnswers) &&
    internal.correctAnswers.length > 0
  ) {
    const correct = internal.correctAnswers;

    const isMatch = Array.isArray(userAnswer)
      ? arraysEqualUnordered(userAnswer, correct)
      : correct.includes(userAnswer);

    return { score: isMatch ? "correct" : "incorrect" };
  }

  // Case 3: Phrase match (case-insensitive)
  if (
    Array.isArray(internal.correctPhrases) &&
    internal.correctPhrases.length > 0
  ) {
    const normalizedAnswer = Array.isArray(userAnswer)
      ? userAnswer.map((s) => s.toLowerCase()).join(" ")
      : userAnswer.toLowerCase();

    const matched = internal.correctPhrases.some((phrase: string) =>
      normalizedAnswer.includes(phrase.toLowerCase())
    );

    return { score: matched ? "correct" : "incorrect" };
  }

  // Case 4: No rule matched — fallback to review
  return {
    score: "review",
    review: needsManualReview(),
  };
}

// Helper: compare arrays regardless of order
function arraysEqualUnordered(a: string[], b: string[]): boolean {
  if (a.length !== b.length) return false;
  const sortedA = [...a].sort();
  const sortedB = [...b].sort();
  return sortedA.every((val, index) => val === sortedB[index]);
}