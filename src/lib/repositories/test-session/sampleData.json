[{"id": "1", "title": "English Business Communication", "status": "needs-review", "description": "Assess your ability to communicate effectively in professional environments.", "mediaUrl": "/videos/english-business.mp4", "benefits": ["Professional communication", "Global readiness", "Career advancement"], "duration": 15, "categories": ["Business", "Language"], "difficulty": "Intermediate", "totalQuestions": 10, "studentName": "<PERSON>", "creator": {"id": "c1", "name": "Pondok IT"}, "reason": "to help learners and recruiters connect better", "usersCount": 5920, "successRate": 88, "test_id": 2, "startedAt": "2020/12/12", "reviewableQuestions": 3}, {"id": "2", "title": "Advanced JavaScript Fundamentals", "status": "in-progress", "description": "Test your advanced knowledge of JavaScript and modern development practices.", "mediaUrl": "/videos/advanced-js.mp4", "benefits": ["Deep JS understanding", "Improved coding skills", "Better job prospects"], "duration": 30, "categories": ["Programming", "JavaScript"], "difficulty": "Advanced", "totalQuestions": 20, "studentName": "<PERSON>", "creator": {"id": "c2", "name": "Code Academy"}, "reason": "to benchmark skills for hiring decisions", "usersCount": 3210, "successRate": 75, "test_id": 2, "startedAt": "2020/12/12", "reviewableQuestions": 3}, {"id": "3", "title": "Digital Marketing Essentials", "status": "completed", "description": "Evaluate your knowledge of modern digital marketing strategies and tools.", "mediaUrl": "/videos/digital-marketing.mp4", "benefits": ["Marketing skills", "Online branding", "Campaign analysis"], "duration": 20, "categories": ["Marketing", "Digital Skills"], "difficulty": "<PERSON><PERSON><PERSON>", "totalQuestions": 15, "studentName": "<PERSON>", "creator": {"id": "c3", "name": "Marketing Hub"}, "reason": "to prepare learners for online marketing roles", "usersCount": 4700, "successRate": 82, "test_id": 1, "startedAt": "2020/12/12", "reviewableQuestions": 2}, {"id": "4", "title": "Data Analysis with Python", "status": "needs-review", "description": "Test your ability to work with data using Python libraries like pandas and NumPy.", "mediaUrl": "/videos/data-python.mp4", "benefits": ["Data proficiency", "Python for analysis", "Career boost in analytics"], "duration": 25, "categories": ["Data Science", "Programming"], "difficulty": "Intermediate", "totalQuestions": 18, "studentName": "<PERSON>", "creator": {"id": "c4", "name": "DataCamp"}, "reason": "to validate data handling and analysis capabilities", "usersCount": 2890, "successRate": 79, "test_id": 1, "startedAt": "2020/12/12", "reviewableQuestions": 3}, {"id": "5", "title": "UX/UI Design Basics", "status": "in-progress", "description": "Assess your understanding of user experience and interface design fundamentals.", "mediaUrl": "/videos/ux-ui.mp4", "benefits": ["Design thinking", "User empathy", "Portfolio-ready skills"], "duration": 18, "categories": ["Design", "UX/UI"], "difficulty": "<PERSON><PERSON><PERSON>", "totalQuestions": 12, "studentName": "<PERSON>", "creator": {"id": "c5", "name": "Creative Studio"}, "reason": "to help designers validate their UX/UI foundations", "usersCount": 1980, "successRate": 85, "test_id": 1, "startedAt": "2020/12/12", "reviewableQuestions": 3}, {"id": "6", "title": "Project Management Principles", "status": "reviewed", "description": "Check your grasp on key project management concepts and practices.", "mediaUrl": "/videos/project-mgmt.mp4", "benefits": ["Organizational skills", "Team leadership", "Effective planning"], "duration": 22, "categories": ["Management", "Business"], "difficulty": "Intermediate", "totalQuestions": 16, "studentName": "<PERSON>", "creator": {"id": "c6", "name": "PM Academy"}, "reason": "to assess readiness for project management roles", "usersCount": 3560, "successRate": 81, "test_id": 1, "startedAt": "2020/12/12", "reviewableQuestions": 3}]