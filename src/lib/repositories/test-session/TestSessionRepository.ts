import { TestSessionQuestion } from "@/src/app/test-session/[id]/model";
import { Result } from "@/types/result";
import { TestResult } from "../test-result/types";

export interface TestSession {
  id: string;
  title: string;
  status: "CLOSED" | "OPEN";
  description: string;
  benefits: string[];
  duration: number;
  categories: string[];
  difficulty: string;
  totalQuestions: number;
  user: TestUser;
}

export interface TestUser {
  id: string;
  name: string;
  email: string;
}

export type SubmittedAnswer = {
  questionId: string;
  answer: string | string[];
};

export interface ExitData {
  userId?: string;
  timeSpent: number;
  exitReason: string;
  exitedAt: Date;
}

export interface TestSessionRepository {
  getById(id: string): Promise<Result<TestSession, string>>;
  create(user: TestUser, testId: string): Promise<TestSession>;
  getQuestions(id: string): Promise<TestSessionQuestion[]>;
  submitAnswers(
    sessionId: string,
    answers: SubmittedAnswer[]
  ): Promise<TestResult>;
  exitSession(sessionId: string, data: ExitData): Promise<void>;
  saveSession(
    session: TestSession,
    questions: TestSessionQuestion[]
  ): Promise<void>;
}

export interface PagingAndSearchParams {
  page?: number;
  pageSize?: number;
  search?: string;
  [key: string]: any; // Other possible filters
}