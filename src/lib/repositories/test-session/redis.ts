import { Result } from "@/types/result";
import {
  ExitData,
  SubmittedAnswer,
  TestSession,
  TestSessionRepository,
  TestUser,
} from "./TestSessionRepository";
import { TestSessionQuestion } from "@/src/app/test-session/[id]/model";
import { TestResult } from "../test-result/types";
import { redisClient } from "../../db/redis";

export class RedisTestSessionRepository implements TestSessionRepository {
  private sessionKey(id: string) {
    return `testSession:${id}`;
  }
  private questionsKey(sessionId: string) {
    return `testSessionQuestions:${sessionId}`;
  }

  async getById(id: string): Promise<Result<TestSession, string>> {
    const cached = await redisClient.get(this.sessionKey(id));
    if (!cached) return { ok: false, error: "Not found in Redis" };
    return { ok: true, value: cached as TestSession };
  }

  async getQuestions(id: string): Promise<TestSessionQuestion[]> {
    const cached = await redisClient.get(this.questionsKey(id));
    if (!cached) return [];
    return cached as TestSessionQuestion[];
  }

  async saveSession(
    session: TestSession,
    questions: TestSessionQuestion[]
  ): Promise<void> {
    await redisClient.set(
      this.sessionKey(session.id),
      JSON.stringify(session),
      {
        ex: 30,
      }
    );
    await redisClient.set(
      this.questionsKey(session.id),
      JSON.stringify(questions),
      { ex: 30 }
    );
  }

  async create(user: TestUser, testId: string): Promise<TestSession> {
    throw new Error("Create is not supported on Redis repository");
  }

  async submitAnswers(
    sessionId: string,
    answers: SubmittedAnswer[]
  ): Promise<TestResult> {
    throw new Error("Method not implemented.");
  }
  async exitSession(sessionId: string, data: ExitData): Promise<void> {
    throw new Error("Method not implemented.");
  }
}
