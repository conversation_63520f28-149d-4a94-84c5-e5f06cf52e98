import { 
  TestSession, 
  TestSessionCreateInput, 
  TestSessionUpdateInput, 
  TestSessionQueryParams,
  SubmittedAnswer,
  TestUser 
} from "./interface";
import { TestSessionDBRepository } from "./DBRepository";
import { TestSessionQuestion } from "@/src/app/test-session/[id]/model";
import { TestResult } from "../test-result/types";
import { redisClient } from "@/src/lib/db/redis";

export class RedisTestSessionRepository implements TestSessionDBRepository {
  private sessionKey(id: string) {
    return `testSession:${id}`;
  }
  private questionsKey(sessionId: string) {
    return `testSessionQuestions:${sessionId}`;
  }

  async getById(id: string, includeDeleted = false): Promise<TestSession | null> {
    try {
      const cached = await redisClient.get(this.sessionKey(id));
      if (!cached) return null;
      
      const session = JSON.parse(cached as string) as TestSession;
      
      // Handle soft delete logic
      if (!includeDeleted && session.deletedAt) {
        return null;
      }
      
      // Ensure dates are properly deserialized
      return {
        ...session,
        startedAt: new Date(session.startedAt),
        completedAt: session.completedAt ? new Date(session.completedAt) : undefined,
        expiresAt: session.expiresAt ? new Date(session.expiresAt) : undefined,
        createdAt: new Date(session.createdAt),
        updatedAt: new Date(session.updatedAt),
        deletedAt: session.deletedAt ? new Date(session.deletedAt) : undefined,
      };
    } catch (error) {
      console.error('Redis getById error:', error);
      return null;
    }
  }

  async getAll(params?: TestSessionQueryParams): Promise<{ items: TestSession[]; total: number }> {
    // Redis is not suitable for complex queries, this should delegate to primary DB
    throw new Error("getAll is not supported on Redis repository - use primary database");
  }

  async create(data: TestSessionCreateInput): Promise<TestSession> {
    throw new Error("create is not supported on Redis repository - use primary database");
  }

  async update(id: string, data: TestSessionUpdateInput): Promise<TestSession | null> {
    try {
      const existing = await this.getById(id);
      if (!existing) return null;

      const updated: TestSession = {
        ...existing,
        ...data,
        updatedAt: new Date(),
      };

      await this.setSession(updated);
      return updated;
    } catch (error) {
      console.error('Redis update error:', error);
      return null;
    }
  }

  async delete(id: string, hardDelete = false): Promise<boolean> {
    try {
      if (hardDelete) {
        const deleted = await redisClient.del(this.sessionKey(id));
        await redisClient.del(this.questionsKey(id));
        return deleted > 0;
      } else {
        const existing = await this.getById(id, true);
        if (!existing) return false;

        const softDeleted: TestSession = {
          ...existing,
          deletedAt: new Date(),
          updatedAt: new Date(),
        };

        await this.setSession(softDeleted);
        return true;
      }
    } catch (error) {
      console.error('Redis delete error:', error);
      return false;
    }
  }

  async restore(id: string): Promise<boolean> {
    try {
      const existing = await this.getById(id, true);
      if (!existing || !existing.deletedAt) return false;

      const restored: TestSession = {
        ...existing,
        deletedAt: undefined,
        updatedAt: new Date(),
      };

      await this.setSession(restored);
      return true;
    } catch (error) {
      console.error('Redis restore error:', error);
      return false;
    }
  }

  async bulkCreate(data: TestSessionCreateInput[]): Promise<TestSession[]> {
    throw new Error("bulkCreate is not supported on Redis repository - use primary database");
  }

  async bulkUpdate(updates: { id: string; data: TestSessionUpdateInput }[]): Promise<number> {
    throw new Error("bulkUpdate is not supported on Redis repository - use primary database");
  }

  async bulkDelete(ids: string[], hardDelete = false): Promise<number> {
    throw new Error("bulkDelete is not supported on Redis repository - use primary database");
  }

  async submitAnswers(sessionId: string, answers: SubmittedAnswer[]): Promise<TestSession | null> {
    try {
      const existing = await this.getById(sessionId);
      if (!existing) return null;

      const updated: TestSession = {
        ...existing,
        answers,
        status: "COMPLETED",
        completedAt: new Date(),
        updatedAt: new Date(),
      };

      await this.setSession(updated);
      return updated;
    } catch (error) {
      console.error('Redis submitAnswers error:', error);
      return null;
    }
  }

  async exitSession(sessionId: string, exitReason: string, timeSpent: number): Promise<TestSession | null> {
    try {
      const existing = await this.getById(sessionId);
      if (!existing) return null;

      const updated: TestSession = {
        ...existing,
        status: "CANCELLED",
        exitReason,
        timeSpent,
        updatedAt: new Date(),
      };

      await this.setSession(updated);
      return updated;
    } catch (error) {
      console.error('Redis exitSession error:', error);
      return null;
    }
  }

  async getSessionsByTestId(testId: string, params?: TestSessionQueryParams): Promise<{
    items: TestSession[];
    total: number;
  }> {
    throw new Error("getSessionsByTestId is not supported on Redis repository - use primary database");
  }

  async getSessionsByUserId(userId: string, params?: TestSessionQueryParams): Promise<{
    items: TestSession[];
    total: number;
  }> {
    throw new Error("getSessionsByUserId is not supported on Redis repository - use primary database");
  }

  // Utility methods for Redis operations

  private async setSession(session: TestSession, ttl = 3600): Promise<void> {
    await redisClient.set(
      this.sessionKey(session.id),
      JSON.stringify(session),
      { ex: ttl }
    );
  }

  async getQuestions(id: string): Promise<TestSessionQuestion[]> {
    try {
      const cached = await redisClient.get(this.questionsKey(id));
      if (!cached) return [];
      return JSON.parse(cached as string) as TestSessionQuestion[];
    } catch (error) {
      console.error('Redis getQuestions error:', error);
      return [];
    }
  }

  async saveSession(
    session: TestSession,
    questions: TestSessionQuestion[],
    ttl = 3600
  ): Promise<void> {
    try {
      await this.setSession(session, ttl);
      await redisClient.set(
        this.questionsKey(session.id),
        JSON.stringify(questions),
        { ex: ttl }
      );
    } catch (error) {
      console.error('Redis saveSession error:', error);
      throw error;
    }
  }

  async clearSession(sessionId: string): Promise<void> {
    try {
      await redisClient.del(this.sessionKey(sessionId));
      await redisClient.del(this.questionsKey(sessionId));
    } catch (error) {
      console.error('Redis clearSession error:', error);
    }
  }
}
