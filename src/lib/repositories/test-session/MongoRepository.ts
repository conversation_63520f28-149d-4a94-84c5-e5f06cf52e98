import { ObjectId } from "mongodb";
import { mongoDbInstance } from "@/src/lib/db/mongoClient";
import { MONGO_COLLECTIONS } from "@/src/lib/db/mongoCollections";
import {
  TestSession,
  TestSessionCreateInput,
  TestSessionUpdateInput,
  TestSessionQueryParams,
  SubmittedAnswer,
  TestUser,
} from "./interface";
import { TestSessionDBRepository } from "./DBRepository";
import { TestSessionQuestion } from "@/src/app/test-session/[id]/model";
import { TestResult } from "../test-result/types";
import { Question } from "../questions/types";
import {
  CategoryPerformance,
  QuestionReview,
} from "../test-result/types";
import { scoreAnswer } from "./scoreAnswer";

export class TestSessionMongoRepository implements TestSessionDBRepository {

  private async getCollection() {
    const client = await mongoDbInstance;
    const db = client.db();
    return db.collection(MONGO_COLLECTIONS.TEST_SESSIONS);
  }

  private async getQuestionsCollection() {
    const client = await mongoDbInstance;
    const db = client.db();
    return db.collection(MONGO_COLLECTIONS.QUESTIONS);
  }

  private async getTestsCollection() {
    const client = await mongoDbInstance;
    const db = client.db();
    return db.collection(MONGO_COLLECTIONS.TESTS);
  }

  private async getTestSessionQuestionsCollection() {
    const client = await mongoDbInstance;
    const db = client.db();
    return db.collection(MONGO_COLLECTIONS.TEST_SESSIONS_QUESTIONS);
  }

  private buildFilter(query: TestSessionQueryParams = {}) {
    const filter: any = {};

    // Handle soft delete
    if (!query.includeDeleted) {
      filter.deletedAt = { $exists: false };
    }

    // Handle search
    if (query.search) {
      filter.$or = [
        { "user.name": { $regex: query.search, $options: "i" } },
        { "user.email": { $regex: query.search, $options: "i" } },
        { testId: { $regex: query.search, $options: "i" } },
      ];
    }

    // Handle custom filters
    if (query.filters) {
      query.filters.forEach(({ field, value }) => {
        if (field === "status") {
          filter.status = value;
        } else if (field === "testId") {
          filter.testId = value;
        } else if (field === "user.id") {
          filter["user.id"] = value;
        } else {
          filter[field] = value;
        }
      });
    }

    return filter;
  }

  private buildSort(sorts?: TestSessionQueryParams["sorts"]) {
    if (!sorts || sorts.length === 0) {
      return { createdAt: -1 };
    }

    const sortObj: any = {};
    sorts.forEach(({ field, direction }) => {
      sortObj[field] = direction === "desc" ? -1 : 1;
    });

    return sortObj;
  }

  async getById(id: string, includeDeleted = false): Promise<TestSession | null> {
    try {
      const collection = await this.getCollection();
      const filter: any = { id: id };

      if (!includeDeleted) {
        filter.deletedAt = { $exists: false };
      }

      const result = await collection.findOne(filter);
      return result ? this.mapFromMongo(result) : null;
    } catch (error) {
      console.error("Error getting test session by ID:", error);
      return null;
    }
  }

  async getAll(
    params?: TestSessionQueryParams
  ): Promise<{ items: TestSession[]; total: number }> {
    const collection = await this.getCollection();
    const filter = this.buildFilter(params);
    const sort = this.buildSort(params?.sorts);

    const [items, total] = await Promise.all([
      collection
        .find(filter)
        .sort(sort)
        .skip(((params?.page || 1) - 1) * (params?.limit || 10))
        .limit(params?.limit || 10)
        .toArray(),
      collection.countDocuments(filter),
    ]);

    return {
      items: items.map((item) => this.mapFromMongo(item)),
      total,
    };
  }

  async create(data: TestSessionCreateInput): Promise<TestSession> {
    try {
      const sessionId = Math.random().toString(36).substring(2, 11);
      const now = new Date();

      // Get test information to determine total questions
      const testsCollection = await this.getTestsCollection();
      const testDoc = await testsCollection.findOne({ id: data.testId });

      if (!testDoc || !Array.isArray(testDoc.questionIds) || testDoc.questionIds.length === 0) {
        throw new Error("No questions found for the given test ID");
      }

      const questionIds = testDoc.questionIds;

      // Fetch question documents
      const questionsCol = await this.getQuestionsCollection();
      const baseQuestions = await questionsCol
        .find({ id: { $in: questionIds } })
        .toArray();

      // Process questions with template handling
      const processedQuestions = baseQuestions.map((q) => {
        const templateKeys = Array.from(
          q.question.matchAll(/{{\s*([\w]+)\s*}}/g),
          (m: any) => m[1]
        );

        const newData = { ...q.data };

        for (const key of templateKeys) {
          if (Array.isArray(newData?.[key])) {
            const arr = newData[key];
            const selected = arr[Math.floor(Math.random() * arr.length)];
            newData[key] = selected;
          }
        }

        return {
          ...q,
          _id: new ObjectId(),
          data: newData,
          sessionId,
          originalQuestionId: q.id,
          id: Math.random().toString(36).substring(2, 11),
          createdAt: now,
        };
      });

      // Create the session document
      const sessionData: TestSession = {
        id: sessionId,
        testId: data.testId,
        user: data.user,
        status: "OPEN",
        startedAt: now,
        timeSpent: 0,
        currentQuestionIndex: 0,
        totalQuestions: processedQuestions.length,
        answers: [],
        createdAt: now,
        updatedAt: now,
        createdBy: data.createdBy,
        expiresAt: data.expiresAt,
      };

      // Save session to database
      const collection = await this.getCollection();
      await collection.insertOne(sessionData);

      // Save processed questions to separate collection
      const sessionQuestionsCol = await this.getTestSessionQuestionsCollection();
      await sessionQuestionsCol.insertMany(processedQuestions);

      // Update test usage count
      await testsCollection.updateOne(
        { id: data.testId },
        {
          $inc: { usersCount: 1 },
          $set: { updatedAt: new Date() },
        }
      );

      return sessionData;
    } catch (error) {
      console.error("Error creating test session:", error);
      throw error;
    }
  }

  async update(id: string, data: TestSessionUpdateInput): Promise<TestSession | null> {
    try {
      const collection = await this.getCollection();
      const updateData = {
        ...data,
        updatedAt: new Date(),
      };

      const result = await collection.findOneAndUpdate(
        { id: id, deletedAt: { $exists: false } },
        { $set: updateData },
        { returnDocument: "after" }
      );

      return result ? this.mapFromMongo(result) : null;
    } catch (error) {
      console.error("Error updating test session:", error);
      return null;
    }
  }

  async delete(id: string, hardDelete = false): Promise<boolean> {
    try {
      const collection = await this.getCollection();

      if (hardDelete) {
        const result = await collection.deleteOne({ id: id });
        return result.deletedCount > 0;
      } else {
        const result = await collection.updateOne(
          { id: id },
          { $set: { deletedAt: new Date(), updatedAt: new Date() } }
        );
        return result.modifiedCount > 0;
      }
    } catch (error) {
      console.error("Error deleting test session:", error);
      return false;
    }
  }

  async restore(id: string): Promise<boolean> {
    try {
      const collection = await this.getCollection();
      const result = await collection.updateOne(
        { id: id },
        { $unset: { deletedAt: "" }, $set: { updatedAt: new Date() } }
      );
      return result.modifiedCount > 0;
    } catch (error) {
      console.error("Error restoring test session:", error);
      return false;
    }
  }

  async bulkCreate(data: TestSessionCreateInput[]): Promise<TestSession[]> {
    const collection = await this.getCollection();
    const now = new Date();

    const sessionDataArray = data.map((item) => ({
      ...item,
      _id: new ObjectId(),
      id: new ObjectId().toString(),
      status: "OPEN" as const,
      startedAt: now,
      timeSpent: 0,
      currentQuestionIndex: 0,
      totalQuestions: 0,
      answers: [],
      createdAt: now,
      updatedAt: now,
    }));

    const result = await collection.insertMany(sessionDataArray);
    const created = await collection
      .find({ _id: { $in: Object.values(result.insertedIds) } })
      .toArray();
    return created.map((item) => this.mapFromMongo(item));
  }

  async bulkUpdate(
    updates: { id: string; data: TestSessionUpdateInput }[]
  ): Promise<number> {
    try {
      const collection = await this.getCollection();
      const bulkOps = updates.map(({ id, data }) => ({
        updateOne: {
          filter: { id: id, deletedAt: { $exists: false } },
          update: { $set: { ...data, updatedAt: new Date() } },
        },
      }));

      const result = await collection.bulkWrite(bulkOps);
      return result.modifiedCount;
    } catch (error) {
      console.error("Error bulk updating test sessions:", error);
      return 0;
    }
  }

  async bulkDelete(ids: string[], hardDelete = false): Promise<number> {
    try {
      const collection = await this.getCollection();

      if (hardDelete) {
        const result = await collection.deleteMany({
          id: { $in: ids },
        });
        return result.deletedCount;
      } else {
        const result = await collection.updateMany(
          { id: { $in: ids } },
          { $set: { deletedAt: new Date(), updatedAt: new Date() } }
        );
        return result.modifiedCount;
      }
    } catch (error) {
      console.error("Error bulk deleting test sessions:", error);
      return 0;
    }
  }

  async submitAnswers(sessionId: string, answers: SubmittedAnswer[]): Promise<TestSession | null> {
    try {
      const collection = await this.getCollection();
      
      // Calculate basic scoring
      const questions = await this.getQuestions(sessionId);
      let correctAnswers = 0;
      let incorrectAnswers = 0;
      
      answers.forEach(answer => {
        const question = questions.find(q => q.id === answer.questionId);
        if (question) {
          // Use the proper scoring logic
          const submittedAnswer = {
            questionId: answer.questionId,
            answer: answer.answer,
          };
          
          const result = scoreAnswer(submittedAnswer, question);
          
          if (result.score === "correct") {
            correctAnswers++;
          } else if (result.score === "incorrect") {
            incorrectAnswers++;
          }
          // Note: We don't track pending reviews in the simple submitAnswers method
        }
      });

      const totalQuestions = questions.length;
      const successRate = totalQuestions > 0 ? (correctAnswers / totalQuestions) * 100 : 0;

      // Update session with answers and completion status
      const result = await collection.findOneAndUpdate(
        { id: sessionId, deletedAt: { $exists: false } },
        { 
          $set: { 
            answers,
            status: "COMPLETED",
            completedAt: new Date(),
            updatedAt: new Date(),
            score: successRate,
            timeSpent: 0, // Can be calculated if needed
          }
        },
        { returnDocument: "after" }
      );

      return result ? this.mapFromMongo(result) : null;
    } catch (error) {
      console.error("Error submitting answers:", error);
      return null;
    }
  }

  async exitSession(sessionId: string, exitReason: string, timeSpent: number): Promise<TestSession | null> {
    try {
      const collection = await this.getCollection();
      
      const result = await collection.findOneAndUpdate(
        { id: sessionId, deletedAt: { $exists: false } },
        { 
          $set: { 
            status: "CANCELLED",
            exitReason,
            timeSpent,
            updatedAt: new Date()
          }
        },
        { returnDocument: "after" }
      );

      return result ? this.mapFromMongo(result) : null;
    } catch (error) {
      console.error("Error exiting session:", error);
      return null;
    }
  }

  async getSessionsByTestId(testId: string, params?: TestSessionQueryParams): Promise<{
    items: TestSession[];
    total: number;
  }> {
    const collection = await this.getCollection();
    const filter = {
      ...this.buildFilter(params),
      testId
    };
    const sort = this.buildSort(params?.sorts);

    const [items, total] = await Promise.all([
      collection
        .find(filter)
        .sort(sort)
        .skip(((params?.page || 1) - 1) * (params?.limit || 10))
        .limit(params?.limit || 10)
        .toArray(),
      collection.countDocuments(filter),
    ]);

    return {
      items: items.map((item) => this.mapFromMongo(item)),
      total,
    };
  }

  async getSessionsByUserId(userId: string, params?: TestSessionQueryParams): Promise<{
    items: TestSession[];
    total: number;
  }> {
    const collection = await this.getCollection();
    const filter = {
      ...this.buildFilter(params),
      "user.id": userId
    };
    const sort = this.buildSort(params?.sorts);

    const [items, total] = await Promise.all([
      collection
        .find(filter)
        .sort(sort)
        .skip(((params?.page || 1) - 1) * (params?.limit || 10))
        .limit(params?.limit || 10)
        .toArray(),
      collection.countDocuments(filter),
    ]);

    return {
      items: items.map((item) => this.mapFromMongo(item)),
      total,
    };
  }

  private mapFromMongo(doc: any): TestSession {
    return {
      id: doc.id || doc._id?.toString(),
      testId: doc.testId || "",
      user: doc.user || { id: "", name: "", email: "" },
      status: doc.status || "OPEN",
      startedAt: doc.startedAt || doc.createdAt || new Date(),
      completedAt: doc.completedAt,
      expiresAt: doc.expiresAt,
      timeSpent: doc.timeSpent || 0,
      currentQuestionIndex: doc.currentQuestionIndex || 0,
      totalQuestions: doc.totalQuestions || 0,
      answers: doc.answers || [],
      score: doc.score,
      exitReason: doc.exitReason,
      createdAt: doc.createdAt || new Date(),
      updatedAt: doc.updatedAt || new Date(),
      deletedAt: doc.deletedAt,
      createdBy: doc.createdBy || doc.user?.id || "",
      updatedBy: doc.updatedBy,
    };
  }

  // Helper methods

  /**
   * Get test session questions
   */
  async getQuestions(sessionId: string): Promise<TestSessionQuestion[]> {
    try {
      const collection = await this.getTestSessionQuestionsCollection();
      const questionDocs = await collection.find({ sessionId }).toArray();
      return questionDocs.map(doc => this.mapToTestSessionQuestion(doc));
    } catch (error) {
      console.error("Error getting questions:", error);
      return [];
    }
  }

  /**
   * Submit answers and get test result
   */
  async submitAnswersAndGetResult(sessionId: string, answers: SubmittedAnswer[]): Promise<TestResult> {
    const questions = await this.getQuestions(sessionId);
    const session = await this.getById(sessionId);
    
    if (!session) {
      throw new Error("Session not found");
    }

    let correctAnswers = 0;
    let incorrectAnswers = 0;
    let pendingReview = 0;
    const reviews: QuestionReview[] = [];

    const categoryMap: Map<string, { correct: number; total: number }> = new Map();

    for (const answer of answers) {
      const question = questions.find((q) => q.id === answer.questionId);
      if (!question) continue;

      // Use the scoreAnswer function for detailed scoring
      const submittedAnswer = {
        questionId: answer.questionId,
        answer: answer.answer,
      };
      
      const result = scoreAnswer(submittedAnswer, question);
      
      // Tally score based on result
      if (result.score === "correct") {
        correctAnswers++;
      } else if (result.score === "incorrect") {
        incorrectAnswers++;
      } else {
        pendingReview++;
        if (result.review) reviews.push(result.review);
      }

      // Category performance tracking
      const categories = question.metadata?.categories || [];
      for (const cat of categories) {
        const stats = categoryMap.get(cat) || { correct: 0, total: 0 };
        stats.total++;
        if (result.score === "correct") stats.correct++;
        categoryMap.set(cat, stats);
      }
    }

    const performanceByCategory: CategoryPerformance[] = Array.from(
      categoryMap.entries()
    ).map(([category, stats]) => ({
      category,
      correct: stats.correct,
      total: stats.total,
      successRate: stats.total ? (stats.correct / stats.total) * 100 : 0,
    }));

    const totalQuestions = questions.length || 1;
    const successRate = (correctAnswers / totalQuestions) * 100;
    const status: "partial" | "complete" = pendingReview > 0 ? "partial" : "complete";

    return {
      id: Math.random().toString(36).substring(2, 11),
      sessionId,
      testTitle: `Test Session ${sessionId}`,
      user: session.user,
      verificationId: Math.random().toString(36).substring(2, 11),
      score: correctAnswers,
      completedOn: new Date(),
      correctAnswers,
      incorrectAnswers,
      pendingReview,
      totalQuestions,
      successRate,
      percentile: 0,
      timeTaken: session.timeSpent || 0,
      badge: "",
      status,
      submittedAnswers: answers,
      questionReviews: reviews,
      performanceByCategory,
      ranking: undefined,
    };
  }

  /**
   * Map MongoDB document to TestSessionQuestion
   */
  private mapToTestSessionQuestion(doc: any): TestSessionQuestion {
    const { _id, ...rest } = doc;

    let options: any[] | undefined = undefined;
    if (rest.options && Array.isArray(rest.options)) {
      options = rest.options.map((opt: any) => {
        const { _id: optId, ...optRest } = opt;
        return {
          id: optRest.id,
          text: optRest.text,
          audio: optRest.audio,
          image: optRest.image,
        };
      });
    }

    return {
      id: rest.id,
      type: rest.type,
      question: rest.question,
      image: rest.image,
      audio: rest.audio,
      options,
      randomizeOptions: rest.randomizeOptions,
      metadata: rest.metadata,
      internal: rest.internal,
      data: rest.data,
      sessionId: rest.sessionId,
      originalQuestionId: rest.originalQuestionId,
      createdAt: rest.createdAt,
    };
  }
}