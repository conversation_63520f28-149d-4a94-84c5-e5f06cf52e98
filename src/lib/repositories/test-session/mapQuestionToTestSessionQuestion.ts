import { BaseOption, TestSessionQuestion } from "@/src/app/test-session/[id]/model";

export function mapQuestionToTestSessionQuestion(
  question: any, // Question
  sessionId: string
): TestSessionQuestion {
  const base: TestSessionQuestion = {
    id: Math.random().toString(36).substring(2, 11),
    type: question.type,
    question: question.question,
    metadata: {},
    sessionId,
    createdAt: new Date(),
    originalQuestionId: question.id,
  };

  switch (question.type) {
    case "multipleChoice":
    case "singleChoice":
    case "imageBased":
    case "audioBased":
      return {
        ...base,
        options: question.options?.map((opt) => ({
          id: opt.id,
          text: opt.text,
          audio: (opt as any).audio,
          image: (opt as any).image,
        })),
      };

    case "audioAnswer":
    case "voiceInput":
    case "fileUpload":
    case "codeInput":
    case "textInput":
      return {
        ...base,
        data: (question as any).data,
        internal: {
          reviewGuide: (question as any).reviewGuide,
          correctAnswer: (question as any).correctAnswer,
          correctAnswers: (question as any).correctAnswers,
          correctPhrases: (question as any).correctPhrases,
          testCases: (question as any).testCases,
        },
        metadata: {
          ...base.metadata,
          caseSensitive: (question as any).caseSensitive,
          allowedFileTypes: (question as any).allowedFileTypes,
          maxFileSize: (question as any).maxFileSize,
          requiredFunctions: (question as any).requiredFunctions,
          language: (question as any).language,
        },
      };

    default:
      return base;
  }
}