import {
  TestSession,
  TestSessionCreateInput,
  TestSessionUpdateInput,
  TestSessionQueryParams,
  TestSessionBusinessLogicInterface,
  SubmittedAnswer,
} from "./interface";
import { createError } from "@/src/lib/utils/common";
import { TestSessionDBRepository } from "./DBRepository";

export class TestSessionBusinessLogic implements TestSessionBusinessLogicInterface {
  public readonly db: TestSessionDBRepository;

  constructor(db: TestSessionDBRepository) {
    this.db = db;
  }

  private validateId(id: string) {
    if (!id || !id.trim()) throw createError("Test Session ID is required", "INVALID_ID");
  }

  private trimCreateInput(data: TestSessionCreateInput): TestSessionCreateInput {
    return {
      ...data,
      testId: data.testId.trim(),
      user: {
        ...data.user,
        id: data.user.id.trim(),
        name: data.user.name.trim(),
        email: data.user.email.trim(),
      },
      createdBy: data.createdBy.trim(),
    };
  }

  private trimUpdateInput(data: TestSessionUpdateInput): TestSessionUpdateInput {
    return {
      ...data,
      exitReason: data.exitReason?.trim(),
      updatedBy: data.updatedBy?.trim(),
    };
  }

  async getById(id: string, includeDeleted = false): Promise<TestSession | null> {
    this.validateId(id);
    return this.db.getById(id, includeDeleted);
  }

  async getAll(params?: TestSessionQueryParams): Promise<{ items: TestSession[]; total: number }> {
    return this.db.getAll(params);
  }

  async create(data: TestSessionCreateInput): Promise<TestSession> {
    const trimmedData = this.trimCreateInput(data);

    // Check if user already has an active session for this test
    const existingSessions = await this.db.getSessionsByUserId(trimmedData.user.id, {
      filters: [
        { field: "testId", value: trimmedData.testId },
        { field: "status", value: "OPEN" }
      ]
    });

    console.log(existingSessions);
    
    if (existingSessions.items.length > 0) {
      throw createError("User already has an active session for this test", "ACTIVE_SESSION_EXISTS");
    }

    console.log(trimmedData);
    
    return this.db.create(trimmedData);
  }

  async update(id: string, data: TestSessionUpdateInput): Promise<TestSession | null> {
    this.validateId(id);

    if (!data || Object.keys(data).length === 0) {
      throw createError("No data provided for update", "INVALID_UPDATE_DATA");
    }

    const existingSession = await this.db.getById(id);
    if (!existingSession) return null;

    const trimmedData = this.trimUpdateInput(data);
    return this.db.update(id, trimmedData);
  }

  async delete(id: string, hardDelete = false): Promise<boolean> {
    this.validateId(id);

    const existingSession = await this.db.getById(id);
    if (!existingSession) throw createError("Test Session not found", "NOT_FOUND");

    return this.db.delete(id, hardDelete);
  }

  async restore(id: string): Promise<boolean> {
    this.validateId(id);

    const session = await this.db.getById(id, true);
    if (!session || !session.deletedAt) return false;

    return this.db.restore(id);
  }

  async bulkCreate(data: TestSessionCreateInput[]): Promise<TestSession[]> {
    if (!data || data.length === 0) {
      throw createError("No data provided for bulk create", "INVALID_BULK_DATA");
    }

    const trimmedData = data.map(item => this.trimCreateInput(item));

    // Check for duplicate active sessions
    const userTestPairs = trimmedData.map(item => ({ userId: item.user.id, testId: item.testId }));
    for (const pair of userTestPairs) {
      const existingSessions = await this.db.getSessionsByUserId(pair.userId, {
        filters: [
          { field: "testId", value: pair.testId },
          { field: "status", value: "OPEN" }
        ]
      });
      
      if (existingSessions.items.length > 0) {
        throw createError(`User ${pair.userId} already has an active session for test ${pair.testId}`, "ACTIVE_SESSIONS_EXIST");
      }
    }

    return this.db.bulkCreate(trimmedData);
  }

  async bulkUpdate(updates: { id: string; data: TestSessionUpdateInput }[]): Promise<number> {
    if (!updates || updates.length === 0) {
      throw createError("No updates provided", "INVALID_BULK_UPDATE_DATA");
    }

    // Validate all IDs
    updates.forEach(update => this.validateId(update.id));

    const trimmedUpdates = updates.map(update => ({
      id: update.id,
      data: this.trimUpdateInput(update.data)
    }));

    return this.db.bulkUpdate(trimmedUpdates);
  }

  async bulkDelete(ids: string[], hardDelete = false): Promise<number> {
    if (!ids || ids.length === 0) {
      throw createError("No IDs provided for bulk delete", "INVALID_BULK_DELETE_DATA");
    }

    // Validate all IDs
    ids.forEach(id => this.validateId(id));

    return this.db.bulkDelete(ids, hardDelete);
  }

  async submitAnswers(sessionId: string, answers: SubmittedAnswer[]): Promise<TestSession | null> {
    this.validateId(sessionId);

    if (!answers || answers.length === 0) {
      throw createError("No answers provided", "INVALID_ANSWERS");
    }

    return this.db.submitAnswers(sessionId, answers);
  }

  async exitSession(sessionId: string, exitReason: string, timeSpent: number): Promise<TestSession | null> {
    this.validateId(sessionId);

    if (!exitReason || !exitReason.trim()) {
      throw createError("Exit reason is required", "INVALID_EXIT_REASON");
    }

    if (timeSpent < 0) {
      throw createError("Time spent cannot be negative", "INVALID_TIME_SPENT");
    }

    return this.db.exitSession(sessionId, exitReason.trim(), timeSpent);
  }

  async getSessionsByTestId(testId: string, params?: TestSessionQueryParams): Promise<{
    items: TestSession[];
    total: number;
  }> {
    if (!testId || !testId.trim()) {
      throw createError("Test ID is required", "INVALID_TEST_ID");
    }

    return this.db.getSessionsByTestId(testId.trim(), params);
  }

  async getSessionsByUserId(userId: string, params?: TestSessionQueryParams): Promise<{
    items: TestSession[];
    total: number;
  }> {
    if (!userId || !userId.trim()) {
      throw createError("User ID is required", "INVALID_USER_ID");
    }

    return this.db.getSessionsByUserId(userId.trim(), params);
  }
}