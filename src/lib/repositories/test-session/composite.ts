import { Result } from "@/types/result";
import {
  TestSession,
  TestSessionRepository,
  TestUser,
} from "./TestSessionRepository";
import { TestSessionQuestion } from "@/src/app/test-session/[id]/model";

export class CompositeRepository {
  constructor(
    private mainRepo: TestSessionRepository, // actual long term db
    private secondaryRepo: TestSessionRepository // cache db
  ) {}

  async getById(id: string): Promise<Result<TestSession, string>> {
    const secondaryResult = await this.secondaryRepo.getById(id);
    if (secondaryResult.ok) return secondaryResult;

    const mainResult = await this.mainRepo.getById(id);
    if (!mainResult.ok) return mainResult;

    const questions = await this.getQuestions(id);
    await this.secondaryRepo.saveSession(mainResult.value, questions);

    return mainResult;
  }

  async getQuestions(id: string): Promise<TestSessionQuestion[]> {
    const cachedQuestions = await this.secondaryRepo.getQuestions(id);
    if (cachedQuestions.length > 0) return cachedQuestions;

    const questions = await this.mainRepo.getQuestions(id);

    const mainItemResult = await this.mainRepo.getById(id);
    if (mainItemResult.ok) {
      await this.secondaryRepo.saveSession(mainItemResult.value, questions);
    }

    return questions;
  }

  async create(user: TestUser, testId: string): Promise<TestSession> {
    const created = await this.mainRepo.create(user, testId);
    const questions = await this.mainRepo.getQuestions(created.id);

    await this.secondaryRepo.saveSession(created, questions);

    return created;
  }

  async submitAnswers(sessionId: string, answers: any) {
    return this.mainRepo.submitAnswers(sessionId, answers);
  }

  async exitSession(sessionId: string, data: any) {
    return this.mainRepo.exitSession(sessionId, data);
  }
}
