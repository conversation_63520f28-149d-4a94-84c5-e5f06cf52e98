import { TestSession, TestSessionCreateInput, TestSessionUpdateInput, TestSessionQueryParams, SubmittedAnswer } from "./interface";

export interface TestSessionDBRepository {
  getById(id: string, includeDeleted?: boolean): Promise<TestSession | null>;
  getAll(params?: TestSessionQueryParams): Promise<{ items: TestSession[]; total: number }>;
  create(data: TestSessionCreateInput): Promise<TestSession>;
  update(id: string, data: TestSessionUpdateInput): Promise<TestSession | null>;
  delete(id: string, hardDelete?: boolean): Promise<boolean>;
  restore(id: string): Promise<boolean>;
  bulkCreate(data: TestSessionCreateInput[]): Promise<TestSession[]>;
  bulkUpdate(updates: { id: string; data: TestSessionUpdateInput }[]): Promise<number>;
  bulkDelete(ids: string[], hardDelete?: boolean): Promise<number>;
  
  // Test session specific methods
  submitAnswers(sessionId: string, answers: SubmittedAnswer[]): Promise<TestSession | null>;
  exitSession(sessionId: string, exitReason: string, timeSpent: number): Promise<TestSession | null>;
  getSessionsByTestId(testId: string, params?: TestSessionQueryParams): Promise<{
    items: TestSession[];
    total: number;
  }>;
  getSessionsByUserId(userId: string, params?: TestSessionQueryParams): Promise<{
    items: TestSession[];
    total: number;
  }>;
}