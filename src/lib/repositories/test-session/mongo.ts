import {
  BaseOption,
  TestSessionQuestion,
} from "@/src/app/test-session/[id]/model";
import {
  TestSession,
  TestUser,
  TestSessionRepository,
  SubmittedAnswer,
  ExitData,
} from "./TestSessionRepository";
import { Result } from "@/types/result";
import { mongoDbInstance } from "../../db/mongoClient";
import { MONGO_COLLECTIONS } from "../../db/mongoCollections";
import { Question } from "../questions/types";
import {
  CategoryPerformance,
  QuestionReview,
  TestResult,
} from "../test-result/types";
import { mapQuestionToTestSessionQuestion } from "./mapQuestionToTestSessionQuestion";
import { scoreAnswer } from "./scoreAnswer";

export function mapToTestSession(doc: any): TestSession {
  const {
    id,
    title,
    status,
    description,
    benefits,
    duration,
    categories,
    difficulty,
    totalQuestions,
    user,
  } = doc;

  const mappedUser: TestUser = {
    id: user?.id ?? "",
    name: user?.name ?? "",
    email: user?.email ?? "",
  };

  return {
    id,
    title,
    status,
    description,
    benefits,
    duration,
    categories,
    difficulty,
    totalQuestions,
    user: mappedUser,
  };
}

export function mapToTestSessionQuestion(doc: any): TestSessionQuestion {
  const { _id, ...rest } = doc;

  let options: BaseOption[] | undefined = undefined;
  if (rest.options && Array.isArray(rest.options)) {
    options = rest.options.map((opt: any) => {
      const { _id: optId, ...optRest } = opt;
      return {
        id: optRest.id,
        text: optRest.text,
        audio: optRest.audio,
        image: optRest.image,
      } as BaseOption;
    });
  }

  return {
    id: rest.id,
    type: rest.type,
    question: rest.question,
    image: rest.image,
    audio: rest.audio,
    options,
    randomizeOptions: rest.randomizeOptions,
    metadata: rest.metadata,
    internal: rest.internal,
    data: rest.data,
    sessionId: rest.sessionId,
    originalQuestionId: rest.originalQuetionId,
    createdAt: rest.createdAt,
  };
}

export function mapToQuestionReview(doc: any): QuestionReview {
  const { id, type, sessionQuestionId, metadata } = doc;

  return {
    id,
    type,
    sessionQuestionId,
    metadata,
  };
}
export class MongoTestSessionRepository implements TestSessionRepository {
  private async getCollectionSessions() {
    const client = await mongoDbInstance;
    const db = client.db(process.env.MONGODB_DB!);
    return db.collection<TestSession>(MONGO_COLLECTIONS.TEST_SESSIONS);
  }

  private async getCollectionTestSessionQuestions() {
    const client = await mongoDbInstance;
    const db = client.db(process.env.MONGODB_DB!);
    return db.collection<TestSessionQuestion>(
      MONGO_COLLECTIONS.TEST_SESSIONS_QUESTIONS
    );
  }

  async getById(id: string): Promise<Result<TestSession, string>> {
    const collection = await this.getCollectionSessions();
    const sessionDoc = await collection.findOne({ id });
    if (!sessionDoc) {
      return { ok: false, error: `TestSession with id ${id} not found` };
    }
    return { ok: true, value: mapToTestSession(sessionDoc) };
  }

  async create(user: TestUser, testId: string): Promise<TestSession> {
    const sessionId = Math.random().toString(36).substring(2, 11);

    const client = await mongoDbInstance;
    const db = client.db(process.env.MONGODB_DB!);

    const testsCollection = db.collection(MONGO_COLLECTIONS.PUBLIC_TEST);
    const questionsCollection = db.collection(MONGO_COLLECTIONS.QUESTIONS);

    // Step 1: Get the public test by ID
    const testDoc = await testsCollection.findOne({ id: testId });

    if (
      !testDoc ||
      !Array.isArray(testDoc.questions) ||
      testDoc.questions.length === 0
    ) {
      throw new Error("No questions found for the given test ID");
    }

    const questionIds = testDoc.questions;

    // Step 2: Fetch actual question documents by IDs
    const baseQuestions = await questionsCollection
      .find({ id: { $in: questionIds } })
      .toArray();

    // Step 3: For each question, handle templates
    const processedQuestions = baseQuestions.map((q) => {
      const templateKeys = Array.from(
        q.question.matchAll(/{{\s*([\w]+)\s*}}/g),
        (m: any) => m[1]
      );

      const newData = { ...q.data };

      for (const key of templateKeys) {
        if (Array.isArray(newData?.[key])) {
          const arr = newData[key];
          const selected = arr[Math.floor(Math.random() * arr.length)];
          newData[key] = selected;
        }
      }

      return {
        ...q,
        data: newData,
      };
    });

    // Step 4: Create session object
    const newSession: TestSession = {
      id: sessionId,
      title: testDoc.title ?? "New Test Session",
      status: "OPEN",
      description: testDoc.description ?? `Session for test ${testId}`,
      benefits: testDoc.benefits ?? [],
      duration: testDoc.duration ?? 30,
      categories: testDoc.categories ?? ["General"],
      difficulty: testDoc.difficulty ?? "Medium",
      totalQuestions: processedQuestions.length,
      user,
    };

    // Step 5: Map questions to session questions
    const sessionQuestions = processedQuestions.map((q) =>
      mapQuestionToTestSessionQuestion(q, sessionId)
    );

    // Step 6: Save session and questions to DB (or cache) via saveSession
    await this.saveSession(newSession, sessionQuestions);

    return newSession;
  }

  async saveSession(
    session: TestSession,
    questions: TestSessionQuestion[]
  ): Promise<void> {
    const sessionCollection = await this.getCollectionSessions();
    const sessionQuestionCollection =
      await this.getCollectionTestSessionQuestions();

    await sessionCollection.insertOne(session);
    await sessionQuestionCollection.insertMany(questions);
  }

  async getQuestions(id: string): Promise<TestSessionQuestion[]> {
    const collection = await this.getCollectionTestSessionQuestions();

    // Assuming TestSessionQuestion has a 'sessionId' field linking questions to sessions
    const questionDocs = await collection.find({ sessionId: id }).toArray();
    return questionDocs.map(mapToTestSessionQuestion);
  }

  async submitAnswers(
    sessionId: string,
    answers: SubmittedAnswer[]
  ): Promise<TestResult> {
    const sessionResult = await this.getById(sessionId);
    if (!sessionResult.ok) throw new Error(sessionResult.error);
    const session = sessionResult.value;

    const questions = await this.getQuestions(sessionId);

    let correctAnswers = 0;
    let incorrectAnswers = 0;
    let pendingReview = 0;
    const reviews: QuestionReview[] = [];

    const categoryMap: Map<string, { correct: number; total: number }> =
      new Map();

    for (const answer of answers) {
      const question = questions.find((q) => q.id === answer.questionId);
      if (!question) continue;

      const result = scoreAnswer(answer, question);

      // Tally score
      if (result.score === "correct") {
        correctAnswers++;
      } else if (result.score === "incorrect") {
        incorrectAnswers++;
      } else {
        pendingReview++;
        if (result.review) reviews.push(result.review);
      }

      // Tally category performance
      const categories = question.metadata?.categories || [];
      for (const cat of categories) {
        const stats = categoryMap.get(cat) || { correct: 0, total: 0 };
        stats.total++;
        if (result.score === "correct") stats.correct++;
        categoryMap.set(cat, stats);
      }
    }

    const performanceByCategory: CategoryPerformance[] = Array.from(
      categoryMap.entries()
    ).map(([category, stats]) => ({
      category,
      correct: stats.correct,
      total: stats.total,
      successRate: stats.total ? (stats.correct / stats.total) * 100 : 0,
    }));

    const totalQuestions = questions.length || 1;
    const successRate = (correctAnswers / totalQuestions) * 100;
    const status: "partial" | "complete" =
      pendingReview > 0 ? "partial" : "complete";

    return {
      id: Math.random().toString(36).substring(2, 11),
      sessionId,
      testTitle: session.title,
      user: session.user,
      verificationId: Math.random().toString(36).substring(2, 11),
      score: correctAnswers,
      completedOn: new Date(),
      correctAnswers,
      incorrectAnswers,
      pendingReview,
      totalQuestions,
      successRate,
      percentile: 0,
      timeTaken: 0,
      badge: "",
      status,
      submittedAnswers: answers,
      questionReviews: reviews,
      performanceByCategory,
      ranking: undefined,
    };
  }

  async exitSession(sessionId: string, data: ExitData): Promise<void> {
    // Optionally save exit data or update session status
    const collection = await this.getCollectionSessions();

    // For example, add exit data to a session 'exits' array or update status
    await collection.updateOne({ id: sessionId }, { $push: { exits: data } });
  }
}
