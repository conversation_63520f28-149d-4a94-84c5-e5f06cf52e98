export interface CourseViewData {
  name: string;
  views: number;
}

export interface CourseCompletionEntry {
  name: string;
  completed: number;
  started: number;
}

export interface CourseEngagementEntry {
  name: string;
  value: number;
}

export interface CourseSectionCompletionEntry {
  name: string;
  completion: number;
}

export interface CourseDashboard {
  viewsData: CourseViewData[];
  completionData: CourseCompletionEntry[];
  engagementData: CourseEngagementEntry[];
  colors: string[];
  sectionCompletionData: CourseSectionCompletionEntry[];
}

export interface CourseComment {
  id: string;
  user: {
    name: string;
    avatar: string;
  };
  content: string;
  timestamp: string;
  videoTitle: string;
  videoTimestamp: string;
  likes: number;
  dislikes: number;
  isLiked: boolean;
  isDisliked: boolean;
  isUnread:boolean,
  isFlagged:boolean,

  createdAt?: Date;
  updatedAt?: Date;
}

export interface Video {
  id: string;
  title: string;
  url: string;
  duration: string;
  description:string
}

export interface Section {
  id: string;
  title: string;
  description:string;
  videos: Video[];
}

export interface Course {
  id: string;
  title: string;
  summary: string;
  students: number;
  completionRate: number; // percentage from 0 to 100
  engagement: number; // percentage from 0 to 100
  description:string

  createdAt?: Date;
  updatedAt?: Date;
}

export interface CourseDetail extends Course {
  course_id:string
  sections: Section[];
}

export interface CourseAnalyticsTotal {
  totalStudents: number;
  completionRate: number;
  averageWatchTime: number;
  totalComments: number;
}

export interface CourseResponse {
  items: Course[];
  total_pages: number;
}

export interface CourseAnalyticsResponse {
  items: CourseDashboard;
  dashboard: CourseAnalyticsTotal;
  total_pages: number;
}


export interface PagingAndSearchParams {
  page?: number;
  pageSize?: number;
  search?: string;
  filter?: 'all' | 'unread' | 'flagged';
  [key: string]: any; // Other possible filters
}

export interface ITestRepository {
  getAll(filters?: PagingAndSearchParams): Promise<CourseResponse>;
  getById(id: string): Promise<Course | null>;
  getDetail(id: string): Promise<CourseDetail | null>;
  create(data: Partial<Course>): Promise<Course>;
  update(id: string, data: Partial<Course>): Promise<Course | null>;
  delete(id: string): Promise<boolean>;

  getAllCourseComments(filters?: PagingAndSearchParams): Promise<CourseComment[]>;
  updateCourseComments(id: string, data: Partial<CourseComment>): Promise<CourseComment | null>;
  deleteCourseComments(id: string): Promise<boolean>;
  
  getAllCourseAnalytics(filters?: PagingAndSearchParams): Promise<CourseAnalyticsResponse >;

  bulkDelete(ids: string[]): Promise<void>;
}
