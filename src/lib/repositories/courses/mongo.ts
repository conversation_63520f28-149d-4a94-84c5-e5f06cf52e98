import { ITestRepository, PagingAndSearchParams, Course, CourseDetail, CourseComment, CourseResponse, CourseAnalyticsResponse } from "./CoursesRepository";
import { mongoDbInstance } from "@/src/lib/db/mongoClient";
import { MONGO_COLLECTIONS } from "../../db/mongoCollections";


function mapToCourse(doc: any): Course {
  const { _id, ...rest } = doc;
  return {
    id: rest.id,
    title: rest.title,
    summary: rest.summary,
    students: rest.students,
    completionRate: rest.completionRate,
    engagement: rest.engagement,
    description: rest.description,
    createdAt: rest.createdAt,
    updatedAt: rest.updatedAt,
  };
}

function mapToCourseAnalytics(doc: any): CourseAnalyticsResponse {
  return {
    items: {
      viewsData: doc?.items?.viewsData || [],
      completionData: doc?.items?.completionData || [],
      engagementData: doc?.items?.engagementData || [],
      colors: doc?.items?.colors || [],
      sectionCompletionData: doc?.items?.sectionCompletionData || [],
    },
    dashboard: {
      totalStudents: doc?.dashboard?.totalStudents || 0,
      completionRate: doc?.dashboard?.completionRate || 0,
      averageWatchTime: doc?.dashboard?.averageWatchTime || 0,
      totalComments: doc?.dashboard?.totalComments || 0,
    },
    total_pages: 1,
  };
}

function mapToCourseDetail(doc: any): CourseDetail {
  const { _id, ...rest } = doc;
  return {
    id: rest.id,
    course_id: rest.course_id,
    title: rest.title,
    summary: rest.summary,
    students: rest.students,
    completionRate: rest.completionRate,
    engagement: rest.engagement,
    description: rest.description,
    sections: rest.sections || [],
    createdAt: rest.createdAt,
    updatedAt: rest.updatedAt,
  };
}

function mapToCourseComment(doc: any): CourseComment {
  const { _id, ...rest } = doc;
  return {
    id: rest.id,
    user: rest.user,
    content: rest.content,
    timestamp: rest.timestamp,
    videoTitle: rest.videoTitle,
    videoTimestamp: rest.videoTimestamp,
    likes: rest.likes,
    dislikes: rest.dislikes,
    isLiked: rest.isLiked,
    isDisliked: rest.isDisliked,
    isUnread: rest.isUnread,
    isFlagged: rest.isFlagged,
    createdAt: rest.createdAt,
    updatedAt: rest.updatedAt,
  };
}

export class MongoCoursesRepository implements ITestRepository {
  private async getCollection() {
    const client = await mongoDbInstance;
    const db = client.db(process.env.MONGODB_DB!);
    return db.collection(MONGO_COLLECTIONS.COURSES);
  }

  private async getCommentsCollection() {
    const client = await mongoDbInstance;
    const db = client.db(process.env.MONGODB_DB!);
    return db.collection("course-comments");
  }

  private async getAnalyticsCollection() {
    const client = await mongoDbInstance;
    const db = client.db(process.env.MONGODB_DB!);
    return db.collection("course-analytics");
  }

  private generateId() {
    return Math.random().toString(36).substring(2, 11);
  }

  private buildQuery(filters?: PagingAndSearchParams): any {
    const query: any = {};
    if (!filters) return query;

    if (filters.search) {
      query.title = { $regex: filters.search, $options: "i" };
    }

    return query;
  }

  async getAll(filters?: PagingAndSearchParams): Promise<CourseResponse> {
    const collection = await this.getCollection();
    const query = this.buildQuery(filters);

    const cursor = collection.find(query);

    if (filters?.page && filters?.pageSize) {
      cursor.skip((filters.page - 1) * filters.pageSize).limit(filters.pageSize);
    }

    const docs = await cursor.toArray();
    const total = await collection.countDocuments(query);
    const pageSize = filters?.pageSize || total;
    const total_pages = Math.ceil(total / pageSize);

    return {
      items: docs.map(mapToCourse),
      total_pages,
    };
  }

  async getById(id: string): Promise<Course | null> {
    const collection = await this.getCollection();
    const doc = await collection.findOne({ id });
    return doc ? mapToCourse(doc) : null;
  }

  async getDetail(id: string): Promise<CourseDetail | null> {
    const collection = await this.getCollection();
    const doc = await collection.findOne({ id });
    return doc ? mapToCourseDetail(doc) : null;
  }

  async create(data: Partial<Course>): Promise<Course> {
    const collection = await this.getCollection();

    const newCourse: Course = {
      id: this.generateId(),
      title: data.title || "Untitled Course",
      summary: data.summary || "",
      students: data.students || 0,
      completionRate: data.completionRate || 0,
      engagement: data.engagement || 0,
      description: data.description || "",
      createdAt: new Date(),
      updatedAt: new Date(),
      ...data,
    };

    await collection.insertOne(newCourse);
    return newCourse;
  }

  async update(id: string, data: Partial<Course>): Promise<Course | null> {
    const collection = await this.getCollection();

    const result = await collection.findOneAndUpdate(
      { id },
      {
        $set: {
          ...data,
          updatedAt: new Date(),
        },
      },
      { returnDocument: "after" }
    );

    return result ? mapToCourse(result) : null;
  }

  async delete(id: string): Promise<boolean> {
    const collection = await this.getCollection();
    const result = await collection.deleteOne({ id });
    return result.deletedCount === 1;
  }

  async bulkDelete(ids: string[]): Promise<void> {
    const collection = await this.getCollection();
    await collection.deleteMany({ id: { $in: ids } });
  }

  async getAllCourseComments(filters?: PagingAndSearchParams): Promise<CourseComment[]> {
    const collection = await this.getCommentsCollection();
    const query: any = {};

    if (filters?.filter === 'unread') {
      query.isUnread = true;
    } else if (filters?.filter === 'flagged') {
      query.isFlagged = true;
    }

    if (filters?.search) {
      query.content = { $regex: filters.search, $options: "i" };
    }

    const cursor = collection.find(query);

    if (filters?.page && filters?.pageSize) {
      cursor.skip((filters.page - 1) * filters.pageSize).limit(filters.pageSize);
    }

    const docs = await cursor.toArray();
    return docs.map(mapToCourseComment);
  }

  async updateCourseComments(id: string, data: Partial<CourseComment>): Promise<CourseComment | null> {
    const collection = await this.getCommentsCollection();

    const result = await collection.findOneAndUpdate(
      { id },
      {
        $set: {
          ...data,
          updatedAt: new Date(),
        },
      },
      { returnDocument: "after" }
    );

    return result ? mapToCourseComment(result) : null;
  }

  async deleteCourseComments(id: string): Promise<boolean> {
    const collection = await this.getCommentsCollection();
    const result = await collection.deleteOne({ id });
    return result.deletedCount === 1;
  }

  async getAllCourseAnalytics(filters?: PagingAndSearchParams): Promise<CourseAnalyticsResponse> {
    const collection = await this.getAnalyticsCollection();
    const doc = await collection.findOne({});

    // Return default analytics if no data found
    const defaultAnalytics: CourseAnalyticsResponse = {
      items: {
        viewsData: [],
        completionData: [],
        engagementData: [],
        colors: [],
        sectionCompletionData: [],
      },
      dashboard: {
        totalStudents: 0,
        completionRate: 0,
        averageWatchTime: 0,
        totalComments: 0,
      },
      total_pages: 1,
    };

    return doc ? mapToCourseAnalytics(doc) : defaultAnalytics;
  }
}
