[{"id": "comment-1", "user": {"name": "<PERSON>", "avatar": "/abstract-letter-aj.png"}, "content": "The section on useReducer was incredibly helpful. I finally understand how to use it properly!", "timestamp": "2 days ago", "videoTitle": "useReducer Deep Dive", "videoTimestamp": "12:45", "likes": 24, "dislikes": 2, "isLiked": true, "isDisliked": false, "isUnread": false, "isFlagged": false}, {"id": "comment-2", "user": {"name": "<PERSON>", "avatar": "/stylized-sm-logo.png"}, "content": "Could you explain the difference between useMemo and useCallback in more detail? I'm still confused about when to use each one.", "timestamp": "1 day ago", "videoTitle": "React.memo and useMemo", "videoTimestamp": "08:20", "likes": 8, "dislikes": 0, "isLiked": false, "isDisliked": false, "isUnread": true, "isFlagged": false}, {"id": "comment-3", "user": {"name": "<PERSON>", "avatar": "/dc-skyline-night.png"}, "content": "The code splitting strategies you shared have made my app so much faster. Thanks for the great tips!", "timestamp": "5 hours ago", "videoTitle": "Code Splitting Strategies", "videoTimestamp": "15:30", "likes": 16, "dislikes": 1, "isLiked": false, "isDisliked": false, "isUnread": true, "isFlagged": false}, {"id": "comment-4", "user": {"name": "<PERSON>", "avatar": "/emergency-room-scene.png"}, "content": "I found a small error in the custom hooks example. The dependency array is missing the userId variable.", "timestamp": "3 hours ago", "videoTitle": "Custom Hooks Patterns", "videoTimestamp": "19:15", "likes": 12, "dislikes": 0, "isLiked": true, "isDisliked": false, "isUnread": true, "isFlagged": false}, {"id": "comment-5", "user": {"name": "<PERSON>", "avatar": "/intertwined-letters.png"}, "content": "This course is exactly what I needed to level up my React skills. The explanations are clear and the examples are practical.", "timestamp": "1 day ago", "videoTitle": "Course Overview", "videoTimestamp": "02:10", "likes": 32, "dislikes": 0, "isLiked": false, "isDisliked": false, "isUnread": false, "isFlagged": false}]