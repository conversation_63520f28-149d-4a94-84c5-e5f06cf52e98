[{"id": "1", "title": "Introduction to Web Development", "description": "", "createdAt": "2023-10-15", "summary": "Learn the fundamentals of HTML, CSS, and JavaScript to build modern websites.", "students": 128, "completionRate": 67, "engagement": 82}, {"id": "2", "title": "Advanced React Patterns", "description": "", "createdAt": "2023-11-20", "summary": "Master advanced React concepts including hooks, context, and performance optimization.", "students": 94, "completionRate": 58, "engagement": 76}, {"id": "3", "title": "Full Stack Development with Next.js", "description": "", "createdAt": "2024-01-05", "summary": "Build complete web applications with Next.js, from frontend to backend.", "students": 156, "completionRate": 72, "engagement": 88}, {"id": "4", "title": "Responsive Design Masterclass", "description": "", "createdAt": "2024-02-18", "summary": "Create beautiful, responsive websites that work on any device.", "students": 112, "completionRate": 63, "engagement": 79}, {"id": "5", "title": "TypeScript for JavaScript Developers", "description": "", "createdAt": "2024-03-10", "summary": "Learn how to add static typing to your JavaScript projects with TypeScript.", "students": 87, "completionRate": 54, "engagement": 71}, {"id": "6", "title": "Backend API Development with Node.js", "description": "", "createdAt": "2024-04-01", "summary": "Design and build RESTful APIs using Node.js, Express, and MongoDB.", "students": 103, "completionRate": 60, "engagement": 74}, {"id": "7", "title": "DevOps Essentials", "description": "", "createdAt": "2024-04-20", "summary": "Understand CI/CD, containerization, and monitoring to improve deployment workflows.", "students": 76, "completionRate": 49, "engagement": 66}, {"id": "8", "title": "Introduction to Cloud Computing", "description": "", "createdAt": "2024-05-08", "summary": "Get started with cloud platforms like AWS, Azure, and Google Cloud.", "students": 134, "completionRate": 65, "engagement": 81}, {"id": "9", "title": "Data Structures and Algorithms", "description": "", "createdAt": "2024-06-01", "summary": "Strengthen your problem-solving skills with key computer science fundamentals.", "students": 148, "completionRate": 70, "engagement": 85}, {"id": "10", "title": "AI for Web Developers", "description": "", "createdAt": "2024-06-15", "summary": "Explore how AI tools and APIs can be integrated into modern web applications.", "students": 97, "completionRate": 61, "engagement": 78}]