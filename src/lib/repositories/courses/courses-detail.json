[{"id": "1", "course_id": "1", "title": "Introduction to Web Development", "createdAt": "2023-10-15", "summary": "Learn the fundamentals of HTML, CSS, and JavaScript to build modern websites.", "students": 128, "completionRate": 67, "engagement": 82, "sections": [{"id": "section-1", "title": "Introduction", "videos": [{"id": "video-1", "title": "Course Overview", "url": "https://www.youtube.com/watch?v=example1", "duration": "5:20"}, {"id": "video-2", "title": "Setting Up Your Environment", "url": "https://www.youtube.com/watch?v=example2", "duration": "12:45"}]}, {"id": "section-2", "title": "Advanced Hooks", "videos": [{"id": "video-3", "title": "useReducer Deep Dive", "url": "https://www.youtube.com/watch?v=example3", "duration": "18:30"}, {"id": "video-4", "title": "Custom Hooks Patterns", "url": "https://www.youtube.com/watch?v=example4", "duration": "22:15"}]}, {"id": "section-3", "title": "Performance Optimization", "videos": [{"id": "video-5", "title": "React.memo and useMemo", "url": "https://www.youtube.com/watch?v=example5", "duration": "15:10"}, {"id": "video-6", "title": "Code Splitting Strategies", "url": "https://www.youtube.com/watch?v=example6", "duration": "20:05"}]}]}]