export interface NotificationType {
  id: number;
  type:
    | "test_completed"
    | "badge_earned"
    | "course_recommendation"
    | "system"
    | "achievement"
    | "test_reminder"
    | "course_update"
    | "social";
  title: string;
  description: string;
  date: Date;
  read: boolean;
  icon: string; // This will be the icon name as string, e.g., "CheckCircle2"
  iconBg: string; // e.g., "bg-blue-100"
  link: string;

  createdAt?: Date;
  updatedAt?: Date;
}

export interface NotificationResponse {
  items: NotificationType[],
  total: {
    all:number,
    unread:number,
    tests:number,
    courses:number,
    badges:number
  }
}
export interface PagingAndSearchParams {
  page?: number;
  pageSize?: number;
  search?: string;
  [key: string]: any;
  read?: boolean;
  type?: string;
}

export interface ITestRepository {
  getAll(filters?: PagingAndSearchParams): Promise<NotificationResponse>;
  getById(id: number): Promise<NotificationType | null>;
  getDetail(id: number): Promise<NotificationType | null>;
  create(data: Partial<NotificationType>): Promise<NotificationType>;
  update(
    id: number,
    data: Partial<NotificationType>
  ): Promise<NotificationType | null>;
  delete(id: number): Promise<boolean>;

  bulkDelete(ids: number[]): Promise<void>;
  markAllAsRead(): Promise<NotificationType[]>;
  deleteAllAsRead(): Promise<NotificationType[]>;
}
