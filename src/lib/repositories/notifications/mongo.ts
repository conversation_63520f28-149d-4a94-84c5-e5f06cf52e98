import { ITestRepository, PagingAndSearchParams, NotificationType, NotificationResponse } from "./NotificationsRepository";
import { mongoDbInstance } from "@/src/lib/db/mongoClient";
import { MONGO_COLLECTIONS } from "../../db/mongoCollections";

function mapToNotification(doc: any): NotificationType {
  const { _id, ...rest } = doc;
  return {
    id: rest.id,
    type: rest.type,
    title: rest.title,
    description: rest.description,
    date: rest.date,
    read: rest.read,
    icon: rest.icon,
    iconBg: rest.iconBg,
    link: rest.link,
    createdAt: rest.createdAt,
    updatedAt: rest.updatedAt,
  };
}

export class MongoNotificationRepository implements ITestRepository {
  private async getCollection() {
    const client = await mongoDbInstance;
    const db = client.db(process.env.MONGODB_DB!);
    return db.collection(MONGO_COLLECTIONS.NOTIFICATIONS);
  }

  private generateId() {
    return Math.floor(Math.random() * 1000000);
  }

  private buildQuery(filters?: PagingAndSearchParams): any {
    const query: any = {};
    if (!filters) return query;

    if (filters.search) {
      query.$or = [
        { title: { $regex: filters.search, $options: "i" } },
        { description: { $regex: filters.search, $options: "i" } }
      ];
    }

    if (filters.read !== undefined) {
      query.read = filters.read;
    }

    if (filters.type) {
      query.type = filters.type;
    }

    return query;
  }

  async getAll(filters?: PagingAndSearchParams): Promise<NotificationResponse> {
    const collection = await this.getCollection();
    const query = this.buildQuery(filters);

    const cursor = collection.find(query);

    if (filters?.page && filters?.pageSize) {
      cursor.skip((filters.page - 1) * filters.pageSize).limit(filters.pageSize);
    }

    const docs = await cursor.toArray();
    
    // Calculate totals
    const allCount = await collection.countDocuments({});
    const unreadCount = await collection.countDocuments({ read: false });
    const testsCount = await collection.countDocuments({ type: { $in: ["test_completed", "test_reminder"] } });
    const coursesCount = await collection.countDocuments({ type: { $in: ["course_recommendation", "course_update"] } });
    const badgesCount = await collection.countDocuments({ type: { $in: ["badge_earned", "achievement"] } });

    return {
      items: docs.map(mapToNotification),
      total: {
        all: allCount,
        unread: unreadCount,
        tests: testsCount,
        courses: coursesCount,
        badges: badgesCount,
      },
    };
  }

  async getById(id: number): Promise<NotificationType | null> {
    const collection = await this.getCollection();
    const doc = await collection.findOne({ id });
    return doc ? mapToNotification(doc) : null;
  }

  async getDetail(id: number): Promise<NotificationType | null> {
    return this.getById(id);
  }

  async create(data: Partial<NotificationType>): Promise<NotificationType> {
    const collection = await this.getCollection();

    const newNotification: NotificationType = {
      id: this.generateId(),
      type: data.type || "system",
      title: data.title || "Untitled Notification",
      description: data.description || "",
      date: data.date || new Date(),
      read: data.read || false,
      icon: data.icon || "Bell",
      iconBg: data.iconBg || "bg-gray-100",
      link: data.link || "#",
      createdAt: new Date(),
      updatedAt: new Date(),
      ...data,
    };

    await collection.insertOne(newNotification);
    return newNotification;
  }

  async update(id: number, data: Partial<NotificationType>): Promise<NotificationType | null> {
    const collection = await this.getCollection();

    const result = await collection.findOneAndUpdate(
      { id },
      {
        $set: {
          ...data,
          updatedAt: new Date(),
        },
      },
      { returnDocument: "after" }
    );

    return result ? mapToNotification(result) : null;
  }

  async delete(id: number): Promise<boolean> {
    const collection = await this.getCollection();
    const result = await collection.deleteOne({ id });
    return result.deletedCount === 1;
  }

  async bulkDelete(ids: number[]): Promise<void> {
    const collection = await this.getCollection();
    await collection.deleteMany({ id: { $in: ids } });
  }

  async markAllAsRead(): Promise<NotificationType[]> {
    const collection = await this.getCollection();
    
    await collection.updateMany(
      { read: false },
      {
        $set: {
          read: true,
          updatedAt: new Date(),
        },
      }
    );

    const docs = await collection.find({}).toArray();
    return docs.map(mapToNotification);
  }

  async deleteAllAsRead(): Promise<NotificationType[]> {
    const collection = await this.getCollection();
    
    // Get all read notifications before deleting
    const readDocs = await collection.find({ read: true }).toArray();
    
    // Delete all read notifications
    await collection.deleteMany({ read: true });

    return readDocs.map(mapToNotification);
  }
}
