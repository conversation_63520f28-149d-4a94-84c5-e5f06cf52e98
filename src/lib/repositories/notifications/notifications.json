[{"id": 1, "type": "test_completed", "title": "Test completed", "description": "You scored 85% on Web Development Fundamentals", "date": "2 hours ago", "read": false, "icon": "CheckCircle2", "iconBg": "bg-blue-100", "link": "/dashboard/my-tests/1"}, {"id": 2, "type": "badge_earned", "title": "Badge earned", "description": "You earned the \"Fast Learner\" badge", "date": "1 day ago", "read": false, "icon": "Award", "iconBg": "bg-yellow-100", "link": "/dashboard/my-tests?tab=achievements"}, {"id": 3, "type": "course_recommendation", "title": "New course recommendation", "description": "Check out \"Advanced JavaScript Concepts\"", "date": "2 days ago", "read": false, "icon": "BookOpen", "iconBg": "bg-green-100", "link": "/dashboard/my-courses"}, {"id": 4, "type": "system", "title": "System maintenance", "description": "The platform will be undergoing maintenance on June 1st from 2-4 AM UTC", "date": "3 days ago", "read": true, "icon": "Settings", "iconBg": "bg-gray-100", "link": "#"}, {"id": 5, "type": "achievement", "title": "Achievement unlocked", "description": "You've completed 5 tests this month!", "date": "5 days ago", "read": true, "icon": "Star", "iconBg": "bg-purple-100", "link": "/dashboard/my-tests?tab=achievements"}, {"id": 6, "type": "test_reminder", "title": "Test reminder", "description": "You have an incomplete test: CSS Grid & Flexbox Mastery", "date": "1 week ago", "read": true, "icon": "Clock", "iconBg": "bg-orange-100", "link": "/dashboard/my-tests"}, {"id": 7, "type": "course_update", "title": "Course updated", "description": "\"React for Begin<PERSON>\" has been updated with new content", "date": "1 week ago", "read": true, "icon": "Zap", "iconBg": "bg-indigo-100", "link": "/dashboard/my-courses"}, {"id": 8, "type": "social", "title": "New follower", "description": "<PERSON> is now following your learning journey", "date": "2 weeks ago", "read": true, "icon": "Users", "iconBg": "bg-pink-100", "link": "#"}]