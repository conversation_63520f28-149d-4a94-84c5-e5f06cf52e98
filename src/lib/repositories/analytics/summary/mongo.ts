import { AnalyticsSummaryRepository, PagingAndSearchParams, StatsItem } from "./AnalyticsSummaryRepository";
import { mongoDbInstance } from "@/src/lib/db/mongoClient";
import { MONGO_COLLECTIONS } from "../../../db/mongoCollections";

function mapToStatsItem(doc: any): StatsItem {
  const { _id, ...rest } = doc;
  return {
    id: rest.id,
    uniq_id: rest.uniq_id,
    value: rest.value,
    change: rest.change,
    date: rest.date,
  };
}

export class MongoAnalyticsSummaryRepository implements AnalyticsSummaryRepository {
  private async getCollection() {
    const client = await mongoDbInstance;
    const db = client.db(process.env.MONGODB_DB!);
    return db.collection(MONGO_COLLECTIONS.ANALYTICS_SUMMARY);
  }

  private buildQuery(filters?: PagingAndSearchParams): any {
    const query: any = {};
    if (!filters) return query;

    if (filters.uniq_id) {
      query.uniq_id = filters.uniq_id;
    }

    if (filters.date) {
      query.date = filters.date;
    }

    return query;
  }

  async getAll(filters?: PagingAndSearchParams): Promise<StatsItem[]> {
    const collection = await this.getCollection();
    const query = this.buildQuery(filters);

    const cursor = collection.find(query);

    if (filters?.page && filters?.pageSize) {
      cursor.skip((filters.page - 1) * filters.pageSize).limit(filters.pageSize);
    }

    const docs = await cursor.toArray();
    
    // If no data found, return default stats
    if (docs.length === 0) {
      return [
        {
          id: 1,
          uniq_id: "total_test_takers",
          value: 0,
          change: "+0%",
          date: new Date(),
        },
        {
          id: 2,
          uniq_id: "tests_completed",
          value: 0,
          change: "+0%",
          date: new Date(),
        },
        {
          id: 3,
          uniq_id: "course_enrollments",
          value: 0,
          change: "+0%",
          date: new Date(),
        },
        {
          id: 4,
          uniq_id: "landing_page_visitors",
          value: 0,
          change: "+0%",
          date: new Date(),
        },
      ];
    }

    return docs.map(mapToStatsItem);
  }
}
