export interface StatsItem {
  id: number;
  uniq_id: "total_test_takers" | "tests_completed" | "course_enrollments" | "landing_page_visitors";
  value: number;
  change: string;
  date: Date;
}

export interface PagingAndSearchParams {
  page?: number;
  pageSize?: number;
  search?: string;
  [key: string]: any; // Other possible filters
}

export const STAT_KEYS = {
  TOTAL_TEST_TAKERS: "total_test_takers",
  TESTS_COMPLETED: "tests_completed",
  COURSE_ENROLLMENTS: "course_enrollments",
  LANDING_PAGE_VISITORS: "landing_page_visitors",
} as const;

export interface AnalyticsSummaryRepository {
  getAll(filters?: PagingAndSearchParams): Promise<StatsItem[]>;
}
