import { mongoDbInstance } from "@/src/lib/db/mongoClient";
import { MONGO_COLLECTIONS } from "@/src/lib/db/mongoCollections";
import {
  DetailedRepository,
  SearchParams,
  UpgradeOverlayType,
  Feature,
  PlanDetails,
} from "./UpgradeOverlayRepository";

function mapToUpgradeOverlayType(doc: any): UpgradeOverlayType {
  const { _id, ...rest } = doc;
  return {
    features: rest.features || [],
    plan: rest.plan || {
      name: "",
      description: "",
      price: "",
      priceUnit: "",
    },
  };
}

export class MongoUpgradeOverlayRepository implements DetailedRepository {
  private async getCollection() {
    const client = await mongoDbInstance;
    const db = client.db(process.env.MONGODB_DB!);
    return db.collection(MONGO_COLLECTIONS.UPGRADE_OVERLAY);
  }

  async getAll(filters?: SearchParams): Promise<UpgradeOverlayType> {
    const collection = await this.getCollection();
    
    // For upgrade overlay, we typically return a single configuration
    // If no data exists in MongoDB, return the default from JSON
    const doc = await collection.findOne({});
    
    if (!doc) {
      // Return default data if no MongoDB document exists
      return {
        features: [
          { id: "f1", text: "Unlimited test creation and sharing" },
          { id: "f2", text: "Advanced analytics dashboard" },
          { id: "f3", text: "Export data to Excel and PDF" },
          { id: "f4", text: "Priority email support" }
        ],
        plan: {
          name: "Pro Plan",
          description: "Everything you need to scale your testing",
          price: "$19.99",
          priceUnit: "per month"
        }
      };
    }

    return mapToUpgradeOverlayType(doc);
  }

  async create(data: UpgradeOverlayType): Promise<UpgradeOverlayType> {
    const collection = await this.getCollection();
    
    const newOverlay = {
      features: data.features || [],
      plan: data.plan || {
        name: "",
        description: "",
        price: "",
        priceUnit: "",
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    await collection.insertOne(newOverlay);
    return mapToUpgradeOverlayType(newOverlay);
  }

  async update(data: Partial<UpgradeOverlayType>): Promise<UpgradeOverlayType | null> {
    const collection = await this.getCollection();

    const result = await collection.findOneAndUpdate(
      {}, // Update the first/only document
      {
        $set: {
          ...data,
          updatedAt: new Date(),
        },
      },
      { returnDocument: "after", upsert: true }
    );

    return result ? mapToUpgradeOverlayType(result) : null;
  }
}
