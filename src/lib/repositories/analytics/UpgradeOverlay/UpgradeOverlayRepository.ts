export interface Feature {
  id: string;
  text: string;
}

export interface PlanDetails {
  name: string;
  description: string;
  price: string;
  priceUnit?: string;
}

export interface UpgradeOverlayType {
  features: Feature[];
  plan: PlanDetails;
}

export interface SearchParams {
  search?: string;
  [key: string]: any; // Other possible filters
}

export interface DetailedRepository {
  getAll(filters?: SearchParams): Promise<UpgradeOverlayType>;
}
