export interface MonthData {
  name: string;
  count: number;
}

export interface DayCompletionData {
  name: string;
  completed: number;
  started: number;
}

export interface CourseEnrollment {
  name: string;
  students: number;
}

export interface LandingPageStats {
  name: string;
  visitors: number;
  conversions: number;
}

export interface ScoreDistribution {
  name: string;
  value: number;
}

export interface DetailedType {
  testTakerData: MonthData[];
  testCompletionData: DayCompletionData[];
  courseEnrollmentData: CourseEnrollment[];
  landingPageData: LandingPageStats[];
  testScoreDistribution: ScoreDistribution[];
}


export interface SearchParams {
  search?: string;
  [key: string]: any; // Other possible filters
}

export interface DetailedRepository {
  getAll(filters?: SearchParams): Promise<DetailedType>;
}
