import { DetailedRepository, SearchParams, DetailedType } from "./DetailedRepository";
import { mongoDbInstance } from "@/src/lib/db/mongoClient";
import { MONGO_COLLECTIONS } from "../../../db/mongoCollections";

function mapToDetailedType(doc: any): DetailedType {
  return {
    testTakerData: doc.testTakerData || [],
    testCompletionData: doc.testCompletionData || [],
    courseEnrollmentData: doc.courseEnrollmentData || [],
    landingPageData: doc.landingPageData || [],
    testScoreDistribution: doc.testScoreDistribution || [],
  };
}

export class MongoAnalyticsDetailedRepository implements DetailedRepository {
  private async getCollection() {
    const client = await mongoDbInstance;
    const db = client.db(process.env.MONGODB_DB!);
    return db.collection(MONGO_COLLECTIONS.ANALYTICS_DETAILED);
  }

  async getAll(filters?: SearchParams): Promise<DetailedType> {
    const collection = await this.getCollection();
    
    // For detailed analytics, we typically store aggregated data in a single document
    // or we could aggregate from multiple collections
    const doc = await collection.findOne({});

    // If no data found, return default empty analytics
    if (!doc) {
      return {
        testTakerData: [
          { name: "Jan", count: 0 },
          { name: "Feb", count: 0 },
          { name: "Mar", count: 0 },
          { name: "Apr", count: 0 },
          { name: "May", count: 0 },
          { name: "Jun", count: 0 },
        ],
        testCompletionData: [
          { name: "Mon", completed: 0, started: 0 },
          { name: "Tue", completed: 0, started: 0 },
          { name: "Wed", completed: 0, started: 0 },
          { name: "Thu", completed: 0, started: 0 },
          { name: "Fri", completed: 0, started: 0 },
          { name: "Sat", completed: 0, started: 0 },
          { name: "Sun", completed: 0, started: 0 },
        ],
        courseEnrollmentData: [],
        landingPageData: [],
        testScoreDistribution: [
          { name: "0-20", value: 0 },
          { name: "21-40", value: 0 },
          { name: "41-60", value: 0 },
          { name: "61-80", value: 0 },
          { name: "81-100", value: 0 },
        ],
      };
    }

    return mapToDetailedType(doc);
  }
}
