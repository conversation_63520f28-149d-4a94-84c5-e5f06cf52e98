import {
  AuthDBRepository,
  EmailVerificationDBRepository,
  PasswordResetDBRepository,
  TokenDBRepository,
  UserDBRepository
} from "./DBRepository";

import { User } from "@/src/lib/types/base";
import { generateId } from "@/src/lib/utils/common";
import { MongoDriver } from "../MongoDriver";
import { MONGO_COLLECTIONS } from "../../db/mongoCollections";

// --- MAPPERS ---
function mapToUser(doc: any): User {
  const { password, _id, ...rest } = doc;
  return {
    id: rest.id,
    email: rest.email,
    name: rest.name,
    createdAt: rest.createdAt,
    updatedAt: rest.updatedAt,
  };
}

function mapToToken(doc: any) {
  return {
    id: doc.id,
    userId: doc.userId,
    token: doc.token,
    refresh_token: doc.refresh_token,
    createdAt: doc.createdAt,
    expiresAt: doc.expiresAt,
  };
}

function mapToReset(doc: any) {
  return {
    id: doc.id,
    userId: doc.userId,
    token: doc.token,
    expiresAt: doc.expiresAt,
    createdAt: doc.createdAt,
  };
}

function mapToVerification(doc: any) {
  return {
    id: doc.id,
    userId: doc.userId,
    token: doc.token,
    expiresAt: doc.expiresAt,
    createdAt: doc.createdAt,
  };
}

export class MongoAuthDBRepository
  implements AuthDBRepository {

  private users;
  private tokens;
  private resets;
  private verifications;

  constructor(driver: MongoDriver) {
    this.users = driver.getCollection(MONGO_COLLECTIONS.USERS);
    this.tokens = driver.getCollection(MONGO_COLLECTIONS.USER_TOKENS);
    this.resets = driver.getCollection(MONGO_COLLECTIONS.PASSWORD_RESETS);
    this.verifications = driver.getCollection(MONGO_COLLECTIONS.EMAIL_VERIFICATIONS);
  }

  // --- USER ---

  async findUserByEmail(email: string): Promise<User | null> {
    const doc = await this.users.findOne({ email });
    return doc ? mapToUser(doc) : null;
  }

  async findUserById(id: string): Promise<User | null> {
    const doc = await this.users.findOne({ id });
    return doc ? mapToUser(doc) : null;
  }

  async findUserWithPasswordByEmail(email: string): Promise<(User & { password: string }) | null> {
    const doc = await this.users.findOne({ email });
    if (!doc) return null;
    const user = mapToUser(doc);
    return { ...user, password: doc.password };
  }

  async createUser(userData: Omit<User, "id" | "createdAt" | "updatedAt"> & { password: string }): Promise<User> {
    const now = new Date();
    const newUser = {
      ...userData,
      id: generateId(),
      createdAt: now,
      updatedAt: now,
    };
    await this.users.insertOne(newUser);
    return mapToUser(newUser);
  }

  async updateUser(id: string, userData: Partial<User & { password?: string }>): Promise<User | null> {
    const result = await this.users.findOneAndUpdate(
      { id },
      { $set: { ...userData, updatedAt: new Date() } },
      { returnDocument: "after" }
    );
    if (!result) return null;
    return result.value ? mapToUser(result.value) : null;
  }

  async deleteUser(id: string): Promise<boolean> {
    const result = await this.users.deleteOne({ id });
    return result.deletedCount > 0;
  }

  async userExistsByEmail(email: string): Promise<boolean> {
    return (await this.users.countDocuments({ email })) > 0;
  }

  async userExistsById(id: string): Promise<boolean> {
    return (await this.users.countDocuments({ id })) > 0;
  }

  // --- TOKEN ---

  async createToken(tokenData: {
    id: string;
    userId: string;
    token: string;
    refresh_token: string;
    createdAt: Date;
    expiresAt: Date;
  }): Promise<void> {
    await this.tokens.insertOne(tokenData);
  }

  async findTokenByValue(token: string): Promise<ReturnType<typeof mapToToken> | null> {
    const doc = await this.tokens.findOne({ token });
    return doc ? mapToToken(doc) : null;
  }

  async findTokenByRefreshToken(refreshToken: string): Promise<ReturnType<typeof mapToToken> | null> {
    const doc = await this.tokens.findOne({ refresh_token: refreshToken });
    return doc ? mapToToken(doc) : null;
  }

  async updateToken(refreshToken: string, newTokenData: {
    token: string;
    refresh_token: string;
    updatedAt: Date;
    expiresAt: Date;
  }): Promise<void> {
    await this.tokens.updateOne({ refresh_token: refreshToken }, { $set: newTokenData });
  }

  async deleteToken(token: string): Promise<void> {
    await this.tokens.deleteOne({ token });
  }

  async deleteTokenByRefreshToken(refreshToken: string): Promise<void> {
    await this.tokens.deleteOne({ refresh_token: refreshToken });
  }

  async deleteTokensByUserId(userId: string): Promise<void> {
    await this.tokens.deleteMany({ userId });
  }

  // --- PASSWORD RESET ---

  async createPasswordResetRequest(data: {
    id: string;
    userId: string;
    token: string;
    expiresAt: Date;
    createdAt: Date;
  }): Promise<void> {
    await this.resets.insertOne(data);
  }

  async findPasswordResetByToken(token: string): Promise<ReturnType<typeof mapToReset> | null> {
    const doc = await this.resets.findOne({ token });
    return doc ? mapToReset(doc) : null;
  }

  async deletePasswordResetByToken(token: string): Promise<void> {
    await this.resets.deleteOne({ token });
  }

  async deletePasswordResetsByUserId(userId: string): Promise<void> {
    await this.resets.deleteMany({ userId });
  }

  // --- EMAIL VERIFICATION ---

  async createEmailVerificationRequest(data: {
    id: string;
    userId: string;
    token: string;
    expiresAt: Date;
    createdAt: Date;
  }): Promise<void> {
    await this.verifications.insertOne(data);
  }

  async findEmailVerificationByToken(token: string): Promise<ReturnType<typeof mapToVerification> | null> {
    const doc = await this.verifications.findOne({ token });
    return doc ? mapToVerification(doc) : null;
  }

  async deleteEmailVerificationByToken(token: string): Promise<void> {
    await this.verifications.deleteOne({ token });
  }

  async deleteEmailVerificationsByUserId(userId: string): Promise<void> {
    await this.verifications.deleteMany({ userId });
  }
}
