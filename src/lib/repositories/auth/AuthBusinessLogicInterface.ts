import { User, UserToken, Login, UserRegister, RefreshTokenInput, AuthResponse } from "@/src/lib/types/base";

export interface AuthBusinessLogicInterface {
  // User authentication
  login(credentials: Login): Promise<UserToken>;
  register(userData: UserRegister): Promise<UserToken>;
  refreshToken(tokenData: RefreshTokenInput): Promise<UserToken>;
  logout(token: string): Promise<void>;

  // User management
  getUserByEmail(email: string): Promise<User | null>;
  getUserById(id: string): Promise<User | null>;
  createUser(userData: Omit<User, "id" | "createdAt" | "updatedAt">): Promise<User>;
  updateUser(id: string, userData: Partial<User>): Promise<User | null>;
  deleteUser(id: string): Promise<boolean>;

  // Token management
  validateToken(token: string): Promise<{ user?: User; error?: 'expired' | 'token-not-found' | string }>;
  invalidateToken(token: string): Promise<void>;

  // Password management
  changePassword(userId: string, currentPassword: string, newPassword: string): Promise<AuthResponse>;
  requestPasswordReset(email: string): Promise<AuthResponse>;
  resetPassword(token: string, newPassword: string): Promise<AuthResponse>;

  // Email verification
  verifyEmail(token: string): Promise<AuthResponse>;
  resendEmailVerification(email: string): Promise<AuthResponse>;

  // Account management
  deleteAccount(userId: string, password: string): Promise<AuthResponse>;

  // User checks
  checkUserCanBeDeleted(id: string): Promise<{ canDelete: boolean; reason?: string }>;
}
