import { AuthBusinessLogicInterface } from "./AuthBusinessLogicInterface";
import { AuthDBRepository } from "./DBRepository";
import { User, UserToken, Login, UserRegister, RefreshTokenInput, AuthResponse } from "@/src/lib/types/base";
import { generateId, createError } from "@/src/lib/utils/common";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import { AuthDBInMemoryRepository } from "./InMemoryRepository";
import { EmailSender } from "@/src/lib/microservices/EmailSender";
import { EventSender } from "@/src/lib/microservices/EventSender";

const JWT_SECRET = process.env.JWT_SECRET || "a";
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET! || "a";
const JWT_EXPIRES_IN = "1h";
const JWT_REFRESH_EXPIRES_IN = "7d";

export class AuthBusinessLogic implements AuthBusinessLogicInterface {
  constructor(
    private dbRepo: AuthDBRepository,
    private inMemoryRepo: AuthDBInMemoryRepository,
    private emailSender: EmailSender,
    private eventService: EventSender
  ) { }

  // Password utilities
  private async hashPassword(password: string): Promise<string> {
    const saltRounds = 12;
    return bcrypt.hash(password, saltRounds);
  }

  private async comparePassword(password: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(password, hashedPassword);
  }

  // Token utilities
  private generateTokens(user: User): { token: string; refresh_token: string } {
    const now = Math.floor(Date.now() / 1000);
    const payload = {
      id: user.id,
      email: user.email,
      iat: now,
      jti: generateId() // Add unique identifier for each token
    };

    const token = jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
    const refresh_token = jwt.sign({ ...payload, jti: generateId() }, JWT_REFRESH_SECRET, { expiresIn: JWT_REFRESH_EXPIRES_IN });

    return { token, refresh_token };
  }

  // Business logic methods
  async login(credentials: Login): Promise<UserToken> {
    const userWithPassword = await this.dbRepo.findUserWithPasswordByEmail(credentials.email);
    if (!userWithPassword) {
      throw createError("Invalid email or password", "INVALID_CREDENTIALS");
    }

    const isValidPassword = await this.comparePassword(credentials.password, userWithPassword.password);
    if (!isValidPassword) {
      throw createError("Invalid email or password", "INVALID_CREDENTIALS");
    }

    // Remove password from user object for return
    const { password, ...user } = userWithPassword;
    const tokens = this.generateTokens(user);

    // Store token in database
    await this.dbRepo.createToken({
      id: generateId(),
      userId: user.id,
      token: tokens.token,
      refresh_token: tokens.refresh_token,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    });
    await this.inMemoryRepo.saveUser(user.id, user);

    await this.eventService.sendUserEvent({
      type: "account_login",
      payload: { user_id: user.id }
    });

    return {
      token: tokens.token,
      refresh_token: tokens.refresh_token,
      user,
    };
  }

  async register(userData: UserRegister): Promise<UserToken> {
    // Check if user already exists
    const existingUser = await this.dbRepo.userExistsByEmail(userData.email);
    if (existingUser) {
      throw createError("User with this email already exists", "USER_EXISTS");
    }

    // Hash password
    const hashedPassword = await this.hashPassword(userData.password);

    // Create user with email verification fields
    const user = await this.dbRepo.createUser({
      email: userData.email,
      password: hashedPassword,
      name: userData.name || "",
      emailVerified: false,
      isActive: true,
    });

    // Generate email verification token
    const verificationToken = generateId() + Date.now().toString();
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    // Store verification request
    await this.dbRepo.createEmailVerificationRequest({
      id: generateId(),
      userId: user.id,
      token: verificationToken,
      expiresAt,
      createdAt: new Date(),
    });

    this.emailSender.sendEmailVerification(user.email, verificationToken);

    const tokens = this.generateTokens(user);

    // Store token in database
    await this.dbRepo.createToken({
      id: generateId(),
      userId: user.id,
      token: tokens.token,
      refresh_token: tokens.refresh_token,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    });

    await this.eventService.sendUserEvent({
      type: "user_registered",
      payload: { user_id: user.id, email: user.email }
    });

    return {
      token: tokens.token,
      refresh_token: tokens.refresh_token,
      user,
    };
  }

  async refreshToken(tokenData: RefreshTokenInput): Promise<UserToken> {
    try {
      const decoded = jwt.verify(tokenData.refresh_token, JWT_REFRESH_SECRET) as any;
      const user = await this.dbRepo.findUserById(decoded.id);

      if (!user) {
        throw createError("Invalid refresh token", "INVALID_TOKEN");
      }

      // Verify refresh token exists in database
      const tokenRecord = await this.dbRepo.findTokenByRefreshToken(tokenData.refresh_token);
      if (!tokenRecord) {
        throw createError("Invalid refresh token", "INVALID_TOKEN");
      }

      // Generate new tokens
      const tokens = this.generateTokens(user);

      // Update token in database
      await this.dbRepo.updateToken(tokenData.refresh_token, {
        token: tokens.token,
        refresh_token: tokens.refresh_token,
        updatedAt: new Date(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      });

      await this.eventService.sendUserEvent({
        type: "token_refreshed",
        payload: { user_id: user.id, old_token_id: decoded.jti || "unknown" }
      });

      return {
        token: tokens.token,
        refresh_token: tokens.refresh_token,
        user,
      };
    } catch (error) {
      throw createError("Invalid refresh token", "INVALID_TOKEN");
    }
  }

  async logout(token: string): Promise<void> {
    await this.dbRepo.deleteToken(token);
  }

  async getUserByEmail(email: string): Promise<User | null> {
    return this.dbRepo.findUserByEmail(email);
  }

  async getUserById(id: string): Promise<User | null> {
    return this.dbRepo.findUserById(id);
  }

  async createUser(userData: Omit<User, "id" | "createdAt" | "updatedAt">): Promise<User> {
    // For createUser, we assume password is provided in userData
    const userWithPassword = userData as any;
    if (!userWithPassword.password) {
      throw createError("Password is required", "VALIDATION_ERROR");
    }

    const hashedPassword = await this.hashPassword(userWithPassword.password);
    return this.dbRepo.createUser({
      ...userData,
      password: hashedPassword,
    });
  }

  async updateUser(id: string, userData: Partial<User>): Promise<User | null> {
    // If password is being updated, hash it
    if (userData.password) {
      userData.password = await this.hashPassword(userData.password);
    }

    return this.dbRepo.updateUser(id, userData);
  }

  async deleteUser(id: string): Promise<boolean> {
    // Delete all user tokens first
    await this.dbRepo.deleteTokensByUserId(id);

    // Then delete the user
    return this.dbRepo.deleteUser(id);
  }

  async validateToken(token: string): Promise<{ user?: User; error?: 'expired' | 'token-not-found' | string }> {
    try {
      const decoded = jwt.verify(token, JWT_SECRET) as any;

      const tokenRecord = await this.dbRepo.findTokenByValue(token);
      if (!tokenRecord) {
        return { error: 'token-not-found' };
      }

      const user = await this.dbRepo.findUserById(decoded.id);
      if (!user) {
        return { error: "User not found" };
      }
      return { user };
    } catch (error: any) {
      if (error.name === "TokenExpiredError") {
        return { error: 'expired' };
      }
      return { error: "Invalid token" };
    }
  }

  async invalidateToken(token: string): Promise<void> {
    await this.dbRepo.deleteToken(token);
  }

  async checkUserCanBeDeleted(id: string): Promise<{ canDelete: boolean; reason?: string }> {
    // Check if user exists
    const userExists = await this.dbRepo.userExistsById(id);
    if (!userExists) {
      return { canDelete: false, reason: "User not found" };
    }

    // Add business logic checks here
    // For example: check if user has active sessions, pending transactions, etc.
    // For now, allow deletion
    return { canDelete: true };
  }

  // Password management
  async changePassword(userId: string, currentPassword: string, newPassword: string): Promise<AuthResponse> {
    try {
      // Get user with password
      const user = await this.dbRepo.findUserById(userId);
      if (!user) {
        return { success: false, message: "User not found" };
      }

      const userWithPassword = await this.dbRepo.findUserWithPasswordByEmail(user.email);
      if (!userWithPassword) {
        return { success: false, message: "User not found" };
      }

      // Verify current password
      const isValidPassword = await this.comparePassword(currentPassword, userWithPassword.password);
      if (!isValidPassword) {
        return { success: false, message: "Current password is incorrect" };
      }

      // Hash new password and update
      const hashedNewPassword = await this.hashPassword(newPassword);
      await this.dbRepo.updateUser(userId, { password: hashedNewPassword });

      // Send password changed event
      await this.eventService.sendUserEvent({
        type: "password_changed",
        payload: { user_id: userId }
      });

      return { success: true, message: "Password changed successfully" };
    } catch (error) {
      return { success: false, message: "Failed to change password" };
    }
  }

  async requestPasswordReset(email: string): Promise<AuthResponse> {
    try {
      const user = await this.dbRepo.findUserByEmail(email);
      if (!user) {
        // Don't reveal if email exists or not for security
        return { success: true, message: "If the email exists, a reset link has been sent" };
      }

      // Generate reset token
      const resetToken = generateId() + Date.now().toString();
      const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

      // Store reset request
      await this.dbRepo.createPasswordResetRequest({
        id: generateId(),
        userId: user.id!,
        token: resetToken,
        expiresAt,
        createdAt: new Date(),
      });


      this.emailSender.sendPasswordReset(email, resetToken);

      await this.eventService.sendUserEvent({
        type: "password_reset_requested",
        payload: { user_id: user.id!, reset_token: resetToken }
      });


      return {
        success: true,
        message: "If the email exists, a reset link has been sent",
        data: process.env.NODE_ENV === "development" ? { resetToken } : {}
      };
    } catch (error) {
      return { success: false, message: "Failed to process password reset request" };
    }
  }

  async resetPassword(token: string, newPassword: string): Promise<AuthResponse> {
    try {
      const resetRequest = await this.dbRepo.findPasswordResetByToken(token);
      if (!resetRequest) {
        return { success: false, message: "Invalid or expired reset token" };
      }

      // Check if token is expired
      if (new Date() > resetRequest.expiresAt) {
        await this.dbRepo.deletePasswordResetByToken(token);
        return { success: false, message: "Reset token has expired" };
      }

      // Hash new password and update user
      const hashedPassword = await this.hashPassword(newPassword);
      await this.dbRepo.updateUser(resetRequest.userId, { password: hashedPassword });

      // Clean up reset token
      await this.dbRepo.deletePasswordResetByToken(token);

      await this.eventService.sendUserEvent({
        type: "password_reset_completed",
        payload: { user_id: resetRequest.userId }
      });

      return { success: true, message: "Password reset successfully" };
    } catch (error) {
      return { success: false, message: "Failed to reset password" };
    }
  }

  // Email verification
  async verifyEmail(token: string): Promise<AuthResponse> {
    try {
      const verification = await this.dbRepo.findEmailVerificationByToken(token);
      if (!verification) {
        return { success: false, message: "Invalid or expired verification token" };
      }

      // Check if token is expired
      if (new Date() > verification.expiresAt) {
        await this.dbRepo.deleteEmailVerificationByToken(token);
        return { success: false, message: "Verification token has expired" };
      }

      // Update user as verified
      await this.dbRepo.updateUser(verification.userId, { emailVerified: true });

      // Clean up verification token
      await this.dbRepo.deleteEmailVerificationByToken(token);

      await this.eventService.sendUserEvent({
        type: "email_verified",
        payload: { user_id: verification.userId }
      });

      return { success: true, message: "Email verified successfully" };
    } catch (error) {
      return { success: false, message: "Failed to verify email" };
    }
  }

  async resendEmailVerification(email: string): Promise<AuthResponse> {
    try {
      const user = await this.dbRepo.findUserByEmail(email);
      if (!user) {
        return { success: false, message: "User not found" };
      }

      if (user.emailVerified) {
        return { success: false, message: "Email is already verified" };
      }

      // Clean up existing verification tokens
      await this.dbRepo.deleteEmailVerificationsByUserId(user.id!);

      // Generate new verification token
      const verificationToken = generateId() + Date.now().toString();
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      // Store verification request
      await this.dbRepo.createEmailVerificationRequest({
        id: generateId(),
        userId: user.id!,
        token: verificationToken,
        expiresAt,
        createdAt: new Date(),
      });

      this.emailSender.sendEmailVerification(user.email, verificationToken);

      // In a real app, send email here
      return {
        success: true,
        message: "Verification email sent",
        data: process.env.NODE_ENV === "development" ? { verificationToken } : {}
      };
    } catch (error) {
      return { success: false, message: "Failed to send verification email" };
    }
  }

  // Account management
  async deleteAccount(userId: string, password: string): Promise<AuthResponse> {
    try {
      const user = await this.dbRepo.findUserById(userId);
      if (!user) {
        return { success: false, message: "User not found" };
      }

      const userWithPassword = await this.dbRepo.findUserWithPasswordByEmail(user.email);
      if (!userWithPassword) {
        return { success: false, message: "User not found" };
      }

      // Verify password
      const isValidPassword = await this.comparePassword(password, userWithPassword.password);
      if (!isValidPassword) {
        return { success: false, message: "Invalid password" };
      }

      // Check if user can be deleted
      const canDelete = await this.checkUserCanBeDeleted(userId);
      if (!canDelete.canDelete) {
        return { success: false, message: canDelete.reason || "Cannot delete user" };
      }

      // Clean up all user data
      await this.dbRepo.deleteTokensByUserId(userId);
      await this.dbRepo.deletePasswordResetsByUserId(userId);
      await this.dbRepo.deleteEmailVerificationsByUserId(userId);

      // Delete user
      const deleted = await this.dbRepo.deleteUser(userId);
      if (!deleted) {
        return { success: false, message: "Failed to delete user" };
      }

      await this.eventService.sendUserEvent({
        type: "account_deleted",
        payload: { user_id: userId }
      });

      return { success: true, message: "Account deleted successfully" };
    } catch (error) {
      return { success: false, message: "Failed to delete account" };
    }
  }
}
