import { User } from "@/src/lib/types/base";
import { InMemoryDriver } from "../RedisDriver";
import { AuthDBInMemoryRepository } from "./InMemoryRepository";

export class AuthDBRedisRepository implements AuthDBInMemoryRepository {
    constructor(private redis: InMemoryDriver) {}

    async saveUser(id: string, user: User): Promise<void> {
        await this.redis.set(`user:${id}`, user);
    }

    async getUserById(id: string): Promise<User | null> {
        return await this.redis.get<User>(`user:${id}`);
    }
}
