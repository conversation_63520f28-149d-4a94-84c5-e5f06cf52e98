export type RoleType = "admin" | "team" | "external";
export type StatusCollaboratorType = "ACTIVE" | "INACTIVE";
export type StatusInviteType = "pending" | "accepted" | "declined";

export interface Invitation {
  id: string;
  email: string;
  role: RoleType;
  status: StatusInviteType; // added possible other statuses if needed
  expiresAt: Date;
  sentAt: Date;
  code: string;
  invitedBy?: string; // User ID or email of who sent the invitation

  createdAt?: Date;
  updatedAt?: Date;
}

export interface CollaboratorUser {
  id: string;
  name: string;
  email: string;
  avatar: string;
  role: RoleType;
  status: StatusCollaboratorType;
  lastActive: string;
  courses: number;

  createdAt?: Date;
  updatedAt?: Date;
}

export interface PagingAndSearchParams {
  page?: number;
  pageSize?: number;
  search?: string;
  [key: string]: any; // Other possible filters
}

export interface ITestRepository {
  getAll(filters?: PagingAndSearchParams): Promise<CollaboratorUser[]>;
  getAllInvitation(filters?: PagingAndSearchParams): Promise<Invitation[]>;
  getById(id: string): Promise<CollaboratorUser | null>;
  create(data: Partial<Invitation>): Promise<Invitation>;
  createCollaborator(data: Partial<CollaboratorUser>): Promise<CollaboratorUser>;
  update(
    id: string,
    data: Partial<CollaboratorUser>
  ): Promise<CollaboratorUser | null>;
  bulkDelete(ids: string[]): Promise<void>;

  updateInvitation(
    id: string,
    data: Partial<Invitation>
  ): Promise<Invitation | null>;
  delete(id: string): Promise<boolean>;
  deleteInvitation(id: string): Promise<boolean>;
  getByIdInvitation(id: string): Promise<Invitation | null>;
  getInvitationByCode(code: string): Promise<Invitation | null>;
  getIncomingInvitations(userEmail: string, params?: PagingAndSearchParams): Promise<Invitation[]>;
  getOutgoingInvitations(invitedBy: string, params?: PagingAndSearchParams): Promise<Invitation[]>;
}
