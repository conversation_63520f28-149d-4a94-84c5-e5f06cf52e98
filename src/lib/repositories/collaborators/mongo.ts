import { ITestRepository, PagingAndSearch<PERSON>ara<PERSON>, CollaboratorUser, Invitation } from "./CollaboratorsRepository";
import { mongoDbInstance } from "@/src/lib/db/mongoClient";
import { MONGO_COLLECTIONS } from "../../db/mongoCollections";


function mapToCollaboratorUser(doc: any): CollaboratorUser {
  const { _id, ...rest } = doc;
  return {
    id: rest.id,
    name: rest.name,
    email: rest.email,
    avatar: rest.avatar,
    role: rest.role,
    status: rest.status,
    lastActive: rest.lastActive,
    courses: rest.courses,
    createdAt: rest.createdAt,
    updatedAt: rest.updatedAt,
  };
}

function mapToInvitation(doc: any): Invitation {
  const { _id, ...rest } = doc;
  return {
    id: rest.id,
    email: rest.email,
    role: rest.role,
    status: rest.status,
    expiresAt: rest.expiresAt,
    sentAt: rest.sentAt,
    code: rest.code,
    createdAt: rest.createdAt,
    updatedAt: rest.updatedAt,
  };
}

export class MongoCollaboratorsRepository implements ITestRepository {
  private async getCollection() {
    const client = await mongoDbInstance;
    const db = client.db(process.env.MONGODB_DB!);
    return db.collection(MONGO_COLLECTIONS.COLLABORATORS);
  }

  private async getInvitationsCollection() {
    const client = await mongoDbInstance;
    const db = client.db(process.env.MONGODB_DB!);
    return db.collection(MONGO_COLLECTIONS.COLLABORATOR_INVITATIONS);
  }

  private generateId() {
    return Math.random().toString(36).substring(2, 11);
  }

  private generateInviteCode() {
    return Math.random().toString(36).substring(2, 11);
  }

  private buildQuery(filters?: PagingAndSearchParams): any {
    const query: any = {};
    if (!filters) return query;

    if (filters.search) {
      query.$or = [
        { name: { $regex: filters.search, $options: "i" } },
        { email: { $regex: filters.search, $options: "i" } }
      ];
    }

    if (filters.role) {
      query.role = filters.role;
    }

    if (filters.status) {
      query.status = filters.status;
    }

    return query;
  }

  async getAll(filters?: PagingAndSearchParams): Promise<CollaboratorUser[]> {
    const collection = await this.getCollection();
    const query = this.buildQuery(filters);

    const cursor = collection.find(query);

    if (filters?.page && filters?.pageSize) {
      cursor.skip((filters.page - 1) * filters.pageSize).limit(filters.pageSize);
    }

    const docs = await cursor.toArray();
    return docs.map(mapToCollaboratorUser);
  }

  async getAllInvitation(filters?: PagingAndSearchParams): Promise<Invitation[]> {
    const collection = await this.getInvitationsCollection();
    const query = this.buildQuery(filters);

    const cursor = collection.find(query);

    if (filters?.page && filters?.pageSize) {
      cursor.skip((filters.page - 1) * filters.pageSize).limit(filters.pageSize);
    }

    const docs = await cursor.toArray();
    return docs.map(mapToInvitation);
  }

  async getById(id: string): Promise<CollaboratorUser | null> {
    const collection = await this.getCollection();
    const doc = await collection.findOne({ id });
    return doc ? mapToCollaboratorUser(doc) : null;
  }

  async getByIdInvitation(id: string): Promise<Invitation | null> {
    const collection = await this.getInvitationsCollection();
    const doc = await collection.findOne({ id });
    return doc ? mapToInvitation(doc) : null;
  }

  async create(data: Partial<Invitation>): Promise<Invitation> {
    const collection = await this.getInvitationsCollection();

    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // Expires in 7 days

    const newInvitation: Invitation = {
      ...data,
      id: this.generateId(),
      email: data.email || "",
      role: data.role || "team",
      status: data.status || "pending",
      expiresAt: expiresAt,
      sentAt: new Date(),
      code: this.generateInviteCode(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    await collection.insertOne(newInvitation);
    return newInvitation;
  }

  async update(id: string, data: Partial<CollaboratorUser>): Promise<CollaboratorUser | null> {
    const collection = await this.getCollection();

    const result = await collection.findOneAndUpdate(
      { id },
      {
        $set: {
          ...data,
          updatedAt: new Date(),
        },
      },
      { returnDocument: "after" }
    );

    return result ? mapToCollaboratorUser(result) : null;
  }

  async updateInvitation(id: string, data: Partial<Invitation>): Promise<Invitation | null> {
    const collection = await this.getInvitationsCollection();

    const result = await collection.findOneAndUpdate(
      { id },
      {
        $set: {
          ...data,
          updatedAt: new Date(),
        },
      },
      { returnDocument: "after" }
    );

    return result ? mapToInvitation(result) : null;
  }

  async delete(id: string): Promise<boolean> {
    const collection = await this.getCollection();
    const result = await collection.deleteOne({ id });
    return result.deletedCount === 1;
  }

  async deleteInvitation(id: string): Promise<boolean> {
    const collection = await this.getInvitationsCollection();
    const result = await collection.deleteOne({ id });
    return result.deletedCount === 1;
  }

  async bulkDelete(ids: string[]): Promise<void> {
    const collection = await this.getCollection();
    await collection.deleteMany({ id: { $in: ids } });
  }
}
