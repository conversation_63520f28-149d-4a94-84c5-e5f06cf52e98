import { Result } from "@/types/result";

export interface CreatorProfile {
  id: string;
  name: string;
  photo: string;
  bio: string;
  skills: string[];
  availability: string[];
  social: SocialLink[];
}

export interface SocialLink {
  type: string; // e.g. "GitHub", "LinkedIn"
  url: string;
}

export interface CreatorQuery {
  name?: string;
  skill?: string;
}

export interface CreatorRepository {
  getAll(query?: CreatorQuery): Promise<CreatorProfile[]>;
  getById(id: string): Promise<Result<CreatorProfile, string>>;
}
