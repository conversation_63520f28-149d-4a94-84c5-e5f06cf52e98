import { Result } from "@/types/result";
import {
  Creator<PERSON>rofile,
  CreatorQuery,
  CreatorRepository,
} from "./interface";
import { mongoDbInstance } from "@/src/lib/db/mongoClient";
import { MONGO_COLLECTIONS } from "../../db/mongoCollections";

function mapToCreatorProfile(doc: any): CreatorProfile {
  return {
    id: doc.id,
    name: doc.name,
    photo: doc.photo,
    bio: doc.bio,
    skills: doc.skills || [],
    availability: doc.availability || [],
    social: doc.social || [],
  };
}

export class MongoCreatorRepository implements CreatorRepository {
  private async getCollection() {
    const client = await mongoDbInstance;
    const db = client.db(); // db name from connection URI
    return db.collection(MONGO_COLLECTIONS.CREATORS);
  }

  async getAll(query?: CreatorQuery): Promise<CreatorProfile[]> {
    const collection = await this.getCollection();

    const mongoQuery: any = {};

    if (query?.name) {
      const regex = new RegExp(query.name, "i");
      mongoQuery.name = { $regex: regex };
    }

    if (query?.skill) {
      const regex = new RegExp(query.skill, "i");
      mongoQuery.skills = { $elemMatch: { $regex: regex } };
    }

    const docs = await collection.find(mongoQuery).toArray();
    return docs.map(mapToCreatorProfile);
  }

  async getById(id: string): Promise<Result<CreatorProfile, string>> {
    const collection = await this.getCollection();
    const doc = await collection.findOne({ id });

    if (!doc) {
      return { ok: false, error: `Creator with id ${id} not found` };
    }

    return { ok: true, value: mapToCreatorProfile(doc) };
  }
}
