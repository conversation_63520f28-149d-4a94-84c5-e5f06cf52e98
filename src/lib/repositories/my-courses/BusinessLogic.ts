import {
  MyCourse,
  MyCourseCreateInput,
  MyCourseUpdateInput,
  MyCourseQueryParams,
  MyCourseBusinessLogicInterface,
} from "./interface";
import { createError } from "@/src/lib/utils/common";
import { MyCourseDBRepository } from "./DBRepository";

export class MyCourseBusinessLogic implements MyCourseBusinessLogicInterface {
  constructor(private readonly db: MyCourseDBRepository) {}

  private validateId(id: string) {
    if (!id || !id.trim()) throw createError("MyCourse ID is required", "INVALID_ID");
  }

  private trimCreateInput(data: MyCourseCreateInput): MyCourseCreateInput {
    return {
      ...data,
      name: data.name.trim(),
      phone: data.phone?.trim() ?? "",
      tags: data.tags?.map(t => t.trim()) ?? [],
    };
  }

  private trimUpdateInput(data: MyCourseUpdateInput): MyCourseUpdateInput {
    return {
      ...data,
      name: data.name?.trim(),
      phone: data.phone?.trim(),
      tags: data.tags?.map(t => t.trim())
    };
  }

  async getById(id: string, includeDeleted = false): Promise<MyCourse | null> {
    this.validateId(id);
    return this.db.getById(id, includeDeleted);
  }

  async getAll(params?: MyCourseQueryParams): Promise<{ items: MyCourse[]; total: number }> {
    // Optionally validate filters/sorts here if needed
    return this.db.getAll(params);
  }

  async create(data: MyCourseCreateInput): Promise<MyCourse> {
    const trimmedData = this.trimCreateInput(data);

    // Check for duplicate name
    const existing = await this.db.getCount({ filters: [{ field: "name", value: trimmedData.name }] });
    if (existing.total > 0) {
      throw createError("MyCourse with the same name already exists", "DUPLICATE_NAME");
    }

    return this.db.create(trimmedData);
  }

  async update(id: string, data: MyCourseUpdateInput): Promise<MyCourse | null> {
    this.validateId(id);

    if (!data || Object.keys(data).length === 0) {
      throw createError("No data provided for update", "INVALID_UPDATE_DATA");
    }

    const existingRule = await this.db.getById(id);
    if (!existingRule) throw createError("MyCourse not found", "NOT_FOUND");

    if (data.name && data.name.trim() !== existingRule.name) {
      const duplicates = await this.db.getAll({ filters: [{ field: "name", value: data.name.trim() }] });
      if (duplicates.items.some(r => r.id !== id)) {
        throw createError("Another MyCourse with this name exists", "DUPLICATE_NAME");
      }
    }

    const trimmedData = this.trimUpdateInput(data);
    return this.db.update(id, trimmedData);
  }

  async delete(id: string, hardDelete = false): Promise<boolean> {
    this.validateId(id);

    const existingRule = await this.db.getById(id);
    if (!existingRule) throw createError("MyCourse not found", "NOT_FOUND");

    return this.db.delete(id, hardDelete);
  }

  async restore(id: string): Promise<boolean> {
    this.validateId(id);

    const myCourse = await this.db.getById(id, true);
    if (!myCourse || !myCourse.deletedAt) return false;

    // Check for conflicts by name
    const conflict = await this.db.getAll({ filters: [{ field: "name", value: myCourse.name }] });
    if (conflict.items.length > 0) return false;

    return this.db.restore(id);
  }

  async bulkCreate(data: MyCourseCreateInput[]): Promise<MyCourse[]> {
    if (!Array.isArray(data) || data.length === 0) {
      throw createError("Input must be a non-empty array", "INVALID_BULK_CREATE_DATA");
    }

    for (const entry of data) {
      this.trimCreateInput(entry); // Will throw if invalid
      const existing = await this.db.getAll({ filters: [{ field: "name", value: entry.name.trim() }] });
      if (existing.items.length > 0) {
        throw createError(`Duplicate name found: ${entry.name}`, "DUPLICATE_NAME");
      }
    }

    const trimmedData = data.map(d => this.trimCreateInput(d));
    return this.db.bulkCreate(trimmedData);
  }

  async bulkUpdate(updates: { id: string; data: MyCourseUpdateInput }[]): Promise<number> {
    if (!Array.isArray(updates) || updates.length === 0) {
      throw createError("Input must be a non-empty array", "INVALID_BULK_UPDATE_DATA");
    }

    for (const { id, data } of updates) {
      this.validateId(id);

      if (!data || Object.keys(data).length === 0) {
        throw createError(`No data provided for update of ID ${id}`, "INVALID_UPDATE_DATA");
      }

      const existingRule = await this.db.getById(id);
      if (!existingRule) throw createError(`Rule with ID ${id} not found`, "NOT_FOUND");

      if (data.name && data.name.trim() !== existingRule.name) {
        const duplicates = await this.db.getAll({ filters: [{ field: "name", value: data.name.trim() }] });
        if (duplicates.items.some(r => r.id !== id)) {
          throw createError(`Duplicate name in update: ${data.name}`, "DUPLICATE_NAME");
        }
      }

      this.trimUpdateInput(data); // Will throw if invalid
    }

    const trimmedUpdates = updates.map(({ id, data }) => ({
      id,
      data: this.trimUpdateInput(data),
    }));

    return this.db.bulkUpdate(trimmedUpdates);
  }

  async bulkDelete(ids: string[], hardDelete = false): Promise<number> {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw createError("IDs must be a non-empty array", "INVALID_BULK_DELETE_DATA");
    }

    for (const id of ids) {
      this.validateId(id);
      const myCourse = await this.db.getById(id);
      if (!myCourse) throw createError(`Rule with ID ${id} not found`, "NOT_FOUND");
    }

    return this.db.bulkDelete(ids, hardDelete);
  }

  async search(query: string): Promise<MyCourse[]> {
    if (!query || !query.trim()) {
      return [];
    }

    const searchTerm = query.trim();
    const result = await this.db.getAll({
      filters: [
        { field: "name", value: { $regex: searchTerm, $options: "i" } }
      ]
    });

    return result.items;
  }
}
