export interface MyCoursesFeedback {
  courseId: string;
  rating: number;
  feedback: string;

  createdAt?: Date;
  updatedAt?: Date;
}
export interface MyCoursesMCQTest {
  id: string;
  course_id: string;
  title: string;
  description: string;
  questions: number;
  timeLimit: string;
  completed: boolean;
  score: number | null;
  section: string;
}

export interface MyCourseNote {
  id: string;
  video_id: string;
  section_id: string;
  title: string;
  content: string;
  videoTimestamp: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface VideoComment {
  id: string;
  video_id: string;
  user: {
    name: string;
    avatar: string;
  };
  content: string;
  timestamp: string;
  videoTimestamp: string;
  likes: number;
  dislikes: number;
  isLiked: boolean;
  isDisliked: boolean;

  createdAt?: Date;
  updatedAt?: Date;
}

interface Video {
  id: string;
  title: string;
  duration: string;
  completed: boolean;
  url: string;
}

interface Section {
  id: string;
  title: string;
  description: string;
  videos: Video[];
}

export interface MyCourse {
  id: string;
  title: string;
  instructor: string;
  instructorAvatar: string;
  completionPercentage: number;
  lastAccessed?: Date;
  summary: string;
  coverImage: string;
  totalLessons: number;
  completedLessons: number;
  estimatedHours: number;
  category: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface MyCourseDetail extends MyCourse {
  my_courses_id: string;
  rating: number;
  totalReviews: number;
  sections: Section[];
}

export interface MyCourseResponse {
  items: MyCourse[];
  total_pages: number;
}

export interface MyCoursesTotalLearningStats {
  totalCourses: number;
  totalHours: number;
  certificatesEarned: number;
}

export interface PagingAndSearchParams {
  page?: number;
  pageSize?: number;
  search?: string;
  [key: string]: any; // Other possible filters
}

export interface FilterVideoComments {
  id?: string;
  filter?: "all" | "my" | "timestamps";
  currentUserName?: string;
  [key: string]: any; // Other possible filters
}

export interface FilterNotes {
  id?: string;
  filter?: "all" | "this-video" | "this-section";
  [key: string]: any; // Other possible filters
}

export interface ITestRepository {
  getAll(filters?: PagingAndSearchParams): Promise<MyCourseResponse>;
  getTotalLearningStats(): Promise<MyCoursesTotalLearningStats>;
  getById(id: string): Promise<MyCourse | null>;
  getDetail(id: string): Promise<MyCourseDetail | null>;
  create(data: Partial<MyCourse>): Promise<MyCourse>;
  update(id: string, data: Partial<MyCourse>): Promise<MyCourse | null>;
  delete(id: string): Promise<boolean>;
  bulkDelete(ids: string[]): Promise<void>;

  getComment(filters?: FilterVideoComments): Promise<VideoComment[] | null>;
  createComment(data: Partial<VideoComment>): Promise<VideoComment>;

  getCourseNotes(filters?: FilterNotes): Promise<MyCourseNote[] | null>;
  createCourseNotes(data: Partial<MyCourseNote>): Promise<MyCourseNote>;
  updateCourseNotes(
    id: string,
    data: Partial<MyCourseNote>
  ): Promise<MyCourseNote | null>;
  deleteCourseNotes(id: string): Promise<boolean>;

  getCourseMCQTest(
    filters?: PagingAndSearchParams
  ): Promise<MyCoursesMCQTest[] | null>;

  createCoursesFeedback(data: Partial<MyCoursesFeedback>): Promise<MyCoursesFeedback>;
}
