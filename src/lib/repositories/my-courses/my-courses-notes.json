[{"id": "note-1", "video_id": "video-1", "section_id": "section-video-1", "title": "useReducer vs useState", "content": "useReducer is better for complex state logic, while useState is simpler for basic state management. Remember the reducer pattern from Redux!", "videoTimestamp": "12:45", "createdAt": "2 days ago", "updatedAt": "2 days ago"}, {"id": "note-2", "video_id": "video-1", "section_id": "section-video-2", "title": "Custom Hooks Best Practices", "content": "1. Start with 'use' prefix\n2. Extract reusable logic\n3. Keep them focused on a single responsibility\n4. Return values, not just functions", "videoTimestamp": "22:15", "createdAt": "1 day ago", "updatedAt": "1 day ago"}, {"id": "note-3", "video_id": "video-1", "section_id": "section-video-2", "title": "Performance Optimization Checklist", "content": "- Use React.memo for expensive components\n- useMemo for expensive calculations\n- useCallback for functions passed to child components\n- Code splitting with React.lazy\n- Virtualization for long lists", "videoTimestamp": "15:10", "createdAt": "5 hours ago", "updatedAt": "5 hours ago"}]