// myCourse.ts

export interface MyCourse {
  id: string;
  name: string;
  phone: string;
  email?: string;
  tags?: string[];
  notes?: { text: string; createdAt: string }[];

  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  createdBy?: string;
  updatedBy?: string;
}

export interface MyCourseCreateInput {
  name: string;
  phone: string;
  email?: string;
  tags?: string[];
  notes?: { text: string; createdAt: string }[];
  createdBy?: string;
}

export interface MyCourseUpdateInput {
  name?: string;
  phone?: string;
  email?: string;
  tags?: string[];
  notes?: { text: string; createdAt: string }[];
  updatedBy?: string;
}

export interface MyCourseQueryParams {
  search?: string;
  filters?: { field: keyof MyCourse | string; value: any }[];
  sorts?: { field: keyof MyCourse | string; direction: "asc" | "desc" }[];
  page?: number;
  limit?: number;
  includeDeleted?: boolean;
}

export interface MyCourseBusinessLogicInterface {
  getById(id: string, includeDeleted?: boolean): Promise<MyCourse | null>;
  getAll(params?: MyCourseQueryParams): Promise<{
    items: MyCourse[];
    total: number;
  }>;
  create(data: MyCourseCreateInput): Promise<MyCourse>;
  update(id: string, data: MyCourseUpdateInput): Promise<MyCourse | null>;
  delete(id: string, hardDelete?: boolean): Promise<boolean>;
  restore(id: string): Promise<boolean>;

  bulkCreate(data: MyCourseCreateInput[]): Promise<MyCourse[]>;
  bulkUpdate(updates: { id: string; data: MyCourseUpdateInput }[]): Promise<number>;
  bulkDelete(ids: string[], hardDelete?: boolean): Promise<number>;
}
