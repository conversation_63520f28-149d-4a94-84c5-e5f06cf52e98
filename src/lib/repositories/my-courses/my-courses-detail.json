[{"id": "1", "my_courses_id": "1", "title": "Advanced React Patterns", "instructor": "<PERSON>", "instructorAvatar": "https://ui.shadcn.com/placeholder.svg", "completionPercentage": 45, "lastAccessed": "Yesterday", "summary": "Master advanced React concepts including hooks, context, and performance optimization.", "coverImage": "/react-patterns-course.png", "totalLessons": 32, "completedLessons": 14, "estimatedHours": 18, "category": "Development", "rating": 4.8, "totalReviews": 245, "sections": [{"id": "section-1", "title": "Introduction", "description": "Get started with the course and set up your development environment.", "videos": [{"id": "video-1", "title": "Course Overview", "duration": "5:20", "completed": true, "url": "https://www.youtube.com/watch?v=766uN6s5y_Y"}, {"id": "video-2", "title": "Setting Up Your Environment", "duration": "12:45", "completed": true, "url": "https://www.youtube.com/watch?v=example2"}]}, {"id": "section-2", "title": "Advanced Hooks", "description": "Deep dive into React hooks and learn advanced patterns.", "videos": [{"id": "video-3", "title": "useReducer Deep Dive", "duration": "18:30", "completed": true, "url": "https://www.youtube.com/watch?v=example3"}, {"id": "video-4", "title": "Custom Hooks Patterns", "duration": "22:15", "completed": false, "url": "https://www.youtube.com/watch?v=example4"}, {"id": "video-5", "title": "Context API with Hooks", "duration": "15:45", "completed": false, "url": "https://www.youtube.com/watch?v=example5"}]}, {"id": "section-3", "title": "Performance Optimization", "description": "Learn techniques to optimize your React applications for better performance.", "videos": [{"id": "video-6", "title": "React.memo and useMemo", "duration": "15:10", "completed": false, "url": "https://www.youtube.com/watch?v=example6"}, {"id": "video-7", "title": "Code Splitting Strategies", "duration": "20:05", "completed": false, "url": "https://www.youtube.com/watch?v=example7"}, {"id": "video-8", "title": "Virtualization for Large Lists", "duration": "17:30", "completed": false, "url": "https://www.youtube.com/watch?v=example8"}]}, {"id": "section-4", "title": "Advanced Component Patterns", "description": "Explore advanced component patterns for building scalable React applications.", "videos": [{"id": "video-9", "title": "Compound Components", "duration": "23:15", "completed": false, "url": "https://www.youtube.com/watch?v=example9"}, {"id": "video-10", "title": "Render Props Pattern", "duration": "19:40", "completed": false, "url": "https://www.youtube.com/watch?v=766uN6s5y_Y0"}, {"id": "video-11", "title": "Higher-Order Components", "duration": "21:05", "completed": false, "url": "https://www.youtube.com/watch?v=766uN6s5y_Y1"}]}]}]