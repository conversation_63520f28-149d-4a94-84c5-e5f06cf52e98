[{"id": "comment-1", "video_id": "video-1", "user": {"name": "<PERSON>", "avatar": "/abstract-letter-aj.png"}, "content": "The explanation of useReducer here is incredibly helpful. I finally understand how to use it properly!", "timestamp": "2 days ago", "videoTimestamp": "12:45", "likes": 24, "dislikes": 2, "isLiked": true, "isDisliked": false}, {"id": "comment-2", "video_id": "video-1", "user": {"name": "<PERSON>", "avatar": "/stylized-sm-logo.png"}, "content": "Could you explain the difference between useMemo and useCallback in more detail? I'm still confused about when to use each one.", "timestamp": "1 day ago", "videoTimestamp": "08:20", "likes": 8, "dislikes": 0, "isLiked": false, "isDisliked": false}, {"id": "comment-3", "video_id": "video-1", "user": {"name": "<PERSON>", "avatar": "/dc-skyline-night.png"}, "content": "The code splitting strategies you shared have made my app so much faster. Thanks for the great tips!", "timestamp": "5 hours ago", "videoTimestamp": "15:30", "likes": 16, "dislikes": 1, "isLiked": false, "isDisliked": false}, {"id": "comment-4", "video_id": "video-1", "user": {"name": "<PERSON>", "avatar": "/emergency-room-scene.png"}, "content": "I found a small error in the custom hooks example. The dependency array is missing the userId variable.", "timestamp": "3 hours ago", "videoTimestamp": "19:15", "likes": 12, "dislikes": 0, "isLiked": true, "isDisliked": false}]