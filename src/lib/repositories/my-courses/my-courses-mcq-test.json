[{"id": "test-1", "course_id": "course-1", "title": "React Hooks Fundamentals", "description": "Test your knowledge of React hooks basics", "questions": 10, "timeLimit": "15 minutes", "completed": true, "score": 90, "section": "Introduction"}, {"id": "test-2", "course_id": "course-1", "title": "Advanced Hooks Quiz", "description": "Challenge yourself with advanced hooks concepts", "questions": 15, "timeLimit": "20 minutes", "completed": true, "score": 75, "section": "Advanced Hooks"}, {"id": "test-3", "course_id": "course-1", "title": "Performance Optimization Test", "description": "Test your understanding of React performance optimization", "questions": 12, "timeLimit": "18 minutes", "completed": false, "score": null, "section": "Performance Optimization"}, {"id": "test-4", "course_id": "course-1", "title": "Component Patterns Assessment", "description": "Evaluate your knowledge of advanced component patterns", "questions": 20, "timeLimit": "30 minutes", "completed": false, "score": null, "section": "Advanced Component Patterns"}]