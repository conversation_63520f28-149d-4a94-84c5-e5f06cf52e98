import { 
  ITestRepository, 
  PagingAndSearchParams, 
  MyCourse, 
  MyCourseDetail, 
  MyCourseResponse, 
  MyCoursesTotalLearningStats,
  VideoComment,
  FilterVideoComments,
  MyCourseNote,
  FilterNotes,
  MyCoursesMCQTest,
  MyCoursesFeedback
} from "./MyCoursesRepository";
import { mongoDbInstance } from "@/src/lib/db/mongoClient";
import { MONGO_COLLECTIONS } from "../../db/mongoCollections";


function mapToMyCourse(doc: any): MyCourse {
  const { _id, ...rest } = doc;
  return {
    id: rest.id,
    title: rest.title,
    instructor: rest.instructor,
    instructorAvatar: rest.instructorAvatar,
    completionPercentage: rest.completionPercentage,
    lastAccessed: rest.lastAccessed,
    summary: rest.summary,
    coverImage: rest.coverImage,
    totalLessons: rest.totalLessons,
    completedLessons: rest.completedLessons,
    estimatedHours: rest.estimatedHours,
    category: rest.category,
    createdAt: rest.createdAt,
    updatedAt: rest.updatedAt,
  };
}

function mapToMyCourseDetail(doc: any): MyCourseDetail {
  const { _id, ...rest } = doc;
  return {
    id: rest.id,
    my_courses_id: rest.my_courses_id,
    title: rest.title,
    instructor: rest.instructor,
    instructorAvatar: rest.instructorAvatar,
    completionPercentage: rest.completionPercentage,
    lastAccessed: rest.lastAccessed,
    summary: rest.summary,
    coverImage: rest.coverImage,
    totalLessons: rest.totalLessons,
    completedLessons: rest.completedLessons,
    estimatedHours: rest.estimatedHours,
    category: rest.category,
    rating: rest.rating,
    totalReviews: rest.totalReviews,
    sections: rest.sections || [],
    createdAt: rest.createdAt,
    updatedAt: rest.updatedAt,
  };
}

function mapToVideoComment(doc: any): VideoComment {
  const { _id, ...rest } = doc;
  return {
    id: rest.id,
    video_id: rest.video_id,
    user: rest.user,
    content: rest.content,
    timestamp: rest.timestamp,
    videoTimestamp: rest.videoTimestamp,
    likes: rest.likes,
    dislikes: rest.dislikes,
    isLiked: rest.isLiked,
    isDisliked: rest.isDisliked,
    createdAt: rest.createdAt,
    updatedAt: rest.updatedAt,
  };
}

function mapToMyCourseNote(doc: any): MyCourseNote {
  const { _id, ...rest } = doc;
  return {
    id: rest.id,
    video_id: rest.video_id,
    section_id: rest.section_id,
    title: rest.title,
    content: rest.content,
    videoTimestamp: rest.videoTimestamp,
    createdAt: rest.createdAt,
    updatedAt: rest.updatedAt,
  };
}

function mapToMyCoursesMCQTest(doc: any): MyCoursesMCQTest {
  const { _id, ...rest } = doc;
  return {
    id: rest.id,
    course_id: rest.course_id,
    title: rest.title,
    description: rest.description,
    questions: rest.questions,
    timeLimit: rest.timeLimit,
    completed: rest.completed,
    score: rest.score,
    section: rest.section,
  };
}

function mapToMyCoursesFeedback(doc: any): MyCoursesFeedback {
  const { _id, ...rest } = doc;
  return {
    courseId: rest.courseId,
    rating: rest.rating,
    feedback: rest.feedback,
    createdAt: rest.createdAt,
    updatedAt: rest.updatedAt,
  };
}

export class MongoMyCoursesRepository implements ITestRepository {
  private async getCollection() {
    const client = await mongoDbInstance;
    const db = client.db(process.env.MONGODB_DB!);
    return db.collection(MONGO_COLLECTIONS.MY_COURSES);
  }

  private async getCommentsCollection() {
    const client = await mongoDbInstance;
    const db = client.db(process.env.MONGODB_DB!);
    return db.collection("my-courses-video-comments");
  }

  private async getNotesCollection() {
    const client = await mongoDbInstance;
    const db = client.db(process.env.MONGODB_DB!);
    return db.collection("my-courses-notes");
  }

  private async getMCQTestCollection() {
    const client = await mongoDbInstance;
    const db = client.db(process.env.MONGODB_DB!);
    return db.collection("my-courses-mcq-tests");
  }

  private async getFeedbackCollection() {
    const client = await mongoDbInstance;
    const db = client.db(process.env.MONGODB_DB!);
    return db.collection("my-courses-feedback");
  }

  private generateId() {
    return Math.random().toString(36).substring(2, 11);
  }

  private buildQuery(filters?: PagingAndSearchParams): any {
    const query: any = {};
    if (!filters) return query;

    if (filters.search) {
      query.$or = [
        { title: { $regex: filters.search, $options: "i" } },
        { instructor: { $regex: filters.search, $options: "i" } }
      ];
    }

    if (filters.category) {
      query.category = filters.category;
    }

    return query;
  }

  async getAll(filters?: PagingAndSearchParams): Promise<MyCourseResponse> {
    const collection = await this.getCollection();
    const query = this.buildQuery(filters);

    const cursor = collection.find(query);

    if (filters?.page && filters?.pageSize) {
      cursor.skip((filters.page - 1) * filters.pageSize).limit(filters.pageSize);
    }

    const docs = await cursor.toArray();
    const total = await collection.countDocuments(query);
    const pageSize = filters?.pageSize || total;
    const total_pages = Math.ceil(total / pageSize);

    return {
      items: docs.map(mapToMyCourse),
      total_pages,
    };
  }

  async getTotalLearningStats(): Promise<MyCoursesTotalLearningStats> {
    const collection = await this.getCollection();
    
    const totalCourses = await collection.countDocuments({});
    const courses = await collection.find({}).toArray();
    
    const totalHours = courses.reduce((sum, course) => sum + (course.estimatedHours || 0), 0);
    const certificatesEarned = courses.filter(course => course.completionPercentage === 100).length;

    return {
      totalCourses,
      totalHours,
      certificatesEarned,
    };
  }

  async getById(id: string): Promise<MyCourse | null> {
    const collection = await this.getCollection();
    const doc = await collection.findOne({ id });
    return doc ? mapToMyCourse(doc) : null;
  }

  async getDetail(id: string): Promise<MyCourseDetail | null> {
    const collection = await this.getCollection();
    const doc = await collection.findOne({ id });
    return doc ? mapToMyCourseDetail(doc) : null;
  }

  async create(data: Partial<MyCourse>): Promise<MyCourse> {
    const collection = await this.getCollection();

    const newMyCourse: MyCourse = {
      ...data,
      id: this.generateId(),
      title: data.title || "Untitled Course",
      instructor: data.instructor || "Unknown Instructor",
      instructorAvatar: data.instructorAvatar || "",
      completionPercentage: data.completionPercentage || 0,
      summary: data.summary || "",
      coverImage: data.coverImage || "",
      totalLessons: data.totalLessons || 0,
      completedLessons: data.completedLessons || 0,
      estimatedHours: data.estimatedHours || 0,
      category: data.category || [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    await collection.insertOne(newMyCourse);
    return newMyCourse;
  }

  async update(id: string, data: Partial<MyCourse>): Promise<MyCourse | null> {
    const collection = await this.getCollection();

    const result = await collection.findOneAndUpdate(
      { id },
      {
        $set: {
          ...data,
          updatedAt: new Date(),
        },
      },
      { returnDocument: "after" }
    );

    return result ? mapToMyCourse(result) : null;
  }

  async delete(id: string): Promise<boolean> {
    const collection = await this.getCollection();
    const result = await collection.deleteOne({ id });
    return result.deletedCount === 1;
  }

  async bulkDelete(ids: string[]): Promise<void> {
    const collection = await this.getCollection();
    await collection.deleteMany({ id: { $in: ids } });
  }

  async getComment(filters?: FilterVideoComments): Promise<VideoComment[] | null> {
    const collection = await this.getCommentsCollection();
    const query: any = {};

    if (filters?.id) {
      query.video_id = filters.id;
    }

    if (filters?.filter === 'my' && filters?.currentUserName) {
      query['user.name'] = filters.currentUserName;
    }

    const docs = await collection.find(query).toArray();
    return docs.map(mapToVideoComment);
  }

  async createComment(data: Partial<VideoComment>): Promise<VideoComment> {
    const collection = await this.getCommentsCollection();

    const newComment: VideoComment = {
      id: this.generateId(),
      video_id: data.video_id || "",
      user: data.user || { name: "Anonymous", avatar: "" },
      content: data.content || "",
      timestamp: data.timestamp || new Date().toISOString(),
      videoTimestamp: data.videoTimestamp || "00:00",
      likes: data.likes || 0,
      dislikes: data.dislikes || 0,
      isLiked: data.isLiked || false,
      isDisliked: data.isDisliked || false,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...data,
    };

    await collection.insertOne(newComment);
    return newComment;
  }

  async getCourseNotes(filters?: FilterNotes): Promise<MyCourseNote[] | null> {
    const collection = await this.getNotesCollection();
    const query: any = {};

    if (filters?.filter === 'this-video' && filters?.id) {
      query.video_id = filters.id;
    } else if (filters?.filter === 'this-section' && filters?.id) {
      query.section_id = filters.id;
    }

    const docs = await collection.find(query).toArray();
    return docs.map(mapToMyCourseNote);
  }

  async createCourseNotes(data: Partial<MyCourseNote>): Promise<MyCourseNote> {
    const collection = await this.getNotesCollection();

    const newNote: MyCourseNote = {
      id: this.generateId(),
      video_id: data.video_id || "",
      section_id: data.section_id || "",
      title: data.title || "Untitled Note",
      content: data.content || "",
      videoTimestamp: data.videoTimestamp || "00:00",
      createdAt: new Date(),
      updatedAt: new Date(),
      ...data,
    };

    await collection.insertOne(newNote);
    return newNote;
  }

  async updateCourseNotes(id: string, data: Partial<MyCourseNote>): Promise<MyCourseNote | null> {
    const collection = await this.getNotesCollection();

    const result = await collection.findOneAndUpdate(
      { id },
      {
        $set: {
          ...data,
          updatedAt: new Date(),
        },
      },
      { returnDocument: "after" }
    );

    return result ? mapToMyCourseNote(result) : null;
  }

  async deleteCourseNotes(id: string): Promise<boolean> {
    const collection = await this.getNotesCollection();
    const result = await collection.deleteOne({ id });
    return result.deletedCount === 1;
  }

  async getCourseMCQTest(filters?: PagingAndSearchParams): Promise<MyCoursesMCQTest[] | null> {
    const collection = await this.getMCQTestCollection();
    const query: any = {};

    if (filters?.course_id) {
      query.course_id = filters.course_id;
    }

    const docs = await collection.find(query).toArray();
    return docs.map(mapToMyCoursesMCQTest);
  }

  async createCoursesFeedback(data: Partial<MyCoursesFeedback>): Promise<MyCoursesFeedback> {
    const collection = await this.getFeedbackCollection();

    const newFeedback: MyCoursesFeedback = {
      courseId: data.courseId || "",
      rating: data.rating || 0,
      feedback: data.feedback || "",
      createdAt: new Date(),
      updatedAt: new Date(),
      ...data,
    };

    await collection.insertOne(newFeedback);
    return newFeedback;
  }
}
