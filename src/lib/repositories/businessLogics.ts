import { MongoAuthDBRepository, AuthBusinessLogic } from "./auth";
import { AuthDBRedisRepository } from "./auth/RedisInMemoryRepository";
import { driver } from "./LiveMongoDriver";
import { driver as inMemoryDriver } from "./LiveRedisDriver";
import { TEMPLATE_CAPITALIZEDBusinessLogic } from "./__TEMPLATE/BusinessLogic";
import { MongoTEMPLATE_CAPITALIZEDRepository } from "./__TEMPLATE/MongoRepository";
import { ResendEmailSender } from "../microservices/ResendEmailSender";
import { eventService } from "../microservices";

const authDb = new MongoAuthDBRepository(driver);
const inMemoryAuthRepo = new AuthDBRedisRepository(inMemoryDriver);
const emailSender = new ResendEmailSender()
export const authBusinessLogic = new AuthBusinessLogic(authDb, inMemoryAuthRepo, emailSender, eventService);

// const aiRulesDb = new MongoAiRuleRepository(driver);
// const aiRulesVectordb = new PineconeAiRuleVectorDBRepository(
//   process.env.PINECONE_API_KEY!,
//   "testing_cs_ai_namespace"
// );
// export const aiRulesBusinessLogic = new AiRuleBusinessLogic(aiRulesDb, aiRulesVectordb);

// const workflowExecutionsDb = new MongoAiWorkflowExecutionRepository(driver);
// export const workflowExecutionsBusinessLogic = new AiWorkflowExecutionBusinessLogic(workflowExecutionsDb);

// const contactsDb = new MongoContactRepository(driver);
// export const contactsBusinessLogic = new ContactBusinessLogic(contactsDb);


// const messageTemplatesDb = new MongoMessageTemplateRepository(driver);
// const messageTemplatesVectordb = new PineconeMessageTemplateVectorDBRepository(
//   process.env.PINECONE_API_KEY!,
//   "testing_cs_ai_namespace"
// );
// export const messageTemplatesBusinessLogic = new MessageTemplateBusinessLogic(messageTemplatesDb, messageTemplatesVectordb);


// const chatMessagesDb = new MongoChatMessageRepository(driver);
// export const chatMessagesBusinessLogic = new ChatMessageBusinessLogic(chatMessagesDb);

// const chatsDb = new MongoChatRepository(driver);
// export const chatsBusinessLogic = new ChatBusinessLogic(chatsDb);

// // Users
// const usersDb = new MongoUserRepository(driver);
// export const usersBusinessLogic = new UserBusinessLogic(usersDb);

// // Teams
// const teamsDb = new MongoTeamRepository(driver);
// export const teamsBusinessLogic = new TeamBusinessLogic(teamsDb);

// // Invitations
// const invitationsDb = new MongoInvitationRepository(driver);
// export const invitationsBusinessLogic = new InvitationBusinessLogic(invitationsDb);

// const rolesDb = new MongoRoleRepository(driver);
// export const rolesBusinessLogic = new RoleBusinessLogic(rolesDb);

const testsDb = new MongoTEMPLATE_CAPITALIZEDRepository(driver);
export const TEMPLATE_CAPITALIZEDsBusinessLogic = new TEMPLATE_CAPITALIZEDBusinessLogic(testsDb);