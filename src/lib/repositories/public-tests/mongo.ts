import { Result } from "@/types/result";
import {
  TestData,
  PublicTestQuery,
  PublicTestsRepository,
} from "./PublicTestsRepository";
import { mongoDbInstance } from "@/src/lib/db/mongoClient";
import { MONGO_COLLECTIONS } from "../../db/mongoCollections";

function mapToTestData(doc: any): TestData {
  return {
    id: doc.id,
    title: doc.title,
    description: doc.description,
    categories: doc.categories,
    difficulties: doc.difficulties,
    benefits: doc.benefits,
    durationInSeconds: doc.durationInSeconds,
    cover: doc.cover,
    mediaUrl: doc.mediaUrl,
    creator: {
      id: doc.creator?.id,
      name: doc.creator?.name,
      avatar: doc.creator?.avatar,
    },
    reason: doc.reason,
    usersCount: doc.usersCount,
    successRate: doc.successRate,
    totalQuestions: doc.totalQuestions,
    premium: doc.premium,
    topics: doc.topics
    // we dont show questions in public test response
  };
}

export class MongoPublicTestsRepository implements PublicTestsRepository {
  private async getCollection() {
    const client = await mongoDbInstance;
    const db = client.db(); // db name comes from URI
    return db.collection(MONGO_COLLECTIONS.PUBLIC_TEST);
  }

  async getAll(filters?: PublicTestQuery): Promise<TestData[]> {
    const collection = await this.getCollection();

    const query: any = {};

    if (filters?.query) {
      const regex = new RegExp(filters.query, "i");
      query.$or = [
        { title: { $regex: regex } },
        { description: { $regex: regex } },
      ];
    }

    if (filters?.categories?.length) {
      query.categories = { $in: filters.categories };
    }

    const docs = await collection.find(query).toArray();
    return docs.map(mapToTestData);
  }

  async getAllCategories(): Promise<string[]> {
    const collection = await this.getCollection();
    const docs = await collection.find({}).toArray();

    const categorySet = new Set<string>();
    docs.forEach((doc) => {
      (doc.categories || []).forEach((cat: string) => categorySet.add(cat));
    });

    return ["All", ...Array.from(categorySet)];
  }

  async getById(id: string): Promise<Result<TestData, string>> {
    const collection = await this.getCollection();
    const doc = await collection.findOne({ id });

    if (!doc) {
      return { ok: false, error: `TestData with id ${id} not found` };
    }

    return { ok: true, value: mapToTestData(doc) };
  }

  async getSimilarTests(filters?: PublicTestQuery): Promise<TestData[]> {
    const collection = await this.getCollection();
    const query: any = {};

    if (filters?.query) {
      const keywords = filters.query
        .toLowerCase()
        .split(/[\s\-_,]+/)
        .filter((word) => word.length > 2); // filter out short/common words

      query.$or = keywords.flatMap((word) => [
        { title: { $regex: new RegExp(word, "i") } },
        { description: { $regex: new RegExp(word, "i") } },
        { reason: { $regex: new RegExp(word, "i") } },
      ]);
    }

    if (filters?.categories?.length) {
      query.categories = { $in: filters.categories };
    }

    const docs = await collection.find(query).limit(10).toArray();
    return docs.map(mapToTestData);
  }
}
