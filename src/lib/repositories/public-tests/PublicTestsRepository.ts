import { Result } from "@/types/result";

export type TestDifficulty = "EASY" | "INTERMEDIATE" | "ADVANCED";

export interface TestData {
  id: string;
  title: string;
  description: string;
  categories: string[];
  difficulties: TestDifficulty[];
  benefits: string[];
  durationInSeconds: number;
  cover: string;
  mediaUrl?: string;
  creator: TestCreator;
  reason: string;
  usersCount: number;
  successRate: number;
  totalQuestions: number;
  questions?: string[]; // question ids
  premium?: PremiumInfo;
  topics?: Topic[]; // ✅ newly added
}

export interface PremiumInfo {
  isPremium?: boolean;
  price?: string; // e.g. "$9.99"
  offerText?: string; // e.g. "Get exclusive content + vouchers!"
  badges?: string[];  // e.g. ["1-on-1 Mentoring", "Certificate Included", "Limited Offer"]
}

export interface TestCreator {
  id: string;
  name: string;
  avatar: string;
}

export interface Topic {
  title: string;
  description: string;
  subtopics: Subtopic[];
}

export interface Subtopic {
  title: string;
  description: string;
}

export interface PublicTestQuery {
  query?: string;
  categories?: string[];
}

export interface PublicTestsRepository {
  getAll(filters?: PublicTestQuery): Promise<TestData[]>;
  getById(id: string): Promise<Result<TestData, string>>;
  getAllCategories(): Promise<string[]>;
  getSimilarTests(filters?: PublicTestQuery): Promise<TestData[]>;
}
