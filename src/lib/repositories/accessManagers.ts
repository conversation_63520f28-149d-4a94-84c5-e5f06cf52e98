import { AccessRule, AuditLog, CompositeRoleStore, CompositeRuleStore, CrudAction, GroupRoleResourceAccessManager, RoleStoreMongoRepository, RoleStoreRedisRepository, RuleStoreMongoRepository, RuleStoreRedisRepository, UserRole } from "@/src/lib/repositories/AccessManager";
import { driver as redisDriver } from "@/src/lib/repositories/LiveRedisDriver";
import { driver as mongoDriver } from "@/src/lib/repositories/LiveMongoDriver";
import { MongoDriver } from "./MongoDriver";

const roleRedisStore = new RoleStoreRedisRepository(redisDriver);
const ruleRedisStore = new RuleStoreRedisRepository(redisDriver);

const roleMongoStore = new RoleStoreMongoRepository(mongoDriver);
const ruleMongoStore = new RuleStoreMongoRepository(mongoDriver);

export const roleStore = new CompositeRoleStore(roleRedisStore, roleMongoStore);
const ruleStore = new CompositeRuleStore(ruleRedisStore, ruleMongoStore);

export class ConsoleAuditLogger implements AuditLog {
    logAccessCheck(info: {
        userId: string;
        resource: string;
        action: CrudAction;
        matchedRule?: AccessRule;
        userRole?: UserRole;
        result: boolean;
    }): void {
        console.log(`[AuditLog] Access Check`, {
            timestamp: new Date().toISOString(),
            ...info
        });
    }

    log(...message: any[]): void {
        console.log(`[AuditLog]`, {
            timestamp: new Date().toISOString(),
            ...message
        });
    }
}

export class MongoAuditLogger implements AuditLog {
    constructor(private db: MongoDriver) { }

    async logAccessCheck(info: {
        userId: string;
        resource: string;
        action: CrudAction;
        matchedRule?: AccessRule;
        userRole?: UserRole;
        result: boolean;
    }): Promise<void> {
        await this.db.getCollection('auditLogs').insertOne({
            type: 'access-check',
            timestamp: new Date(),
            userId: info.userId,
            resource: info.resource,
            action: info.action,
            result: info.result,
            matchedRule: info.matchedRule ?? null,
            userRole: info.userRole ?? null,
        });
    }

    async log(...message: any[]): Promise<void> {
        await this.db.getCollection('auditLogs').insertOne({
            type: 'log',
            timestamp: new Date(),
            message,
        });
    }
}

const auditLog = new ConsoleAuditLogger()
const mongoAuditLog = new MongoAuditLogger(mongoDriver);

export function contactAccessManager(groupId: string): GroupRoleResourceAccessManager {
    return new GroupRoleResourceAccessManager(groupId, roleStore, ruleStore, mongoAuditLog);
}

export function aiRulesAccessManager(groupId: string): GroupRoleResourceAccessManager {
    return new GroupRoleResourceAccessManager(groupId, roleStore, ruleStore, mongoAuditLog);
}
