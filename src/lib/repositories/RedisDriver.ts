export interface InMemoryDriver {
  /**
   * Set a value in Redis
   * @param key - Redis key
   * @param value - Any JSON-serializable value
   */
  set<T = unknown>(key: string, value: T): Promise<void>;

  /**
   * Get a value from Redis
   * @param key - Redis key
   * @returns Parsed value or null
   */
  get<T = unknown>(key: string): Promise<T | null>;

  /**
   * Optional close hook (e.g., for mock or live drivers)
   */
  close?(): Promise<void>;

  /**
   * Get all keys matching a pattern
   * @param pattern - Pattern to match
   * @returns Array of keys
   */
  keys(pattern: string): Promise<string[]>;
}
