import { ISettingsRepository, UserSettings } from "./SettingsRepository";
import { mongoDbInstance } from "@/src/lib/db/mongoClient";
import { MONGO_COLLECTIONS } from "../../db/mongoCollections";

function mapToUserSettings(doc: any): UserSettings {
  const { _id, ...rest } = doc;
  return {
    id: rest.id,
    userId: rest.userId,
    emailNotifications: rest.emailNotifications ?? true,
    pushNotifications: rest.pushNotifications ?? true,
    courseUpdates: rest.courseUpdates ?? true,
    testReminders: rest.testReminders ?? true,
    collaboratorActivity: rest.collaboratorActivity ?? false,
    notificationFrequency: rest.notificationFrequency ?? "immediate",
    theme: rest.theme ?? "system",
    colorScheme: rest.colorScheme ?? "blue",
    fontSize: rest.fontSize ?? "medium",
    compactMode: rest.compactMode ?? false,
    animations: rest.animations ?? true,
    language: rest.language ?? "en",
    region: rest.region ?? "US",
    dateFormat: rest.dateFormat ?? "MM/DD/YYYY",
    timeFormat: rest.timeFormat ?? "12",
    twoFactorEnabled: rest.twoFactorEnabled ?? false,
    sessionTimeout: rest.sessionTimeout ?? 30,
    createdAt: rest.createdAt,
    updatedAt: rest.updatedAt,
  };
}

export class MongoSettingsRepository implements ISettingsRepository {
  private async getCollection() {
    const client = await mongoDbInstance;
    const db = client.db(process.env.MONGODB_DB!);
    return db.collection(MONGO_COLLECTIONS.USER_SETTINGS);
  }

  private generateId() {
    return Math.floor(Math.random() * 1000000).toString();
  }

  async getByUserId(userId: string): Promise<UserSettings | null> {
    const collection = await this.getCollection();
    const doc = await collection.findOne({ userId });
    
    if (!doc) {
      // Return default settings if none exist
      return this.create({ userId });
    }
    
    return mapToUserSettings(doc);
  }

  async create(data: Partial<UserSettings>): Promise<UserSettings> {
    const collection = await this.getCollection();

    const newSettings: UserSettings = {
      id: this.generateId(),
      userId: data.userId || "default",
      emailNotifications: data.emailNotifications ?? true,
      pushNotifications: data.pushNotifications ?? true,
      courseUpdates: data.courseUpdates ?? true,
      testReminders: data.testReminders ?? true,
      collaboratorActivity: data.collaboratorActivity ?? false,
      notificationFrequency: data.notificationFrequency ?? "immediate",
      theme: data.theme ?? "system",
      colorScheme: data.colorScheme ?? "blue",
      fontSize: data.fontSize ?? "medium",
      compactMode: data.compactMode ?? false,
      animations: data.animations ?? true,
      language: data.language ?? "en",
      region: data.region ?? "US",
      dateFormat: data.dateFormat ?? "MM/DD/YYYY",
      timeFormat: data.timeFormat ?? "12",
      twoFactorEnabled: data.twoFactorEnabled ?? false,
      sessionTimeout: data.sessionTimeout ?? 30,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    await collection.insertOne(newSettings);
    return newSettings;
  }

  async update(userId: string, data: Partial<UserSettings>): Promise<UserSettings | null> {
    const collection = await this.getCollection();

    const result = await collection.findOneAndUpdate(
      { userId },
      {
        $set: {
          ...data,
          updatedAt: new Date(),
        },
      },
      { returnDocument: "after", upsert: true }
    );

    return result ? mapToUserSettings(result) : null;
  }

  async delete(userId: string): Promise<boolean> {
    const collection = await this.getCollection();
    const result = await collection.deleteOne({ userId });
    return result.deletedCount === 1;
  }

  async updateNotificationSettings(
    userId: string, 
    settings: Partial<Pick<UserSettings, 'emailNotifications' | 'pushNotifications' | 'courseUpdates' | 'testReminders' | 'collaboratorActivity' | 'notificationFrequency'>>
  ): Promise<UserSettings | null> {
    return this.update(userId, settings);
  }

  async updateAppearanceSettings(
    userId: string, 
    settings: Partial<Pick<UserSettings, 'theme' | 'colorScheme' | 'fontSize' | 'compactMode' | 'animations'>>
  ): Promise<UserSettings | null> {
    return this.update(userId, settings);
  }

  async updateLanguageSettings(
    userId: string, 
    settings: Partial<Pick<UserSettings, 'language' | 'region' | 'dateFormat' | 'timeFormat'>>
  ): Promise<UserSettings | null> {
    return this.update(userId, settings);
  }

  async updateSecuritySettings(
    userId: string, 
    settings: Partial<Pick<UserSettings, 'twoFactorEnabled' | 'sessionTimeout'>>
  ): Promise<UserSettings | null> {
    return this.update(userId, settings);
  }
}
