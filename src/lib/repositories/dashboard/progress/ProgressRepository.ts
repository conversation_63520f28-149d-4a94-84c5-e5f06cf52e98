// Define interfaces for the skill and badge structures
export interface Skill {
  skill: string;
  progress: number;
}

export interface Badge {
  name: string;
  id: number;
}

export interface PerformanceEntry {
  month: string;
  score: number;
}

// Define the main data structure
export interface ProgressStats {
  userId:string
  technicalSkills: Skill[];
  designSkills: Skill[];
  badges: Badge[];
  performanceData: PerformanceEntry[];
}

export interface PagingAndSearchParams {
  page?: number;
  pageSize?: number;
  search?: string;
  [key: string]: any; // Other possible filters
}


export interface IProgressRepository {
  getByUserId(id: string): Promise<ProgressStats | null>;
}
