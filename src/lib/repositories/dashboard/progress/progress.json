[{"userId": "wadidaw123", "technicalSkills": [{"skill": "HTML", "progress": 90}, {"skill": "CSS", "progress": 75}, {"skill": "JavaScript", "progress": 65}, {"skill": "React", "progress": 50}, {"skill": "Node.js", "progress": 40}], "designSkills": [{"skill": "UI/UX Design", "progress": 85}, {"skill": "Color Theory", "progress": 70}, {"skill": "Typography", "progress": 60}, {"skill": "Responsive Design", "progress": 80}, {"skill": "Design Systems", "progress": 45}], "badges": [{"name": "Perfect Score", "id": 1}, {"name": "Fast Learner", "id": 2}, {"name": "Top 10%", "id": 3}, {"name": "Persistent", "id": 4}, {"name": "Expert", "id": 5}], "performanceData": [{"month": "Jan", "score": 65}, {"month": "Feb", "score": 70}, {"month": "Mar", "score": 75}, {"month": "Apr", "score": 68}, {"month": "May", "score": 72}, {"month": "Jun", "score": 80}, {"month": "Jul", "score": 85}, {"month": "Aug", "score": 82}]}, {"userId": "alpha789xyz", "technicalSkills": [{"skill": "HTML", "progress": 85}, {"skill": "CSS", "progress": 80}, {"skill": "JavaScript", "progress": 70}, {"skill": "Vue.js", "progress": 60}, {"skill": "Python", "progress": 55}], "designSkills": [{"skill": "UI/UX Design", "progress": 75}, {"skill": "Color Theory", "progress": 65}, {"skill": "Typography", "progress": 55}, {"skill": "Responsive Design", "progress": 78}, {"skill": "Design Systems", "progress": 50}], "badges": [{"name": "Perfect Score", "id": 1}, {"name": "Fast Learner", "id": 2}, {"name": "Top 10%", "id": 3}, {"name": "Persistent", "id": 4}, {"name": "Expert", "id": 5}], "performanceData": [{"month": "Jan", "score": 60}, {"month": "Feb", "score": 65}, {"month": "Mar", "score": 67}, {"month": "Apr", "score": 70}, {"month": "May", "score": 75}, {"month": "Jun", "score": 78}, {"month": "Jul", "score": 80}, {"month": "Aug", "score": 83}]}]