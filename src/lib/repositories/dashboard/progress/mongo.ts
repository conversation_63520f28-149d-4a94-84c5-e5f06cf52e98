import {
  Badge,
  IProgressRepository,
  PerformanceEntry,
  ProgressStats,
  Skill,
} from "./ProgressRepository";
import { mongoDbInstance } from "@/src/lib/db/mongoClient";
import { MONGO_COLLECTIONS } from "../../../db/mongoCollections";

function mapSkillList(skills: any[]): Skill[] {
  return Array.isArray(skills)
    ? skills.map((s) => ({
        skill: String(s.skill),
        progress: typeof s.progress === "number" ? s.progress : 0,
      }))
    : [];
}

function mapBadgeList(badges: any[]): Badge[] {
  return Array.isArray(badges)
    ? badges.map((b) => ({
        id: typeof b.id === "number" ? b.id : parseInt(String(b.id), 10) || 0,
        name: String(b.name),
      }))
    : [];
}

function mapPerformanceData(data: any[]): PerformanceEntry[] {
  return Array.isArray(data)
    ? data.map((p) => ({
        month: String(p.month),
        score: typeof p.score === "number" ? p.score : 0,
      }))
    : [];
}

export function mapToProgressStats(doc: any): ProgressStats {
  return {
    userId: String(doc.userId),
    technicalSkills: mapSkillList(doc.technicalSkills),
    designSkills: mapSkillList(doc.designSkills),
    badges: mapBadgeList(doc.badges),
    performanceData: mapPerformanceData(doc.performanceData),
  };
}


export class MongoProgressRepository implements IProgressRepository {
  private async getCollection() {
    const client = await mongoDbInstance;
    const db = client.db(process.env.MONGODB_DB!);
    return db.collection(MONGO_COLLECTIONS.PROGRESS_STATS);
  }

  async getByUserId(userId: string): Promise<ProgressStats | null> {
    const collection = await this.getCollection();

    const doc = await collection.findOne({ userId });
    if (!doc) return null;

    const { _id, ...progress } = doc;
    return mapToProgressStats(progress);
  }
}
