import { ITestRepository, PagingAndSearchParams, ActivityItem } from "./OverviewRepository";
import { mongoDbInstance } from "@/src/lib/db/mongoClient";
import { MONGO_COLLECTIONS } from "../../../db/mongoCollections";

function mapToActivityItem(doc: any): ActivityItem {
  const { _id, ...rest } = doc;
  return {
    type: rest.type,
    title: rest.title,
    date: rest.date,
    image: rest.image,
    score: rest.score,
    description: rest.description,
  };
}

export class MongoDashboardOverviewRepository implements ITestRepository {
  private async getCollection() {
    const client = await mongoDbInstance;
    const db = client.db(process.env.MONGODB_DB!);
    return db.collection(MONGO_COLLECTIONS.DASHBOARD_OVERVIEW);
  }

  private buildQuery(filters?: PagingAndSearchParams): any {
    const query: any = {};
    if (!filters) return query;

    if (filters.search) {
      query.$or = [
        { title: { $regex: filters.search, $options: "i" } },
        { description: { $regex: filters.search, $options: "i" } }
      ];
    }

    if (filters.type) {
      query.type = filters.type;
    }

    return query;
  }

  async getAll(filters?: PagingAndSearchParams): Promise<ActivityItem[]> {
    const collection = await this.getCollection();
    const query = this.buildQuery(filters);

    const cursor = collection.find(query).sort({ date: -1 }); // Sort by date descending

    if (filters?.page && filters?.pageSize) {
      cursor.skip((filters.page - 1) * filters.pageSize).limit(filters.pageSize);
    }

    const docs = await cursor.toArray();
    
    // If no data found, return default activities
    if (docs.length === 0) {
      return [
        {
          type: "test_completed",
          title: "Welcome to the Dashboard",
          date: new Date(),
          image: "/images/welcome.png",
          score: 0,
          description: "Start taking tests to see your activity here",
        },
      ];
    }

    return docs.map(mapToActivityItem);
  }
}
