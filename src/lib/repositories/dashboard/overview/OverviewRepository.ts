
// --- Activity Types
type ActivityType = 'test_completed' | 'badge_earned';

export interface ActivityItem {
  type: ActivityType;
  title: string;
  date: Date;
  image: string;
  score?: number;
  description?: string;
}

export interface PagingAndSearchParams {
  page?: number;
  pageSize?: number;
  search?: string;
  [key: string]: any; // Other possible filters
}


export interface ITestRepository {
  getAll(filters?: PagingAndSearchParams): Promise<ActivityItem[]>;
}
