[{"id": "rec-1", "title": "Advanced JavaScript Concepts", "description": "Deep dive into closures, prototypes, and async programming", "image": "https://ui.shadcn.com/placeholder.svg", "instructor": "<PERSON>", "duration": "4 hours", "level": "Intermediate", "match": 95, "reason": "Based on your interest in React"}, {"id": "rec-2", "title": "React for Be<PERSON>ners", "description": "Learn the fundamentals of React and build your first app", "image": "https://ui.shadcn.com/placeholder.svg", "instructor": "<PERSON>", "duration": "6 hours", "level": "<PERSON><PERSON><PERSON>", "match": 90, "reason": "Complements your current course"}, {"id": "rec-3", "title": "CSS Grid & Flexbox Mastery", "description": "Master modern CSS layout techniques", "image": "https://ui.shadcn.com/placeholder.svg", "instructor": "<PERSON>", "duration": "3 hours", "level": "Intermediate", "match": 85, "reason": "Popular with students of this course"}]