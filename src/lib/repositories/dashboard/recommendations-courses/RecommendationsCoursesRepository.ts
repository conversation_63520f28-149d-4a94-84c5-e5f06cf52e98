export interface RecommendationsCourse  {
  id:string,
  reason:string,
  title: string;
  description: string;
  image: string;
  instructor: string;
  duration: string;
  level: 'Beginner' | 'Intermediate' | 'Advanced';
  match: number;
};

export interface PagingAndSearchParams {
  page?: number;
  pageSize?: number;
  search?: string;
  [key: string]: any; // Other possible filters
}

export interface ITestRepository {
  getAll(filters?: PagingAndSearchParams): Promise<RecommendationsCourse[]>;
}
