import { ITestRepository, PagingAndSearchParams, RecommendationsCourse } from "./RecommendationsCoursesRepository";
import { mongoDbInstance } from "@/src/lib/db/mongoClient";
import { MONGO_COLLECTIONS } from "../../../db/mongoCollections";

function mapToRecommendationsCourse(doc: any): RecommendationsCourse {
  const { _id, ...rest } = doc;
  return {
    id: rest.id,
    reason: rest.reason,
    title: rest.title,
    description: rest.description,
    image: rest.image,
    instructor: rest.instructor,
    duration: rest.duration,
    level: rest.level,
    match: rest.match,
  };
}

export class MongoDashboardRecommendationsCoursesRepository implements ITestRepository {
  private async getCollection() {
    const client = await mongoDbInstance;
    const db = client.db(process.env.MONGODB_DB!);
    return db.collection(MONGO_COLLECTIONS.DASHBOARD_RECOMMENDATIONS_COURSES);
  }

  private buildQuery(filters?: PagingAndSearchParams): any {
    const query: any = {};
    if (!filters) return query;

    if (filters.search) {
      query.$or = [
        { title: { $regex: filters.search, $options: "i" } },
        { description: { $regex: filters.search, $options: "i" } },
        { instructor: { $regex: filters.search, $options: "i" } }
      ];
    }

    if (filters.level) {
      query.level = filters.level;
    }

    return query;
  }

  async getAll(filters?: PagingAndSearchParams): Promise<RecommendationsCourse[]> {
    const collection = await this.getCollection();
    const query = this.buildQuery(filters);

    const cursor = collection.find(query).sort({ match: -1 }); // Sort by match percentage descending

    if (filters?.page && filters?.pageSize) {
      cursor.skip((filters.page - 1) * filters.pageSize).limit(filters.pageSize);
    }

    const docs = await cursor.toArray();
    
    // If no data found, return default recommendations
    if (docs.length === 0) {
      return [
        {
          id: "default-1",
          reason: "Based on your interests",
          title: "Getting Started with Programming",
          description: "Learn the fundamentals of programming with this beginner-friendly course.",
          image: "/images/programming-basics.png",
          instructor: "John Doe",
          duration: "4 weeks",
          level: "Beginner",
          match: 85,
        },
        {
          id: "default-2",
          reason: "Popular choice",
          title: "Web Development Fundamentals",
          description: "Master the basics of web development including HTML, CSS, and JavaScript.",
          image: "/images/web-dev.png",
          instructor: "Jane Smith",
          duration: "6 weeks",
          level: "Beginner",
          match: 78,
        },
      ];
    }

    return docs.map(mapToRecommendationsCourse);
  }
}
