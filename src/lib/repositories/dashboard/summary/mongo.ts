import { ITestRepository, UserStats } from "./DashboardSummaryRepository";
import { mongoDbInstance } from "@/src/lib/db/mongoClient";
import { MONGO_COLLECTIONS } from "../../../db/mongoCollections";

function mapToUserStats(doc: any): UserStats {
  const { _id, ...rest } = doc;
  return {
    name: rest.name,
    id: rest.id,
    testsCompleted: rest.testsCompleted,
    testsChange: rest.testsChange,
    averageScore: rest.averageScore,
    scoreChange: rest.scoreChange,
    badges: rest.badges,
    badgesChange: rest.badgesChange,
    studyTime: rest.studyTime,
    studyTimeChange: rest.studyTimeChange,
  };
}

export class MongoDashboardSummaryRepository implements ITestRepository {
  private async getCollection() {
    const client = await mongoDbInstance;
    const db = client.db(process.env.MONGODB_DB!);
    return db.collection(MONGO_COLLECTIONS.DASHBOARD_SUMMARY);
  }

  async getById(id: string): Promise<UserStats | null> {
    const collection = await this.getCollection();
    const doc = await collection.findOne({ id });
    
    // If no data found, return default user stats
    if (!doc) {
      return {
        name: "User",
        id: id,
        testsCompleted: 0,
        testsChange: "+0%",
        averageScore: 0,
        scoreChange: "+0%",
        badges: 0,
        badgesChange: "+0%",
        studyTime: "0h 0m",
        studyTimeChange: "+0%",
      };
    }

    return mapToUserStats(doc);
  }
}
