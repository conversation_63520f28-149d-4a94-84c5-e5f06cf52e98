export interface BaseQueryParams<T> {
  search?: string;
  filters?: { field: keyof T | string; value: any }[];
  sorts?: { field: keyof T | string; direction: 'asc' | 'desc' }[];
  limit?: number;
  offset?: number;
  includeDeleted?: boolean;
}

export function buildMongoQuery<T>(
  initialQuery: { query: Record<string, any>; sort: Record<string, any> },
  params?: BaseQueryParams<T>,
  searchableFields: (keyof T | string)[] = []
): { query: Record<string, any>; sort: Record<string, any>; limit: number; offset: number } {
  const query = { ...initialQuery.query };
  const sort = { ...initialQuery.sort };

  if (params?.search && searchableFields.length > 0) {
    const searchTerm = params.search.trim();
    query.$or = searchableFields.map(field => ({
      [field]: { $regex: searchTerm, $options: 'i' }
    }));
  }

  // ❌ Exclude soft-deleted unless explicitly included
  if (!params?.includeDeleted && !params?.filters?.some(f => f.field === 'deletedAt')) {
    query.deletedAt = { $exists: false };
  }

  if (!sort || Object.keys(sort).length === 0) {
    sort.createdAt = -1;
  }

  // 🔢 Pagination
  const limit = params?.limit ?? 20;
  const page = params?.offset ?? 1;
  const offset = (page - 1) * limit;

  return { query, sort, limit, offset };
}
