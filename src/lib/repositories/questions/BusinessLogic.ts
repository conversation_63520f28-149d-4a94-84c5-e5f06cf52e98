import { QuestionBusinessLogicInterface, Question, QuestionCreateInput, QuestionUpdateInput, QuestionQueryParams } from "./interface";
import { QuestionDBRepository } from "./DBRepository";
import { createError } from "@/src/lib/utils/common";

function escapeRegexSpecialChars(str: string): string {
  return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

export class QuestionBusinessLogic implements QuestionBusinessLogicInterface {
  constructor(private dbRepository: QuestionDBRepository) {}

  async getById(id: string, includeDeleted?: boolean): Promise<Question | null> {
    if (!id || typeof id !== 'string' || id.trim() === '') {
      throw createError("INVALID_ID", "Question ID is required");
    }
    return await this.dbRepository.getById(id, includeDeleted);
  }

  async getAll(params?: QuestionQueryParams): Promise<{ items: Question[]; total: number }> {
    return await this.dbRepository.getAll(params);
  }

  async create(data: QuestionCreateInput): Promise<Question> {
    // Trim string fields
    const trimmedData = {
      ...data,
      question: data.question?.trim(),
      type: data.type?.trim(),
      difficulty: data.difficulty?.trim(),
      tags: data.tags?.map(tag => tag.trim()).filter(Boolean) || [],
    };

    // Check for duplicate question text
    const existingQuestions = await this.dbRepository.getAll({
      search: escapeRegexSpecialChars(trimmedData.question || ''),
      includeDeleted: false,  
    });

    const duplicateQuestion = existingQuestions.items.find(
      q => q.question?.toLowerCase() === trimmedData.question?.toLowerCase()
    );

    if (duplicateQuestion) {
      throw createError("DUPLICATE_QUESTION", "A question with this text already exists");
    }

    return await this.dbRepository.create(trimmedData as QuestionCreateInput);
  }

  async update(id: string, data: QuestionUpdateInput): Promise<Question | null> {
    if (!id || typeof id !== 'string' || id.trim() === '') {
      throw createError("INVALID_ID", "Question ID is required");
    }

    // Trim string fields
    const trimmedData = {
      ...data,
      question: data.question?.trim(),
      type: data.type?.trim(),
      difficulty: data.difficulty?.trim(),
      tags: data.tags?.map(tag => tag.trim()).filter(Boolean),
    };

    // Check for duplicate question text (excluding current question)
    if (trimmedData.question) {
      const escapedSearch = escapeRegexSpecialChars(trimmedData.question || '');
      const existingQuestions = await this.dbRepository.getAll({
        search: escapedSearch,
        includeDeleted: false,
      });

      const duplicateQuestion = existingQuestions.items.find(
        q => q.question?.toLowerCase() === trimmedData.question?.toLowerCase() && q.id !== id
      );

      if (duplicateQuestion) {
        throw createError("DUPLICATE_QUESTION", "A question with this text already exists");
      }
    }

    return await this.dbRepository.update(id, trimmedData as QuestionUpdateInput);
  }

  async delete(id: string, hardDelete?: boolean): Promise<boolean> {
    if (!id || typeof id !== 'string' || id.trim() === '') {
      throw createError("INVALID_ID", "Question ID is required");
    }

    const question = await this.dbRepository.getById(id, true);
    if (!question) {
      throw createError("NOT_FOUND", "Question not found");
    }

    return await this.dbRepository.delete(id, hardDelete);
  }

  async restore(id: string): Promise<boolean> {
    if (!id || typeof id !== 'string' || id.trim() === '') {
      throw createError("INVALID_ID", "Question ID is required");
    }

    const question = await this.dbRepository.getById(id, true);
    if (!question) {
      throw createError("NOT_FOUND", "Question not found");
    }

    if (!question.deletedAt) {
      throw createError("NOT_DELETED", "Question is not deleted");
    }

    // Check for duplicate question text when restoring
    const existingQuestions = await this.dbRepository.getAll({
      search: escapeRegexSpecialChars(question.question || ''),
      includeDeleted: false,
    });

    const duplicateQuestion = existingQuestions.items.find(
      q => q.question.toLowerCase() === question.question.toLowerCase()
    );

    if (duplicateQuestion) {
      throw createError("DUPLICATE_QUESTION", "Cannot restore: A question with this text already exists");
    }

    return await this.dbRepository.restore(id);
  }

  async bulkCreate(data: QuestionCreateInput[]): Promise<Question[]> {
    // Check for duplicate question texts within the batch
    const questions = data.map(item => item.question?.trim().toLowerCase()).filter(Boolean);
    const uniqueQuestions = new Set(questions);
    if (questions.length !== uniqueQuestions.size) {
      throw createError("DUPLICATE_QUESTIONS", "Duplicate questions found in the batch");
    }

    // Check for existing questions
    for (const item of data) {
      const existingQuestions = await this.dbRepository.getAll({
        search: escapeRegexSpecialChars(item.question?.trim() || ''),
        includeDeleted: false,
      });

      const duplicateQuestion = existingQuestions.items.find(
        q => q.question.toLowerCase() === item.question?.trim().toLowerCase()
      );

      if (duplicateQuestion) {
        throw createError("DUPLICATE_QUESTIONS", `Question "${item.question}" already exists`);
      }
    }

    return await this.dbRepository.bulkCreate(data);
  }

  async bulkUpdate(updates: { id: string; data: QuestionUpdateInput }[]): Promise<number> {
    // Validate all IDs first
    for (const update of updates) {
      if (!update.id || typeof update.id !== 'string' || update.id.trim() === '') {
        throw createError("INVALID_ID", "All question IDs are required");
      }
    }

    // Check for duplicate question texts when updating
    for (const update of updates) {
      if (update.data.question) {
        const existingQuestions = await this.dbRepository.getAll({
          search: escapeRegexSpecialChars(update.data.question.trim() || '' ),
          includeDeleted: false,
        });

        const duplicateQuestion = existingQuestions.items.find(
          q => q.question.toLowerCase() === update.data.question!.trim().toLowerCase() && q.id !== update.id
        );

        if (duplicateQuestion) {
          throw createError("DUPLICATE_QUESTIONS", `Question "${update.data.question}" already exists`);
        }
      }
    }

    return await this.dbRepository.bulkUpdate(updates);
  }

  async bulkDelete(ids: string[], hardDelete?: boolean): Promise<number> {
    // Validate all IDs
    for (const id of ids) {
      if (!id || typeof id !== 'string' || id.trim() === '') {
        throw createError("INVALID_ID", "All question IDs are required");
      }
    }

    return await this.dbRepository.bulkDelete(ids, hardDelete);
  }

  async clone(id: string): Promise<Question | null> {
    return await this.dbRepository.clone(id)
  }
}