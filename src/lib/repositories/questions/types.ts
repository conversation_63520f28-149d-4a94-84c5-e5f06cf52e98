export type BaseQuestion = {
  id: string;
  type: QuestionType;
  difficulty: string;
  question: string;
  internal: Record<string, object>;
  createdAt: Date;
  updatedAt: Date;
  tags: string[];
};

export type OptionText = {
  id: string;
  text: string;
};

export type OptionAudio = {
  id: string;
  audio: string;
};

export type MultipleChoiceQuestion = BaseQuestion & {
  type: "multipleChoice";
  options: OptionText[];
  correctAnswers: string[];
};

export type SingleChoiceQuestion = BaseQuestion & {
  type: "singleChoice";
  options: OptionText[];
  correctAnswer: string;
};

export type TextInputQuestion = BaseQuestion & {
  type: "textInput";
  reviewGuide: string;
  caseSensitive: boolean;
};

export type TestCase = {
  input: string
  expectedOutput: string
}

export type CodeInputQuestion = BaseQuestion & {
  type: "codeInput";
  reviewGuide: {
    expectedOutput?: string;
    requiredFunctions?: string[];
    comments?: string;
  };

  language?: string
  correctAnswer?: string
  testCases?: TestCase[]
};

export type ImageBasedQuestion = BaseQuestion & {
  type: "imageBased";
  image: string;
  options: OptionText[];
  correctAnswers: string[];
};

export type AudioBasedQuestion = BaseQuestion & {
  type: "audioBased";
  audio: string;
  options: OptionText[];
  correctAnswers: string[];
};

export type AudioAnswerQuestion = BaseQuestion & {
  type: "audioAnswer";
  options: OptionAudio[];
  reviewGuide: string;
};

export type VoiceInputQuestion = BaseQuestion & {
  type: "voiceInput";
  reviewGuide: string;
  maxDuration?: number // in seconds
  correctPhrases?: string[]
};

export type FileUploadQuestion = BaseQuestion & {
  type: "fileUpload";
  reviewGuide: string;

  allowedFileTypes?: string[];
  maxFileSize?: number; // size in KB (or bytes if you prefer)
};

export type QuestionType =
  | "multipleChoice"
  | "singleChoice"
  | "textInput"
  | "codeInput"
  | "imageBased"
  | "audioBased"
  | "audioAnswer"
  | "voiceInput"
  | "fileUpload";

export type Question =
  | MultipleChoiceQuestion
  | SingleChoiceQuestion
  | TextInputQuestion
  | CodeInputQuestion
  | ImageBasedQuestion
  | AudioBasedQuestion
  | AudioAnswerQuestion
  | VoiceInputQuestion
  | FileUploadQuestion;

export interface QuestionAnswer {
  id: string;
  questionId: string;
  answer: string[];
  isCorrect?: boolean;
  reviewedAt?: string;
}

export interface QuestionFilters {
  difficulty?: "easy" | "medium" | "hard";
  tags?: string[];
  createdBy?: string;
}


export type QuestionBankItemType = "QUESTION" | "QUESTION_GROUP";

export interface QuestionGroup {
  id: string;
  type: "QUESTION_GROUP";
  name: string;
  description: string;
  tags: string[];
  questionCount: number;
  updatedAt: Date;
}

export type QuestionBank = QuestionGroup | Question;
