import {
  Question,
  QuestionCreateInput,
  QuestionUpdateInput,
  QuestionQueryParams,
} from "./interface";
import { ObjectId } from "mongodb";
import { QuestionDBRepository } from "./DBRepository";
import { mongoDbInstance } from "@/src/lib/db/mongoClient";
import { MONGO_COLLECTIONS } from "@/src/lib/db/mongoCollections";

export class MongoQuestionRepository implements QuestionDBRepository {
  private async getCollection() {
    const client = await mongoDbInstance;
    const db = client.db();
    return db.collection(MONGO_COLLECTIONS.QUESTIONS);
  }

  private generateId() {
    return Math.random().toString(36).substring(2, 11);
  }
  
  private buildFilter(query: QuestionQueryParams = {}) {
    const filter: any = {};

    // Handle soft delete
    if (!query.includeDeleted) {
      filter.deletedAt = { $exists: false };
    }

    // Handle search
    if (query.search) {
      filter.$or = [
        { question: { $regex: query.search, $options: "i" } },
        { description: { $regex: query.search, $options: "i" } },
        { category: { $regex: query.search, $options: "i" } },
      ];
    }

    // Handle custom filters
    if (query.filters) {
      query.filters.forEach(({ field, value }) => {
        if (field === "tags" && Array.isArray(value)) {
          filter.tags = { $in: value };
        } else if (field === "difficulty") {
          filter.difficulty = value;
        } else if (field === "type") {
          filter.type = value;
        } else if (field === "category") {
          filter.category = value;
        } else if (field === "isActive") {
          filter.isActive = value;
        } else if (field === "createdBy") {
          filter.createdBy = value;
        } else {
          filter[field] = value;
        }
      });
    }

    return filter;
  }

  private buildSort(sorts?: QuestionQueryParams["sorts"]) {
    if (!sorts || sorts.length === 0) {
      return { createdAt: -1 };
    }

    const sortObj: any = {};
    sorts.forEach(({ field, direction }) => {
      sortObj[field] = direction === "desc" ? -1 : 1;
    });

    return sortObj;
  }

  private mapFromMongo(doc: any): Question {
    return {
      id: doc._id.toString(),
      question: doc.question,
      type: doc.type,
      options: doc.options,
      correctAnswer: doc.correctAnswer,
      difficulty: doc.difficulty,
      tags: doc.tags || [],
      isActive: doc.isActive,
      createdAt: doc.createdAt,
      updatedAt: doc.updatedAt,
      deletedAt: doc.deletedAt,
      createdBy: doc.createdBy,
      updatedBy: doc.updatedBy,
      internal: doc.internal,
    };
  }

  async getById(id: string, includeDeleted = false): Promise<Question | null> {
    const collection = await this.getCollection();
    const filter: any = { _id: new ObjectId(id) };

    if (!includeDeleted) {
      filter.deletedAt = { $exists: false };
    }

    const result = await collection.findOne(filter);
    return result ? this.mapFromMongo(result) : null;
  }

  async getAll(
    params?: QuestionQueryParams
  ): Promise<{ items: Question[]; total: number }> {
    const collection = await this.getCollection();
    const filter = this.buildFilter(params);
    const sort = this.buildSort(params?.sorts);

    const [items, total] = await Promise.all([
      collection
        .find(filter)
        .sort(sort)
        .skip(((params?.page || 1) - 1) * (params?.limit || 10))
        .limit(params?.limit || 10)
        .toArray(),
      collection.countDocuments(filter),
    ]);

    return {
      items: items.map((item) => this.mapFromMongo(item)),
      total,
    };
  }

  async getCount(params?: QuestionQueryParams): Promise<{ total: number }> {
    const collection = await this.getCollection();
    const filter = this.buildFilter(params);
    const total = await collection.countDocuments(filter);
    return { total };
  }

  async create(data: QuestionCreateInput): Promise<Question> {
    const collection = await this.getCollection();
    const now = new Date();

    const isID = new ObjectId();
    const questionData = {
      ...data,
      _id: isID,
      id: isID.toString(),
      isActive: data.isActive ?? true,
      createdAt: now,
      updatedAt: now,
    };

    const result = await collection.insertOne(questionData);
    const created = await collection.findOne({ _id: result.insertedId });
    return this.mapFromMongo(created!);
  }

  async update(
    id: string,
    data: QuestionUpdateInput
  ): Promise<Question | null> {
    console.log("wadidaw Updating question with ID:", id, "with data:", data);

    const collection = await this.getCollection();
    const updateData = {
      ...data,
      updatedAt: new Date(),
    };

    console.log("Updating question with ID:", id, "with data:", updateData);

    const result = await collection.findOneAndUpdate(
      { _id: new ObjectId(id), deletedAt: { $exists: false } },
      { $set: updateData },
      { returnDocument: "after" }
    );

    return result ? this.mapFromMongo(result) : null;
  }

  async delete(id: string, hardDelete = false): Promise<boolean> {
    const collection = await this.getCollection();

    if (hardDelete) {
      const result = await collection.deleteOne({ _id: new ObjectId(id) });
      return result.deletedCount > 0;
    } else {
      const result = await collection.updateOne(
        { _id: new ObjectId(id) },
        { $set: { deletedAt: new Date() } }
      );
      return result.modifiedCount > 0;
    }
  }

  async restore(id: string): Promise<boolean> {
    const collection = await this.getCollection();
    const result = await collection.updateOne(
      { _id: new ObjectId(id) },
      {
        $unset: { deletedAt: "" },
        $set: { updatedAt: new Date() },
      }
    );
    return result.modifiedCount > 0;
  }

  async bulkCreate(data: QuestionCreateInput[]): Promise<Question[]> {
    const collection = await this.getCollection();
    const now = new Date();

    const isID = new ObjectId();
    const questionDataArray = data.map((item) => ({
      ...item,
      _id: isID,
      id: isID.toString(),
      isActive: item.isActive ?? true,
      createdAt: now,
      updatedAt: now,
    }));

    const result = await collection.insertMany(questionDataArray);
    const created = await collection
      .find({ _id: { $in: Object.values(result.insertedIds) } })
      .toArray();
    return created.map((item) => this.mapFromMongo(item));
  }

  async bulkUpdate(
    updates: { id: string; data: QuestionUpdateInput }[]
  ): Promise<number> {
    const collection = await this.getCollection();
    const bulkOps = updates.map(({ id, data }) => ({
      updateOne: {
        filter: { _id: new ObjectId(id), deletedAt: { $exists: false } },
        update: { $set: { ...data, updatedAt: new Date() } },
      },
    }));

    const result = await collection.bulkWrite(bulkOps);
    return result.modifiedCount;
  }

  async bulkDelete(ids: string[], hardDelete = false): Promise<number> {
    const collection = await this.getCollection();

    if (hardDelete) {
      const result = await collection.deleteMany({
        _id: { $in: ids.map((id) => new ObjectId(id)) },
      });
      return result.deletedCount;
    } else {
      const result = await collection.updateMany(
        { _id: { $in: ids.map((id) => new ObjectId(id)) } },
        { $set: { deletedAt: new Date() } }
      );
      return result.modifiedCount;
    }
  }

  async clone(id: string): Promise<Question | null> {
    const collection = await this.getCollection();
    const original = await collection.findOne({ id });

    if (!original) return null;

    const clone: Question = {
      ...this.mapFromMongo(original),
      id: this.generateId(),
      question: "(Clone) " + original.question,
      createdAt: new Date(),
    };

    await collection.insertOne(clone);
    return clone;
  }
}
