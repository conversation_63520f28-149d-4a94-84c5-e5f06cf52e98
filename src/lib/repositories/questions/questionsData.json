[{"id": "q-1", "type": "multipleChoice", "question": "Which of the following are programming languages?\n", "options": [{"id": "opt-A", "text": "Python\n"}, {"id": "opt-B", "text": "HTML\n"}, {"id": "opt-C", "text": "CSS\n"}, {"id": "opt-D", "text": "JavaScript\n"}], "correctAnswers": ["opt-A", "opt-D"], "shuffleOptions": true, "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "q-2", "type": "singleChoice", "question": "What is the capital city of France?\n", "options": [{"id": "opt-A", "text": "London\n"}, {"id": "opt-B", "text": "Paris\n"}, {"id": "opt-C", "text": "Berlin\n"}, {"id": "opt-D", "text": "Madrid\n"}], "correctAnswers": ["opt-B"], "shuffleOptions": true, "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "q-3", "type": "textInput", "question": "Explain the concept of polymorphism in object-oriented programming.\n", "reviewGuide": "Look for explanations involving method overriding, multiple forms, base and derived class behavior, and examples in object-oriented languages like Java or Python.", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "q-4", "type": "codeInput", "question": "Write a function in JavaScript that returns the square of a number.\n", "reviewGuide": {"expectedOutput": "Input: 4 → Output: 16", "requiredFunctions": ["function square(num)", "return num * num"], "comments": "The function should be named and return the square without using Math.pow."}, "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "q-5", "type": "imageBased", "question": "Identify the animal shown in the picture.\n", "image": "https://www.hdwallpapersfreedownload.com/uploads/large/animals/cat-download.jpg", "options": [{"id": "q-A", "text": "Tiger\n"}, {"id": "q-B", "text": "Lion\n"}, {"id": "q-C", "text": "<PERSON><PERSON>\n"}, {"id": "q-D", "text": "Cheetah\n"}], "correctAnswers": ["q-B"], "shuffleOptions": true, "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "q-6", "type": "audioBased", "question": "Listen to the audio and select the language spoken.\n", "audio": "https://samplelib.com/lib/preview/mp3/sample-3s.mp3", "options": [{"id": "q-A", "text": "French\n"}, {"id": "q-B", "text": "Spanish\n"}, {"id": "q-C", "text": "Italian\n"}, {"id": "q-D", "text": "Portuguese\n"}], "correctAnswers": ["q-B"], "shuffleOptions": true, "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "q-7", "type": "audioAnswer", "question": "Which audio clip correctly pronounces the word 'entrepreneur'?\n", "options": [{"id": "q-A", "audio": "https://samplelib.com/lib/preview/mp3/sample-3s.mp3"}, {"id": "q-B", "audio": "https://samplelib.com/lib/preview/mp3/sample-3s.mp3"}, {"id": "q-C", "audio": "https://samplelib.com/lib/preview/mp3/sample-3s.mp3"}, {"id": "q-D", "audio": "https://samplelib.com/lib/preview/mp3/sample-3s.mp3"}], "reviewGuide": "Identify the clip with native-like pronunciation: stress on last syllables, correct vowel sounds in 'entre' and 'neur'.", "shuffleOptions": true, "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "q-8", "type": "voiceInput", "question": "Please record yourself reading the following sentence: 'The quick brown fox jumps over the lazy dog.'\n", "reviewGuide": "Check for clear pronunciation, complete sentence, proper pacing, and correct word sequence.", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "q-9", "type": "fileUpload", "question": "Upload your resume in PDF format.\n", "reviewGuide": "Ensure the file is a valid PDF, contains professional resume structure (name, experience, education, skills), and no blank pages.", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "q-10", "type": "multipleChoice", "question": "Imagine you are a time traveler visiting the dawn of computer programming.\n\nWhich of these **languages** would you expect to find in the early days?\n\n- A language used for scientific calculations\n- A markup language for web pages\n- A styling language for design\n- A language that runs on browsers\n", "options": [{"id": "q-A", "text": "FORTRAN: The pioneer for scientific and numeric computing.\n"}, {"id": "q-B", "text": "HTML: Used for creating and structuring web pages.\n"}, {"id": "q-C", "text": "CSS: Used for designing how web pages look.\n"}, {"id": "q-D", "text": "JavaScript: The language that adds interactivity to websites.\n"}], "correctAnswers": ["q-A", "q-D"], "shuffleOptions": true, "createdAt": "2025-05-25T00:00:00.000Z", "updatedAt": "2025-05-25T00:00:00.000Z"}, {"id": "q-11", "type": "singleChoice", "question": "You stand at the crossroads of a famous city, known as the **City of Light**.\n\n> *'It’s not just about the Eiffel Tower or the Seine River, but the heart of culture and history.*'\n\nWhere are you?\n", "options": [{"id": "opt-A", "text": "London: The historic capital of England.\n"}, {"id": "opt-B", "text": "Paris: The romantic capital of France.\n"}, {"id": "opt-C", "text": "Berlin: Known for its art and nightlife.\n"}, {"id": "opt-D", "text": "Madrid: The lively heart of Spain.\n"}], "correctAnswers": ["opt-B"], "shuffleOptions": true, "createdAt": "2025-05-25T00:00:00.000Z", "updatedAt": "2025-05-25T00:00:00.000Z"}, {"id": "q-cat-mcq", "type": "imageBased", "question": "What is this cat thinking?", "image": "https://media.istockphoto.com/id/*********/photo/let-me-think.jpg?s=612x612&w=0&k=20&c=EC1bomKg5sfnbUdrI1T15k34BHDZvZQwkCPZophe8zw=", "options": [{"id": "opt-A", "text": "I've mastered <PERSON>'s Taxonomy."}, {"id": "opt-B", "text": "Where's my treat for finishing this test?"}, {"id": "opt-C", "text": "MCQs are purr-fectly effective."}, {"id": "opt-D", "text": "All of the answers."}], "correctAnswers": ["opt-D"], "shuffleOptions": true, "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "q-cat-closeup-tech", "type": "imageBased", "question": "These cats are intensely focused on something near the camera. As a tech whiz, what do you think it’s debugging?", "image": "https://www.hdwallpapersfreedownload.com/uploads/large/animals/cat-download.jpg", "options": [{"id": "opt-A", "text": "A bug in the new AI model."}, {"id": "opt-B", "text": "A glitch in the quantum computer simulator."}, {"id": "opt-C", "text": "Its own paw print in the touchscreen interface."}, {"id": "opt-D", "text": "The source code of the snack dispenser."}], "correctAnswers": ["opt-D"], "shuffleOptions": true, "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "q-10", "type": "textInput", "question": "Perhatikan data chart berikut ini dalam format JSON:\n\n```json\n{\n  \"type\": \"bar\",\n  \"data\": {\n    \"labels\": [\"<PERSON><PERSON>ri\", \"<PERSON><PERSON><PERSON>\", \"Mare<PERSON>\", \"April\"],\n    \"datasets\": [\n      {\n        \"label\": \"Penjualan Produk A\",\n        \"data\": [150, 200, 170, 220],\n        \"backgroundColor\": \"rgba(75, 192, 192, 0.6)\"\n      },\n      {\n        \"label\": \"Penjualan Produk B\",\n        \"data\": [180, 160, 190, 210],\n        \"backgroundColor\": \"rgba(153, 102, 255, 0.6)\"\n      }\n    ]\n  },\n  \"options\": {\n    \"responsive\": true,\n    \"plugins\": {\n      \"legend\": {\"position\": \"top\"},\n      \"title\": {\n        \"display\": true,\n        \"text\": \"Data Penjualan Produk per Bulan\"\n      }\n    }\n  }\n}\n```\n\nJelaskan isi chart tersebut dan informasi utama apa yang dapat kita ambil dari data ini.", "reviewGuide": "<PERSON>i penjelasan yang menyebutkan bahwa ini adalah chart bar yang membandingkan penjualan Produk A dan Produk B selama 4 bulan (Januari–April), dengan nilai penjualan yang berbeda, dan pengamatan tren seperti kenaikan atau penurunan penjualan pada tiap produk.", "createdAt": "2024-05-25T00:00:00.000Z", "updatedAt": "2024-05-25T00:00:00.000Z"}, {"id": "q-10", "type": "textInput", "question": "Perhatikan data chart berikut ini dalam format JSON:\n\n```chart\n{\n  \"type\": \"line\",\n  \"data\": [\n    { \"name\": \"Jan\", \"Sales\": 4000, \"Revenue\": 2400 },\n    { \"name\": \"Feb\", \"Sales\": 3000, \"Revenue\": 1398 },\n    { \"name\": \"Mar\", \"Sales\": 2000, \"Revenue\": 9800 },\n    { \"name\": \"Apr\", \"Sales\": 2780, \"Revenue\": 3908 },\n    { \"name\": \"May\", \"Sales\": 1890, \"Revenue\": 4800 },\n    { \"name\": \"Jun\", \"Sales\": 2390, \"Revenue\": 3800 },\n    { \"name\": \"Jul\", \"Sales\": 3490, \"Revenue\": 4300 }\n  ],\n  \"options\": {\n    \"xKey\": \"name\",\n    \"yKeys\": [\"Sales\", \"Revenue\"],\n    \"colors\": [\"#8884d8\", \"#82ca9d\"],\n    \"title\": \"Monthly Sales & Revenue\",\n    \"showGrid\": true,\n    \"showLegend\": true,\n    \"aspectRatio\": 2\n  }\n}\n```\n\nJelaskan isi chart tersebut dan informasi utama apa yang dapat kita ambil dari data ini.", "reviewGuide": "<PERSON>i penjelasan yang menyebutkan bahwa ini adalah chart garis (line chart) yang menampilkan data penjualan (Sales) dan pendapatan (Revenue) selama 7 bulan (Januari sampai Juli). Perhatikan tren naik/turun pada kedua metrik tersebut dan bagaimana keduanya saling berhubungan.", "createdAt": "2024-05-25T00:00:00.000Z", "updatedAt": "2024-05-25T00:00:00.000Z"}, {"id": "q-11", "type": "textInput", "question": "Perhatikan data chart berikut ini dalam format JSON:\n\n```chart\n{\n  \"type\": \"bar\",\n  \"data\": [\n    { \"name\": \"Q1\", \"Profit\": 4000, \"Loss\": 2400 },\n    { \"name\": \"Q2\", \"Profit\": 3000, \"Loss\": 1398 },\n    { \"name\": \"Q3\", \"Profit\": 2000, \"Loss\": 9800 },\n    { \"name\": \"Q4\", \"Profit\": 2780, \"Loss\": 3908 }\n  ],\n  \"options\": {\n    \"xKey\": \"name\",\n    \"yKeys\": [\"Profit\", \"Loss\"],\n    \"colors\": [\"#ff8042\", \"#0088fe\"],\n    \"title\": \"Quarterly Profit & Loss\",\n    \"showGrid\": true,\n    \"showLegend\": true,\n    \"aspectRatio\": 2,\n    \"stacked\": true\n  }\n}\n```\n\nJelaskan isi chart ini dan informasi penting apa saja yang dapat kita tarik dari data tersebut, terutama terkait tren keuntungan dan kerugian tiap kuartal.", "reviewGuide": "<PERSON>i penjelasan yang menyebutkan bahwa ini adalah chart bar bertumpuk (stacked bar chart) yang menampilkan data keuntungan (Profit) dan ker<PERSON><PERSON> (Loss) selama 4 kuartal (Q1 sampai Q4). Harus ada pengamatan tren perubahan profit dan loss, serta bagaimana keduanya berkontribusi terhadap total nilai setiap kuartal.", "createdAt": "2025-05-25T00:00:00.000Z", "updatedAt": "2025-05-25T00:00:00.000Z"}, {"id": "q-12", "type": "textInput", "question": "Perhatikan data chart berikut ini dalam format JSON:\n\n```chart\n{\n  \"type\": \"area\",\n  \"data\": [\n    { \"name\": \"Monday\", \"Visitors\": 2400, \"Signups\": 400 },\n    { \"name\": \"Tuesday\", \"Visitors\": 1398, \"Signups\": 300 },\n    { \"name\": \"Wednesday\", \"Visitors\": 9800, \"Signups\": 500 },\n    { \"name\": \"Thursday\", \"Visitors\": 3908, \"Signups\": 200 },\n    { \"name\": \"Friday\", \"Visitors\": 4800, \"Signups\": 300 },\n    { \"name\": \"Saturday\", \"Visitors\": 3800, \"Signups\": 100 },\n    { \"name\": \"Sunday\", \"Visitors\": 4300, \"Signups\": 350 }\n  ],\n  \"options\": {\n    \"xKey\": \"name\",\n    \"yKeys\": [\"Visitors\", \"Signups\"],\n    \"colors\": [\"#82ca9d\", \"#ffc658\"],\n    \"title\": \"Daily Visitors & Signups\",\n    \"showGrid\": true,\n    \"showLegend\": true,\n    \"aspectRatio\": 2,\n    \"stacked\": false\n  }\n}\n```\n\nJelaskan isi chart ini dan apa informasi utama yang bisa kita ambil mengenai jumlah pengunjung dan pendaftar tiap hari dalam seminggu.", "reviewGuide": "Cari penjelasan yang menyebutkan bahwa ini adalah area chart yang menun<PERSON>kkan jumlah pen<PERSON> (Visitors) dan pendaftar (Signups) harian selama seminggu. Harus ada analisis tren hari dengan kunjungan tertinggi dan terendah serta perbandingan antara Visitors dan Signups.", "createdAt": "2025-05-25T00:00:00.000Z", "updatedAt": "2025-05-25T00:00:00.000Z"}, {"id": "q-13", "type": "textInput", "question": "Perhatikan data chart berikut ini dalam format JSON:\n\n```chart\n{\n  \"type\": \"pie\",\n  \"data\": [\n    { \"category\": \"Chrome\", \"Users\": 68 },\n    { \"category\": \"Firefox\", \"Users\": 20 },\n    { \"category\": \"Safari\", \"Users\": 8 },\n    { \"category\": \"Others\", \"Users\": 4 }\n  ],\n  \"options\": {\n    \"xKey\": \"category\",\n    \"yKeys\": [\"Users\"],\n    \"colors\": [\"#0088fe\", \"#00c49f\", \"#ffbb28\", \"#ff8042\"],\n    \"title\": \"Browser Usage Share\",\n    \"showLegend\": true,\n    \"aspectRatio\": 1\n  }\n}\n```\n\nJelaskan isi chart ini dan apa kesimpulan utama mengenai pembagian pengguna browser berdasarkan data tersebut.", "reviewGuide": "<PERSON>i penjelasan yang menyebutkan bahwa ini adalah pie chart yang menggambarkan persentase penggunaan browser. Harus ada analisis bahwa Chrome memiliki pangsa pasar terbesar, Firefox dan <PERSON> di posisi berik<PERSON>nya, serta kategori Others paling kecil.", "createdAt": "2025-05-25T00:00:00.000Z", "updatedAt": "2025-05-25T00:00:00.000Z"}, {"id": "q-14", "type": "textInput", "question": "## 🟩 Soal Data Analis: Bar Chart — Analisis <PERSON>gin Keuntungan\n\n> 📊 **Perhatikan data chart berikut ini dalam format JSON:**\n\n```chart\n{\n  \"type\": \"bar\",\n  \"data\": [\n    { \"Month\": \"January\", \"Revenue\": 12000, \"Expenses\": 8000 },\n    { \"Month\": \"February\", \"Revenue\": 15000, \"Expenses\": 9000 },\n    { \"Month\": \"March\", \"Revenue\": 17000, \"Expenses\": 11000 },\n    { \"Month\": \"April\", \"Revenue\": 14000, \"Expenses\": 9500 },\n    { \"Month\": \"May\", \"Revenue\": 18000, \"Expenses\": 13000 }\n  ],\n  \"options\": {\n    \"xKey\": \"Month\",\n    \"yKeys\": [\"Revenue\", \"Expenses\"],\n    \"colors\": [\"#8884d8\", \"#ff7300\"],\n    \"title\": \"Monthly Revenue vs Expenses\",\n    \"showGrid\": true,\n    \"showLegend\": true,\n    \"aspectRatio\": 1.8,\n    \"stacked\": false\n  }\n}\n```\n\n**Pertanyaan:**\n\nJelaskan apa yang ditampilkan dalam chart ini.\n\n> 🔍 Fokus pada *margin keuntungan* tiap bulan dan identifikasi:\n> - Bulan dengan margin tertinggi dan terendah\n> - <PERSON><PERSON> atau fluktuasi\n> - Insight penting yang bisa digunakan untuk pengambilan keputusan keuangan", "reviewGuide": "Jawaban harus menyebutkan perbandingan antara Revenue dan Expenses untuk menghitung margin tiap bulan. Harus ada identifikasi bulan dengan margin tertinggi dan terendah, serta insight tren dari data untuk mendukung keputusan finansial.", "createdAt": "2025-05-25T00:00:00.000Z", "updatedAt": "2025-05-25T00:00:00.000Z"}, {"id": "q-15", "type": "textInput", "question": "##🟧 Soal Data Analis: Pie Chart — Proporsi Kategori\n\n> 🥧 **Perhatikan distribusi kategori produk berdasarkan data berikut:**\n\n```chart\n{\n  \"type\": \"pie\",\n  \"data\": [\n    { \"Category\": \"Electronics\", \"Value\": 45 },\n    { \"Category\": \"Clothing\", \"Value\": 25 },\n    { \"Category\": \"Home Appliances\", \"Value\": 15 },\n    { \"Category\": \"Books\", \"Value\": 10 },\n    { \"Category\": \"Other\", \"Value\": 5 }\n  ],\n  \"options\": {\n    \"nameKey\": \"Category\",\n    \"valueKey\": \"Value\",\n    \"colors\": [\"#0088FE\", \"#00C49F\", \"#FFBB28\", \"#FF8042\", \"#8884d8\"],\n    \"title\": \"Product Category Share\",\n    \"showLegend\": true\n  }\n}\n```\n\n**Pertanyaan:**\n\n💡 Apa insight utama yang bisa kamu ambil dari komposisi ini?\n\nJika kamu adalah analis di tim e-commerce, bagaimana kamu menyarankan strategi promosi atau pengembangan kategori berdasarkan proporsi tersebut?", "reviewGuide": "Jawaban ideal menyebutkan kategori dominan (Electronics) dan kategori minor (Books, Other). Harus ada saran strategi seperti promosi kategori kecil untuk diversifikasi atau peningkatan margin, serta pertimbangan mempertahankan dominasi kategori utama.", "createdAt": "2025-05-25T00:00:00.000Z", "updatedAt": "2025-05-25T00:00:00.000Z"}, {"id": "q-16", "type": "screenRecordInput", "question": "Please record yourself reading the following sentence: 'The quick brown fox jumps over the lazy dog.'\n", "reviewGuide": "Check for clear pronunciation, complete sentence, proper pacing, and correct word sequence.", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "q-17", "type": "webcamInput", "question": "Please record yourself reading the following sentence: 'The quick brown fox jumps over the lazy dog.'\n", "reviewGuide": "Check for clear pronunciation, complete sentence, proper pacing, and correct word sequence.", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}]