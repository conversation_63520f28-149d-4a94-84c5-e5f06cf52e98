import { Question, QuestionFilters } from "./types";

export interface QuestionRepository {
  getAll(filters?: QuestionFilters): Promise<Question[]>;
  getAllByTestId(testId: string, filters?: QuestionFilters): Promise<Question[]>;
  getById(id: string): Promise<Question | null>;
  create(payload: Omit<Question, "id" | "createdAt">): Promise<Question>;
  update(id: string, payload: Partial<Question>): Promise<Question | null>;
  delete(id: string): Promise<boolean>;
  bulkCreate(
    questions: Omit<Question, "id" | "createdAt">[]
  ): Promise<Question[]>;
  bulkDelete(ids: string[]): Promise<boolean>;
  clone(id: string): Promise<Question | null>;
}
