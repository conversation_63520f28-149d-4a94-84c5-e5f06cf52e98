import { Question, QuestionCreateInput, QuestionUpdateInput, QuestionQueryParams } from "./interface";

export interface QuestionDBRepository {
  getById(id: string, includeDeleted?: boolean): Promise<Question | null>;
  getAll(params?: QuestionQueryParams): Promise<{
    items: Question[];
    total: number;
  }>;
  create(data: QuestionCreateInput): Promise<Question>;
  update(id: string, data: QuestionUpdateInput): Promise<Question | null>;
  delete(id: string, hardDelete?: boolean): Promise<boolean>;
  restore(id: string): Promise<boolean>;
  bulkCreate(data: QuestionCreateInput[]): Promise<Question[]>;
  bulkUpdate(updates: { id: string; data: QuestionUpdateInput }[]): Promise<number>;
  bulkDelete(ids: string[], hardDelete?: boolean): Promise<number>;
  clone(id: string): Promise<Question | null>;
}