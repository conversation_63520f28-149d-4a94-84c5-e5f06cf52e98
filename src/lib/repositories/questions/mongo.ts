import { QuestionRepository } from "./QuestionsRepository";
import {
  Question,
  QuestionFilters,
} from "./types";
import { mongoDbInstance } from "@/src/lib/db/mongoClient";
import { MONGO_COLLECTIONS } from "../../db/mongoCollections";


function mapToQuestion(doc: any): Question {
  const { _id, ...rest } = doc;

  const base = {
    id: rest.id,
    question: rest.question,
    createdAt: rest.createdAt,
    updatedAt: rest.updatedAt,
    difficulty: rest.difficulty,
    tags: rest.tags ?? [],
    internal: rest.internal ?? {},
  };

  switch (rest.type) {
    case "multipleChoice":
      return {
        ...base,
        type: "multipleChoice",
        options: rest.options,
        correctAnswers: rest.correctAnswers,
      };
    case "singleChoice":
      return {
        ...base,
        type: "singleChoice",
        options: rest.options,
        correctAnswer: rest.correctAnswer,
      };
    case "textInput":
      return {
        ...base,
        type: "textInput",
        reviewGuide: rest.reviewGuide,
        caseSensitive: rest.caseSensitive,
      };
    case "codeInput":
      return {
        ...base,
        type: "codeInput",
        reviewGuide: rest.reviewGuide,
        language: rest.language,
        correctAnswer: rest.correctAnswer,
        testCases: rest.testCases,
      };
    case "imageBased":
      return {
        ...base,
        type: "imageBased",
        image: rest.image,
        options: rest.options,
        correctAnswers: rest.correctAnswers,
      };
    case "audioBased":
      return {
        ...base,
        type: "audioBased",
        audio: rest.audio,
        options: rest.options,
        correctAnswers: rest.correctAnswers,
      };
    case "audioAnswer":
      return {
        ...base,
        type: "audioAnswer",
        options: rest.options,
        reviewGuide: rest.reviewGuide,
      };
    case "voiceInput":
      return {
        ...base,
        type: "voiceInput",
        reviewGuide: rest.reviewGuide,
        maxDuration: rest.maxDuration,
        correctPhrases: rest.correctPhrases,
      };
    case "fileUpload":
      return {
        ...base,
        type: "fileUpload",
        reviewGuide: rest.reviewGuide,
        allowedFileTypes: rest.allowedFileTypes,
        maxFileSize: rest.maxFileSize,
      };
    default:
      throw new Error(`Unknown question type: ${rest.type}`);
  }
}


export class MongoQuestionRepository implements QuestionRepository {
  private async getCollection() {
    const client = await mongoDbInstance;
    const db = client.db();
    return db.collection(MONGO_COLLECTIONS.QUESTIONS);
  }

  private generateId() {
    return Math.random().toString(36).substring(2, 11);
  }

  private buildQuery(filters?: QuestionFilters): any {
    const query: any = {};
    if (!filters) return query;

    if (filters.difficulty) {
      query.difficulty = filters.difficulty;
    }

    if (filters.tags && filters.tags.length > 0) {
      query.tags = { $all: filters.tags };
    }

    if (filters.createdBy) {
      query.createdBy = filters.createdBy;
    }

    return query;
  }

  async getAll(filters?: QuestionFilters): Promise<Question[]> {
    const collection = await this.getCollection();
    const query = this.buildQuery(filters);
    const docs = await collection.find(query).toArray();
    return docs.map(mapToQuestion);
  }

  async getAllByTestId(testId: string, filters?: QuestionFilters): Promise<Question[]> {
    const collection = await this.getCollection();
    const query = { ...this.buildQuery(filters), testId };
    const docs = await collection.find(query).toArray();
    return docs.map(mapToQuestion);
  }

  async getById(id: string): Promise<Question | null> {
    const collection = await this.getCollection();
    const doc = await collection.findOne({ id });
    return doc ? mapToQuestion(doc) : null;
  }

  async create(payload: Omit<Question, "id" | "createdAt" | "updatedAt">): Promise<Question> {
    const collection = await this.getCollection();

    //@ts-ignore
    const newQuestion: Question = {
      ...payload,
      id: this.generateId(),
      createdAt: new Date(),
    };

    await collection.insertOne(newQuestion);
    return mapToQuestion(newQuestion);
  }

  async update(id: string, payload: Partial<Question>): Promise<Question | null> {
    const collection = await this.getCollection();

    const updateDoc = {
      $set: {
        ...payload,
        updatedAt: new Date(),
      },
    };

    const result = await collection.findOneAndUpdate({ id }, updateDoc, {
      returnDocument: "after",
    });

    //@ts-ignore
    return result ? mapToQuestion(result) : null;
  }

  async delete(id: string): Promise<boolean> {
    const collection = await this.getCollection();
    const result = await collection.deleteOne({ id });
    return result.deletedCount === 1;
  }

  async bulkCreate(questions: Omit<Question, "id" | "createdAt" | "updatedAt">[]): Promise<Question[]> {
    const collection = await this.getCollection();

    const newQuestions = questions.map((q) => ({
      ...q,
      id: this.generateId(),
      createdAt: new Date(),
      updatedAt: new Date(),
    }));

    await collection.insertMany(newQuestions);
    return newQuestions.map(mapToQuestion);
  }

  async bulkDelete(ids: string[]): Promise<boolean> {
    const collection = await this.getCollection();
    const result = await collection.deleteMany({ id: { $in: ids } });
    return result.deletedCount! > 0;
  }

  async clone(id: string): Promise<Question | null> {
    const collection = await this.getCollection();
    const original = await collection.findOne({ id });

    if (!original) return null;

    const clone: Question = {
      ...mapToQuestion(original),
      id: this.generateId(),
      question: "(Clone) " + original.question,
      createdAt: new Date(),
    };

    await collection.insertOne(clone);
    return clone;
  }
}
