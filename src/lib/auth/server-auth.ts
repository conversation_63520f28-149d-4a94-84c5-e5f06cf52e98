import { headers } from "next/headers";
import { authBusinessLogic } from "@/src/lib/repositories/businessLogics";
import { User } from "@/src/lib/types/base";

export async function getCurrentUser(): Promise<User | null> {
  try {
    const headersList = headers();
    const token = headersList.get('x-internal-token');
    
    if (!token) {
      return null;
    }

    const validateResult = await authBusinessLogic.validateToken(token);
    return validateResult.user || null;
  } catch (error) {
    console.error("Failed to get current user:", error);
    return null;
  }
}
