import { Question } from "./question-types";

export interface Instructor {
  name: string;
  avatar: string;
}

export interface Test {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: "Easy" | "Medium" | "Hard" | "Expert";
  benefits: string[];
  usersCount: number;
  successRate: number;
  duration: string;
  image: string;
  instructor: Instructor;
  questions?: Question[];
}

export interface TestBuilder {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty:
    | "Beginner"
    | "Easy"
    | "Medium"
    | "Intermediate"
    | "Advanced"
    | "Hard"
    | "Expert";
  benefits: string[];
  usersCount: number;
  successRate: number;
  duration: string;
  image: string;
  instructor: {
    name: string;
    avatar: string;
  };
  date: Date;
  startTime: string;
  endTime: string;
  status: "done" | "in progress" | "cancelled" | "DRAFT";
  questions: number;
  participants: Instructor[];
}


export interface TestSession {
  id: string
  studentName: string
  studentEmail: string
  startedAt: string
  completedAt?: string
  status: "in-progress" | "completed" | "needs-review" | "reviewed"
  score?: number
  totalQuestions: number
  answeredQuestions: number
  reviewableQuestions: number // Questions that need human review
  test_id: number
}