"use client";

/* eslint-disable @next/next/no-img-element */
import { useRouter } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Users } from "lucide-react";
import { RoadmapData, RoadmapLevel } from "@/src/lib/repositories/public-roadmaps";

const getLevelColor = (level: RoadmapLevel) => {
  switch (level) {
    case "BEGINNER":
      return "bg-green-100 text-green-800";
    case "INTERMEDIATE":
      return "bg-blue-100 text-blue-800";
    case "ADVANCED":
      return "bg-purple-100 text-purple-800";
    case "FULL_JOURNEY":
      return "bg-pink-100 text-pink-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export default function RoadmapListItem({ roadmap }: { roadmap: RoadmapData }) {
  const router = useRouter();

  const handleViewRoadmap = () => {
    router.push(`/roadmaps/${roadmap.id}`);
  };

  return (
    <div className="flex w-full gap-6 p-4 border-b border-gray-200 hover:bg-gray-50 transition">
      {/* Thumbnail */}
      <div className="min-w-[160px] max-w-[160px] h-28 overflow-hidden rounded-md border">
        <img
          src={roadmap.cover}
          alt={roadmap.title}
          className="w-full h-full object-cover"
        />
      </div>

      {/* Content */}
      <div className="flex-1 flex flex-col justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            {roadmap.title}
          </h3>
          <p className="text-sm text-gray-600 mb-2">{roadmap.description}</p>

          {/* Levels */}
          <div className="flex flex-wrap gap-2 mb-2">
            {roadmap.levels.map((level, index) => (
              <Badge
                key={index}
                className={`${getLevelColor(level)} text-xs py-0.5 px-2`}
              >
                {level}
              </Badge>
            ))}
          </div>

          {/* Tags */}
          {roadmap.tags && roadmap.tags.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-2">
              {roadmap.tags.map((tag, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="bg-slate-50 text-slate-700 border-slate-200 py-0.5 px-2 text-xs"
                >
                  {tag}
                </Badge>
              ))}
            </div>
          )}

          {/* User Count */}
          <div className="flex items-center gap-2 text-xs text-gray-600 mt-1">
            <Users className="w-3 h-3" />
            <span>{roadmap.usersCount.toLocaleString()} learners joined</span>
          </div>
        </div>

        {/* Footer: Creator + Action */}
        <div className="flex items-center justify-between mt-4">
          <div className="flex items-center gap-2">
            <Avatar className="h-7 w-7">
              <AvatarImage
                src={roadmap.creator.avatar || "/placeholder.svg"}
                alt={roadmap.creator.name}
              />
              <AvatarFallback>{roadmap.creator.name.charAt(0)}</AvatarFallback>
            </Avatar>
            <span className="text-sm text-gray-700">{roadmap.creator.name}</span>
          </div>

          <Button
            onClick={handleViewRoadmap}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-1 text-sm"
          >
            View Roadmap
          </Button>
        </div>
      </div>
    </div>
  );
}
