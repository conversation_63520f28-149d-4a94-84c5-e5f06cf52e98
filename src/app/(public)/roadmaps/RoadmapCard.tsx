/* eslint-disable @next/next/no-img-element */
"use client";

import Image from "next/image";
import { useRouter } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Users, Layers } from "lucide-react";
import { RoadmapData, RoadmapLevel } from "@/src/lib/repositories/public-roadmaps";

const getLevelColor = (level: RoadmapLevel) => {
  switch (level) {
    case "BEGINNER":
      return "bg-green-100 text-green-800";
    case "INTERMEDIATE":
      return "bg-blue-100 text-blue-800";
    case "ADVANCED":
      return "bg-purple-100 text-purple-800";
    case "FULL_JOURNEY":
      return "bg-pink-100 text-pink-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export default function RoadmapCard({ roadmap }: { roadmap: RoadmapData }) {
  const router = useRouter();

  const handleOpenRoadmap = () => {
    router.push(`/roadmaps/${roadmap.id}`);
  };

  return (
    <div className="bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100 hover:shadow-md transition-shadow flex flex-col relative">
      <div className="relative h-48 w-full overflow-hidden rounded-t-xl">
        <img
          src={roadmap.cover}
          alt={roadmap.title}
          className="w-full h-full object-cover"
        />

        <div className="absolute top-4 right-4 flex flex-wrap gap-1 z-10">
          {roadmap.levels.map((level, idx) => (
            <Badge
              key={idx}
              className={`${getLevelColor(level)} py-1 px-2 rounded-full text-xs`}
            >
              {level}
            </Badge>
          ))}
        </div>
      </div>

      <div className="p-6 flex flex-col flex-1">
        <h3 className="text-xl font-bold mb-2">{roadmap.title}</h3>
        <p className="text-gray-600 mb-4">{roadmap.description}</p>

        {roadmap.tags && roadmap.tags.length > 0 && (
          <div className="mb-4 flex flex-wrap gap-2">
            {roadmap.tags.map((tag, idx) => (
              <Badge
                key={idx}
                variant="outline"
                className="bg-slate-50 text-slate-700 border-slate-200 py-1 px-2 rounded-full text-xs"
              >
                {tag}
              </Badge>
            ))}
          </div>
        )}

        <div className="flex justify-between items-center mt-auto">
          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8">
              <AvatarImage
                src={roadmap.creator.avatar || "/placeholder.svg"}
                alt={roadmap.creator.name}
              />
              <AvatarFallback>{roadmap.creator.name.charAt(0)}</AvatarFallback>
            </Avatar>
            <span className="text-sm">{roadmap.creator.name}</span>
          </div>

          <Button
            onClick={handleOpenRoadmap}
            className="bg-blue-600 hover:bg-blue-700 text-white"
            aria-label="Open roadmap"
          >
            View Roadmap
          </Button>
        </div>

        <div className="mt-3 flex items-center gap-1 text-gray-600 text-sm">
          <Users className="h-4 w-4" />
          {roadmap.usersCount.toLocaleString()} learners joined
        </div>
      </div>
    </div>
  );
}
