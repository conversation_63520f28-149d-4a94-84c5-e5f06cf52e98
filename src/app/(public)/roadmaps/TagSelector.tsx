import React from "react";

interface TagSelectorProps {
  selected: string;
  onChange: (selected: string) => void;
  tags: string[];
}

export default function TagSelector({
  selected,
  onChange,
  tags,
}: TagSelectorProps) {
  return (
    <div className="flex flex-wrap gap-3 justify-center py-2">
      {["All", ...tags].map((tag) => {
        const isSelected = selected === tag || (tag === "All" && selected === "");
        return (
          <button
            key={tag}
            onClick={() => onChange(tag)}
            className={`px-4 py-2 rounded-full border transition-colors duration-200
              ${
                isSelected
                  ? "bg-blue-600 text-white border-blue-600"
                  : "bg-white text-gray-700 border-gray-300 hover:bg-gray-100"
              }
            `}
            type="button"
          >
            {tag}
          </button>
        );
      })}
    </div>
  );
}
