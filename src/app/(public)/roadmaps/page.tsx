import { repo } from "@/src/lib/repositories/public-roadmaps";
import PublicRoadmapsClient from "./PublicRoadmapsClient";

interface Props {
  searchParams: {
    query?: string;
    tags?: string;
  };
}

export default async function PublicRoadmapsPage({ searchParams }: Props) {
  const query = searchParams.query?.trim() || "";
  const tag = searchParams.tags || "All";

  const allRoadmaps = await repo.getAll({
    query: query || undefined,
    tags: tag !== "All" ? [tag] : undefined,
  });

  const allTags = await repo.getAllTags(); // Assuming you update repo to provide this instead of categories

  return (
    <PublicRoadmapsClient
      initialRoadmaps={allRoadmaps}
      availableTags={allTags} // you may want to rename this prop to `tags` for clarity
      initialQuery={query}
      initialTag={tag} // rename this prop to initialTag if you want to keep consistent naming
    />
  );
}
