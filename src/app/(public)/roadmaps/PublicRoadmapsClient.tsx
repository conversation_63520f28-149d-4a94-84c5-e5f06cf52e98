"use client";

import { useRouter } from "next/navigation";
import { Search, LayoutGrid, List } from "lucide-react";
import { Input } from "@/components/ui/input";
import RoadmapCard from "./RoadmapCard";
import RoadmapListItem from "./RoadmapListItem";
import CtaSection from "./CtaSection";
import FeaturesSection from "./FeaturesSection";
import { useMemo, useState } from "react";
import { RoadmapData } from "@/src/lib/repositories/public-roadmaps";
import en from "./locales/en.json";
import { useLocalization } from "@/src/hooks/useLocalization/client";
import TagSelector from "./TagSelector";

interface Props {
  initialRoadmaps: RoadmapData[];
  availableTags: string[]; // updated prop name
  initialQuery: string;
  initialTag: string;
}

export default function PublicRoadmapsClient({
  initialRoadmaps,
  availableTags,
  initialQuery,
  initialTag,
}: Props) {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState(initialQuery);
  const [selectedTag, setSelectedTag] = useState(initialTag);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  const { t } = useLocalization("public_roadmap", { en });

  const filteredRoadmaps = useMemo(() => {
    return initialRoadmaps.filter((roadmap) => {
      const matchesSearch =
        roadmap.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        roadmap.description.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesTag =
        selectedTag === t("all") ||
        (roadmap.tags?.includes(selectedTag) ?? false);

      return matchesSearch && matchesTag;
    });
  }, [initialRoadmaps, searchQuery, selectedTag, t]);

  const pushQuery = (query: string, tag: string) => {
    const params = new URLSearchParams();
    if (query.trim()) params.set("query", query.trim());
    if (tag !== t("all")) params.set("tags", tag);
    router.push(`/roadmaps?${params.toString()}`);
  };

  return (
    <main className="container mx-auto px-4 py-8 max-w-7xl bg-gray-50">
      <h1 className="text-3xl md:text-4xl font-bold mb-4 text-center">
        {t("discover_public_roadmaps")}
      </h1>
      <p className="text-gray-600 max-w-3xl mx-auto mb-8 text-center">
        {t("description")}
      </p>

      <div className="max-w-3xl mx-auto mb-6">
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-5 w-5" />
          <Input
            type="search"
            placeholder={t("search_placeholder")}
            className="pl-10 h-12 rounded-lg"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter") pushQuery(searchQuery, selectedTag);
            }}
            aria-label={t("search_aria_label")}
          />
        </div>

        <div className="flex justify-between items-center mb-4">
          <TagSelector
            tags={availableTags}
            selected={selectedTag}
            onChange={(tag) => {
              setSelectedTag(tag);
              pushQuery(searchQuery, tag);
            }}
          />
        </div>
      </div>

      <p className="text-gray-600 mb-6 text-center">
        {t("showing_roadmaps")}{" "}
        <span className="font-semibold">{filteredRoadmaps.length}</span>{" "}
        {filteredRoadmaps.length !== 1 ? t("roadmaps_plural") : t("roadmap_singular")}
      </p>

      <div className="w-full flex gap-2 justify-end mb-6">
        <button
          onClick={() => setViewMode("grid")}
          className={`p-2 rounded-md ${
            viewMode === "grid" ? "bg-gray-200" : "hover:bg-gray-100"
          }`}
          aria-label="Grid View"
        >
          <LayoutGrid className="h-5 w-5" />
        </button>
        <button
          onClick={() => setViewMode("list")}
          className={`p-2 rounded-md ${
            viewMode === "list" ? "bg-gray-200" : "hover:bg-gray-100"
          }`}
          aria-label="List View"
        >
          <List className="h-5 w-5" />
        </button>
      </div>

      {/* List/Grid View */}
      <section
        className={
          viewMode === "grid"
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mx-auto mb-6"
            : "flex flex-col gap-4 mx-auto mb-6"
        }
      >
        {filteredRoadmaps.length > 0 ? (
          filteredRoadmaps.map((roadmap) =>
            viewMode === "grid" ? (
              <RoadmapCard key={roadmap.id} roadmap={roadmap} />
            ) : (
              <RoadmapListItem key={roadmap.id} roadmap={roadmap} />
            )
          )
        ) : (
          <p className="col-span-full text-center text-gray-500">
            {t("no_roadmaps_found")}
          </p>
        )}
      </section>

      <CtaSection />
      <FeaturesSection />
    </main>
  );
}
