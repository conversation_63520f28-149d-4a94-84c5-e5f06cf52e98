// app/roadmaps/[id]/page.tsx

import { repo } from "@/src/lib/repositories/public-roadmaps";
import { notFound } from "next/navigation";
import RoadmapDetailClient from "./client";

interface Props {
  params: { id: string };
}

export default async function RoadmapDetailPage({ params }: Props) {
  const roadmapId = params.id;

  const result = await repo.getById(roadmapId);
  if (!result.ok) return notFound();

  const roadmap = result.value;

  return <RoadmapDetailClient roadmap={roadmap} timeline={roadmap.timeline} />;
}
