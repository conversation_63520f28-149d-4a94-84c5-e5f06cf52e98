/* eslint-disable @next/next/no-img-element */
"use client";

import { Button } from "@/components/ui/button";
import {
  RoadmapData,
  RoadmapStage,
} from "@/src/lib/repositories/public-roadmaps";
import Link from "next/link";

interface Props {
  roadmap: RoadmapData;
  timeline: RoadmapStage[];
}

export default function RoadmapDetailClient({ roadmap, timeline }: Props) {
  return (
    <main className="max-w-5xl mx-auto px-6 py-10">
      {roadmap.cover && (
        <RoadmapCover src={roadmap.cover} alt={roadmap.title} />
      )}
      <h1 className="text-3xl font-bold mb-2">{roadmap.title}</h1>
      <RoadmapMeta roadmap={roadmap} />
      <MotivationBanner />
      <p className="text-gray-700 mb-10">{roadmap.description}</p>
      <Timeline timeline={timeline} />
    </main>
  );
}

function RoadmapCover({ src, alt }: { src: string; alt: string }) {
  return (
    <div className="mb-6">
      <img
        src={src}
        alt={`${alt} cover`}
        className="w-full h-64 object-cover rounded-md shadow"
      />
    </div>
  );
}

function RoadmapMeta({ roadmap }: { roadmap: RoadmapData }) {
  return (
    <div className="flex flex-wrap items-center gap-6 text-sm text-gray-600 mb-6">
      <div className="flex items-center gap-2">
        <img
          src={roadmap.creator.avatar}
          alt={roadmap.creator.name}
          className="w-8 h-8 rounded-full object-cover"
        />
        <span className="font-medium">{roadmap.creator.name}</span>
      </div>

      <div className="flex items-center gap-1">
        <span className="text-yellow-500">★</span>
        <span>4.8</span>
        <span className="text-gray-400">/ 5.0</span>
      </div>

      <div className="flex items-center gap-1">
        <svg
          className="w-4 h-4 text-gray-400"
          fill="none"
          stroke="currentColor"
          strokeWidth="1.5"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M17 20h5v-2a4 4 0 00-4-4h-1m-6 6H2v-2a4 4 0 014-4h1m3-4a4 4 0 100-8 4 4 0 000 8zm10 0a4 4 0 100-8 4 4 0 000 8z"
          />
        </svg>
        <span>{roadmap.usersCount.toLocaleString()} learners</span>
      </div>
    </div>
  );
}

function MotivationBanner() {
  return (
    <div className="bg-gradient-to-r from-green-100 to-green-200 border border-green-300 rounded-xl p-6 mb-10 flex flex-col sm:flex-row sm:items-center sm:justify-between shadow-sm">
      <div>
        <h3 className="text-lg font-semibold text-green-800 mb-1">
          You can finish this roadmap in 3 months 🚀
        </h3>
        <p className="text-sm text-green-700">
          Complete all tests and start applying for backend engineering jobs
          with confidence.
        </p>
      </div>
      <div className="mt-4 sm:mt-0">
        <Link href="#timeline">
          <Button className="bg-green-600 hover:bg-green-700 text-white text-sm px-5 py-2">
            Start Learning Now
          </Button>
        </Link>
      </div>
    </div>
  );
}

function Timeline({ timeline }: { timeline: RoadmapStage[] }) {
  return (
    <section className="border-l-2 border-gray-300 ml-4">
      {timeline.map((stage) => (
        <div key={stage.level} className="relative mb-10 pl-6">
          <div className="absolute -left-3 w-6 h-6 bg-blue-500 rounded-full border-4 border-white shadow"></div>

          <h2 className="text-xl font-semibold">{stage.title}</h2>
          <p className="text-gray-500 mb-2">{stage.description}</p>

          <ul className="list-disc ml-5 mb-4 text-sm text-gray-700">
            {stage.skills.map((skill, idx) => (
              <li key={idx}>{skill}</li>
            ))}
          </ul>

          <div className="bg-gray-100 p-4 rounded-md">
            <h3 className="font-medium mb-4 text-gray-800">Assessments</h3>
            <div className="space-y-3">
              {stage.tests.map((test) => (
                <TestCard key={test.id} test={test} />
              ))}
            </div>
          </div>
        </div>
      ))}
    </section>
  );
}

function TestCard({
  test,
}: {
  test: {
    id: string;
    title: string;
    type: string;
    description?: string;
    questionsCount?: number;
    passScore?: number;
    reviewRequired?: boolean;
    course?: {
      provider?: string;
      url: string;
    }; // New field: external course URL
  };
}) {
  return (
    <div className="bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200">
      <div className="p-5 flex flex-col gap-3 sm:flex-row sm:justify-between sm:items-center">
        <div className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <span className="text-base font-semibold text-gray-800">
              {test.title}
            </span>
            <span
              className={`text-xs px-2 py-0.5 rounded-full ${
                test.type === "quiz"
                  ? "bg-blue-100 text-blue-700"
                  : "bg-purple-100 text-purple-700"
              }`}
            >
              {test.type}
            </span>
          </div>

          {test.description && (
            <p className="text-sm text-gray-500">{test.description}</p>
          )}

          {test.type === "quiz" && (
            <p className="text-xs text-gray-500">
              {test.questionsCount} questions · Pass score: {test.passScore}%
            </p>
          )}

          {test.reviewRequired && (
            <span className="inline-block w-max text-xs mt-1 px-2 py-1 bg-yellow-100 text-yellow-800 rounded font-medium">
              Manual review required
            </span>
          )}
        </div>

        <div className="flex flex-col sm:flex-row gap-2 mt-3 sm:mt-0">
          {test.course && (
            <a
              href={test.course.url}
              target="_blank"
              rel="noopener noreferrer"
              className="w-full sm:w-auto"
            >
              <Button className="bg-indigo-600 hover:bg-indigo-700 text-white text-sm px-4 py-2 w-full">
                Take {test.course.provider} Course
              </Button>
            </a>
          )}
          <Link href={`/test-landing/${test.id}`} className="w-full sm:w-auto">
            <Button className="bg-green-600 hover:bg-green-700 text-white text-sm px-4 py-2 w-full">
              Start Test
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
