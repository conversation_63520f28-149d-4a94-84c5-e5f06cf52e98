/* eslint-disable @next/next/no-img-element */
"use client";

import { useRouter } from "next/navigation";
import { useState } from "react";
import { Test, Topic } from "@/src/lib/repositories/tests/interface";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Users, Clock, ChevronDown, ChevronUp } from "lucide-react";
import TestCreatorInfo from "./TestCreatorInfo";
import TestCard from "../../tests/TestCard";
import { CreatorProfile } from "@/src/lib/repositories/creators";

interface BuyTestDetailClientProps {
  test: Test;
  topics?: Topic[];
  creatorDetail: CreatorProfile;
  similarFreeTests: Test[];
}

const difficultyColors = {
  EASY: "bg-green-100 text-green-800",
  INTERMEDIATE: "bg-blue-100 text-blue-800",
  ADVANCED: "bg-purple-100 text-purple-800",
};

// Internal component: Test Cover + info card
function TestCoverCard({ test }: { test: Test }) {
  return (
    <div className="relative rounded-xl overflow-hidden shadow-2xl">
      <img
        src={test.cover}
        alt={test.title}
        className="w-full h-64 object-cover brightness-75"
      />
      <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent"></div>
      <div className="absolute bottom-6 left-6 right-6 text-white">
        <h1 className="text-3xl font-extrabold drop-shadow-lg">{test.title}</h1>
        <p className="mt-2 max-w-2xl drop-shadow-md">{test.description}</p>

        <div className="mt-4 flex flex-wrap gap-2">
          {(test.categories || []).map((cat) => (
            <Badge
              key={cat}
              variant="outline"
              className="border-white text-white"
            >
              {cat}
            </Badge>
          ))}
          {(test.difficulties || []).map((diff) => (
            <Badge
              key={diff}
              className={`${difficultyColors[diff] ?? "bg-gray-100 text-gray-800"}`}
            >
              {diff}
            </Badge>
          ))}
        </div>

        <div className="mt-4 flex items-center space-x-6 text-sm font-semibold drop-shadow-md">
          <div className="flex items-center gap-1">
            <Users className="w-5 h-5" />
            <span>{test.usersCount.toLocaleString()} Users</span>
          </div>
          <div className="flex items-center gap-1">
            <Clock className="w-5 h-5" />
            <span>{Math.round((test.durationInSeconds || 0) / 60)} min</span>
          </div>
        </div>
      </div>
    </div>
  );
}

// Internal component: Unavailable message
function UnavailableMessage({ title }: { title: string }) {
  return (
    <div className="text-center max-w-3xl mx-auto space-y-4">
      <h2 className="text-2xl font-bold text-red-600">
        Sorry! This test is currently unavailable
      </h2>
      <p className="text-gray-700">
        The premium test <strong>“{title}”</strong> has reached its capacity or
        is temporarily unavailable for purchase.
      </p>
      <p className="text-gray-700">
        But don't worry — we’ve selected some{" "}
        <strong>similar free tests</strong> for you to try first!
      </p>
    </div>
  );
}

// Internal component: Collapsible topics list
function TopicsList({
  topics,
  openTopics,
  toggleTopic,
}: {
  topics: Topic[];
  openTopics: number[];
  toggleTopic: (index: number) => void;
}) {
  return (
    <section className="max-w-4xl mx-auto bg-white rounded-xl shadow-lg p-8">
      <h3 className="text-2xl font-bold mb-6 text-gray-800">
        🧠 Topics Covered in This Test
      </h3>

      <div className="space-y-6">
        {topics.map((topic, idx) => {
          const isOpen = openTopics.includes(idx);
          return (
            <div
              key={idx}
              className="bg-gray-50 rounded-lg p-5 shadow-sm hover:shadow-md transition cursor-pointer border border-gray-200"
              onClick={() => toggleTopic(idx)}
            >
              <div className="flex justify-between items-center">
                <h4 className="text-xl font-semibold text-gray-900">
                  {topic.title}
                </h4>
                {isOpen ? (
                  <ChevronUp className="w-6 h-6 text-gray-500" />
                ) : (
                  <ChevronDown className="w-6 h-6 text-gray-500" />
                )}
              </div>

              {isOpen && (
                <div className="mt-4 text-gray-700 space-y-4">
                  <p className="text-base">{topic.description}</p>

                  {topic.subtopics && topic.subtopics.length > 0 && (
                    <ul className="mt-4 space-y-4">
                      {topic.subtopics.map((sub, subIdx) => (
                        <li
                          key={subIdx}
                          className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm"
                        >
                          <h5 className="text-md font-semibold text-indigo-600 mb-1">
                            {sub.title}
                          </h5>
                          <p className="text-sm text-gray-700">{sub.description}</p>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              )}
            </div>
          );
        })}
      </div>
    </section>
  );
}

export default function BuyTestDetailClient({
  test,
  topics,
  creatorDetail,
  similarFreeTests,
}: BuyTestDetailClientProps) {
  const router = useRouter();
  const [openTopics, setOpenTopics] = useState<number[]>([]);

  const toggleTopic = (index: number) => {
    setOpenTopics((prev) =>
      prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]
    );
  };

  return (
    <div className="bg-gray-50 px-6 py-8 flex justify-center">
      <div className="max-w-7xl w-full flex gap-12 top-20">
        {/* Left content: Test info + achievements + similar tests */}
        <div className="flex-1 space-y-10">
          <TestCoverCard test={test} />

          <UnavailableMessage title={test.title} />

          {topics && topics.length > 0 && (
            <TopicsList
              topics={topics}
              openTopics={openTopics}
              toggleTopic={toggleTopic}
            />
          )}

          {/* Similar Free Tests Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {similarFreeTests.map((freeTest) => (
              <TestCard key={freeTest.id} test={freeTest} isCompact />
            ))}
          </div>

          {/* Explore All Button */}
          <div className="text-center">
            <Button
              onClick={() => router.push("/tests")}
              className="bg-green-600 hover:bg-green-700 text-white px-8 py-3 font-semibold shadow-lg"
            >
              Explore All Free Tests
            </Button>
          </div>
        </div>

        {/* Right sidebar: Test creator info */}
        <TestCreatorInfo creatorDetail={creatorDetail} />
      </div>
    </div>
  );
}
