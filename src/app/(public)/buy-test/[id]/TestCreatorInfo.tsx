/* eslint-disable @next/next/no-img-element */
import { Badge } from "@/components/ui/badge";
import { Briefcase, MessageCircle, UserPlus } from "lucide-react";

interface TestCreatorInfoProps {
  creatorDetail: {
    name: string;
    photo: string;
    bio: string;
    skills: string[];
    availability?: string[]; // available services/offers
    social?: { type: string; url: string }[];
  };
}

export default function TestCreatorInfo({ creatorDetail }: TestCreatorInfoProps) {
  return (
    <aside className="w-80 bg-white rounded-lg shadow-lg p-6 sticky top-32 self-start">
      <h3 className="text-xl font-bold mb-4 text-gray-800">About the Creator</h3>

      <img
        src={creatorDetail.photo}
        alt={creatorDetail.name}
        className="w-24 h-24 rounded-full mx-auto mb-4 object-cover ring-2 ring-blue-500"
      />
      <h4 className="text-lg font-semibold text-center mb-2 text-gray-900">
        {creatorDetail.name}
      </h4>
      <p className="text-gray-600 mb-4 text-center text-sm">{creatorDetail.bio}</p>

      {creatorDetail.availability && creatorDetail.availability.length > 0 && (
        <div className="mb-6">
          <h5 className="font-semibold mb-2 text-green-700 flex items-center gap-1">
            <UserPlus className="w-4 h-4" />
            Open to
          </h5>
          <div className="flex flex-wrap gap-2">
            {creatorDetail.availability.map((item, idx) => (
              <span
                key={idx}
                className="bg-green-100 text-green-800 text-xs font-medium px-3 py-1 rounded-full border border-green-200"
              >
                {item}
              </span>
            ))}
          </div>
        </div>
      )}

      <div className="mb-6">
        <h5 className="font-semibold mb-2 text-gray-800">Skills</h5>
        <div className="flex flex-wrap gap-2">
          {creatorDetail.skills.map((skill) => (
            <Badge key={skill} variant="secondary">
              {skill}
            </Badge>
          ))}
        </div>
      </div>

      {creatorDetail.social && creatorDetail.social.length > 0 && (
        <div className="mt-6">
          <h5 className="font-semibold mb-2 text-gray-800 flex items-center gap-1">
            <MessageCircle className="w-4 h-4" />
            Connect
          </h5>
          <ul className="space-y-1 text-sm">
            {creatorDetail.social.map(({ type, url }) => (
              <li key={type}>
                <a
                  href={url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                >
                  {type}
                </a>
              </li>
            ))}
          </ul>
        </div>
      )}
    </aside>
  );
}
