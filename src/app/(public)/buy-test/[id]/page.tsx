import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import BuyTestDetailClient from "./BuyTestDetailClient";
import { Test } from "@/src/lib/repositories/tests/interface";
import { TestDBRepository } from "@/src/lib/repositories/tests/index";
import {
  repo as creatorRepo,
} from "@/src/lib/repositories/creators";

interface Props {
  params: {
    id: string;
  };
}

export default async function BuyTestPage({ params }: Props) {
  const result = await TestDBRepository.getById(params.id);

  if (!result) {
    return (
      <div className="bg-gray-100 flex flex-col items-center justify-center px-6 py-16 text-center">
        <div className="max-w-lg bg-white rounded-xl shadow-lg p-10">
          <div
            aria-hidden="true"
            className="text-7xl mb-6 animate-pulse"
            role="img"
            aria-label="Warning"
          >
            ⚠️
          </div>
          <h1 className="text-3xl font-extrabold text-gray-900 mb-4">
            Test Session Not Found
          </h1>
          <p className="text-gray-600 mb-8 leading-relaxed">
            We couldn’t find the test you’re looking for. It may have been
            removed, is no longer available, or the ID you entered is incorrect.
          </p>
          <Link href="/">
            <Button
              variant="default"
              className="px-8 py-3 text-lg font-semibold shadow-md hover:shadow-xl transition-shadow duration-300"
            >
              Back to Home
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  // Example condition to toggle availability
  const isUnavailable = true;

  if (!isUnavailable) {
    return (
      <div className="bg-white flex items-center justify-center text-center px-6 py-16">
        <div>
          <h2 className="text-2xl font-semibold text-green-600 mb-4">
            This test is available!
          </h2>
          <p className="mb-6 text-gray-700">
            You can proceed to purchase or start this test now.
          </p>
          {/* Uncomment if you want to link to test landing page */}
          {/* <Link href={`/test-landing/${testData.id}`}>
            <Button>Go to Test Page</Button>
          </Link> */}
        </div>
      </div>
    );
  }

  const testData = result;

  const similarTests = await TestDBRepository.getSimilarTests({
    categories: testData.categories,
    query: testData.title,
  });

  // Use creator id from test data
  const creatorDetailResult = await creatorRepo.getById(testData.creator.id);

  if (!creatorDetailResult.ok) {
    return (
      <div className="bg-gray-100 flex flex-col items-center justify-center px-6 py-16 text-center">
        <div className="max-w-lg bg-white rounded-xl shadow-lg p-10">
          <h2 className="text-2xl font-semibold text-red-600 mb-4">
            Creator Not Found
          </h2>
          <p className="text-gray-600 mb-8 leading-relaxed">
            Sorry, we couldn’t find information about the creator of this test.
          </p>
          <Link href="/">
            <Button
              variant="default"
              className="px-8 py-3 text-lg font-semibold shadow-md hover:shadow-xl transition-shadow duration-300"
            >
              Back to Home
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  const creatorDetail = creatorDetailResult.value;

  return (
    <div className="bg-gray-50">
      <BuyTestDetailClient
        test={testData}
        topics={testData.topics}
        creatorDetail={creatorDetail}
        similarFreeTests={similarTests.items.filter((a) => !a.premium).slice(0, 3)}
      />
    </div>
  );
}
