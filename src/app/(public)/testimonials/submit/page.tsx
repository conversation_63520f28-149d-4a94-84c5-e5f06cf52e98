"use client";

import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { CheckCircle } from "lucide-react";

export default function SubmitTestimonialPage() {
  const [name, setName] = useState("");
  const [role, setRole] = useState("");
  const [company, setCompany] = useState("");
  const [avatarUrl, setAvatarUrl] = useState("");
  const [tags, setTags] = useState<("creator" | "taker")[]>([]);
  const [text, setText] = useState("");
  const [submitted, setSubmitted] = useState(false);

  const maxChars = 300;
  const pointsPerChar = 0.5; // For example, 2 chars = 1 point
  const pointsEarned = Math.floor(
    Math.min(text.length, maxChars) * pointsPerChar
  );

  const toggleTag = (tag: "creator" | "taker") => {
    setTags((prev) =>
      prev.includes(tag) ? prev.filter((t) => t !== tag) : [...prev, tag]
    );
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const payload = {
      name,
      role,
      company,
      avatarUrl,
      tags,
      text,
      points: pointsEarned,
    };

    console.log("Submitting testimonial:", payload);
    // TODO: send to backend via fetch/post
    setSubmitted(true);
  };

  return (
    <div className="max-w-2xl mx-auto py-12 px-4">
      <h1 className="text-3xl font-bold mb-6 text-center">
        Submit a Testimonial
      </h1>

      {submitted ? (
        <div className="bg-green-100 text-green-800 p-6 rounded-lg text-center shadow">
          <CheckCircle className="w-8 h-8 mx-auto mb-2" />
          <p className="font-semibold text-lg">Thank you for your feedback!</p>
          <p>
            You’ve earned <strong>{pointsEarned} points</strong> 🎉
          </p>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <Label>Name</Label>
            <Input
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label>Role</Label>
              <Input value={role} onChange={(e) => setRole(e.target.value)} />
            </div>
            <div>
              <Label>Company</Label>
              <Input
                value={company}
                onChange={(e) => setCompany(e.target.value)}
              />
            </div>
          </div>

          <div>
            <Label>Avatar URL (optional)</Label>
            <Input
              placeholder="https://example.com/avatar.jpg"
              value={avatarUrl}
              onChange={(e) => setAvatarUrl(e.target.value)}
            />
          </div>

          <div>
            <Label>Your Testimonial</Label>
            <Textarea
              value={text}
              onChange={(e) => setText(e.target.value.slice(0, maxChars))}
              rows={4}
              required
            />
            <p className="text-sm text-muted-foreground mt-1">
              {text.length}/{maxChars} characters — Earn {pointsEarned} points
            </p>
          </div>

          <div>
            <Label className="mb-2 block">You are a:</Label>
            <div className="flex gap-3">
              <Badge
                variant={tags.includes("creator") ? "default" : "secondary"}
                className="cursor-pointer"
                onClick={() => toggleTag("creator")}
              >
                Creator
              </Badge>
              <Badge
                variant={tags.includes("taker") ? "default" : "secondary"}
                className="cursor-pointer"
                onClick={() => toggleTag("taker")}
              >
                Test Taker
              </Badge>
            </div>
          </div>

          <Button
            type="submit"
            disabled={text.trim().length === 0}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            Submit and Earn {pointsEarned} Points
          </Button>
        </form>
      )}
    </div>
  );
}
