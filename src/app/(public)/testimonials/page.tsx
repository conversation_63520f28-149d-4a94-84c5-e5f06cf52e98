"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Star, Linkedin, Twitter, Globe } from "lucide-react";

interface Testimonial {
  id: string;
  name: string;
  avatarUrl?: string;
  role?: string;
  company?: string;
  rating: number;
  text: string;
  tags?: ("creator" | "taker")[];
  socialLinks?: {
    linkedin?: string;
    twitter?: string;
    website?: string;
  };
}

export default function TestimonialsPage() {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);

  useEffect(() => {
    setTestimonials([
      {
        id: "t1",
        name: "<PERSON><PERSON>",
        avatarUrl: "/avatars/alya.jpg",
        role: "Frontend Developer",
        company: "Tokopedia",
        rating: 5,
        text: "Thanks to the premium test, I boosted my skills and secured a job interview!",
        tags: ["taker"],
        socialLinks: {
          linkedin: "https://linkedin.com/in/alyahartanto",
        },
      },
      {
        id: "t2",
        name: "Junaid <PERSON>",
        role: "Data Analyst",
        company: "Gojek",
        rating: 4,
        text: "Earned points, accessed premium content, and shared with friends. Great value!",
        tags: ["creator", "taker"],
        socialLinks: {
          twitter: "https://twitter.com/junaid_akbar",
          website: "https://junaid.dev",
        },
      },
    ]);
  }, []);

  const renderTag = (tag: "creator" | "taker") => {
    switch (tag) {
      case "creator":
        return (
          <Badge variant="secondary" className="text-blue-700 bg-blue-100">
            Creator
          </Badge>
        );
      case "taker":
        return (
          <Badge variant="secondary" className="text-green-700 bg-green-100">
            Test Taker
          </Badge>
        );
    }
  };

  return (
    <div className="bg-background py-12 px-4 min-h-screen">
      <div className="max-w-4xl mx-auto space-y-10">
        <div className="text-center">
          <h1 className="text-4xl font-bold tracking-tight mb-2">
            What Users Are Saying
          </h1>
          <p className="text-muted-foreground">
            Real feedback from creators and learners like you.
          </p>
        </div>

        <div className="space-y-6">
          {testimonials.map((t) => (
            <div
              key={t.id}
              className="bg-card border border-border rounded-lg p-6 flex gap-4 items-start shadow-sm"
            >
              <Avatar className="h-12 w-12">
                {t.avatarUrl ? (
                  <AvatarImage src={t.avatarUrl} alt={t.name} />
                ) : (
                  <AvatarFallback>{t.name.charAt(0)}</AvatarFallback>
                )}
              </Avatar>
              <div className="flex-1">
                <div className="flex items-center justify-between flex-wrap gap-2">
                  <div>
                    <h3 className="text-lg font-semibold leading-tight">
                      {t.name}
                    </h3>
                    {t.role && (
                      <p className="text-sm text-muted-foreground">
                        <span>{t.role}</span>
                        <span> </span>
                        {t.company && (
                          <span className="text-sm text-muted-foreground font-bold">
                            @ {t.company}
                          </span>
                        )}
                      </p>
                    )}
                  </div>
                  <div className="flex gap-2">
                    {t.tags?.map((tag) => renderTag(tag))}
                  </div>
                </div>

                <p className="mt-2 text-sm text-foreground">“{t.text}”</p>

                <div className="mt-2 flex gap-1 text-yellow-500">
                  {Array.from({ length: t.rating }).map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-current" />
                  ))}
                  {Array.from({ length: 5 - t.rating }).map((_, i) => (
                    <Star key={i + 5} className="w-4 h-4 opacity-30" />
                  ))}
                </div>

                {t.socialLinks && (
                  <div className="mt-3 flex gap-3 text-muted-foreground">
                    {t.socialLinks.linkedin && (
                      <a
                        href={t.socialLinks.linkedin}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="hover:text-blue-600"
                      >
                        <Linkedin className="w-4 h-4" />
                      </a>
                    )}
                    {t.socialLinks.twitter && (
                      <a
                        href={t.socialLinks.twitter}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="hover:text-sky-500"
                      >
                        <Twitter className="w-4 h-4" />
                      </a>
                    )}
                    {t.socialLinks.website && (
                      <a
                        href={t.socialLinks.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="hover:text-gray-700"
                      >
                        <Globe className="w-4 h-4" />
                      </a>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        <div className="text-center pt-6">
          <Button
            variant="default"
            className="bg-green-600 hover:bg-green-700 text-white"
            onClick={() => (window.location.href = "/create-test#testimonial")}
          >
            Share Your Experience
          </Button>
        </div>
      </div>
    </div>
  );
}
