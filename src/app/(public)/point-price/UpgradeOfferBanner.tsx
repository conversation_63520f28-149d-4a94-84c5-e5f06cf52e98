"use client";

import { <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

export function UpgradeOfferBanner() {
  return (
    <div className="bg-gradient-to-r from-green-500 to-emerald-400 text-white rounded-xl px-6 py-5 mb-8 flex flex-col md:flex-row items-center justify-between shadow-lg">
      <div className="flex items-center gap-4 mb-4 md:mb-0">
        <Sparkles className="w-7 h-7 text-white animate-pulse" />
        <div>
          <p className="font-bold text-lg md:text-xl">
            🎉 Limited Time Offer: Get{" "}
            <span className="underline">400 Points</span> Instantly!
          </p>
          <p className="text-sm md:text-base text-white/90">
            One-time upgrade for only <strong>IDR 100,000</strong>. Unlock
            premium features and benefits now.
          </p>
        </div>
      </div>
      <div className="flex items-center gap-3">
        <div className="bg-white text-green-700 text-xs font-semibold px-2 py-1 rounded-full flex items-center gap-1 shadow-sm">
          <Clock className="w-4 h-4" />
          Limited Time
        </div>
        <Button className="bg-white text-green-700 hover:bg-white/90 font-bold px-5 py-2 rounded-md shadow">
          Upgrade Now
        </Button>
      </div>
    </div>
  );
}
