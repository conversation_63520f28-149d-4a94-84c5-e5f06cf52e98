"use client";

import { Calculator, ArrowRightCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

export function ROICalculatorBanner() {
  return (
    <div className="bg-gradient-to-r from-yellow-400 to-yellow-500 text-yellow-900 rounded-xl px-6 py-5 mb-8 flex flex-col md:flex-row items-center justify-between shadow-lg mt-12">
      <div className="flex items-center gap-4 mb-4 md:mb-0">
        <Calculator className="w-7 h-7 text-yellow-900 animate-pulse" />
        <div>
          <p className="font-bold text-lg md:text-xl">
            💡 Calculate Your ROI Before You Purchase or Upgrade!
          </p>
          <p className="text-sm md:text-base text-yellow-900/90 max-w-md">
            See the real benefits of your investment — no more trial and error.
            Plan smarter and maximize your earnings.
          </p>
        </div>
      </div>
      <div className="flex items-center gap-3">
        <Button
          className="bg-yellow-900 text-yellow-400 hover:bg-yellow-800 font-bold px-5 py-2 rounded-md shadow flex items-center gap-2"
          onClick={() => {
            document
              .getElementById("roi-calculator")
              ?.scrollIntoView({ behavior: "smooth" });
          }}
        >
          Calculate ROI <ArrowRightCircle className="w-5 h-5" />
        </Button>
      </div>
    </div>
  );
}
