// components/PointBenefits.tsx

"use client";

import { CheckCircle, FilePlus2, UserPlus } from "lucide-react";

interface BenefitItem {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const benefits: BenefitItem[] = [
  {
    icon: <CheckCircle className="text-green-500 w-6 h-6 mt-1" />,
    title: "Take Premium Tests",
    description: "Use points to access exclusive, high-quality assessments.",
  },
  {
    icon: <FilePlus2 className="text-blue-500 w-6 h-6 mt-1" />,
    title: "Create Tests & Earn",
    description: "Publish tests and earn passive income as users engage.",
  },
  {
    icon: <UserPlus className="text-purple-500 w-6 h-6 mt-1" />,
    title: "Earn by Sharing",
    description: "Invite others and earn bonus points when they participate.",
  },
];

export function PointBenefits() {
  return (
    <section className="bg-white rounded-lg shadow p-6 mb-12">
      <h2 className="text-2xl font-bold text-gray-800 mb-4">Why Buy Points?</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-left text-gray-700">
        {benefits.map((benefit, idx) => (
          <div key={idx} className="flex items-start gap-3">
            {benefit.icon}
            <div>
              <h4 className="font-semibold">{benefit.title}</h4>
              <p className="text-sm">{benefit.description}</p>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}
