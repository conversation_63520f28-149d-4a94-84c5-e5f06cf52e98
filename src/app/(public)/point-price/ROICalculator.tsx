"use client";

import { useState, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";

export function ROICalculator() {
  const [pointsPerTest, setPointsPerTest] = useState(50);
  const [pricePerTest, setPricePerTest] = useState(10000);
  const [testTakers, setTestTakers] = useState(10);
  const [testsPerMonth, setTestsPerMonth] = useState(2);
  const [showResults, setShowResults] = useState(false);
  const currencySymbol = "IDR";

  // Formatting helper (delimiters + no trailing zeros)
  const formatNumber = (value: number) =>
    value.toLocaleString(undefined, { maximumFractionDigits: 0 });

  // Calculate cumulative earnings month by month for 12 months
  const data = useMemo(() => {
    let cumulativePoints = 0;
    let cumulativeCurrency = 0;
    const months = 12;
    const result: any[] = [];

    for (let month = 1; month <= months; month++) {
      const monthlyPoints = pointsPerTest * testTakers * testsPerMonth;
      const monthlyCurrency = pricePerTest * testTakers * testsPerMonth;

      cumulativePoints += monthlyPoints;
      cumulativeCurrency += monthlyCurrency;

      result.push({
        month: `Month ${month}`,
        Points: cumulativePoints,
        Currency: cumulativeCurrency,
      });
    }

    return result;
  }, [pointsPerTest, pricePerTest, testTakers, testsPerMonth]);

  const totalPoints = testTakers * pointsPerTest * testsPerMonth;
  const totalCurrency = testTakers * pricePerTest * testsPerMonth;

  return (
    <div className="bg-white rounded-lg shadow p-6 mx-auto mt-6">
      <h2 className="text-2xl font-bold mb-6">ROI Calculator</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <Label htmlFor="pointsPerTest">Points Earned per Test</Label>
          <Input
            id="pointsPerTest"
            type="number"
            min={0}
            value={pointsPerTest}
            onChange={(e) => setPointsPerTest(Number(e.target.value))}
            className="mt-1"
          />
        </div>

        <div>
          <Label htmlFor="pricePerTest">
            Price per Test ({currencySymbol})
          </Label>
          <Input
            id="pricePerTest"
            type="number"
            min={0}
            value={pricePerTest}
            onChange={(e) => setPricePerTest(Number(e.target.value))}
            className="mt-1"
          />
        </div>

        <div>
          <Label htmlFor="testTakers">Test Takers per Test per Month</Label>
          <Input
            id="testTakers"
            type="number"
            min={0}
            value={testTakers}
            onChange={(e) => setTestTakers(Number(e.target.value))}
            className="mt-1"
          />
        </div>

        <div>
          <Label htmlFor="testsPerMonth">Tests Created per Month</Label>
          <Input
            id="testsPerMonth"
            type="number"
            min={0}
            value={testsPerMonth}
            onChange={(e) => setTestsPerMonth(Number(e.target.value))}
            className="mt-1"
          />
        </div>
      </div>

      <Button
        onClick={() => setShowResults(true)}
        className="bg-green-600 hover:bg-green-700 text-white mt-4"
      >
        Calculate ROI
      </Button>

      {showResults && (
        <>
          {/* Banner */}
          <div className="mt-6 mb-6 bg-green-100 text-green-900 rounded-md p-4 font-semibold text-center text-lg">
            Wow you got {formatNumber(totalPoints)} points and {currencySymbol}{" "}
            {formatNumber(totalCurrency)} monthly!
          </div>

          <div className="mb-6 text-gray-800 text-center">
            <p>
              Estimated Monthly Earnings:{" "}
              <span className="font-semibold">{formatNumber(totalPoints)} points</span>
            </p>
            <p>
              (or {currencySymbol} {formatNumber(totalCurrency)})
            </p>
          </div>

          <div style={{ width: "100%", height: 320 }}>
            <ResponsiveContainer>
              <LineChart
                data={data}
                margin={{ top: 5, right: 30, left: 10, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis
                  yAxisId="points"
                  orientation="left"
                  stroke="#22c55e"
                  tickFormatter={(value) => formatNumber(value)}
                />
                <YAxis
                  yAxisId="currency"
                  orientation="right"
                  stroke="#10b981"
                  tickFormatter={(value) => `${currencySymbol} ${formatNumber(value)}`}
                />
                <Tooltip
                  formatter={(value: number, name: string) => {
                    if (name === "Currency") {
                      return `${currencySymbol} ${formatNumber(value)}`;
                    }
                    return formatNumber(value);
                  }}
                />
                <Legend />
                <Line
                  yAxisId="points"
                  type="monotone"
                  dataKey="Points"
                  stroke="#22c55e"
                  activeDot={{ r: 8 }}
                  name="Points Earned"
                />
                <Line
                  yAxisId="currency"
                  type="monotone"
                  dataKey="Currency"
                  stroke="#10b981"
                  name={`Currency Earned (${currencySymbol})`}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </>
      )}
    </div>
  );
}
