// components/PricingTiers.tsx

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import Link from "next/link";

interface Tier {
  id: string;
  label: string;
  basePoints: number;
  basePriceIdr: number;
  bonus?: number;
}

interface PricingTiersProps {
  tiers: Tier[];
  currency: string;
  convert: (idr: number) => number;
  isProUser: boolean;
  onBuyClick: (tierId: string) => void;
  onUpgradeClick: () => void;
  pointsPerPremiumTest?: number;
}

export function PricingTiers({
  tiers,
  currency,
  convert,
  isProUser,
  onBuyClick,
  onUpgradeClick,
  pointsPerPremiumTest = 100,
}: PricingTiersProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {tiers.map((tier) => {
        const price = convert(tier.basePriceIdr);
        const totalPoints = tier.basePoints + (tier.bonus || 0);
        const premiumTestsCount = Math.floor(
          totalPoints / pointsPerPremiumTest
        );

        return (
          <div
            key={tier.id}
            className="bg-white rounded-xl shadow p-6 text-center flex flex-col gap-4 border border-gray-100"
          >
            {tier.id === "popular" && (
              <Badge className="self-center bg-blue-100 text-blue-800">
                Best Value
              </Badge>
            )}

            <h2 className="text-2xl font-bold">{tier.label}</h2>

            <div className="text-gray-700 text-lg">
              {totalPoints.toLocaleString()} Points{" "}
              {tier.bonus && (
                <span className="text-green-600 ml-1 text-sm">
                  (+{tier.bonus} bonus)
                </span>
              )}
            </div>

            <p className="text-sm text-gray-600">
              That’s enough for <strong>{premiumTestsCount}</strong> premium{" "}
              {premiumTestsCount === 1 ? "test" : "tests"}.
            </p>

            <p className="text-xs text-gray-500 italic">
              Buy once, use anytime — your points never expire.
            </p>

            <div className="text-2xl font-semibold text-gray-800">
              {currency} {price.toLocaleString()}
            </div>

            {isProUser ? (
              <Button
                className="bg-green-600 hover:bg-green-700 text-white"
                onClick={() => onBuyClick(tier.id)}
              >
                Buy Now
              </Button>
            ) : (
              <Button
                className="bg-yellow-600 hover:bg-yellow-700 text-white"
                onClick={onUpgradeClick}
              >
                Upgrade to Pro to Buy
              </Button>
            )}

            <Link href="/testimonials">
              <Button
                variant="ghost"
                className="text-sm text-gray-600 underline mt-2 hover:text-gray-900"
              >
                See Testimonials
              </Button>
            </Link>
          </div>
        );
      })}
    </div>
  );
}
