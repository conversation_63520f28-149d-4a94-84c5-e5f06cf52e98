"use client";

import { useState, useEffect } from "react";
import { PointBenefits } from "./Benefit";
import { UpgradeOfferBanner } from "./UpgradeOfferBanner";
import { PricingTiers } from "./PricingTiers";
import { ROICalculator } from "./ROICalculator";
import { ROICalculatorBanner } from "./ROICalculatorBanner";

const tiers = [
  { id: "starter", label: "Starter", basePoints: 500, basePriceIdr: 100_000 },
  {
    id: "popular",
    label: "Popular",
    basePoints: 2000,
    basePriceIdr: 400_000,
    bonus: 200,
  },
  {
    id: "pro",
    label: "Pro",
    basePoints: 5000,
    basePriceIdr: 1_000_000,
    bonus: 800,
  },
];

const supportedCurrencies = ["IDR", "USD", "EUR", "JPY"];

export default function PointsPricingPage() {
  const [currency, setCurrency] = useState("IDR");
  const [rates, setRates] = useState<Record<string, number>>({ IDR: 1 });

  useEffect(() => {
    fetch(`/api/convert?base=IDR`)
      .then((res) => res.json())
      .then((data) => {
        setRates({ IDR: 1, ...data.rates });
      })
      .catch(console.error);
  }, []);

  const convert = (idr: number) => {
    const rate = rates[currency] || 1;
    return Math.ceil((idr / rate) * 100) / 100;
  };

  return (
    <div className="bg-gray-50 py-12 px-4">
      <div className="max-w-5xl mx-auto text-center">
        <h1 className="text-4xl font-extrabold mb-4">Buy Points</h1>
        <p className="text-gray-600 mb-8">
          Use points to unlock premium tests, create your own, or earn by
          sharing.
        </p>

        <UpgradeOfferBanner />

        <PointBenefits />

        <PricingTiers
          tiers={tiers}
          currency={currency}
          convert={convert}
          isProUser={true}
          onBuyClick={function (tierId: string): void {
            throw new Error("Function not implemented.");
          }}
          onUpgradeClick={function (): void {
            throw new Error("Function not implemented.");
          }}
        />

        <ROICalculatorBanner />

        <ROICalculator />
      </div>
    </div>
  );
}
