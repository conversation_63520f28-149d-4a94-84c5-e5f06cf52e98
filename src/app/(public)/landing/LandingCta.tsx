import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>R<PERSON> } from "lucide-react";
import Link from "next/link";

export function LandingCta() {
  return (
    <section className="py-20 bg-gradient-to-br from-green-600 to-green-700 text-white">
      <div className="container mx-auto px-4 text-center">
        <h2 className="text-3xl md:text-4xl font-bold mb-6">
          Ready to Accelerate Your Career?
        </h2>
        <p className="text-xl mb-8 max-w-2xl mx-auto">
          Join thousands of professionals who are showcasing their skills and
          advancing their careers with SkillPintar.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/tests" passHref>
            <Button className="bg-white text-green-600 hover:bg-gray-100 h-12 px-6 text-base flex space-x-2">
              Start Testing Now <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
}
