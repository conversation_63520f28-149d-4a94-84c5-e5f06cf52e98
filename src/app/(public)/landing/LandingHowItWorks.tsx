import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, ArrowRight } from "lucide-react";

export function LandingHowItWorks() {
    return (
      <section id="how-it-works" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-16 flex flex-col items-center">
            <Badge className="mb-4 px-2 w-fit bg-green-100 text-green-800 hover:bg-green-100">
              How It Works
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Simple Steps to Career Success
            </h2>
            <p className="text-lg text-gray-600">
              Our platform makes it easy to showcase your skills and advance your
              career in just a few steps.
            </p>
          </div>
  
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Step 1 */}
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold text-green-600">1</span>
              </div>
              <h3 className="text-xl font-bold mb-3">Take Skill Tests</h3>
              <p className="text-gray-600">
                Choose from our library of industry-standard tests to verify your
                skills and knowledge.
              </p>
            </div>
  
            {/* Step 2 */}
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold text-green-600">2</span>
              </div>
              <h3 className="text-xl font-bold mb-3">Build Your Profile</h3>
              <p className="text-gray-600">
                Create a professional profile showcasing your verified skills,
                experience, and achievements.
              </p>
            </div>
  
            {/* Step 3 */}
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold text-green-600">3</span>
              </div>
              <h3 className="text-xl font-bold mb-3">Advance Your Career</h3>
              <p className="text-gray-600">
                Share your profile with employers, negotiate better salaries, and
                unlock new opportunities.
              </p>
            </div>
          </div>
  
          <div className="mt-16 text-center flex justify-center">
            <Link href="/tests">
              <Button className="bg-green-600 hover:bg-green-700 h-12 px-6 text-base flex space-x-2 text-white">
                Get Started Now <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </section>
    );
  }