import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { CheckCir<PERSON>, ArrowRight } from "lucide-react";
import Link from "next/link";

export function LandingPricing() {
  return (
    <section id="pricing" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-16 flex flex-col items-center">
          <Badge className="mb-4 px-2 w-fit bg-green-100 text-green-800 hover:bg-green-100">
            Pricing
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Affordable Plans for Career Growth
          </h2>
          <p className="text-lg text-gray-600">
            Invest in your future with our limited-time pricing. Prices will
            increase soon, so act now!
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-100 relative">
            {/* Limited Time Offer Badge */}
            <div className="absolute top-6 right-6">
              <Badge className="bg-red-100 text-red-800 hover:bg-red-100 px-3 py-1">
                Limited Time Offer
              </Badge>
            </div>

            <div className="p-8 md:p-12">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
                <div>
                  <h3 className="text-2xl font-bold mb-2">Professional Plan</h3>
                  <p className="text-gray-600">
                    Everything you need to accelerate your career
                  </p>
                </div>
                <div className="mt-4 md:mt-0 text-center">
                  <p className="text-sm text-gray-500 line-through">
                    IDR 50,000/month
                  </p>
                  <div className="flex items-center">
                    <p className="text-4xl font-bold text-green-600">
                      IDR 10,000
                    </p>
                    <span className="text-gray-500 ml-2">/month</span>
                  </div>
                  <p className="text-xs text-red-600 font-medium">
                    Price increases on June 1, 2025
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div>
                  <h4 className="font-medium mb-4">Plan Includes:</h4>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                      <span>Unlimited skill tests</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                      <span>Professional profile</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                      <span>Verified skill badges</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                      <span>AI skill recommendations</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                      <span>Skill gap analysis</span>
                    </li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-4">Also Included:</h4>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                      <span>Shareable certificates</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                      <span>Employer visibility</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                      <span>Career insights</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                      <span>Priority support</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                      <span>Monthly new tests</span>
                    </li>
                  </ul>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="/tests">
                  <Button className="bg-green-600 hover:bg-green-700 h-12 px-6 text-base flex-1 flex space-x-2 text-white">
                    Get Started Now <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
                <Link href="https://wa.me/6282243905005">
                  <Button variant="outline" className="h-12 px-6 text-base">
                    Book a Demo
                  </Button>
                </Link>
              </div>
            </div>
          </div>

          <p className="text-center text-sm text-gray-500 mt-6">
            All plans come with a 14-day money-back guarantee. No questions
            asked.
          </p>
        </div>
      </div>
    </section>
  );
}
