import Image from "next/image";
import { Badge } from "@/components/ui/badge";
import { Star } from "lucide-react";

export function LandingTestimonials() {
    return (
      <section id="testimonials" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-16 flex flex-col items-center">
            <Badge className="mb-4 px-2 w-fit bg-green-100 text-green-800 hover:bg-green-100">
              Testimonials
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              What Our Users Say
            </h2>
            <p className="text-lg text-gray-600">
              Hear from professionals who have transformed their careers with our
              platform.
            </p>
          </div>
  
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Testimonial 1 */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
              <div className="flex items-center mb-4">
                <div className="flex text-yellow-400">
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                </div>
              </div>
              <p className="text-gray-600 mb-6">
                {
                  "After verifying my skills on SkillPintar, I was able to negotiate a 30% salary increase. The verified badges gave me the confidence and proof I needed during interviews."
                }
              </p>
              <div className="flex items-center">
                <Image
                  src="/placeholder.svg?height=48&width=48"
                  alt="User"
                  width={48}
                  height={48}
                  className="rounded-full mr-4"
                />
                <div>
                  <h4 className="font-medium">Budi Santoso</h4>
                  <p className="text-sm text-gray-500">
                    Senior Developer at TechCorp
                  </p>
                </div>
              </div>
            </div>
  
            {/* Testimonial 2 */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
              <div className="flex items-center mb-4">
                <div className="flex text-yellow-400">
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                </div>
              </div>
              <p className="text-gray-600 mb-6">
                {
                  "The AI recommendations helped me identify skill gaps I didn't know I had. After taking the suggested tests, I was able to land a job in a completely new field."
                }
              </p>
              <div className="flex items-center">
                <Image
                  src="/placeholder.svg?height=48&width=48"
                  alt="User"
                  width={48}
                  height={48}
                  className="rounded-full mr-4"
                />
                <div>
                  <h4 className="font-medium">Siti Rahayu</h4>
                  <p className="text-sm text-gray-500">
                    UX Designer at CreativeStudio
                  </p>
                </div>
              </div>
            </div>
  
            {/* Testimonial 3 */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
              <div className="flex items-center mb-4">
                <div className="flex text-yellow-400">
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                </div>
              </div>
              <p className="text-gray-600 mb-6">
                {
                  "As a freelancer, my SkillPintar profile has become my most valuable marketing tool. Clients trust my skills because they're verified, and I've been able to increase my rates significantly."
                }
              </p>
              <div className="flex items-center">
                <Image
                  src="/placeholder.svg?height=48&width=48"
                  alt="User"
                  width={48}
                  height={48}
                  className="rounded-full mr-4"
                />
                <div>
                  <h4 className="font-medium">Agus Wijaya</h4>
                  <p className="text-sm text-gray-500">Freelance Web Developer</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    );
}