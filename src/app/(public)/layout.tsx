import { Inter } from "next/font/google";
import { <PERSON>ada<PERSON> } from "next";
import { cn } from "@/src/lib/utils";
import Header from "../LandingHeader";
import Footer from "../LandingFooter";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: {
    default: "Your App Name",
    template: "%s | Your App Name",
  },
  description: "Build and take powerful, interactive assessments.",
  icons: {
    icon: "/favicon.ico",
  },
  manifest: "/site.webmanifest",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head />
      <body
        className={cn(inter.className, "min-h-screen bg-gray-50 antialiased")}
      >
        <Header />
        {children}
        <Footer />
      </body>
    </html>
  );
}
