"use client";

import Image from "next/image";
import { useEffect, useState } from "react";

const slides = [
  {
    image: "/student-1.png",
    alt: "Student taking test",
    caption: "Fast, reliable testing",
    text: "Join from anywhere in the world and get results instantly.",
  },
  {
    image: "/student-2.png",
    alt: "Online test",
    caption: "Smart assessments",
    text: "AI-generated questions tailored for software engineers.",
  },
  {
    image: "/student-3.png",
    alt: "Engineer coding",
    caption: "Real-world coding challenges",
    text: "Evaluate practical skills with hands-on problems.",
  },
];

export default function JoinIllustration() {
  const [index, setIndex] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setIndex((prev) => (prev + 1) % slides.length);
    }, 4000); // 4s per slide
    return () => clearInterval(timer);
  }, []);

  const current = slides[index];

  return (
    <div className="hidden lg:block bg-green-200 relative overflow-hidden">
      <div className="absolute inset-0 bg-[url('/grid-pattern-green.png')] bg-center opacity-40 z-0" />

      <div className="relative h-full flex flex-col items-center justify-center p-10 text-center z-10">
        <Image
          src={current.image}
          alt={current.alt}
          width={300}
          height={300}
          className="mb-6 rounded-lg shadow-lg"
        />
        <h2 className="text-2xl font-bold text-green-900">{current.caption}</h2>
        <p className="text-green-800 mt-2 max-w-md">{current.text}</p>
      </div>

      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2 z-10">
        {slides.map((_, i) => (
          <div
            key={i}
            className={`w-3 h-3 rounded-full ${
              i === index ? "bg-green-800" : "bg-green-400"
            }`}
          />
        ))}
      </div>
    </div>
  );
}
