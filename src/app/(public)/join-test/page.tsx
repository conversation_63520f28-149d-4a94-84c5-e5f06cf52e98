"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import JoinF<PERSON> from "./JoinForm";
import JoinIllustration from "./JoinIllustration";

// Simulated DB fetch — replace with actual DB call
async function getTestByCode(
  code: string
): Promise<{ testId: string; title: string }> {
  const fakeDatabase: Record<string, { testId: string; title: string }> = {
    WKCXbfdsahQA: { testId: "test-001", title: "Digital Marketing Basics" },
    XYZ123ABC: { testId: "test-002", title: "Python for Data Analysis" },
    INVITEONLY: { testId: "test-003", title: "Pytorch for Machine Learning" },
  };

  return fakeDatabase[code] || { testId: "", title: "Unknown Test" };
}

export default function JoinExam() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const code = searchParams.get("code");
  const directTestId = searchParams.get("testId");

  const [fullName, setFullName] = useState("");
  const [email, setEmail] = useState("");
  const [testId, setTestId] = useState<string | null>(null);
  const [testTitle, setTestTitle] = useState("");
  const [invitationCode, setInvitationCode] = useState<string | null>(null);
  const [hideInvitationCode, setHideInvitationCode] = useState(false);

  useEffect(() => {
    const savedName = localStorage.getItem("fullName");
    const savedEmail = localStorage.getItem("email");
    if (savedName) setFullName(savedName);
    if (savedEmail) setEmail(savedEmail);
  }, []);

  useEffect(() => {
    if (directTestId) {
      setTestId(directTestId);
      setTestTitle("Your Selected Test"); // Optional: fetch actual title here
      setHideInvitationCode(true);
    } else if (code) {
      getTestByCode(code).then(({ testId, title }) => {
        setTestId(testId);
        setTestTitle(title);
        setInvitationCode(code);
        setHideInvitationCode(false);
      });
    }
  }, [code, directTestId]);

  const handleJoin = () => {
    if (!fullName.trim() || !email.trim()) {
      alert("Please enter your full name and email");
      return;
    }

    if (!testId) {
      alert("Invalid test information. Please check your invitation link.");
      return;
    }

    // Simulate a unique user ID (you can use real one from DB if available)
    const userId = `user-${Math.random().toString(36).substring(2, 10)}`;

    // Save to localStorage
    localStorage.setItem("user_id", userId);
    localStorage.setItem("fullName", fullName);
    localStorage.setItem("email", email);

    // Redirect to the test landing
    router.push(`/test-landing/${testId}`);
  };

  return (
    <div className="bg-gray-100 flex flex-col">
      <div className="p-4 md:p-8 h-full w-full">
        <div className="bg-white rounded-3xl shadow-sm h-full w-full overflow-clip">
          <div className="grid grid-cols-1 lg:grid-cols-2 h-full">
            <JoinForm
              fullName={fullName}
              email={email}
              invitationCode={invitationCode ?? ""}
              testTitle={testTitle}
              hideInvitationCode={hideInvitationCode}
              onChangeName={setFullName}
              onChangeEmail={setEmail}
              onJoin={handleJoin}
              onBack={() => router.back()}
            />
            <JoinIllustration />
          </div>
        </div>
      </div>
    </div>
  );
}
