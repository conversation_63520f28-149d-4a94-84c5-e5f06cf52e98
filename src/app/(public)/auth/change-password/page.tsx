"use client"

import { useLocalization } from "@/src/localization/functions/client";
import { useState } from "react";
import { authLocales } from "../locales";
import { AuthAPI } from "@/src/services/authApi";

export default function ChangePasswordPage() {
  const { t } = useLocalization("auth",authLocales);
  const [oldPassword, setOldPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [message, setMessage] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await AuthAPI.ChangePassword({ currentPassword: oldPassword, newPassword }).request();
      setMessage(t("auth.change_success"));
    } catch (err: any) {
      setMessage(err.message || t("auth.change_error"));
    }
  };

  return (
    <div className="max-w-md mx-auto mt-20 p-6 bg-white shadow rounded-md">
      <h1 className="text-2xl font-semibold mb-4">{t("auth.change_password")}</h1>
      <form onSubmit={handleSubmit} className="space-y-4">
        <input
          type="password"
          placeholder={t("auth.old_password")}
          value={oldPassword}
          onChange={(e) => setOldPassword(e.target.value)}
          className="w-full border border-gray-300 px-4 py-2 rounded"
        />
        <input
          type="password"
          placeholder={t("auth.new_password")}
          value={newPassword}
          onChange={(e) => setNewPassword(e.target.value)}
          className="w-full border border-gray-300 px-4 py-2 rounded"
        />
        <button
          type="submit"
          className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700"
        >
          {t("auth.change")}
        </button>
        {message && <p className="text-sm text-gray-600">{message}</p>}
      </form>
    </div>
  );
}
