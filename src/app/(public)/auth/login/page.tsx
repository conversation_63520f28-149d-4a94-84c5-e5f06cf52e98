"use client"

import { useLocalization } from "@/src/localization/functions/client"
import { AuthAPI } from "@/src/services/authApi"
import { useState } from "react"
import { authLocales } from "../locales"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { secureStorage, StorageKeys } from "@/src/utils/SecureStorage"
// import { secureStorage, StorageKeys } from "@/src/lib/utils/SecureStorage"

export default function LoginPage() {
  const { t } = useLocalization("auth", authLocales)
  const router = useRouter()
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [error, setError] = useState("")

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const { token, refresh_token } = await Auth<PERSON><PERSON>.Login({ email, password }).request()
      secureStorage.setItem(StorageKeys.UserToken, token)
      secureStorage.setItem(StorageKeys.CookieToken, token)
      secureStorage.setItem(StorageKeys.RefreshToken, refresh_token)
      window.location.href = "/dashboard"
    } catch (err: any) {
      setError(err?.message || t("auth.login_error"))
    }
  }

  return (
    <div className="max-w-md mx-auto mt-20 p-6 bg-white shadow rounded-md">
      <h1 className="text-2xl font-semibold mb-4">{t("auth.login")}</h1>
      <form onSubmit={handleLogin} className="space-y-4">
        <input
          className="w-full border border-gray-300 px-4 py-2 rounded"
          placeholder={t("auth.email")}
          type="email"
          value={email}
          onChange={e => setEmail(e.target.value)}
        />
        <input
          className="w-full border border-gray-300 px-4 py-2 rounded"
          placeholder={t("auth.password")}
          type="password"
          value={password}
          onChange={e => setPassword(e.target.value)}
        />
        <button
          type="submit"
          className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700"
        >
          {t("auth.login")}
        </button>
        {error && <p className="text-sm text-red-600">{error}</p>}
      </form>

      {/* Links below the form */}
      <div className="mt-6 flex justify-between items-center text-sm text-blue-600">
        <Link href="/auth/register" className="hover:underline">
          {t("auth.register")}
        </Link>
        <Link href="/auth/forgot-password" className="hover:underline">
          {t("auth.forgot_password")}
        </Link>
      </div>
    </div>
  )
}
