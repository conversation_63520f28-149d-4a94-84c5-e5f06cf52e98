"use client"

import { useLocalization } from "@/src/hooks/useLocalization/client";
import { AuthAPI } from "@/src/services/authApi";
import { useState } from "react";
import { authLocales } from "../locales";

export default function ResendVerificationPage() {
  const { t } = useLocalization("auth", authLocales);
  const [email, setEmail] = useState("");
  const [message, setMessage] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await AuthAPI.ResendVerification({ email }).request();
      setMessage(t("auth.verification_sent"));
    } catch (err: any) {
      setMessage(err.message || t("auth.verification_error"));
    }
  };

  return (
    <div className="max-w-md mx-auto mt-20 p-6 bg-white shadow rounded-md">
      <h1 className="text-2xl font-semibold mb-4">{t("auth.resend_verification")}</h1>
      <form onSubmit={handleSubmit} className="space-y-4">
        <input
          type="email"
          placeholder={t("auth.email")}
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          className="w-full border border-gray-300 px-4 py-2 rounded"
        />
        <button
          type="submit"
          className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700"
        >
          {t("auth.send")}
        </button>
        {message && <p className="text-sm text-gray-600">{message}</p>}
      </form>
    </div>
  );
}
