"use client";

import { useLocalization } from "@/src/localization/functions/client";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { authLocales } from "../locales";
import { AuthAPI } from "@/src/services/authApi";

interface VerifyEmailPageProps {
  searchParams: {
    token?: string;
  };
}

export default function VerifyEmailPage({ searchParams }: VerifyEmailPageProps) {
  const { t } = useLocalization("auth", authLocales);
  const router = useRouter();
  const token = searchParams.token || "";

  const [email, setEmail] = useState("");
  const [message, setMessage] = useState("");
  const [error, setError] = useState("");
  const [verified, setVerified] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!token) return;

    const verify = async () => {
      setLoading(true);
      setError("");
      setMessage("");

      try {
        await AuthAPI.VerifyEmail({ token }).request();
        setVerified(true);
        setMessage(t("auth.verification_success"));
      } catch (err: any) {
        setError(err?.message || t("auth.verification_failed"));
      } finally {
        setLoading(false);
      }
    };

    verify();
  }, []);

  const handleResend = async (e: React.FormEvent) => {
    e.preventDefault();
    setMessage("");
    setError("");

    try {
      await AuthAPI.ResendVerification({ email }).request();
      setMessage(t("auth.verification_sent"));
    } catch (err: any) {
      setError(err?.message || t("auth.verification_error"));
    }
  };

  return (
    <div className="max-w-md mx-auto mt-20 p-6 bg-white shadow rounded-md">
      <h1 className="text-2xl font-semibold mb-4">{t("auth.verify_email")}</h1>

      {loading && <p className="text-sm text-gray-600">{t("auth.verifying")}</p>}

      {!loading && verified && (
        <>
          <p className="text-green-600 mb-6">{message}</p>
          <button
            onClick={() => router.push("/auth/login")}
            className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700"
          >
            {t("auth.login")}
          </button>
        </>
      )}

      {!loading && !verified && (
        <>
          {error && <p className="text-red-600 mb-4">{error}</p>}

          <form onSubmit={handleResend} className="space-y-4">
            <p className="text-sm text-gray-600">{t("auth.verification_description")}</p>
            <input
              type="email"
              placeholder={t("auth.email")}
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full border border-gray-300 px-4 py-2 rounded"
              required
            />
            <button
              type="submit"
              className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700"
            >
              {t("auth.resend_verification")}
            </button>
          </form>

          {message && <p className="text-green-600 mt-4">{message}</p>}
        </>
      )}
    </div>
  );
}
