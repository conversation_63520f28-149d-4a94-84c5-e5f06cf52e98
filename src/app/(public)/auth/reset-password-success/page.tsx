"use client";

import Link from "next/link";
import { useLocalization } from "@/src/localization/functions/client";
import { authLocales } from "../locales";

export default function ResetPasswordSuccessPage() {
  const { t } = useLocalization("auth", authLocales);

  return (
    <div className="max-w-md mx-auto mt-20 p-6 bg-white shadow rounded-md text-center">
      <h1 className="text-2xl font-semibold mb-4 text-green-600">
        {t("auth.reset_success_title")}
      </h1>
      <p className="mb-6 text-gray-700">{t("auth.reset_success_message")}</p>

      <Link href="/auth/login">
        <button className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700">
          {t("auth.go_to_login")}
        </button>
      </Link>
    </div>
  );
}
