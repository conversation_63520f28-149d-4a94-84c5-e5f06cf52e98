"use client";

import { useLocalization } from "@/src/localization/functions/client";
import { AuthAPI } from "@/src/services/authApi";
import { useState } from "react";
import { authLocales } from "../locales";
import Link from "next/link"; // ✅ Import Link

export default function RegisterPage() {
  const { t } = useLocalization("auth", authLocales);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [name, setName] = useState("");
  const [message, setMessage] = useState<string | null>(null);

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const res = await AuthAPI.Register({ email, password, name }).request();
      setMessage(t("auth.register_success"));
    } catch (err: any) {
      setMessage(err?.message || t("auth.register_error"));
    }
  };

  return (
    <div className="max-w-md mx-auto mt-20 p-6 bg-white shadow rounded-md">
      <h1 className="text-2xl font-semibold mb-4">{t("auth.register")}</h1>
      <form onSubmit={handleRegister} className="space-y-4">
        <input
          className="w-full border border-gray-300 px-4 py-2 rounded"
          placeholder={t("auth.name")}
          value={name}
          onChange={e => setName(e.target.value)}
        />
        <input
          className="w-full border border-gray-300 px-4 py-2 rounded"
          placeholder={t("auth.email")}
          type="email"
          value={email}
          onChange={e => setEmail(e.target.value)}
        />
        <input
          className="w-full border border-gray-300 px-4 py-2 rounded"
          placeholder={t("auth.password")}
          type="password"
          value={password}
          onChange={e => setPassword(e.target.value)}
        />
        <button
          type="submit"
          className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700"
        >
          {t("auth.register")}
        </button>
        {message && <p className="text-sm text-gray-600">{message}</p>}
      </form>

      {/* 👇 Use Link instead of button */}
      <div className="mt-4 text-center">
        <p className="text-sm">
          {t("auth.have_account")}{" "}
          <Link
            href="/auth/login"
            className="text-blue-600 hover:underline"
          >
            {t("auth.login")}
          </Link>
        </p>
      </div>
    </div>
  );
}
