"use client";

import { useRouter } from "next/navigation";
import { useLocalization } from "@/src/hooks/useLocalization/client";
import { AuthAPI } from "@/src/services/authApi";
import { useState } from "react";
import { authLocales } from "../locales";

export default function ResetPasswordPageClient({ params }: { params: { token: string } }) {
  const { t } = useLocalization("auth", authLocales);
  const token = params.token;
  const router = useRouter();

  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState("");

  const validate = () => {
    if (!password || !confirmPassword) {
      return t("auth.password_required");
    }
    if (password.length < 8) {
      return t("auth.password_too_short");
    }
    if (password !== confirmPassword) {
      return t("auth.password_mismatch");
    }
    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const validationError = validate();
    if (validationError) {
      setError(validationError);
      return;
    }

    setError("");
    try {
      await AuthAPI
        .ResetPassword({ token, newPassword: password })
        .request();
      router.push("/auth/reset-password-success");
    } catch (err: any) {
      setError(err.message || t("auth.reset_error"));
    }
  };

  return (
    <div className="max-w-md mx-auto mt-20 p-6 bg-white shadow rounded-md">
      <h1 className="text-2xl font-semibold mb-4">{t("auth.reset_password")}</h1>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <input
            type={showPassword ? "text" : "password"}
            placeholder={t("auth.new_password")}
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="w-full border border-gray-300 px-4 py-2 rounded"
          />
          <div className="text-sm mt-1">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={showPassword}
                onChange={() => setShowPassword(!showPassword)}
              />
              <span>{t("auth.show_password")}</span>
            </label>
          </div>
        </div>

        <div>
          <input
            type="password"
            placeholder={t("auth.confirm_password")}
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            className="w-full border border-gray-300 px-4 py-2 rounded"
          />
        </div>

        {error && <p className="text-sm text-red-500">{error}</p>}

        <button
          type="submit"
          className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700"
        >
          {t("auth.reset")}
        </button>
      </form>
    </div>
  );
}
