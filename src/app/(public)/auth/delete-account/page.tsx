"use client"

import { useLocalization } from "@/src/hooks/useLocalization/client";
import { AuthAPI } from "@/src/services/authApi";
import { useState } from "react";
import { authLocales } from "../locales";
import { useRouter } from "next/navigation";

export default function DeleteAccountPage() {
  const { t } = useLocalization("auth", authLocales);
  const router = useRouter();
  const [password, setPassword] = useState("");
  const [message, setMessage] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await AuthAPI.DeleteAccount({ password }).request();
      localStorage.clear();
      setMessage(t("auth.deleted_success"));
      setTimeout(() => router.push("/auth/register"), 1500);
    } catch (err: any) {
      setMessage(err.message || t("auth.deleted_error"));
    }
  };

  return (
    <div className="max-w-md mx-auto mt-20 p-6 bg-white shadow rounded-md">
      <h1 className="text-2xl font-semibold mb-4 text-red-600">{t("auth.delete_account")}</h1>
      <form onSubmit={handleSubmit} className="space-y-4">
        <input
          type="password"
          placeholder={t("auth.confirm_password")}
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          className="w-full border border-gray-300 px-4 py-2 rounded"
        />
        <button
          type="submit"
          className="w-full bg-red-600 text-white py-2 rounded hover:bg-red-700"
        >
          {t("auth.delete")}
        </button>
        {message && <p className="text-sm text-gray-600">{message}</p>}
      </form>
    </div>
  );
}
