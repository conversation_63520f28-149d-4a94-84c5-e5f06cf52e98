import { repo } from "@/src/lib/repositories/public-tests";
import PublicTestsClient from "./PublicTestsClient";

interface Props {
  searchParams: {
    query?: string;
    categories?: string;
  };
}

export default async function PublicTestsPage({ searchParams }: Props) {
  const query = searchParams.query?.trim() || "";
  const category = searchParams.categories || "All";

  const allTests = await repo.getAll({
    query: query || undefined,
    categories: category !== "All" ? [category] : undefined,
  });

  const allCategories = await repo.getAllCategories()

  return (
    <PublicTestsClient
      initialTests={allTests}
      categories={allCategories}
      initialQuery={query}
      initialCategory={category}
    />
  );
}
