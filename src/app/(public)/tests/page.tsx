import { TestDBRepository } from "@/src/lib/repositories/tests";
import PublicTestsClient from "./PublicTestsClient";

interface Props {
  searchParams: {
    query?: string;
    categories?: string;
  };
}

export default async function PublicTestsPage({ searchParams }: Props) {
  const query = searchParams.query?.trim() || "";
  const category = searchParams.categories || "All";

  const allTests = await TestDBRepository.getAll({
    filters: [
      { field: "access", value: "PUBLIC" },
    ],
  });

  const allCategories = await TestDBRepository.getAllCategories({
    filters: [
      { field: "access", value: "PUBLIC" },
    ],
  }) || { items: [], total: 0 };

  return !allTests ? null : (
    <PublicTestsClient
      initialTests={allTests.items}
      categories={!allCategories ? [] : allCategories.items}
      initialQuery={query}
      initialCategory={category}
    />
  );
}
