import { Award, BookO<PERSON>, TrendingUp } from "lucide-react";

const features = [
  {
    title: "Verify Your Skills",
    description:
      "Get objective assessment of your abilities and showcase your verified skills to potential employers.",
    icon: Award,
  },
  {
    title: "Identify Knowledge Gaps",
    description:
      "Discover areas where you need improvement and get personalized recommendations for further learning.",
    icon: BookOpen,
  },
  {
    title: "Advance Your Career",
    description:
      "Build a comprehensive skill profile that helps you stand out in job applications and negotiations.",
    icon: TrendingUp,
  },
];

export default function FeaturesSection() {
  return (
    <div className="mb-12">
      <h2 className="text-2xl font-bold text-center mb-8">Why Take Our Tests?</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {features.map((feature) => (
          <div
            key={feature.title}
            className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 text-center"
          >
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <feature.icon className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-lg font-bold mb-2">{feature.title}</h3>
            <p className="text-gray-600">{feature.description}</p>
          </div>
        ))}
      </div>
    </div>
  );
}
