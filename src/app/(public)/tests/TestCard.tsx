/* eslint-disable @next/next/no-img-element */
"use client";

import Image from "next/image";
import { useRouter } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Users, Clock } from "lucide-react";
import en from "./locales/en.json";
import { registerTranslation } from "@/src/lib/i18n";
import { TestData, TestDifficulty } from "@/src/lib/repositories/public-tests";

const getDifficultyColor = (difficulty: TestDifficulty) => {
  switch (difficulty) {
    case "EASY":
      return "bg-green-100 text-green-800";
    case "INTERMEDIATE":
      return "bg-blue-100 text-blue-800";
    case "ADVANCED":
      return "bg-purple-100 text-purple-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getSuccessRateColor = (rate: number) => {
  if (rate >= 75) return "text-green-600";
  if (rate >= 50) return "text-blue-600";
  return "text-orange-600";
};

export default function TestCard({
  test,
  isCompact,
}: {
  test: TestData;
  isCompact?: boolean;
}) {
  const { t } = registerTranslation("public-test", { en });
  const router = useRouter();

  const isPremium = test.premium?.isPremium ?? false;
  const price = test.premium?.price ?? null;
  const offerText = test.premium?.offerText ?? null;

  const handleTakeTest = () => {
    const userId = localStorage.getItem("user_id");
    if (userId) {
      router.push(`/test-landing/${test.id}`);
    } else {
      router.push(`/join-test?testId=${test.id}`);
    }
  };

  const handleBuyTest = () => {
    router.push(`/buy-test/${test.id}`);
  };

  return (
    <div className="bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100 hover:shadow-md transition-shadow flex flex-col relative">
      <div className="relative h-48 w-full overflow-hidden rounded-t-xl">
        <img
          src={test.cover}
          alt={test.title}
          className="w-full h-full object-cover"
        />

        {/* Difficulty Badges */}
        <div className="absolute top-4 right-4 flex flex-wrap gap-1 z-10">
          {test.difficulties.map((difficulty, index) => (
            <Badge
              key={index}
              className={`${getDifficultyColor(difficulty)} py-1 px-2 rounded-full text-xs`}
            >
              {difficulty}
            </Badge>
          ))}
        </div>

        {/* Premium Badge */}
        {isPremium &&
          test.premium?.badges &&
          test.premium.badges.length > 0 && (
            <div className="p-2 mb-4 flex flex-wrap gap-2">
              {test.premium.badges.map((badge, idx) => (
                <Badge
                  key={idx}
                  className="bg-yellow-100 text-yellow-900 border-yellow-300 py-1 px-3 rounded-full font-semibold text-xs shadow-inner"
                >
                  {badge}
                </Badge>
              ))}
            </div>
          )}
      </div>

      <div className="p-6 flex flex-col flex-1">
        <h3 className="text-xl font-bold mb-2">{test.title}</h3>
        <p className="text-gray-600 mb-4">{test.description}</p>

        {!isCompact && test.benefits.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">
              {t("benefits")}:
            </h4>
            <div className="flex flex-wrap gap-2">
              {test.benefits.map((benefit, idx) => (
                <Badge
                  key={idx}
                  variant="outline"
                  className="bg-green-50 text-green-700 border-green-200 py-1 px-2 rounded-full"
                >
                  {benefit}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Premium Offer Banner */}
        {!isCompact && isPremium && offerText && (
          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-300 rounded-md text-yellow-800 text-sm font-semibold flex items-center gap-2 shadow-inner">
            <svg
              className="w-5 h-5 flex-shrink-0"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M13 16h-1v-4h-1m1-4h.01M12 20a8 8 0 100-16 8 8 0 000 16z"
              ></path>
            </svg>
            {offerText}
          </div>
        )}

        {!isCompact && !isPremium && (
          <>
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-1 text-gray-600">
                <Users className="h-4 w-4" />
                <span className="text-sm">
                  {t("users").replace(
                    "{{count}}",
                    test.usersCount.toLocaleString()
                  )}
                </span>
              </div>
              <div className="flex items-center gap-1">
                <span className="text-sm font-medium">{t("successRate")}</span>
                <span
                  className={`text-sm font-bold ${getSuccessRateColor(test.successRate)}`}
                >
                  {test.successRate}%
                </span>
              </div>
            </div>
            <div className="flex items-center gap-2 mb-4 text-gray-600">
              <Clock className="h-4 w-4" />
              <span className="text-sm">
                {t("duration", { duration: test.durationInSeconds })}
              </span>
            </div>
          </>
        )}

        <div className="flex justify-between items-center mt-auto">
          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8">
              <AvatarImage
                src={test.creator.avatar || "/placeholder.svg"}
                alt={test.creator.name}
              />
              <AvatarFallback>{test.creator.name.charAt(0)}</AvatarFallback>
            </Avatar>
            <span className="text-sm">{test.creator.name}</span>
          </div>

          {isPremium ? (
            <Button
              onClick={handleBuyTest}
              className="bg-yellow-500 hover:bg-yellow-600 text-yellow-900 font-semibold"
              aria-label="Buy premium test"
            >
              {price ? `Buy Test - ${price}` : "Buy Test"}
            </Button>
          ) : (
            <Button
              onClick={handleTakeTest}
              className="bg-green-600 hover:bg-green-700 text-white"
              aria-label="Take test"
            >
              {t("takeTest")}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
