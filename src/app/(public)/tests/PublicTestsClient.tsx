"use client";

import { useRouter } from "next/navigation";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import TestCard from "./TestCard";
import CategorySelector from "./CategorySelector";
import CtaSection from "./CtaSection";
import FeaturesSection from "./FeaturesSection";
import { useMemo, useState, useEffect } from "react";
import { Test } from "@/src/lib/repositories/tests/interface";
import { LayoutGrid, List } from "lucide-react";
import TestListItem from "./TestListItem";

import en from "./locales/en.json";
import { useLocalization } from "@/src/localization/functions/client";

interface Props {
  initialTests: Test[];
  categories: string[];
  initialQuery: string;
  initialCategory: string;
}

export default function PublicTestsClient({
  initialTests,
  categories,
  initialQuery,
  initialCategory,
}: Props) {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState(initialQuery);
  const [selectedCategory, setSelectedCategory] = useState(initialCategory);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  const { t } = useLocalization("public_test", { en });

  const filteredTests = useMemo(() => {
    return initialTests.filter((test) => {
      const matchesSearch = 
        test.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (test.description || '').toLowerCase().includes(searchQuery.toLowerCase());

      const matchesCategory = !selectedCategory || selectedCategory.length <= 0 ? true :
        selectedCategory === t("all") ||
        (test.categories || ['']).includes(selectedCategory);

      return matchesSearch && matchesCategory;
    });
  }, [initialTests, searchQuery, selectedCategory, t]);

  
  const pushQuery = (query: string, category: string) => {
    const params = new URLSearchParams();
    if (query.trim()) params.set("query", query.trim());
    if (category !== t("all")) params.set("categories", category);
    router.push(`/tests?${params.toString()}`);
  };

  return (
    <main className="container mx-auto px-4 py-8 max-w-7xl bg-gray-50">
      <h1 className="text-3xl md:text-4xl font-bold mb-4 text-center">
        {t("discover_public_tests")}
      </h1>
      <p className="text-gray-600 max-w-3xl mx-auto mb-8 text-center">
        {t("description")}
      </p>

      <div className="max-w-3xl mx-auto mb-6">
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-5 w-5" />
          <Input
            type="search"
            placeholder={t("search_placeholder")}
            className="pl-10 h-12 rounded-lg"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter") pushQuery(searchQuery, selectedCategory);
            }}
            aria-label={t("search_aria_label")}
          />
        </div>

        <div className="flex justify-between items-center mb-4">
          <CategorySelector
            categories={categories}
            selected={selectedCategory}
            onChange={(cat) => {
              setSelectedCategory(cat);
              pushQuery(searchQuery, cat);
            }}
          />
        </div>
      </div>

      <p className="text-gray-600 mb-6 text-center">
        {t("showing_tests")}{" "}
        <span className="font-semibold">{filteredTests.length}</span>{" "}
        {filteredTests.length !== 1 ? t("tests_plural") : t("test_singular")}
      </p>

      <div className="w-full flex gap-2 justify-end mb-6">
        <button
          onClick={() => setViewMode("grid")}
          className={`p-2 rounded-md ${
            viewMode === "grid" ? "bg-gray-200" : "hover:bg-gray-100"
          }`}
          aria-label="Grid View"
        >
          <LayoutGrid className="h-5 w-5" />
        </button>
        <button
          onClick={() => setViewMode("list")}
          className={`p-2 rounded-md ${
            viewMode === "list" ? "bg-gray-200" : "hover:bg-gray-100"
          }`}
          aria-label="List View"
        >
          <List className="h-5 w-5" />
        </button>
      </div>
      {/* List/Grid View */}
      <section
        className={
          viewMode === "grid"
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mx-auto mb-6"
            : "flex flex-col gap-4 mx-auto mb-6"
        }
      >
        {filteredTests.map((test) =>
          viewMode === "grid" ? (
            <TestCard key={test.id} test={test} />
          ) : (
            <TestListItem key={test.id} test={test} />
          )
        )}

        {filteredTests.length === 0 && (
          <p className="col-span-full text-center text-gray-500">
            {t("no_tests_found")}
          </p>
        )}
      </section>

      <CtaSection />
      <FeaturesSection />
    </main>
  );
}
