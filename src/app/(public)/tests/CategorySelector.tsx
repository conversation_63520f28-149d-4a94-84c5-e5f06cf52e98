import React from "react";

interface CategorySelectorProps {
  selected: string;
  onChange: (selected: string) => void;
  categories: string[];
}

export default function CategorySelector({
  selected,
  onChange,
  categories,
}: CategorySelectorProps) {
  return (
    <div className="flex flex-wrap gap-3 justify-center py-2">
      {categories.map((category) => {
        const isSelected = selected === category;
        return (
          <button
            key={category}
            onClick={() => onChange(category)}
            className={`px-4 py-2 rounded-full border transition-colors duration-200
              ${
                isSelected
                  ? "bg-green-600 text-white border-green-600"
                  : "bg-white text-gray-700 border-gray-300 hover:bg-gray-100"
              }
            `}
            type="button"
          >
            {category}
          </button>
        );
      })}
    </div>
  );
}
