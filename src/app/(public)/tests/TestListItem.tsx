"use client";

/* eslint-disable @next/next/no-img-element */
import { useRouter } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Users, Clock } from "lucide-react";
import { TestData, TestDifficulty } from "@/src/lib/repositories/public-tests";
import en from "./locales/en.json";
import { registerTranslation } from "@/src/lib/i18n";

const getDifficultyColor = (difficulty: TestDifficulty) => {
  switch (difficulty) {
    case "EASY":
      return "bg-green-100 text-green-800";
    case "INTERMEDIATE":
      return "bg-blue-100 text-blue-800";
    case "ADVANCED":
      return "bg-purple-100 text-purple-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getSuccessRateColor = (rate: number) => {
  if (rate >= 75) return "text-green-600";
  if (rate >= 50) return "text-blue-600";
  return "text-orange-600";
};

export default function TestListItem({ test }: { test: TestData }) {
  const { t } = registerTranslation("public-test", { en });
  const router = useRouter();

  const isPremium = test.premium?.isPremium ?? false;
  const price = test.premium?.price ?? null;
  const offerText = test.premium?.offerText ?? null;

  const handleTakeTest = () => {
    const userId = localStorage.getItem("user_id");
    if (userId) {
      router.push(`/test-landing/${test.id}`);
    } else {
      router.push(`/join-test?testId=${test.id}`);
    }
  };

  const handleBuyTest = () => {
    router.push(`/buy-test/${test.id}`);
  };

  return (
    <div className="flex w-full gap-6 p-4 border-b border-gray-200 hover:bg-gray-50 transition">
      {/* Thumbnail */}
      <div className="min-w-[160px] max-w-[160px] h-28 overflow-hidden rounded-md border">
        <img
          src={test.cover}
          alt={test.title}
          className="w-full h-full object-cover"
        />
      </div>

      {/* Content */}
      <div className="flex-1 flex flex-col justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            {test.title}
          </h3>
          <p className="text-sm text-gray-600 mb-2">{test.description}</p>

          <div className="flex flex-wrap gap-2 mb-2">
            {test.difficulties.map((difficulty, index) => (
              <Badge
                key={index}
                className={`${getDifficultyColor(difficulty)} text-xs py-0.5 px-2`}
              >
                {difficulty}
              </Badge>
            ))}

            {isPremium &&
              test.premium?.badges?.map((badge, idx) => (
                <Badge
                  key={idx}
                  className="bg-yellow-100 text-yellow-900 border-yellow-300 py-0.5 px-2 text-xs"
                >
                  {badge}
                </Badge>
              ))}
          </div>

          {isPremium && offerText && (
            <div className="text-xs text-yellow-700 bg-yellow-50 border border-yellow-200 px-2 py-1 rounded-md inline-block mb-2">
              {offerText}
            </div>
          )}

          <div className="flex items-center gap-4 text-xs text-gray-600">
            <div className="flex items-center gap-1">
              <Users className="w-3 h-3" />
              <span>
                {t("users").replace("{{count}}", test.usersCount.toLocaleString())}
              </span>
            </div>
            <div className="flex items-center gap-1">
              <Clock className="w-3 h-3" />
              <span>
                {t("duration", { duration: test.durationInSeconds })}
              </span>
            </div>
            <div className="flex items-center gap-1">
              <span>{t("successRate")}:</span>
              <span className={getSuccessRateColor(test.successRate)}>
                {test.successRate}%
              </span>
            </div>
          </div>
        </div>

        <div className="flex items-center justify-between mt-3">
          <div className="flex items-center gap-2">
            <Avatar className="h-7 w-7">
              <AvatarImage
                src={test.creator.avatar || "/placeholder.svg"}
                alt={test.creator.name}
              />
              <AvatarFallback>{test.creator.name.charAt(0)}</AvatarFallback>
            </Avatar>
            <span className="text-sm text-gray-700">{test.creator.name}</span>
          </div>

          {isPremium ? (
            <Button
              onClick={handleBuyTest}
              className="bg-yellow-500 hover:bg-yellow-600 text-yellow-900 px-4 py-1 text-sm"
            >
              {price ? `Buy - ${price}` : "Buy Test"}
            </Button>
          ) : (
            <Button
              onClick={handleTakeTest}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-1 text-sm"
            >
              {t("takeTest")}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
