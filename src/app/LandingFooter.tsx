import Link from "next/link";
import React from "react";

const Footer: React.FC = () => {
  return (
    <footer className="bg-gray-900 text-white py-12">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <div className="flex items-center mb-4">
              <svg
                width="32"
                height="32"
                viewBox="0 0 40 40"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="text-green-500"
              >
                <path
                  d="M20 40C31.0457 40 40 31.0457 40 20C40 8.9543 31.0457 0 20 0C8.9543 0 0 8.9543 0 20C0 31.0457 8.9543 40 20 40Z"
                  fill="currentColor"
                  fillOpacity="0.1"
                />
                <path
                  d="M28.5001 15.5833C28.1221 15.5833 27.7596 15.4341 27.4939 15.1684C27.2283 14.9028 27.0791 14.5403 27.0791 14.1623C27.0791 13.7843 27.2283 13.4218 27.4939 13.1561C27.7596 12.8905 28.1221 12.7413 28.5001 12.7413C28.8781 12.7413 29.2406 12.8905 29.5063 13.1561C29.7719 13.4218 29.9211 13.7843 29.9211 14.1623C29.9211 14.5403 29.7719 14.9028 29.5063 15.1684C29.2406 15.4341 28.8781 15.5833 28.5001 15.5833ZM28.5001 27.2583C28.1221 27.2583 27.7596 27.1091 27.4939 26.8434C27.2283 26.5778 27.0791 26.2153 27.0791 25.8373C27.0791 25.4593 27.2283 25.0968 27.4939 24.8311C27.7596 24.5655 28.1221 24.4163 28.5001 24.4163C28.8781 24.4163 29.2406 24.5655 29.5063 24.8311C29.7719 25.0968 29.9211 25.4593 29.9211 25.8373C29.9211 26.2153 29.7719 26.5778 29.5063 26.8434C29.2406 27.1091 28.8781 27.2583 28.5001 27.2583ZM20.0001 32.2583C19.6221 32.2583 19.2596 32.1091 18.9939 31.8434C18.7283 31.5778 18.5791 31.2153 18.5791 30.8373C18.5791 30.4593 18.7283 30.0968 18.9939 29.8311C19.2596 29.5655 19.6221 29.4163 20.0001 29.4163C20.3781 29.4163 20.7406 29.5655 21.0063 29.8311C21.2719 30.0968 21.4211 30.4593 21.4211 30.8373C21.4211 31.2153 21.2719 31.5778 21.0063 31.8434C20.7406 32.1091 20.3781 32.2583 20.0001 32.2583ZM11.5001 27.2583C11.1221 27.2583 10.7596 27.1091 10.4939 26.8434C10.2283 26.5778 10.0791 26.2153 10.0791 25.8373C10.0791 25.4593 10.2283 25.0968 10.4939 24.8311C10.7596 24.5655 11.1221 24.4163 11.5001 24.4163C11.8781 24.4163 12.2406 24.5655 12.5063 24.8311C12.7719 25.0968 12.9211 25.4593 12.9211 25.8373C12.9211 26.2153 12.7719 26.5778 12.5063 26.8434C12.2406 27.1091 11.8781 27.2583 11.5001 27.2583ZM11.5001 15.5833C11.1221 15.5833 10.7596 15.4341 10.4939 15.1684C10.2283 14.9028 10.0791 14.5403 10.0791 14.1623C10.0791 13.7843 10.2283 13.4218 10.4939 13.1561C10.7596 12.8905 11.1221 12.7413 11.5001 12.7413C11.8781 12.7413 12.2406 12.8905 12.5063 13.1561C12.7719 13.4218 12.9211 13.7843 12.9211 14.1623C12.9211 14.5403 12.7719 14.9028 12.5063 15.1684C12.2406 15.4341 11.8781 15.5833 11.5001 15.5833ZM20.0001 10.5833C19.6221 10.5833 19.2596 10.4341 18.9939 10.1684C18.7283 9.90278 18.5791 9.54028 18.5791 9.16228C18.5791 8.78428 18.7283 8.42178 18.9939 8.15611C19.2596 7.89044 19.6221 7.74128 20.0001 7.74128C20.3781 7.74128 20.7406 7.89044 21.0063 8.15611C21.2719 8.42178 21.4211 8.78428 21.4211 9.16228C21.4211 9.54028 21.2719 9.90278 21.0063 10.1684C20.7406 10.4341 20.3781 10.5833 20.0001 10.5833Z"
                  fill="currentColor"
                />
              </svg>
              <span className="ml-2 text-xl font-bold">SkillPintar</span>
            </div>
            <p className="text-gray-400 mb-4">
              Verify your skills, build your profile, and accelerate your career
              growth.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-white">
                <svg
                  className="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    fillRule="evenodd"
                    d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                    clipRule="evenodd"
                  />
                </svg>
              </a>
              <a href="#" className="text-gray-400 hover:text-white">
                <svg
                  className="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    fillRule="evenodd"
                    d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                    clipRule="evenodd"
                  />
                </svg>
              </a>
              <a href="#" className="text-gray-400 hover:text-white">
                <svg
                  className="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                </svg>
              </a>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-bold mb-4">Product</h3>
            <ul className="space-y-2">
              <li>
                <a href="/#features" className="text-gray-400 hover:text-white">
                  Features
                </a>
              </li>
              <li>
                <a href="/#pricing" className="text-gray-400 hover:text-white">
                  Pricing
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-400 hover:text-white">
                  Test Library
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-400 hover:text-white">
                  Skill Profiles
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-400 hover:text-white">
                  For Employers
                </a>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-bold mb-4">Resources</h3>
            <ul className="space-y-2">
              <li>
                <a href="/blog" className="text-gray-400 hover:text-white">
                  Blog
                </a>
              </li>
              <li>
                <a href="/documentation" className="text-gray-400 hover:text-white">
                  Documentation
                </a>
              </li>
              <li>
                <a href="/guides" className="text-gray-400 hover:text-white">
                  Guides
                </a>
              </li>
              <li>
                <a href="/developer" className="text-gray-400 hover:text-white">
                  Developer
                </a>
              </li>
              <li>
                <a href="/support" className="text-gray-400 hover:text-white">
                  Support
                </a>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-bold mb-4">Company</h3>
            <ul className="space-y-2">
              <li>
                <a href="/about" className="text-gray-400 hover:text-white">
                  About Us
                </a>
              </li>
              <li>
                <a href="/careers" className="text-gray-400 hover:text-white">
                  Careers
                </a>
              </li>
              <li>
                <a href="/contact" className="text-gray-400 hover:text-white">
                  Contact
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-400 hover:text-white">
                  Privacy Policy
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-400 hover:text-white">
                  Terms of Service
                </a>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400">
            © 2025, made by <span className="text-green-500">Skill Pintar</span>{" "}
            learning is sharing.
          </p>
          <div className="flex gap-6 mt-4 md:mt-0">
            <Link href="#" className="text-gray-400 hover:text-white">
              Creative Tim
            </Link>
            <Link href="/about" className="text-gray-400 hover:text-white">
              About Us
            </Link>
            <Link href="/blog" className="text-gray-400 hover:text-white">
              Blog
            </Link>
            <Link href="#" className="text-gray-400 hover:text-white">
              License
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
