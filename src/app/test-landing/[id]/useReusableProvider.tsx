"use client"

import React, { createContext, use<PERSON>ontext, ReactNode } from "react";

export function createReusableProvider<T>() {
  const Context = createContext<T | undefined>(undefined);

  function Provider({ value, children }: { value: T; children: ReactNode }) {
    return <Context.Provider value={value}>{children}</Context.Provider>;
  }

  function useReusableProviderData() {
    const context = useContext(Context);
    if (!context) throw new Error("useReusableProviderData must be used within Provider");
    return context;
  }

  return { Provider, useReusableProviderData };
}
