import Link from "next/link";
import FooterSimple from "../../FooterSimple";
import { TestLandingBenefit } from "./TestLandingBenefit";
import { TestLandingCta } from "./TestLandingCta";
import { TestLandingEncourage } from "./TestLandingEncourage";
import { TestLandingHero } from "./TestLandingHero";
import { TestLandingMedia } from "./TestLandingMedia";
import { TestLandingTestDetails } from "./TestLandingTestDetails";
import { TestDataProvider } from "./testDataProvider";
import { Button } from "@/components/ui/button";
import { repo } from "@/src/lib/repositories/public-tests";

interface Props {
  params: {
    id: string;
  };
}

export default async function TestLanding({ params }: Props) {
  const result = await repo.getById(params.id);

  if (!result.ok) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center px-6 py-12 text-center">
        <div className="max-w-md">
          <div className="text-indigo-600 text-6xl mb-6">⚠️</div>
          <h1 className="text-2xl md:text-3xl font-semibold text-gray-800 mb-4">
            Test Session Not Found
          </h1>
          <p className="text-gray-600 mb-6">
            We couldn’t find the test testData you're looking for. It may have
            been removed or the ID might be incorrect.
          </p>
          <Link href="/">
            <Button variant="default">Back to Home</Button>
          </Link>
        </div>
      </div>
    );
  }

  const testData = result.value;

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <div className="flex-1 container mx-auto p-4 md:p-8">
        <TestDataProvider value={testData}>
          <div className="bg-white rounded-3xl shadow-sm overflow-hidden p-6 md:p-10 max-w-4xl mx-auto">
            <TestLandingHero />
            <TestLandingMedia />
            <TestLandingTestDetails />
            <TestLandingBenefit />
            <TestLandingCta />
            <TestLandingEncourage />
          </div>
        </TestDataProvider>
      </div>
      <FooterSimple />
    </div>
  );
}
