"use client"

import { useTestData } from "./testDataProvider";

export function TestLandingTestDetails() {
  const testData = useTestData();

  return (
    <div>
      <div className="text-center mb-6">
        <p className="text-gray-500 max-w-xl mx-auto">{testData.description}</p>
      </div>
      <div className="flex flex-col md:flex-row justify-center items-center gap-4 md:gap-10 mb-10 text-center">
        <div>
          <p className="text-gray-600">
            Total Questions:{" "}
            <span className="font-bold text-gray-900">
              {testData.totalQuestions}
            </span>
          </p>
        </div>
        <div className="hidden md:block w-px h-6 bg-gray-300"></div>
        <div>
          <p className="text-gray-600">
            Duration:{" "}
            <span className="font-bold text-gray-900">{testData.durationInSeconds}</span>
          </p>
        </div>
        <div className="hidden md:block w-px h-6 bg-gray-300"></div>
        <div>
          <p className="text-gray-600">
            Tried by:{" "}
            <span className="font-bold text-gray-900">{testData.usersCount}</span>
          </p>
        </div>
        <div className="hidden md:block w-px h-6 bg-gray-300"></div>
        <div>
          <p className="text-gray-600">
            Success Rate:{" "}
            <span className="font-bold text-green-600">{testData.successRate}%</span>
          </p>
        </div>
      </div>
    </div>
  );
}
