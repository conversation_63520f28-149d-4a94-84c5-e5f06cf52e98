"use client"

import Image from "next/image";
import { useTestData } from "./testDataProvider";

export function TestLandingMedia() {
  const { mediaUrl } = useTestData();

  // Simple check for video extensions
  const isVideo =
    mediaUrl &&
    /\.(mp4|webm|ogg|mov|mkv)$/i.test(mediaUrl);

  // Simple check for image extensions
  const isImage =
    mediaUrl &&
    /\.(jpg|jpeg|png|gif|bmp|svg|webp)$/i.test(mediaUrl);

  if (!mediaUrl) {
    return null; // no media to show
  }

  if (isVideo) {
    return (
      <div className="relative aspect-video bg-black rounded-xl overflow-hidden mb-10">
        <video
          src={mediaUrl}
          controls
          className="object-cover w-full h-full"
        />
      </div>
    );
  }

  if (isImage) {
    return (
      <div className="relative aspect-video bg-black rounded-xl overflow-hidden mb-10">
        <Image
          src={mediaUrl}
          alt="Test related media"
          fill
          className="object-cover"
        />
      </div>
    );
  }

  return null; // fallback if url exists but no recognized type
}
