"use client";

import Link from "next/link";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";

const Header = () => {
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 0);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <header className="sticky top-5 mx-auto mt-4 w-[95%] z-50 rounded-xl bg-white/60 backdrop-blur border border-gray-200 shadow-sm">
      <div className="container mx-auto px-4 py-4 flex items-center justify-between">
        {/* Logo */}
        <Link href="/">
          <div className="flex items-center">
            <svg
              width="32"
              height="32"
              viewBox="0 0 40 40"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="text-orange-500"
            >
              <path
                d="M20 40C31.0457 40 40 31.0457 40 20C40 8.9543 31.0457 0 20 0C8.9543 0 0 8.9543 0 20C0 31.0457 8.9543 40 20 40Z"
                fill="currentColor"
                fillOpacity="0.1"
              />
              <path
                d="M28.5 15.5833c-.378 0-.7405-.1492-1.0062-.4149s-.4149-.6285-.4149-1.0065.1492-.7405.4149-1.0062.6282-.4149 1.0062-.4149.7405.1492 1.0062.4149.4149.6282.4149 1.0062-.1492.7405-.4149 1.0062-.6282.4149-1.0062.4149Zm0 11.675c-.378 0-.7405-.1492-1.0062-.4149s-.4149-.6285-.4149-1.0065.1492-.7405.4149-1.0062.6282-.4149 1.0062-.4149.7405.1492 1.0062.4149.4149.6282.4149 1.0062-.1492.7405-.4149 1.0062-.6282.4149-1.0062.4149Zm-8.5 5c-.378 0-.7405-.1492-1.0062-.4149s-.4149-.6285-.4149-1.0065.1492-.7405.4149-1.0062.6282-.4149 1.0062-.4149.7405.1492 1.0062.4149.4149.6282.4149 1.0062-.1492.7405-.4149 1.0062-.6282.4149-1.0062.4149ZM11.5 27.2583c-.378 0-.7405-.1492-1.0062-.4149s-.4149-.6285-.4149-1.0065.1492-.7405.4149-1.0062.6282-.4149 1.0062-.4149.7405.1492 1.0062.4149.4149.6282.4149 1.0062-.1492.7405-.4149 1.0062-.6282.4149-1.0062.4149ZM11.5 15.5833c-.378 0-.7405-.1492-1.0062-.4149s-.4149-.6285-.4149-1.0065.1492-.7405.4149-1.0062.6282-.4149 1.0062-.4149.7405.1492 1.0062.4149.4149.6282.4149 1.0062-.1492.7405-.4149 1.0062-.6282.4149-1.0062.4149ZM20 10.5833c-.378 0-.7405-.1492-1.0062-.4149s-.4149-.6285-.4149-1.0065.1492-.7405.4149-1.0062.6282-.4149 1.0062-.4149.7405.1492 1.0062.4149.4149.6282.4149 1.0062-.1492.7405-.4149 1.0062-.6282.4149-1.0062.4149Z"
                fill="currentColor"
              />
            </svg>
            <span className="ml-2 text-xl font-bold">SkillPintar</span>
          </div>
        </Link>

        {/* Navigation */}
        <nav className="hidden lg:flex items-center space-x-8">
          <Link href="/#features" className="text-gray-600 hover:text-gray-900">
            Features
          </Link>
          <Link
            href="/#how-it-works"
            className="text-gray-600 hover:text-gray-900"
          >
            How It Works
          </Link>
          <Link href="/#pricing" className="text-gray-600 hover:text-gray-900">
            Pricing
          </Link>
          <Link
            href="/#testimonials"
            className="text-gray-600 hover:text-gray-900"
          >
            Testimonials
          </Link>
        </nav>

        {/* Actions */}
        <div className="flex items-center gap-4">
          <Link
            href="/join-test?code=1"
            className="hidden md:inline-flex text-gray-600 hover:text-gray-900"
          >
            Join a Test
          </Link>
          <Button className="bg-green-600 hover:bg-green-700">
            <Link
              href="/tests"
              className="inline-flex text-white"
            >
              Go to Public Tests
            </Link>
          </Button>
        </div>
      </div>
    </header>
  );
};

export default Header;
