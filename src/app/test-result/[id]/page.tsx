import SkillTestResult from "@/components/skill-test-result/skill-test-result";
import { repo } from "@/src/lib/repositories/test-result";

interface Props {
  params: {
    id: string;
  };
}

export default async function TestResultPage({ params }: Props) {
  let result, questionAnalysis, recommendations;
  try {
    result = await repo.getById(params.id);
    questionAnalysis = await repo.getQuestionAnalysisByTestResultId(params.id);
    recommendations = await repo.getRecommendationsByTestResultId(params.id);
  } catch {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="text-center">
          <h1 className="text-3xl font-semibold mb-2">
            ⚠️ Test Result Not Found
          </h1>
          <p className="mb-4 text-gray-600">
            The test result you’re looking for does not exist or has been
            removed.
          </p>
          <a
            href="/"
            className="text-indigo-600 underline hover:text-indigo-800"
          >
            Back to Home
          </a>
        </div>
      </div>
    );
  }

  return (
    <SkillTestResult
      result={result}
      recommendations={recommendations}
      questionAnalysis={questionAnalysis}
    />
  );
}
