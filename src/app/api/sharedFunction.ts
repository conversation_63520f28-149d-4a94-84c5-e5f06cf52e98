import { authBusinessLogic } from "@/src/lib/repositories/businessLogics";
import { ResponseWrapper } from "@/src/lib/types/responseWrapper";
import { NextRequest, NextResponse } from "next/server";
import { ERROR_CODES } from "./error_codes";
import { SessionContext } from "@/src/lib/repositories/auth/types";

type SessionContextSuccess = { context: SessionContext, response: null };
type SessionContextError = { response: NextResponse, context: null };

export async function buildSessionContext(
  nextRequest: NextRequest
): Promise<SessionContextSuccess | SessionContextError> {
  const token = nextRequest.headers.get('x-internal-token')!;
  const organizationId = nextRequest.headers.get('x-organization-id')!;
  const validateResult = await authBusinessLogic.validateToken(token);

  if (!validateResult.user) {
    return {
      context: null,
      response: NextResponse.json(
        new ResponseWrapper(
          "failed",
          undefined,
          validateResult.error ? [validateResult.error] : [],
          [ERROR_CODES.INVALID_TOKEN]
        ),
        { status: 401 }
      ),
    };
  }

  return {
    response: null,
    context: {
      user: validateResult.user,
      organization: organizationId ? { id: organizationId } : undefined,
    },
  };
}
