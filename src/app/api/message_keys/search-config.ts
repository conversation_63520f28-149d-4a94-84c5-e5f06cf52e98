// Search configuration message keys for labels and descriptions
export const SEARCH_CONFIG_KEYS = {
  // My courses search config
  COURSE_TITLE: "api.search_config.course.title",
  COURSE_INSTRUCTOR: "api.search_config.course.instructor",
  COURSE_COMPLETION: "api.search_config.course.completion",
  COURSE_HOURS: "api.search_config.course.hours",
  COURSE_CATEGORY: "api.search_config.course.category",
  COURSE_IS_COMPLETED: "api.search_config.course.is_completed",
  SORT_TITLE: "api.search_config.sort.title",
  SORT_INSTRUCTOR: "api.search_config.sort.instructor",
  SORT_COMPLETION: "api.search_config.sort.completion",
  SORT_HOURS: "api.search_config.sort.hours",
  CATEGORY_DEVELOPMENT: "api.search_config.category.development",
  CATEGORY_DESIGN: "api.search_config.category.design",
  CATEGORY_BUSINESS: "api.search_config.category.business",
  CATEGORY_MARKETING: "api.search_config.category.marketing",
  CATEGORY_DATA_SCIENCE: "api.search_config.category.data_science",
  CATEGORY_OTHER: "api.search_config.category.other",
  COMPLETION_PERCENTAGE: "api.search_config.completion_percentage",
  ESTIMATED_HOURS: "api.search_config.estimated_hours",
  IS_COMPLETED: "api.search_config.is_completed",


  // Sort options
  SORT_NAME: "api.search_config.sort.name",
  SORT_EMAIL: "api.search_config.sort.email",
  SORT_PHONE: "api.search_config.sort.phone",
  SORT_CREATED_DATE: "api.search_config.sort.created_date",
  SORT_UPDATED_DATE: "api.search_config.sort.updated_date",
  SORT_STATUS: "api.search_config.sort.status",

  // Date filter options
  DATE_TODAY: "api.search_config.date.today",
  DATE_YESTERDAY: "api.search_config.date.yesterday",
  DATE_THIS_WEEK: "api.search_config.date.this_week",
  DATE_LAST_WEEK: "api.search_config.date.last_week",
  DATE_THIS_MONTH: "api.search_config.date.this_month",
  DATE_LAST_MONTH: "api.search_config.date.last_month",
  DATE_THIS_YEAR: "api.search_config.date.this_year",
  DATE_LAST_YEAR: "api.search_config.date.last_year",
  DATE_CUSTOM: "api.search_config.date.custom",
  DATE_ALL: "api.search_config.date.all",

  // Date filter descriptions
  DATE_TODAY_DESC: "api.search_config.date.today_desc",
  DATE_YESTERDAY_DESC: "api.search_config.date.yesterday_desc",
  DATE_THIS_WEEK_DESC: "api.search_config.date.this_week_desc",
  DATE_LAST_WEEK_DESC: "api.search_config.date.last_week_desc",
  DATE_THIS_MONTH_DESC: "api.search_config.date.this_month_desc",
  DATE_LAST_MONTH_DESC: "api.search_config.date.last_month_desc",
  DATE_THIS_YEAR_DESC: "api.search_config.date.this_year_desc",
  DATE_LAST_YEAR_DESC: "api.search_config.date.last_year_desc",
  DATE_CUSTOM_DESC: "api.search_config.date.custom_desc",
  DATE_ALL_DESC: "api.search_config.date.all_desc",

  // Status options
  STATUS_ACTIVE: "api.search_config.status.active",
  STATUS_INACTIVE: "api.search_config.status.inactive",
  STATUS_PENDING: "api.search_config.status.pending",
  STATUS_ARCHIVED: "api.search_config.status.archived",
  STATUS_DELETED: "api.search_config.status.deleted",

  // Created by options
  CREATED_BY_SYSTEM: "api.search_config.created_by.system",
  CREATED_BY_ADMIN: "api.search_config.created_by.admin",
  CREATED_BY_USER: "api.search_config.created_by.user",
  CREATED_BY_IMPORT: "api.search_config.created_by.import",
  CREATED_BY_API: "api.search_config.created_by.api",

  // Boolean options
  BOOLEAN_YES: "api.search_config.boolean.yes",
  BOOLEAN_NO: "api.search_config.boolean.no",

  // Placeholders
  PLACEHOLDER_NAME: "api.search_config.placeholder.name",
  PLACEHOLDER_PHONE: "api.search_config.placeholder.phone",
  PLACEHOLDER_EMAIL: "api.search_config.placeholder.email"
} as const;
