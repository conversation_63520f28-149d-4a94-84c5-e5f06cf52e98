import { NextRequest, NextResponse } from "next/server";
import { repo } from "@/src/lib/repositories/settings";
import { securitySettingsSchema, ERROR_CODES } from "../../schemas";
import { 
  validateRequestBody, 
  handleServerError, 
  handleSuccessResponse,
  handleNotFoundError 
} from "../../validation-utils";

export async function PUT(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId") || "default-user"; // In real app, get from auth
    
    const data = await req.json();
    
    const validatedData = validateRequestBody(securitySettingsSchema, data);
    if (validatedData instanceof NextResponse) {
      return validatedData;
    }
    
    const updated = await repo.updateSecuritySettings(userId, validatedData);
    if (!updated) {
      return handleNotFoundError("User settings not found");
    }
    
    return handleSuccessResponse(updated);
  } catch (error) {
    return handleServerError("Failed to update security settings.", ERROR_CODES.UPDATE_FAILED);
  }
}
