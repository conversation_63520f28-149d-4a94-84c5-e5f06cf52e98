import { NextRequest, NextResponse } from "next/server";
import { repo } from "@/src/lib/repositories/settings";
import { userSettingsSchema, ERROR_CODES } from "../schemas";
import { 
  validateRequestBody, 
  handleServerError, 
  handleSuccessResponse,
  handleNotFoundError 
} from "../validation-utils";

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId") || "default-user"; // In real app, get from auth
    
    const result = await repo.getByUserId(userId);
    if (!result) {
      return handleNotFoundError("User settings not found");
    }
    
    return handleSuccessResponse(result);
  } catch (error) {
    return handleServerError("Failed to fetch user settings.", ERROR_CODES.GET_BY_ID_FAILED);
  }
}

export async function POST(req: NextRequest) {
  try {
    const data = await req.json();
    
    const validatedData = validateRequestBody(userSettingsSchema, data);
    if (validatedData instanceof NextResponse) {
      return validatedData;
    }
    
    const created = await repo.create(validatedData);
    return handleSuccessResponse(created, 201);
  } catch (error) {
    return handleServerError("Failed to create user settings.", ERROR_CODES.CREATE_FAILED);
  }
}

export async function PUT(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId") || "default-user"; // In real app, get from auth
    
    const data = await req.json();
    
    const validatedData = validateRequestBody(userSettingsSchema.partial(), data);
    if (validatedData instanceof NextResponse) {
      return validatedData;
    }
    
    const updated = await repo.update(userId, validatedData);
    if (!updated) {
      return handleNotFoundError("User settings not found");
    }
    
    return handleSuccessResponse(updated);
  } catch (error) {
    return handleServerError("Failed to update user settings.", ERROR_CODES.UPDATE_FAILED);
  }
}
