import { NextRequest, NextResponse } from "next/server";
import { repo } from "@/src/lib/repositories/dashboard/progress";

export async function GET(req: NextRequest, { params }: { params: { userId: string } }) {
  const userProgress = await repo.getByUserId(params.userId);
  if (!userProgress) return NextResponse.json({ error: "Dashboard progress not found" }, { status: 404 });
  return NextResponse.json(userProgress);
}