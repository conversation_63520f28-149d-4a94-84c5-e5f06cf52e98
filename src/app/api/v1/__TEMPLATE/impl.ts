import { TEMPLATE_CAPITALIZEDBusinessLogicInterface, TEMPLATE_CAPITALIZED, TEMPLATE_CAPITALIZEDCreateInput, TEMPLATE_CAPITALIZEDUpdateInput } from "@/src/lib/repositories/__TEMPLATE";
import { ResponseWrapper } from "@/src/lib/types/responseWrapper";
import { TEMPLATE_CAPITALIZEDCreateSchema, TEMPLATE_CAPITALIZEDUpdateSchema } from "@/src/lib/validations/__TEMPLATE";
import { ERROR_CODES } from "@/src/app/api/error_codes";

// Create TEMPLATE_CAPITALIZED Implementation
export async function implHandleCreateTEMPLATE_CAPITALIZED(
  data: any,
  businessLogic: TEMPLATE_CAPITALIZEDBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate input data
    const validationResult = TEMPLATE_CAPITALIZEDCreateSchema.safeParse(data);
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err =>
        `${err.path.join('.')}: ${err.message}`
      );

      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          errors,
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    const TEMPLATE_CAMELCASED = await businessLogic.create(validationResult.data as TEMPLATE_CAPITALIZEDCreateInput);

    return {
      status: 201,
      body: new ResponseWrapper("success", TEMPLATE_CAMELCASED),
    };
  } catch (error: any) {
    console.error("Create TEMPLATE_CAMELCASED error:", error);

    if (error.code === "DUPLICATE_PHONE" || error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE]
        ),
      };
    }

    if (error.code === "INVALID_EMAIL") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to create TEMPLATE_CAMELCASED. Please try again."],
        [ERROR_CODES.CREATE_FAILED]
      ),
    };
  }
}

// Update TEMPLATE_CAPITALIZED Implementation
export async function implHandleUpdateTEMPLATE_CAPITALIZED(
  id: string,
  data: any,
  businessLogic: TEMPLATE_CAPITALIZEDBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Check for empty update object
    if (!data || Object.keys(data).length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["No data provided for update"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // Validate input data
    const validationResult = TEMPLATE_CAPITALIZEDUpdateSchema.safeParse(data);
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err =>
        `${err.path.join('.')}: ${err.message}`
      );

      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          errors,
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    const TEMPLATE_CAMELCASED = await businessLogic.update(id, validationResult.data);

    if (!TEMPLATE_CAMELCASED) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["TEMPLATE_CAPITALIZED not found"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", TEMPLATE_CAMELCASED),
    };
  } catch (error: any) {
    console.error("Update TEMPLATE_CAMELCASED error:", error);

    if (error.code === "DUPLICATE_PHONE" || error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE]
        ),
      };
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to update TEMPLATE_CAMELCASED. Please try again."],
        [ERROR_CODES.UPDATE_FAILED]
      ),
    };
  }
}

// Delete TEMPLATE_CAPITALIZED Implementation
export async function implHandleDeleteTEMPLATE_CAPITALIZED(
  id: string,
  businessLogic: TEMPLATE_CAPITALIZEDBusinessLogicInterface,
  hardDelete: boolean = false
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    const success = await businessLogic.delete(id, hardDelete);

    if (!success) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["TEMPLATE_CAPITALIZED not found"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", { message: "TEMPLATE_CAPITALIZED deleted successfully" }),
    };
  } catch (error: any) {
    console.error("Delete TEMPLATE_CAMELCASED error:", error);

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to delete TEMPLATE_CAMELCASED. Please try again."],
        [ERROR_CODES.DELETE_FAILED]
      ),
    };
  }
}

// Get TEMPLATE_CAPITALIZED by ID Implementation
export async function implHandleGetTEMPLATE_CAPITALIZED(
  id: string,
  businessLogic: TEMPLATE_CAPITALIZEDBusinessLogicInterface,
  includeDeleted: boolean = false
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    const TEMPLATE_CAMELCASED = await businessLogic.getById(id, includeDeleted);

    if (!TEMPLATE_CAMELCASED) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["TEMPLATE_CAPITALIZED not found"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", TEMPLATE_CAMELCASED),
    };
  } catch (error: any) {
    console.error("Get TEMPLATE_CAMELCASED error:", error);

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to fetch TEMPLATE_CAMELCASED. Please try again."],
        [ERROR_CODES.FETCH_FAILED]
      ),
    };
  }
}

interface GetAllResultPaginated<T> {
  items: T[];
  page: number;
  total: number;
}

// Get All TEMPLATE_CAPITALIZED Implementation (handles all, search, and tag filtering)
export async function implHandleGetAllTEMPLATE_CAPITALIZEDs(
  businessLogic: TEMPLATE_CAPITALIZEDBusinessLogicInterface,
  params?: {
    search?: string;
    includeDeleted?: boolean;
    page?: number;
    limit?: number;
    sorts?: {
      field: keyof TEMPLATE_CAPITALIZED | string;
      direction: "asc" | "desc";
    }[],
    filters?: {
      field: keyof TEMPLATE_CAPITALIZED | string;
      value: TEMPLATE_CAPITALIZED[keyof TEMPLATE_CAPITALIZED] | any;
    }[];
  }
): Promise<{
  status: number;
  body: ResponseWrapper<GetAllResultPaginated<TEMPLATE_CAPITALIZED>>;
}> {
  try {
    // Validate search parameter if provided
    if (params?.search !== undefined) {
      if (!params.search || params.search.trim() === '') {
        return {
          status: 400,
          body: new ResponseWrapper<GetAllResultPaginated<TEMPLATE_CAPITALIZED>>(
            "failed",
            undefined,
            ["Search keyword cannot be empty"],
            [ERROR_CODES.VALIDATION_FAILED]
          ),
        };
      }
    }

    // Validate filters if provided
    if (params?.filters && params.filters.length > 0) {
      for (const filter of params.filters) {
        if (!filter.field || filter.field.trim() === '') {
          return {
            status: 400,
            body: new ResponseWrapper<GetAllResultPaginated<TEMPLATE_CAPITALIZED>>(
              "failed",
              undefined,
              ["Filter field cannot be empty"],
              [ERROR_CODES.VALIDATION_FAILED]
            ),
          };
        }
      }
    }

    // Validate sorts if provided
    if (params?.sorts && params.sorts.length > 0) {
      for (const sort of params.sorts) {
        if (!sort.field || sort.field.trim() === '') {
          return {
            status: 400,
            body: new ResponseWrapper<GetAllResultPaginated<TEMPLATE_CAPITALIZED>>(
              "failed",
              undefined,
              ["Sort field cannot be empty"],
              [ERROR_CODES.VALIDATION_FAILED]
            ),
          };
        }
        if (!['asc', 'desc'].includes(sort.direction)) {
          return {
            status: 400,
            body: new ResponseWrapper<GetAllResultPaginated<TEMPLATE_CAPITALIZED>>(
              "failed",
              undefined,
              ["Sort direction must be 'asc' or 'desc'"],
              [ERROR_CODES.VALIDATION_FAILED]
            ),
          };
        }
      }
    }

    // Build query parameters for the repository
    const queryParams: any = {
      includeDeleted: params?.includeDeleted,
      page: params?.page,
      limit: params?.limit,
    };

    // Add search if provided
    if (params?.search) {
      queryParams.search = params.search.trim();
    }

    // Add filters if provided
    if (params?.filters && params.filters.length > 0) {
      queryParams.filters = params.filters;
    }

    // Add sorts if provided
    if (params?.sorts && params.sorts.length > 0) {
      queryParams.sorts = params.sorts;
    }

    const result = await businessLogic.getAll(queryParams);

    return {
      status: 200,
      body: new ResponseWrapper<GetAllResultPaginated<TEMPLATE_CAPITALIZED>>("success", { ...result, page: queryParams.page }),
    };
  } catch (error: any) {
    console.error("Get TEMPLATE_CAMELCASED error:", error);

    return {
      status: 500,
      body: new ResponseWrapper<GetAllResultPaginated<TEMPLATE_CAPITALIZED>>(
        "failed",
        undefined,
        ["Failed to fetch TEMPLATE_CAMELCASED. Please try again."],
        [ERROR_CODES.FETCH_FAILED]
      ),
    };
  }
}

// Bulk Create TEMPLATE_CAPITALIZED Implementation
export async function implHandleBulkCreateTEMPLATE_CAPITALIZED(
  data: any[],
  businessLogic: TEMPLATE_CAPITALIZEDBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate that data is an array
    if (!Array.isArray(data) || data.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Input must be a non-empty array"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // Validate each item in the array
    const validatedData: TEMPLATE_CAPITALIZEDCreateInput[] = [];
    for (let i = 0; i < data.length; i++) {
      const validationResult = TEMPLATE_CAPITALIZEDCreateSchema.safeParse(data[i]);
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(err =>
          `Item ${i}: ${err.path.join('.')}: ${err.message}`
        );
        return {
          status: 400,
          body: new ResponseWrapper(
            "failed",
            undefined,
            errors,
            [ERROR_CODES.VALIDATION_FAILED]
          ),
        };
      }
      validatedData.push(validationResult.data as TEMPLATE_CAPITALIZEDCreateInput);
    }

    const TEMPLATE_CAMELCASED = await businessLogic.bulkCreate(validatedData);

    return {
      status: 201,
      body: new ResponseWrapper("success", TEMPLATE_CAMELCASED),
    };
  } catch (error: any) {
    console.error("Bulk create TEMPLATE_CAMELCASED error:", error);

    if (error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to bulk create TEMPLATE_CAMELCASED. Please try again."],
        [ERROR_CODES.CREATE_FAILED]
      ),
    };
  }
}

// Bulk Update TEMPLATE_CAPITALIZED Implementation
export async function implHandleBulkUpdateTEMPLATE_CAPITALIZED(
  updates: { id: string; data: any }[],
  businessLogic: TEMPLATE_CAPITALIZEDBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate that updates is an array
    if (!Array.isArray(updates) || updates.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Updates must be a non-empty array"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // Validate each update in the array
    const validatedUpdates: { id: string; data: TEMPLATE_CAPITALIZEDUpdateInput }[] = [];
    for (let i = 0; i < updates.length; i++) {
      const update = updates[i];

      if (!update.id || typeof update.id !== 'string') {
        return {
          status: 400,
          body: new ResponseWrapper(
            "failed",
            undefined,
            [`Update ${i}: ID is required and must be a string`],
            [ERROR_CODES.VALIDATION_FAILED]
          ),
        };
      }

      const validationResult = TEMPLATE_CAPITALIZEDUpdateSchema.safeParse(update.data);
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(err =>
          `Update ${i}: ${err.path.join('.')}: ${err.message}`
        );
        return {
          status: 400,
          body: new ResponseWrapper(
            "failed",
            undefined,
            errors,
            [ERROR_CODES.VALIDATION_FAILED]
          ),
        };
      }

      validatedUpdates.push({
        id: update.id,
        data: validationResult.data
      });
    }

    const updatedCount = await businessLogic.bulkUpdate(validatedUpdates);

    return {
      status: 200,
      body: new ResponseWrapper("success", { updatedCount }),
    };
  } catch (error: any) {
    console.error("Bulk update TEMPLATE_CAMELCASED error:", error);

    if (error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to bulk update TEMPLATE_CAMELCASED. Please try again."],
        [ERROR_CODES.UPDATE_FAILED]
      ),
    };
  }
}

// Bulk Delete TEMPLATE_CAPITALIZED Implementation
export async function implHandleBulkDeleteTEMPLATE_CAPITALIZED(
  ids: string[],
  businessLogic: TEMPLATE_CAPITALIZEDBusinessLogicInterface,
  hardDelete: boolean = false
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate that ids is an array
    if (!Array.isArray(ids) || ids.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["IDs must be a non-empty array"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // Validate each ID
    for (let i = 0; i < ids.length; i++) {
      const id = ids[i];
      if (!id || typeof id !== 'string' || id.trim() === '') {
        return {
          status: 400,
          body: new ResponseWrapper(
            "failed",
            undefined,
            [`ID at index ${i} is required`],
            [ERROR_CODES.VALIDATION_FAILED]
          ),
        };
      }
    }

    const deletedCount = await businessLogic.bulkDelete(ids, hardDelete);

    return {
      status: 200,
      body: new ResponseWrapper("success", { deletedCount }),
    };
  } catch (error: any) {
    console.error("Bulk delete TEMPLATE_CAMELCASED error:", error);

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to bulk delete TEMPLATE_CAMELCASED. Please try again."],
        [ERROR_CODES.DELETE_FAILED]
      ),
    };
  }
}

// Restore TEMPLATE_CAPITALIZED Implementation
export async function implHandleRestoreTEMPLATE_CAPITALIZED(
  id: string,
  businessLogic: TEMPLATE_CAPITALIZEDBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate ID
    if (!id || typeof id !== 'string' || id.trim() === '') {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["TEMPLATE_CAPITALIZED ID is required"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    const restored = await businessLogic.restore(id);

    if (!restored) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["TEMPLATE_CAPITALIZED not found or cannot be restored"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", { restored: true }),
    };
  } catch (error: any) {
    console.error("Restore TEMPLATE_CAMELCASED error:", error);

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to restore TEMPLATE_CAMELCASED. Please try again."],
        [ERROR_CODES.UPDATE_FAILED]
      ),
    };
  }
}