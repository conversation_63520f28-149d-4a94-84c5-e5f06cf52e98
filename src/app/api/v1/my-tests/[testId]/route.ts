import { NextRequest, NextResponse } from "next/server";
import { repo } from "@/src/lib/repositories/my-tests";

export async function GET(req: NextRequest, { params }: { params: { testId: string } }) {
  const test = await repo.getById(params.testId);
  if (!test) return NextResponse.json({ error: "Test not found" }, { status: 404 });
  return NextResponse.json(test);
}

export async function PUT(req: NextRequest, { params }: { params: { testId: string } }) {
  const data = await req.json();
  const updated = await repo.update(params.testId, data);
  if (!updated) return NextResponse.json({ error: "Test not found" }, { status: 404 });
  return NextResponse.json(updated);
}

export async function DELETE(req: NextRequest, { params }: { params: { testId: string } }) {
  const success = await repo.delete(params.testId);
  if (!success) return NextResponse.json({ error: "Test not found" }, { status: 404 });
  return NextResponse.json({ message: 'Internal Server Error' }, { status: 200 })

}
