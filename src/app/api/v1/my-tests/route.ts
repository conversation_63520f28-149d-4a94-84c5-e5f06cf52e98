import { NextRequest, NextResponse } from "next/server";
import { repo } from "@/src/lib/repositories/my-tests";
import { myTestSchema, paginationSchema, ERROR_CODES } from "../schemas";
import {
  validateRequestBody,
  validateQueryParams,
  handleServerError,
  handleSuccessResponse
} from "../validation-utils";

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const params = Object.fromEntries(searchParams.entries());

    const validatedParams = validateQueryParams(paginationSchema, params);
    if (validatedParams instanceof NextResponse) {
      return validatedParams;
    }

    const result = await repo.getAll(validatedParams);
    return handleSuccessResponse(result);
  } catch (error) {
    return handleServerError("Failed to fetch my tests.", ERROR_CODES.GET_ALL_FAILED);
  }
}

export async function POST(req: NextRequest) {
  try {
    const data = await req.json();

    const validatedData = validateRequestBody(myTestSchema, data);
    if (validatedData instanceof NextResponse) {
      return validatedData;
    }

    const created = await repo.create(validatedData);
    return handleSuccessResponse(created, 201);
  } catch (error) {
    return handleServerError("Failed to create my test.", ERROR_CODES.CREATE_FAILED);
  }
}
