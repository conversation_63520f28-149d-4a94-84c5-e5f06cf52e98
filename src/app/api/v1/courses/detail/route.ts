import { NextRequest, NextResponse } from "next/server";
import { repo } from "@/src/lib/repositories/courses";
import { z } from "zod";
import { ERROR_CODES } from "../../schemas";
import {
  validateQueryParams,
  handleServerError,
  handleSuccessResponse,
  handleNotFoundError
} from "../../validation-utils";

const courseDetailQuerySchema = z.object({
  courses_id: z.string().min(1, "Course ID is required"),
});

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const params = Object.fromEntries(searchParams.entries());

    const validatedParams = validateQueryParams(courseDetailQuerySchema, params);
    if (validatedParams instanceof NextResponse) {
      return validatedParams;
    }

    const result = await repo.getDetail(validatedParams.courses_id);
    if (!result) {
      return handleNotFoundError("Course not found");
    }

    return handleSuccessResponse(result);
  } catch (error) {
    return handleServerError("Failed to fetch course detail.", ERROR_CODES.GET_BY_ID_FAILED);
  }
}
