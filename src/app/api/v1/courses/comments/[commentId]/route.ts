import { NextRequest, NextResponse } from "next/server";
import { repo } from "@/src/lib/repositories/courses";

export async function PUT(req: NextRequest, { params }: { params: { commentId: string } }) {
  const data = await req.json();
  const updated = await repo.updateCourseComments(params.commentId, data);
  if (!updated) return NextResponse.json({ error: "Comment not found" }, { status: 404 });
  return NextResponse.json(updated);
}

export async function DELETE(req: NextRequest, { params }: { params: { commentId: string } }) {
  const success = await repo.deleteCourseComments(params.commentId);
  if (!success) return NextResponse.json({ error: "Comment not found" }, { status: 404 });
  return NextResponse.json({ message: 'Internal Server Error' }, { status: 200 })

}
