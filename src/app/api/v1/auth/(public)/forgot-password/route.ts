import { NextRequest, NextResponse } from "next/server";
import { authBusinessLogic } from "@/src/lib/repositories/businessLogics";
import { implHandleForgotPassword } from "./impl";
import { ResponseWrapper } from "@/src/lib/types/responseWrapper";

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const result = await implHandleForgotPassword(body, authBusinessLogic);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("Forgot password route error:", error);
    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        null,
        ["Internal server error"]
      ),
      { status: 500 }
    );
  }
}
