import { AuthBusinessLogicInterface } from "@/src/lib/repositories/auth/AuthBusinessLogicInterface";
import { ForgotPasswordSchema } from "@/src/lib/schemas/auth";
import { ResponseWrapper } from "@/src/lib/types/responseWrapper";

export async function implHandleForgotPassword(
  body: any,
  authBusinessLogic: AuthBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate request body
    const validation = ForgotPasswordSchema.safeParse(body);
    if (!validation.success) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          null,
          validation.error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
        )
      };
    }

    const { email } = validation.data;

    // Request password reset
    const result = await authBusinessLogic.requestPasswordReset(email);

    return {
      status: 200,
      body: new ResponseWrapper(
        "success",
        {
          message: result.message,
          resetToken: result.data?.resetToken // Only for testing
        }
      )
    };

  } catch (error) {
    console.error("Forgot password error:", error);
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        null,
        ["Internal server error"]
      )
    };
  }
}
