import { NextRequest, NextResponse } from "next/server";
import { authBusinessLogic } from "@/src/lib/repositories/businessLogics";
import { implHandleRegister } from "./impl";
import { aiRulesAccessManager, contactAccessManager } from "@/src/lib/repositories/accessManagers";

export async function POST(req: NextRequest) {
  const body = await req.json();
  const result = await implHandleRegister(body, authBusinessLogic, {
    contacts: (groupId) => contactAccessManager(groupId),
    aiRules: (groupId) => aiRulesAccessManager(groupId),
  }
  );

  return NextResponse.json(result.body, { status: result.status });
}
