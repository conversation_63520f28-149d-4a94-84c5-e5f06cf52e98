import { AuthBusinessLogicInterface } from "@/src/lib/repositories/auth/AuthBusinessLogicInterface";
import { ResendVerificationSchema } from "@/src/lib/schemas/auth";
import { ResponseWrapper } from "@/src/lib/types/responseWrapper";

export async function implHandleResendVerification(
  body: any,
  authBusinessLogic: AuthBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate request body
    const validation = ResendVerificationSchema.safeParse(body);
    if (!validation.success) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          null,
          validation.error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
        )
      };
    }

    const { email } = validation.data;

    // Resend verification email
    const result = await authBusinessLogic.resendEmailVerification(email);

    if (!result.success) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          null,
          [result.message]
        )
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper(
        "success",
        {
          message: result.message,
          verificationToken: result.data?.verificationToken // Only for testing
        }
      )
    };

  } catch (error) {
    console.error("Resend verification error:", error);
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        null,
        ["Internal server error"]
      )
    };
  }
}
