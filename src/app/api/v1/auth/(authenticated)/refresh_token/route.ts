import { NextRequest, NextResponse } from "next/server";
import { implHandleRefreshToken } from "./impl";
import { authBusinessLogic } from "@/src/lib/repositories/businessLogics";

export async function POST(req: NextRequest) {
  const body = await req.json();
  const result = await implHandleRefreshToken(body, authBusinessLogic);

  return NextResponse.json(result.body, { status: result.status });
}
