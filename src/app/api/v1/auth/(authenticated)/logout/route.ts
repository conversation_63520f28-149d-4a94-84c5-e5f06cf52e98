import { NextRequest, NextResponse } from "next/server";
import { authBusinessLogic } from "@/src/lib/repositories/businessLogics";
import { implHandleLogout } from "./impl";
import { buildSessionContext } from "@/src/app/api/sharedFunction";

export async function POST(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req);
    if (response) {
      return response;
    }
    const body = await req.json();
    const result = await implHandleLogout(body, authBusinessLogic);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("Logout route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        error: "Internal server error",
        errorCodes: []
      },
      { status: 500 }
    );
  }
}
