import { AuthBusinessLogicInterface } from "@/src/lib/repositories/auth/AuthBusinessLogicInterface";
import { LogoutSchema } from "@/src/lib/schemas/auth";
import { ResponseWrapper } from "@/src/lib/types/responseWrapper";

export async function implHandleLogout(
  body: any,
  authBusinessLogic: AuthBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate request body
    const validation = LogoutSchema.safeParse(body);
    if (!validation.success) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          null,
          validation.error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
        )
      };
    }

    const { token } = validation.data;

    // Logout (invalidate token)
    await authBusinessLogic.logout(token);

    return {
      status: 200,
      body: new ResponseWrapper(
        "success",
        { message: "Logged out successfully" }
      )
    };

  } catch (error) {
    console.error("Logout error:", error);
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        null,
        ["Internal server error"]
      )
    };
  }
}
