import { NextRequest, NextResponse } from "next/server";
import { repo } from "@/src/lib/repositories/collaborators";
import { ResponseWrapper } from "@/src/lib/api/response";
import { ERROR_CODES } from "@/src/lib/api/errorCodes";

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userEmail = searchParams.get("userEmail");
    
    if (!userEmail) {
      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          undefined,
          ["User email is required."],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
        { status: 400 }
      );
    }

    const params = Object.fromEntries(searchParams.entries());
    const result = await repo.getIncomingInvitations(userEmail, params);
    return NextResponse.json(new ResponseWrapper("success", result));
  } catch (error) {
    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to fetch incoming invitations."],
        [ERROR_CODES.GET_ALL_FAILED]
      ),
      { status: 500 }
    );
  }
}
