import { NextRequest, NextResponse } from "next/server";
import { repo } from "@/src/lib/repositories/collaborators";
import { ResponseWrapper } from "@/src/lib/api/response";
import { ERROR_CODES } from "@/src/lib/api/errorCodes";

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const invitedBy = searchParams.get("invitedBy");
    
    if (!invitedBy) {
      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          undefined,
          ["InvitedBy parameter is required."],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
        { status: 400 }
      );
    }

    const params = Object.fromEntries(searchParams.entries());
    const result = await repo.getOutgoingInvitations(invitedBy, params);
    return NextResponse.json(new ResponseWrapper("success", result));
  } catch (error) {
    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to fetch outgoing invitations."],
        [ERROR_CODES.GET_ALL_FAILED]
      ),
      { status: 500 }
    );
  }
}
