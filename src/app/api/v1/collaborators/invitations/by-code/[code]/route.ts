import { NextRequest, NextResponse } from "next/server";
import { repo } from "@/src/lib/repositories/collaborators";
import { ResponseWrapper } from "@/src/lib/types/responseWrapper";

const ERROR_CODES = {
  NOT_FOUND: "ERROR_INVITATION_NOT_FOUND",
  EXPIRED: "ERROR_INVITATION_EXPIRED",
  ALREADY_PROCESSED: "ERROR_INVITATION_ALREADY_PROCESSED",
  UNKNOWN_ERROR: "ERROR_UNKNOWN",
};

export async function GET(_req: NextRequest, { params }: { params: { code: string } }) {
  try {
    const invitation = await repo.getInvitationByCode(params.code);
    
    if (!invitation) {
      return NextResponse.json(
        new ResponseWrapper("failed", undefined, ["Invitation not found"], [ERROR_CODES.NOT_FOUND]),
        { status: 404 }
      );
    }

    // Check if invitation is expired
    if (new Date() > new Date(invitation.expiresAt)) {
      return NextResponse.json(
        new ResponseWrapper("failed", undefined, ["Invitation has expired"], [ERROR_CODES.EXPIRED]),
        { status: 400 }
      );
    }

    // Check if invitation is already processed
    if (invitation.status !== "pending") {
      return NextResponse.json(
        new ResponseWrapper("failed", undefined, ["Invitation has already been processed"], [ERROR_CODES.ALREADY_PROCESSED]),
        { status: 400 }
      );
    }

    return NextResponse.json(new ResponseWrapper("success", invitation));
  } catch (error) {
    console.error("Get invitation by code error:", error);
    return NextResponse.json(
      new ResponseWrapper("failed", undefined, ["Unknown error occurred"], [ERROR_CODES.UNKNOWN_ERROR]),
      { status: 500 }
    );
  }
}
