import { NextRequest, NextResponse } from "next/server";
import { repo } from "@/src/lib/repositories/my-courses";
import { myCourseNoteSchema, ERROR_CODES } from "../../schemas";
import {
  validateRequestBody,
  handleServerError,
  handleSuccessResponse
} from "../../validation-utils";
import { z } from "zod";

const notesQuerySchema = z.object({
  filter: z.enum(["this-video", "this-section", "all"]).optional(),
  id: z.string().optional(),
});

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const params = Object.fromEntries(searchParams.entries());

    const validatedParams = notesQuerySchema.safeParse(params);
    const queryParams = validatedParams.success ? validatedParams.data : {};

    const result = await repo.getCourseNotes(queryParams);
    return handleSuccessResponse(result);
  } catch (error) {
    return handleServerError("Failed to fetch course notes.", ERROR_CODES.GET_ALL_FAILED);
  }
}

export async function POST(req: NextRequest) {
  try {
    const data = await req.json();

    const validatedData = validateRequestBody(myCourseNoteSchema, data);
    if (validatedData instanceof NextResponse) {
      return validatedData;
    }

    const created = await repo.createCourseNotes(validatedData);
    return handleSuccessResponse(created, 201);
  } catch (error) {
    return handleServerError("Failed to create course note.", ERROR_CODES.CREATE_FAILED);
  }
}