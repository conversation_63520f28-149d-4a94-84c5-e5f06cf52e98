import { NextRequest, NextResponse } from "next/server";
import { repo } from "@/src/lib/repositories/my-courses";

export async function PUT(req: NextRequest, { params }: { params: { notesId: string } }) {
  const data = await req.json();
  const updated = await repo.updateCourseNotes(params.notesId, data);
  if (!updated) return NextResponse.json({ error: "Courses notes not found" }, { status: 404 });
  return NextResponse.json(updated);
}

export async function DELETE(req: NextRequest, { params }: { params: { notesId: string } }) {
  const success = await repo.deleteCourseNotes(params.notesId);
  if (!success) return NextResponse.json({ error: "Courses notes not found" }, { status: 404 });
  return NextResponse.json({ message: 'Internal Server Error' }, { status: 200 })

}
