import { NextRequest, NextResponse } from "next/server";
import { repo } from "@/src/lib/repositories/my-courses";
import { myCourseFeedbackSchema, ERROR_CODES } from "../../schemas";
import {
  validateRequestBody,
  handleServerError,
  handleSuccessResponse
} from "../../validation-utils";

export async function POST(req: NextRequest) {
  try {
    const data = await req.json();

    const validatedData = validateRequestBody(myCourseFeedbackSchema, data);
    if (validatedData instanceof NextResponse) {
      return validatedData;
    }

    const created = await repo.createCoursesFeedback(validatedData);
    return handleSuccessResponse(created, 201);
  } catch (error) {
    return handleServerError("Failed to create course feedback.", ERROR_CODES.CREATE_FAILED);
  }
}