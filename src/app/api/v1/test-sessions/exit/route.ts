import { NextRequest, NextResponse } from "next/server";
import { TestSessionBusinessLogicInstance } from "@/src/lib/repositories/test-session";
import { implHandleExitSession } from "../impl";
import { ERROR_CODES } from "@/src/app/api/error_codes";
import { ResponseWrapper } from "@/src/lib/types/responseWrapper";

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const result = await implHandleExitSession(body, TestSessionBusinessLogicInstance);
    return NextResponse.json(new ResponseWrapper("success", result.body), { status: result.status});
  } catch (error) {
    console.error("Exit session error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}