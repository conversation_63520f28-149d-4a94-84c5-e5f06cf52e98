import { TestSessionBusinessLogicInterface, TestSession, TestSessionCreateInput, TestSessionUpdateInput, SubmittedAnswer } from "@/src/lib/repositories/test-session/interface";
import { ResponseWrapper } from "@/src/lib/types/responseWrapper";
import { TestSessionCreateSchema, TestSessionUpdateSchema, SubmitAnswersSchema, ExitSessionSchema } from "@/src/lib/validations/test-sessions";
import { ERROR_CODES } from "@/src/app/api/error_codes";

// Create Test Session Implementation
export async function implHandleCreateTestSession(
  data: any,
  businessLogic: TestSessionBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate input data
    const validationResult = TestSessionCreateSchema.safeParse(data);
    
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err =>
        `${err.path.join('.')}: ${err.message}`
      );

      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          errors,
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    const session = await businessLogic.create(validationResult.data as TestSessionCreateInput);

    return {
      status: 201,
      body: new ResponseWrapper("success", session),
    };
  } catch (error: any) {
    console.error("Create test session error:", error);

    if (error.code === "ACTIVE_SESSION_EXISTS") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to create test session. Please try again."],
        [ERROR_CODES.CREATE_FAILED]
      ),
    };
  }
}

// Update Test Session Implementation
export async function implHandleUpdateTestSession(
  id: string,
  data: any,
  businessLogic: TestSessionBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Check for empty update object
    if (!data || Object.keys(data).length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["No data provided for update"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // Validate input data
    const validationResult = TestSessionUpdateSchema.safeParse(data);
    
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err =>
        `${err.path.join('.')}: ${err.message}`
      );

      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          errors,
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    const session = await businessLogic.update(id, validationResult.data);

    if (!session) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Test session not found"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", session),
    };
  } catch (error: any) {
    console.error("Update test session error:", error);

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to update test session. Please try again."],
        [ERROR_CODES.UPDATE_FAILED]
      ),
    };
  }
}

// Delete Test Session Implementation
export async function implHandleDeleteTestSession(
  id: string,
  businessLogic: TestSessionBusinessLogicInterface,
  hardDelete: boolean = false
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    const success = await businessLogic.delete(id, hardDelete);

    if (!success) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Test session not found"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", { message: "Test session deleted successfully" }),
    };
  } catch (error: any) {
    console.error("Delete test session error:", error);

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to delete test session. Please try again."],
        [ERROR_CODES.DELETE_FAILED]
      ),
    };
  }
}

// Get Test Session by ID Implementation
export async function implHandleGetTestSessionById(
  businessLogic: TestSessionBusinessLogicInterface,
  id: string,
  includeDeleted: boolean = false
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    const session = await businessLogic.getById(id, includeDeleted);

    if (!session) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Test session not found"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", session),
    };
  } catch (error: any) {
    console.error("Get test session error:", error);

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to fetch test session. Please try again."],
        [ERROR_CODES.FETCH_FAILED]
      ),
    };
  }
}

interface GetAllResultPaginated<T> {
  items: T[];
  page: number;
  total: number;
}

// Get All Test Sessions Implementation
export async function implHandleGetAllTestSessions(
  businessLogic: TestSessionBusinessLogicInterface,
  params?: {
    search?: string;
    includeDeleted?: boolean;
    page?: number;
    limit?: number;
    sorts?: {
      field: keyof TestSession | string;
      direction: "asc" | "desc";
    }[],
    filters?: {
      field: keyof TestSession | string;
      value: TestSession[keyof TestSession] | any;
    }[];
  }
): Promise<{
  status: number;
  body: ResponseWrapper<GetAllResultPaginated<TestSession>>;
}> {
  try {
    // Validate search parameter if provided
    if (params?.search !== undefined) {
      if (!params.search || params.search.trim() === '') {
        return {
          status: 400,
          body: new ResponseWrapper<GetAllResultPaginated<TestSession>>(
            "failed",
            undefined,
            ["Search keyword cannot be empty"],
            [ERROR_CODES.VALIDATION_FAILED]
          ),
        };
      }
    }

    // Build query parameters for the repository
    const queryParams: any = {
      includeDeleted: params?.includeDeleted,
      page: params?.page,
      limit: params?.limit,
    };

    // Add search if provided
    if (params?.search) {
      queryParams.search = params.search.trim();
    }

    // Add filters if provided
    if (params?.filters && params.filters.length > 0) {
      queryParams.filters = params.filters;
    }

    // Add sorts if provided
    if (params?.sorts && params.sorts.length > 0) {
      queryParams.sorts = params.sorts;
    }

    const result = await businessLogic.getAll(queryParams);

    return {
      status: 200,
      body: new ResponseWrapper<GetAllResultPaginated<TestSession>>("success", { ...result, page: queryParams.page }),
    };
  } catch (error: any) {
    console.error("Get test sessions error:", error);

    return {
      status: 500,
      body: new ResponseWrapper<GetAllResultPaginated<TestSession>>(
        "failed",
        undefined,
        ["Failed to fetch test sessions. Please try again."],
        [ERROR_CODES.FETCH_FAILED]
      ),
    };
  }
}

// Submit Answers Implementation
export async function implHandleSubmitAnswers(
  data: any,
  businessLogic: TestSessionBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    const validationResult = SubmitAnswersSchema.safeParse(data);
    
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err =>
        `${err.path.join('.')}: ${err.message}`
      );

      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          errors,
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    const { sessionId, answers } = validationResult.data;
    const session = await businessLogic.submitAnswers(sessionId, answers);

    if (!session) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Test session not found"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    // If the repository supports getting test results, try to get them too
    let testResult = null;
    try {
      // Try to get test result if the repository has the method
      if ('submitAnswersAndGetResult' in businessLogic.db) {
        testResult = await (businessLogic.db as any).submitAnswersAndGetResult(sessionId, answers);
      }
    } catch (error) {
      console.warn("Could not get test result:", error);
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", {
        session,
        testResult // Include test result if available for backward compatibility
      }),
    };
  } catch (error: any) {
    console.error("Submit answers error:", error);

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to submit answers. Please try again."],
        [ERROR_CODES.UPDATE_FAILED]
      ),
    };
  }
}

// Exit Session Implementation
export async function implHandleExitSession(
  data: any,
  businessLogic: TestSessionBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    const validationResult = ExitSessionSchema.safeParse(data);
    
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err =>
        `${err.path.join('.')}: ${err.message}`
      );

      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          errors,
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    const { sessionId, exitReason, timeSpent } = validationResult.data;
    const session = await businessLogic.exitSession(sessionId, exitReason, timeSpent);

    if (!session) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Test session not found"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", session),
    };
  } catch (error: any) {
    console.error("Exit session error:", error);

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to exit session. Please try again."],
        [ERROR_CODES.UPDATE_FAILED]
      ),
    };
  }
}

// Restore Test Session Implementation
export async function implHandleRestoreTestSession(
  id: string,
  businessLogic: TestSessionBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate ID
    if (!id || typeof id !== 'string' || id.trim() === '') {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Test Session ID is required"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    const restored = await businessLogic.restore(id);

    if (!restored) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Test session not found or cannot be restored"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", { restored: true }),
    };
  } catch (error: any) {
    console.error("Restore test session error:", error);

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to restore test session. Please try again."],
        [ERROR_CODES.UPDATE_FAILED]
      ),
    };
  }
}