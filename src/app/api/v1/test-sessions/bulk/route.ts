import { NextRequest, NextResponse } from "next/server";
import { TestSessionBusinessLogicInstance } from "@/src/lib/repositories/test-session";
import { ERROR_CODES } from "@/src/app/api/error_codes";
import { ResponseWrapper } from "@/src/lib/types/responseWrapper";
import { TestSessionCreateSchema, TestSessionUpdateSchema } from "@/src/lib/validations/test-sessions";

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    
    // Validate that body is an array
    if (!Array.isArray(body) || body.length === 0) {
      return NextResponse.json(
        {
          status: "failed",
          data: null,
          errors: ["Input must be a non-empty array"],
          errorCodes: [ERROR_CODES.VALIDATION_FAILED]
        },
        { status: 400 }
      );
    }

    // Validate each item in the array
    for (let i = 0; i < body.length; i++) {
      const validationResult = TestSessionCreateSchema.safeParse(body[i]);
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(err =>
          `Item ${i}: ${err.path.join('.')}: ${err.message}`
        );
        return NextResponse.json(
          {
            status: "failed",
            data: null,
            errors,
            errorCodes: [ERROR_CODES.VALIDATION_FAILED]
          },
          { status: 400 }
        );
      }
    }

    const sessions = await TestSessionBusinessLogicInstance.bulkCreate(body);
    return NextResponse.json(new ResponseWrapper("success", sessions), { status: 201 });
  } catch (error: any) {
    console.error("Bulk create test sessions error:", error);
    
    if (error.code === "ACTIVE_SESSIONS_EXIST") {
      return NextResponse.json(
        new ResponseWrapper("failed", undefined, [error.message], [ERROR_CODES.DUPLICATE_RESOURCE]),
        { status: 409 }
      );
    }

    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}

export async function PUT(req: NextRequest) {
  try {
    const body = await req.json();
    
    // Validate that body is an array
    if (!Array.isArray(body) || body.length === 0) {
      return NextResponse.json(
        {
          status: "failed",
          data: null,
          errors: ["Updates must be a non-empty array"],
          errorCodes: [ERROR_CODES.VALIDATION_FAILED]
        },
        { status: 400 }
      );
    }

    // Validate each update in the array
    for (let i = 0; i < body.length; i++) {
      const update = body[i];

      if (!update.id || typeof update.id !== 'string') {
        return NextResponse.json(
          {
            status: "failed",
            data: null,
            errors: [`Update ${i}: ID is required and must be a string`],
            errorCodes: [ERROR_CODES.VALIDATION_FAILED]
          },
          { status: 400 }
        );
      }

      const validationResult = TestSessionUpdateSchema.safeParse(update.data);
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(err =>
          `Update ${i}: ${err.path.join('.')}: ${err.message}`
        );
        return NextResponse.json(
          {
            status: "failed",
            data: null,
            errors,
            errorCodes: [ERROR_CODES.VALIDATION_FAILED]
          },
          { status: 400 }
        );
      }
    }

    const updatedCount = await TestSessionBusinessLogicInstance.bulkUpdate(body);
    return NextResponse.json(new ResponseWrapper("success", { updatedCount }));
  } catch (error) {
    console.error("Bulk update test sessions error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const hardDelete = searchParams.get('hardDelete') === 'true';
    
    const body = await req.json();
    const { ids } = body;
    
    if (!Array.isArray(ids)) {
      return NextResponse.json(
        {
          status: "failed",
          data: null,
          errors: ["IDs must be an array"],
          errorCodes: [ERROR_CODES.VALIDATION_FAILED]
        },
        { status: 400 }
      );
    }

    const deletedCount = await TestSessionBusinessLogicInstance.bulkDelete(ids, hardDelete);
    return NextResponse.json(new ResponseWrapper("success", { deletedCount }));
  } catch (error) {
    console.error("Bulk delete test sessions error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}