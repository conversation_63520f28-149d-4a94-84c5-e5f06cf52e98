import { NextRequest, NextResponse } from "next/server";
import { TestSessionBusinessLogicInstance } from "@/src/lib/repositories/test-session";
import { implHandleGetTestSessionById, implHandleUpdateTestSession, implHandleDeleteTestSession } from "../impl";
import { ERROR_CODES } from "@/src/app/api/error_codes";

export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(req.url);
    const includeDeleted = searchParams.get('includeDeleted') === 'true';
    
    const result = await implHandleGetTestSessionById(TestSessionBusinessLogicInstance, params.id, includeDeleted);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("Test Session GET by ID route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await req.json();
    const result = await implHandleUpdateTestSession(params.id, body, TestSessionBusinessLogicInstance);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("Test Session PUT route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(req.url);
    const hardDelete = searchParams.get('hardDelete') === 'true';
    
    const result = await implHandleDeleteTestSession(params.id, TestSessionBusinessLogicInstance, hardDelete);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("Test Session DELETE route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}