import { NextRequest, NextResponse } from "next/server";
import { TestSessionBusinessLogicInstance } from "@/src/lib/repositories/test-session";
import { implHandleRestoreTestSession } from "../../impl";
import { ERROR_CODES } from "@/src/app/api/error_codes";

export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const result = await implHandleRestoreTestSession(params.id, TestSessionBusinessLogicInstance);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("Restore test session error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}