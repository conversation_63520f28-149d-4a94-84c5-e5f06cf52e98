import { NextRequest, NextResponse } from "next/server";
import { TestSessionBusinessLogicInstance } from "@/src/lib/repositories/test-session";
import { implHandleSubmitAnswers } from "../impl";
import { ERROR_CODES } from "@/src/app/api/error_codes";
import { ResponseWrapper } from "@/src/lib/types/responseWrapper";
import { repo as resultRepo } from "@/src/lib/repositories/test-result";
import { eventService } from "@/src/lib/microservices";

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const result = await implHandleSubmitAnswers(body, TestSessionBusinessLogicInstance);
    
    await resultRepo.save(result.body.data?.testResult);

    await eventService.sendTestSessionEvent({
      type: "test_session_completed",
      payload: {
        test_session_id: result.body.data.testResult.sessionId,
        user_id: result.body.data.testResult.user.id,
      },
    });
    return NextResponse.json(new ResponseWrapper("success", result.body), { status: result.status});
  } catch (error) {
    console.error("Submit answers error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}