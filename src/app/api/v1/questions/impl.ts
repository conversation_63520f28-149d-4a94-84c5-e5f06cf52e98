import { QuestionBusinessLogicInterface, Question, QuestionCreateInput, QuestionUpdateInput } from "@/src/lib/repositories/questions/interface";
import { ResponseWrapper } from "@/src/lib/types/responseWrapper";
import { QuestionCreateSchema, QuestionUpdateSchema } from "@/src/lib/validations/questions";
import { ERROR_CODES } from "@/src/app/api/error_codes";

// Create Question Implementation
export async function implHandleCreateQuestion(
  data: any,
  businessLogic: QuestionBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate input data
    console.log("Creating question with data:", data);
    
    const validationResult = QuestionCreateSchema.safeParse(data);
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err =>
        `${err.path.join('.')}: ${err.message}`
      );

      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          errors,
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    console.log("Creating question with data:", validationResult.data);
    
    const question = await businessLogic.create(validationResult.data as QuestionCreateInput);

    return {
      status: 201,
      body: new ResponseWrapper("success", question),
    };
  } catch (error: any) {
    console.error("Create question error:", error);

    if (error.code === "DUPLICATE_QUESTION") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to create question. Please try again."],
        [ERROR_CODES.CREATE_FAILED]
      ),
    };
  }
}

// Update Question Implementation
export async function implHandleUpdateQuestion(
  id: string,
  data: any,
  businessLogic: QuestionBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Check for empty update object
    if (!data || Object.keys(data).length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["No data provided for update"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // Validate input data
    const validationResult = QuestionUpdateSchema.safeParse(data);
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err =>
        `${err.path.join('.')}: ${err.message}`
      );

      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          errors,
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    console.log("Updating question with ID:", id);
    
    const question = await businessLogic.update(id, validationResult.data as QuestionUpdateInput);

    if (!question) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Question not found"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", question),
    };
  } catch (error: any) {
    console.error("Update question error:", error);

    if (error.code === "DUPLICATE_QUESTION") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE]
        ),
      };
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to update question. Please try again."],
        [ERROR_CODES.UPDATE_FAILED]
      ),
    };
  }
}

// Delete Question Implementation
export async function implHandleDeleteQuestion(
  id: string,
  businessLogic: QuestionBusinessLogicInterface,
  hardDelete: boolean = false
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    const success = await businessLogic.delete(id, hardDelete);

    if (!success) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Question not found"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", { message: "Question deleted successfully" }),
    };
  } catch (error: any) {
    console.error("Delete question error:", error);

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to delete question. Please try again."],
        [ERROR_CODES.DELETE_FAILED]
      ),
    };
  }
}

// Get Question by ID Implementation
export async function implHandleGetQuestionById(
  businessLogic: QuestionBusinessLogicInterface,
  id: string,
  includeDeleted: boolean = false
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    const question = await businessLogic.getById(id, includeDeleted);

    if (!question) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Question not found"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", question),
    };
  } catch (error: any) {
    console.error("Get question error:", error);

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to fetch question. Please try again."],
        [ERROR_CODES.FETCH_FAILED]
      ),
    };
  }
}

interface GetAllResultPaginated<T> {
  items: T[];
  page: number;
  total: number;
}

// Get All Questions Implementation (handles all, search, and filtering)
export async function implHandleGetAllQuestions(
  businessLogic: QuestionBusinessLogicInterface,
  params?: {
    search?: string;
    includeDeleted?: boolean;
    page?: number;
    limit?: number;
    sorts?: {
      field: keyof Question | string;
      direction: "asc" | "desc";
    }[],
    filters?: {
      field: keyof Question | string;
      value: Question[keyof Question] | any;
    }[];
  }
): Promise<{
  status: number;
  body: ResponseWrapper<GetAllResultPaginated<Question>>;
}> {
  try {
    // Validate search parameter if provided
    if (params?.search !== undefined) {
      if (!params.search || params.search.trim() === '') {
        return {
          status: 400,
          body: new ResponseWrapper<GetAllResultPaginated<Question>>(
            "failed",
            undefined,
            ["Search keyword cannot be empty"],
            [ERROR_CODES.VALIDATION_FAILED]
          ),
        };
      }
    }

    // Validate filters if provided
    if (params?.filters && params.filters.length > 0) {
      for (const filter of params.filters) {
        if (!filter.field || filter.field.trim() === '') {
          return {
            status: 400,
            body: new ResponseWrapper<GetAllResultPaginated<Question>>(
              "failed",
              undefined,
              ["Filter field cannot be empty"],
              [ERROR_CODES.VALIDATION_FAILED]
            ),
          };
        }
      }
    }

    // Validate sorts if provided
    if (params?.sorts && params.sorts.length > 0) {
      for (const sort of params.sorts) {
        if (!sort.field || sort.field.trim() === '') {
          return {
            status: 400,
            body: new ResponseWrapper<GetAllResultPaginated<Question>>(
              "failed",
              undefined,
              ["Sort field cannot be empty"],
              [ERROR_CODES.VALIDATION_FAILED]
            ),
          };
        }
        if (!['asc', 'desc'].includes(sort.direction)) {
          return {
            status: 400,
            body: new ResponseWrapper<GetAllResultPaginated<Question>>(
              "failed",
              undefined,
              ["Sort direction must be 'asc' or 'desc'"],
              [ERROR_CODES.VALIDATION_FAILED]
            ),
          };
        }
      }
    }

    // Build query parameters for the repository
    const queryParams: any = {
      includeDeleted: params?.includeDeleted,
      page: params?.page,
      limit: params?.limit,
    };

    // Add search if provided
    if (params?.search) {
      queryParams.search = params.search.trim();
    }

    // Add filters if provided
    if (params?.filters && params.filters.length > 0) {
      queryParams.filters = params.filters;
    }

    // Add sorts if provided
    if (params?.sorts && params.sorts.length > 0) {
      queryParams.sorts = params.sorts;
    }

    const result = await businessLogic.getAll(queryParams);

    return {
      status: 200,
      body: new ResponseWrapper<GetAllResultPaginated<Question>>("success", { ...result, page: queryParams.page }),
    };
  } catch (error: any) {
    console.error("Get questions error:", error);

    return {
      status: 500,
      body: new ResponseWrapper<GetAllResultPaginated<Question>>(
        "failed",
        undefined,
        ["Failed to fetch questions. Please try again."],
        [ERROR_CODES.FETCH_FAILED]
      ),
    };
  }
}

// Bulk Create Questions Implementation
export async function implHandleBulkCreateQuestions(
  data: any[],
  businessLogic: QuestionBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate that data is an array
    if (!Array.isArray(data) || data.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Input must be a non-empty array"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // Validate each item in the array
    const validatedData: QuestionCreateInput[] = [];
    for (let i = 0; i < data.length; i++) {
      const validationResult = QuestionCreateSchema.safeParse(data[i]);
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(err =>
          `Item ${i}: ${err.path.join('.')}: ${err.message}`
        );
        return {
          status: 400,
          body: new ResponseWrapper(
            "failed",
            undefined,
            errors,
            [ERROR_CODES.VALIDATION_FAILED]
          ),
        };
      }
      validatedData.push(validationResult.data as QuestionCreateInput);
    }

    const questions = await businessLogic.bulkCreate(validatedData);

    return {
      status: 201,
      body: new ResponseWrapper("success", questions),
    };
  } catch (error: any) {
    console.error("Bulk create questions error:", error);

    if (error.code === "DUPLICATE_QUESTIONS") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to bulk create questions. Please try again."],
        [ERROR_CODES.CREATE_FAILED]
      ),
    };
  }
}

// Bulk Update Questions Implementation
export async function implHandleBulkUpdateQuestions(
  updates: { id: string; data: any }[],
  businessLogic: QuestionBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate that updates is an array
    if (!Array.isArray(updates) || updates.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Updates must be a non-empty array"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // Validate each update in the array
    const validatedUpdates: { id: string; data: QuestionUpdateInput }[] = [];
    for (let i = 0; i < updates.length; i++) {
      const update = updates[i];

      if (!update.id || typeof update.id !== 'string') {
        return {
          status: 400,
          body: new ResponseWrapper(
            "failed",
            undefined,
            [`Update ${i}: ID is required and must be a string`],
            [ERROR_CODES.VALIDATION_FAILED]
          ),
        };
      }

      const validationResult = QuestionUpdateSchema.safeParse(update.data);
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(err =>
          `Update ${i}: ${err.path.join('.')}: ${err.message}`
        );
        return {
          status: 400,
          body: new ResponseWrapper(
            "failed",
            undefined,
            errors,
            [ERROR_CODES.VALIDATION_FAILED]
          ),
        };
      }

      validatedUpdates.push({
        id: update.id,
        data: validationResult.data as QuestionUpdateInput
      });
    }

    const updatedCount = await businessLogic.bulkUpdate(validatedUpdates);

    return {
      status: 200,
      body: new ResponseWrapper("success", { updatedCount }),
    };
  } catch (error: any) {
    console.error("Bulk update questions error:", error);

    if (error.code === "DUPLICATE_QUESTIONS") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to bulk update questions. Please try again."],
        [ERROR_CODES.UPDATE_FAILED]
      ),
    };
  }
}

// Bulk Delete Questions Implementation
export async function implHandleBulkDeleteQuestions(
  ids: string[],
  businessLogic: QuestionBusinessLogicInterface,
  hardDelete: boolean = false
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate that ids is an array
    if (!Array.isArray(ids) || ids.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["IDs must be a non-empty array"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // Validate each ID
    for (let i = 0; i < ids.length; i++) {
      const id = ids[i];
      if (!id || typeof id !== 'string' || id.trim() === '') {
        return {
          status: 400,
          body: new ResponseWrapper(
            "failed",
            undefined,
            [`ID at index ${i} is required`],
            [ERROR_CODES.VALIDATION_FAILED]
          ),
        };
      }
    }

    const deletedCount = await businessLogic.bulkDelete(ids, hardDelete);

    return {
      status: 200,
      body: new ResponseWrapper("success", { deletedCount }),
    };
  } catch (error: any) {
    console.error("Bulk delete questions error:", error);

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to bulk delete questions. Please try again."],
        [ERROR_CODES.DELETE_FAILED]
      ),
    };
  }
}

// Restore Question Implementation
export async function implHandleRestoreQuestion(
  id: string,
  businessLogic: QuestionBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate ID
    if (!id || typeof id !== 'string' || id.trim() === '') {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Question ID is required"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    const restored = await businessLogic.restore(id);

    if (!restored) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Question not found or cannot be restored"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", { restored: true }),
    };
  } catch (error: any) {
    console.error("Restore question error:", error);

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to restore question. Please try again."],
        [ERROR_CODES.UPDATE_FAILED]
      ),
    };
  }
}