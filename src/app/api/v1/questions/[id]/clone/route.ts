import { QuestionBusinessLogicInstance } from "@/src/lib/repositories/questions";
import { NextRequest, NextResponse } from "next/server";
import { ResponseWrapper } from "@/src/lib/types/responseWrapper";

const ERROR_CODES = {
  CLONE_FAILED: "ERROR_CLONE_FAILED",
  UNKNOWN_ERROR: "ERROR_UNKNOWN",
};

export async function POST(
  _: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cloned = await QuestionBusinessLogicInstance.clone(params.id);
    if (!cloned) {
      return NextResponse.json(
        new ResponseWrapper("failed", undefined, ["Failed to clone question"], [ERROR_CODES.CLONE_FAILED]),
        { status: 400 }
      );
    }
    return NextResponse.json(new ResponseWrapper("success", cloned));
  } catch (err) {
    return NextResponse.json(
      new ResponseWrapper("failed", undefined, ["Failed to clone question"], [ERROR_CODES.UNKNOWN_ERROR]),
      { status: 500 }
    );
  }
}