import { NextRequest, NextResponse } from "next/server";
import { QuestionBusinessLogicInstance } from "@/src/lib/repositories/questions";
import { implHandleGetQuestionById, implHandleUpdateQuestion, implHandleDeleteQuestion } from "../impl";
import { ERROR_CODES } from "@/src/app/api/error_codes";

export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(req.url);
    const includeDeleted = searchParams.get('includeDeleted') === 'true';
    
    const result = await implHandleGetQuestionById(QuestionBusinessLogicInstance, params.id, includeDeleted);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("Question GET route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await req.json();
    const result = await implHandleUpdateQuestion(params.id, body, QuestionBusinessLogicInstance);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("Question PUT route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(req.url);
    const hardDelete = searchParams.get('hardDelete') === 'true';
    
    const result = await implHandleDeleteQuestion(params.id, QuestionBusinessLogicInstance, hardDelete);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("Question DELETE route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}