import { QuestionBusinessLogicInstance } from "@/src/lib/repositories/questions";
import { NextRequest, NextResponse } from "next/server";
import { ResponseWrapper } from "@/src/lib/types/responseWrapper";
import { implHandleBulkDeleteQuestions } from "../impl";

const ERROR_CODES = {
  BULK_DELETE_FAILED: "ERROR_BULK_DELETE_FAILED",
  UNKNOWN_ERROR: "ERROR_UNKNOWN",
};

export async function POST(req: NextRequest) {
  try {
    const { ids } = await req.json();
    const deleted = await implHandleBulkDeleteQuestions(ids, QuestionBusinessLogicInstance);

    if (!deleted) {
      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          undefined,
          ["Failed to delete questions in bulk"],
          [ERROR_CODES.BULK_DELETE_FAILED]
        ),
        { status: 400 }
      );
    }

    return NextResponse.json(new ResponseWrapper("success", { deleted }));
  } catch (error) {
    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to delete questions in bulk"],
        [ERROR_CODES.UNKNOWN_ERROR]
      ),
      { status: 500 }
    );
  }
}