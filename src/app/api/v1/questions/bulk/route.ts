import { NextRequest, NextResponse } from "next/server";
import { QuestionBusinessLogicInstance } from "@/src/lib/repositories/questions";
import { implHandleBulkCreateQuestions, implHandleBulkUpdateQuestions, implHandleBulkDeleteQuestions } from "../impl";
import { ERROR_CODES } from "@/src/app/api/error_codes";

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const result = await implHandleBulkCreateQuestions(body, QuestionBusinessLogicInstance);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("Bulk create questions error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}

export async function PUT(req: NextRequest) {
  try {
    const body = await req.json();
    const result = await implHandleBulkUpdateQuestions(body, QuestionBusinessLogicInstance);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("Bulk update questions error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const hardDelete = searchParams.get('hardDelete') === 'true';
    
    const body = await req.json();
    const { ids } = body;
    
    if (!Array.isArray(ids)) {
      return NextResponse.json(
        {
          status: "failed",
          data: null,
          errors: ["IDs must be an array"],
          errorCodes: [ERROR_CODES.VALIDATION_FAILED]
        },
        { status: 400 }
      );
    }

    const result = await implHandleBulkDeleteQuestions(ids, QuestionBusinessLogicInstance, hardDelete);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("Bulk delete questions error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}