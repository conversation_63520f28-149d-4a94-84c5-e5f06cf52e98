import MESSAGE_KEYS from '@/src/app/api/message_keys'
import { BooleanSearch } from '../filters/BooleanSearch'
import { MultipleSelectSearch } from '../filters/MultipleSelectSearch'
import { StringSearch } from '../filters/StringSearch'
import { StringSort } from '../sorts/StringSort'
import {
  DateRangeSearchLastMonth,
  DateRangeSearchLastWeek,
  DateRangeSearchLastYear,
  DateRangeSearchThisMonth,
  DateRangeSearchThisWeek,
  DateRangeSearchThisYear,
  DateRangeSearchToday,
  DateRangeSearchYesterday
} from './common'
import { ERROR_KEYS } from './errors'
import { SearchConfigEntity } from './interface'
import { BaseSearchConfig } from './baseSearchConfig'
import { NumberSort } from '../sorts/NumberSort'

export class MyCoursesSearchConfig extends BaseSearchConfig implements SearchConfigEntity {
  constructor() {
    const filters = [
      new StringSearch('title', MESSAGE_KEYS.SEARCH_CONFIG.COURSE_TITLE, {
        validations: [
          (value: string) => {
            if (value.length == 0) throw new Error(ERROR_KEYS.TITLE_MUST_NOT_EMPTY)
          }
        ]
      }),

      new StringSearch('instructor', MESSAGE_KEYS.SEARCH_CONFIG.COURSE_INSTRUCTOR, {
        validations: [
          (value: string) => {
            if (value.length == 0) throw new Error(ERROR_KEYS.INSTRUCTOR_MUST_NOT_EMPTY)
          }
        ]
      }),

      new MultipleSelectSearch('category', MESSAGE_KEYS.SEARCH_CONFIG.COURSE_CATEGORY, [
        { value: 'Development', label: MESSAGE_KEYS.SEARCH_CONFIG.CATEGORY_DEVELOPMENT },
        { value: 'Design', label: MESSAGE_KEYS.SEARCH_CONFIG.CATEGORY_DESIGN },
        { value: 'Business', label: MESSAGE_KEYS.SEARCH_CONFIG.CATEGORY_BUSINESS },
        { value: 'Marketing', label: MESSAGE_KEYS.SEARCH_CONFIG.CATEGORY_MARKETING },
        { value: 'Data Science', label: MESSAGE_KEYS.SEARCH_CONFIG.CATEGORY_DATA_SCIENCE }
      ]),

      new BooleanSearch('isCompleted', MESSAGE_KEYS.SEARCH_CONFIG.IS_COMPLETED)
    ]

    const dateFilters = [
      DateRangeSearchToday,
      DateRangeSearchYesterday,
      DateRangeSearchThisWeek,
      DateRangeSearchLastWeek,
      DateRangeSearchThisMonth,
      DateRangeSearchLastMonth,
      DateRangeSearchThisYear,
      DateRangeSearchLastYear
    ]

    const searchableFields = [
      'title',
      'instructor',
      'summary',
      'category'
    ]

    const sorts = [
      new StringSort('title', MESSAGE_KEYS.SEARCH_CONFIG.SORT_TITLE),
      new StringSort('instructor', MESSAGE_KEYS.SEARCH_CONFIG.SORT_INSTRUCTOR),
      new NumberSort('completionPercentage', MESSAGE_KEYS.SEARCH_CONFIG.SORT_COMPLETION),
      new NumberSort('estimatedHours', MESSAGE_KEYS.SEARCH_CONFIG.SORT_HOURS),
    ]
    
    super(filters, sorts, searchableFields, dateFilters)
  }
}

export const myCoursesSearchConfig = new MyCoursesSearchConfig()