export class MongoQueryBuilder {
    private query: Record<string, any> = {}

    getQuery() {
        return this.query
    }

    addDateRange(field: string, start: Date, end: Date) {
        this.query[field] = { $gte: start, $lte: end }
    }

    addStringFilter(field: string, value: string, operator?: string) {
        if (!value) return

        const escapedValue = this.escapeRegExp(value)

        switch (operator) {
            case 'equals':
                this.query[field] = value
                break
            case 'startsWith':
                this.query[field] = { $regex: `^${escapedValue}`, $options: 'i' }
                break
            case 'endsWith':
                this.query[field] = { $regex: `${escapedValue}$`, $options: 'i' }
                break
            case 'contains':
            default:
                this.query[field] = { $regex: escapedValue, $options: 'i' }
                break
        }
    }

    private escapeRegExp(input: string): string {
        return input.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    }


    addBooleanFilter(field: string, value: boolean) {
        this.query[field] = value
    }

    addMultipleSelectFilter(field: string, values: string[]) {
        this.query[field] = { $in: values }
    }

    addRegexFilter(field: string, regex: RegExp) {
        this.query[field] = regex
    }

    addNotRegexFilter(field: string, regex: RegExp) {
        this.query[field] = { $not: regex }
    }
}