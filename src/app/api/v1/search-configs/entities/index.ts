import { SearchConfigApiResponseVisitor } from '../SearchConfigApiResponseVisitor'
import { SearchConfigEntity } from './interface'
import { myCoursesSearchConfig } from './my-courses'

const entities: Record<string, SearchConfigEntity> = {
  myCourses: myCoursesSearchConfig
}

export function hasEntity(entityName: string): boolean {
  return entityName in entities
}

export function getRegisteredEntities(): string[] {
  return Object.keys(entities)
}

export function buildApiResponse(entityName: string): any {
  const entity = entities[entityName]
  const visitor = new SearchConfigApiResponseVisitor()

  entity.buildForApiResponse(visitor)

  return visitor.getResult()
}
