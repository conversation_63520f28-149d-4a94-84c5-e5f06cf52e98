import { DateRangeSearch } from '../filters/DateRangeSearch'
import { MESSAGE_KEYS } from '@/src/app/api/message_keys'

function startOfDay(date: Date): Date {
  const d = new Date(date)
  d.setHours(0, 0, 0, 0)
  return d
}

function endOfDay(date: Date): Date {
  const d = new Date(date)
  d.setHours(23, 59, 59, 999)
  return d
}


export const DateRangeSearchToday = new DateRangeSearch('createdAt', 'today', MESSAGE_KEYS.SEARCH_CONFIG.DATE_TODAY, {
  dateRangeMapper: () => {
    const today = new Date()
    return {
      start: startOfDay(today),
      end: endOfDay(today),
    }
  }
})

export const DateRangeSearchYesterday = new DateRangeSearch('createdAt', 'yesterday', MESSAGE_KEYS.SEARCH_CONFIG.DATE_YESTERDAY, {
  dateRangeMapper: () => {
    const yesterday = new Date()
    yesterday.setDate(yesterday.getDate() - 1)
    return {
      start: startOfDay(yesterday),
      end: endOfDay(yesterday),
    }
  }
})

export const DateRangeSearchThisWeek = new DateRangeSearch('createdAt', 'this_week', MESSAGE_KEYS.SEARCH_CONFIG.DATE_THIS_WEEK, {
  dateRangeMapper: () => {
    const today = new Date()
    const start = startOfDay(new Date(today.setDate(today.getDate() - today.getDay())))
    return {
      start,
      end: endOfDay(new Date()),
    }
  }
})

export const DateRangeSearchLastWeek = new DateRangeSearch('createdAt', 'last_week', MESSAGE_KEYS.SEARCH_CONFIG.DATE_LAST_WEEK, {
  dateRangeMapper: () => {
    const today = new Date()
    const lastWeekEnd = new Date(today.setDate(today.getDate() - today.getDay()))
    const lastWeekStart = new Date(lastWeekEnd)
    lastWeekStart.setDate(lastWeekEnd.getDate() - 7)
    return {
      start: startOfDay(lastWeekStart),
      end: endOfDay(lastWeekEnd),
    }
  }
})

export const DateRangeSearchThisMonth = new DateRangeSearch('createdAt', 'this_month', MESSAGE_KEYS.SEARCH_CONFIG.DATE_THIS_MONTH, {
  dateRangeMapper: () => {
    const now = new Date()
    const start = startOfDay(new Date(now.getFullYear(), now.getMonth(), 1))
    return {
      start,
      end: endOfDay(now),
    }
  }
})

export const DateRangeSearchLastMonth = new DateRangeSearch('createdAt', 'last_month', MESSAGE_KEYS.SEARCH_CONFIG.DATE_LAST_MONTH, {
  dateRangeMapper: () => {
    const now = new Date()
    const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0)
    const lastMonthStart = new Date(lastMonthEnd.getFullYear(), lastMonthEnd.getMonth(), 1)
    return {
      start: startOfDay(lastMonthStart),
      end: endOfDay(lastMonthEnd),
    }
  }
})

export const DateRangeSearchThisYear = new DateRangeSearch('createdAt', 'this_year', MESSAGE_KEYS.SEARCH_CONFIG.DATE_THIS_YEAR, {
  dateRangeMapper: () => {
    const now = new Date()
    const start = startOfDay(new Date(now.getFullYear(), 0, 1))
    return {
      start,
      end: endOfDay(now),
    }
  }
})

export const DateRangeSearchLastYear = new DateRangeSearch('createdAt', 'last_year', MESSAGE_KEYS.SEARCH_CONFIG.DATE_LAST_YEAR, {
  dateRangeMapper: () => {
    const now = new Date()
    const lastYearStart = new Date(now.getFullYear() - 1, 0, 1)
    const lastYearEnd = new Date(now.getFullYear() - 1, 11, 31)
    return {
      start: startOfDay(lastYearStart),
      end: endOfDay(lastYearEnd),
    }
  }
})
