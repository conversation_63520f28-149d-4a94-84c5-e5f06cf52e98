import { ResponseWrapper } from "@/src/lib/types/responseWrapper";
import { ERROR_CODES } from "@/src/app/api/error_codes";
import { MESSAGE_KEYS } from "@/src/app/api/message_keys";
import { createSuccessResponse, createErrorResponse, createNotFoundResponse } from "@/src/lib/utils/api-response-helper";
import { hasEntity, getRegisteredEntities, buildApiResponse } from './entities'

// ✨ Generic search configuration interfaces
export interface SortOption {
  value: string
  label: string
  field: string // Backend field name
  type?: 'string' | 'number' | 'date' | 'boolean'
}

export interface DateFilterOption {
  value: string
  label: string
  description?: string
}

export interface FilterOption {
  id: string
  name: string
  field: string // Backend field name
  type: 'boolean' | 'select' | 'multiselect' | 'range'
  options?: Array<{ value: string; label: string }> // For select/multiselect filters
}

export interface SearchConfig {
  entity: string
  sortOptions: SortOption[]
  dateFilterOptions: DateFilterOption[]
  filters: FilterOption[]
  defaultSort?: {
    field: string
    direction: 'asc' | 'desc'
  }
  searchableFields: string[] // Fields that can be searched
}

// ✨ Configurations are now loaded from separate JSON files

// ✨ Get search configuration implementation
export async function implHandleGetSearchConfig(
  entity: string
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    if (!entity || entity.trim() === '') {
      return createErrorResponse(
        [MESSAGE_KEYS.ERROR.VALIDATION_FAILED],
        [ERROR_CODES.VALIDATION_FAILED],
        400
      );
    }

    if (!hasEntity(entity)) {
      const availableEntities = getRegisteredEntities()
      return createErrorResponse(
        [`Search configuration not found for entity: ${entity}. Available entities: ${availableEntities.join(', ')}`],
        [ERROR_CODES.NOT_FOUND],
        404
      );
    }

    const config = buildApiResponse(entity);

    if (!config) {
      return createErrorResponse(
        [`Failed to build configuration for entity: ${entity}`],
        [ERROR_CODES.FETCH_FAILED],
        500
      );
    }

    return createSuccessResponse(config);
  } catch (error: any) {
    console.error("Get search config error:", error);

    return createErrorResponse(
      [MESSAGE_KEYS.ERROR.FETCH_FAILED],
      [ERROR_CODES.FETCH_FAILED],
      500
    );
  }
}

// ✨ Update search configuration implementation (for admin use)
export async function implHandleUpdateSearchConfig(
  entity: string,
  config: any
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    if (!entity || entity.trim() === '') {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          null,
          ["Entity parameter is required"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    if (!config) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          null,
          ["Configuration data is required"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // ✨ Check if entity exists in registry
    if (!hasEntity(entity)) {
      const availableEntities = getRegisteredEntities()
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          null,
          [`Entity not found: ${entity}. Available entities: ${availableEntities.join(', ')}`],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    // ✨ For now, return error as dynamic configuration updates are not implemented
    // In a real implementation, you would update the entity configuration
    return {
      status: 501,
      body: new ResponseWrapper(
        "failed",
        null,
        ["Dynamic configuration updates are not yet implemented. Configurations are defined in code."],
        [ERROR_CODES.UPDATE_FAILED]
      ),
    };
  } catch (error: any) {
    console.error("Update search config error:", error);

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        null,
        ["Failed to update search configuration. Please try again."],
        [ERROR_CODES.UPDATE_FAILED]
      ),
    };
  }
}

// ✨ Get available entities implementation
export async function implHandleGetAvailableEntities(): Promise<{
  status: number;
  body: ResponseWrapper<string[]>;
}> {
  try {
    // ✨ Get entities from registry
    const entities = getRegisteredEntities();

    return {
      status: 200,
      body: new ResponseWrapper("success", entities),
    };
  } catch (error: any) {
    console.error("Get available entities error:", error);

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        [],
        ["Failed to fetch available entities. Please try again."],
        [ERROR_CODES.FETCH_FAILED]
      ),
    };
  }
}


