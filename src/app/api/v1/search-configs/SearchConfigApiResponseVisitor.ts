export interface BuiltFilter {
  id: string
  name: string
  field: string
  type: string
  placeholder?: string
  [key: string]: any
}

export interface BuiltSortOption {
  value: string
  label: string
  field: string
  type: string
  [key: string]: any
}

export interface BuiltDateOption {
  [key: string]: any
}

export interface BuiltNumberOption {
  [key: string]: any
}

export class SearchConfigApiResponseVisitor {
  private filters: BuiltFilter[] = []
  private dateFilterOptions: BuiltDateOption[] = []
  private sortOptions: BuiltSortOption[] = []
  private customFields: Record<string, any>[] = []

  addFilter(filter: BuiltFilter) {
    this.filters.push(filter)
  }

  addSortOption(option: BuiltSortOption) {
    this.sortOptions.push(option)
  }

  addDateFilterOption(option: BuiltDateOption) {
    this.dateFilterOptions.push(option)
  }

  addNumberFilterOption(option: BuiltFilter) {
    this.filters.push(option)
  }

  addCustomField(params: Record<string, any>) {
    this.customFields.push(params)
  }

  getResult() {
    return {
      filters: this.filters,
      sortOptions: this.sortOptions,
      dateFilterOptions: this.dateFilterOptions,
      ...Object.assign({}, ...this.customFields)
    }
  }
}
