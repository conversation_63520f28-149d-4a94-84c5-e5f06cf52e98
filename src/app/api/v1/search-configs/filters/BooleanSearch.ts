import { SearchConfigApiResponseVisitor } from '../SearchConfigApiResponseVisitor'
import { QueryValueHolder, QueryBuilder, SearchFilter } from './base'


class BooleanQueryValueHolder implements QueryValueHolder {
  constructor(private field: string, private value: boolean) {}

  visit(queryBuilder: QueryBuilder) {
    queryBuilder.addBooleanFilter(this.field, this.value)
  }
}

export class BooleanSearch implements SearchFilter {
  readonly field: string
  readonly name: string
  readonly label: string

  constructor(field: string, label: string) {
    this.field = field
    this.name = field
    this.label = label
  }

  buildForApiResponse(visitor: SearchConfigApiResponseVisitor) {
    visitor.addFilter({
      id: this.field,
      field: this.field,
      name: this.label,
      type: 'boolean',
    })
  }

  parseFromQuery(query: URLSearchParams): { field: string, value: QueryValueHolder } | null {
    const value = query.get(this.field)
    if (value == null) return null

    if (value !== 'true' && value !== 'false') {
      throw new Error(`Invalid boolean value for ${this.field}`)
    }

    return { field: this.field, value: new BooleanQueryValueHolder(this.field, value === 'true') }
  }
}
