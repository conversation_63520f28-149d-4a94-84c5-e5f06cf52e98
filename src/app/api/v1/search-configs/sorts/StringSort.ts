import { Sort } from './interface'
import { SearchConfigApiResponseVisitor } from '../SearchConfigApiResponseVisitor'

export class StringSort extends Sort {
    constructor(field: string, label: string) {
        super(field, label)
    }

    buildForApiResponse(visitor: SearchConfigApiResponseVisitor) {
        visitor.addSortOption({
            value: this.field,
            label: this.label,
            field: this.field,
            type: 'string',
        })
    }
}
