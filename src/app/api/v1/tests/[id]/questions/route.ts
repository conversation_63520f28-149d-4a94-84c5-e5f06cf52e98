import { TestBusinessLogicInstance } from "@/src/lib/repositories/tests";
import { ResponseWrapper } from "@/src/lib/types/responseWrapper";
import { NextRequest, NextResponse } from "next/server";
import { ERROR_CODES } from "@/src/app/api/error_codes";

export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log("Fetching questions for test:", params.id);
    
    const test = await TestBusinessLogicInstance.getById(params.id);
    if (!test) {
      return NextResponse.json(
        new ResponseWrapper("failed", undefined, ["Test not found"], [ERROR_CODES.NOT_FOUND]),
        { status: 404 }
      );
    }

    // For now, return the questionIds from the test
    // In the future, this could be enhanced to fetch full question details
    const questions = test.questionIds.map((id, index) => ({
      id,
      question: `Question ${index + 1}`, // Placeholder - would need to fetch from questions repository
      type: "multipleChoice" // Placeholder - would need to fetch from questions repository
    }));

    return NextResponse.json(new ResponseWrapper("success", questions));
  } catch (error) {
    console.error("Failed to fetch questions for test:", error);
    return NextResponse.json(
      new ResponseWrapper("failed", undefined, ["Failed to fetch questions for test"], [ERROR_CODES.FETCH_FAILED]),
      { status: 500 }
    );
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  const testId = params.id;

  try {
    const body = await req.json();
    const { added, removed } = body;
    
    if (!Array.isArray(added) || !Array.isArray(removed)) {
      return NextResponse.json(
        new ResponseWrapper("failed", undefined, ["Invalid input format"], [ERROR_CODES.VALIDATION_FAILED]),
        { status: 400 }
      );
    }

    // Get current test
    const test = await TestBusinessLogicInstance.getById(testId);
    
    if (!test) {
      return NextResponse.json(
        new ResponseWrapper("failed", undefined, ["Test not found"], [ERROR_CODES.NOT_FOUND]),
        { status: 404 }
      );
    }

    // Update questionIds
    const updatedQuestionIds = [
      ...test.questionIds.filter(id => !removed.includes(id)),
      ...added.filter(id => !test.questionIds.includes(id))
    ];

    console.log("Updating test with new questionIds:", updatedQuestionIds);
    
    // Update the test with new questionIds
    delete test.deletedAt
    test.updatedAt = new Date();
    const updatedTest = await TestBusinessLogicInstance.update(testId, {
      ...test,
      questionIds: updatedQuestionIds,
      totalQuestions: updatedQuestionIds.length
    });

    if (!updatedTest) {
      return NextResponse.json(
        new ResponseWrapper("failed", undefined, ["Failed to update test"], [ERROR_CODES.UPDATE_FAILED]),
        { status: 500 }
      );
    }

    return NextResponse.json(new ResponseWrapper("success", { updated: true }));
  } catch (error) {
    console.error('[PUT /tests/:id/questions] Error:', error);
    return NextResponse.json(
      new ResponseWrapper("failed", undefined, ["Internal server error"], [ERROR_CODES.INTERNAL_SERVER_ERROR]),
      { status: 500 }
    );
  }
}
