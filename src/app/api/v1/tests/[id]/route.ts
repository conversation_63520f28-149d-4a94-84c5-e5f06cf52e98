import { NextRequest, NextResponse } from "next/server";
import { TestBusinessLogicInstance } from "@/src/lib/repositories/tests";
import { implHandleGetTestById, implHandleUpdateTest, implHandleDeleteTest } from "../impl";
import { ERROR_CODES } from "@/src/app/api/error_codes";
import { eventService } from "@/src/lib/microservices";

export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(req.url);
    const includeDeleted = searchParams.get('includeDeleted') === 'true';
    console.log("Received GET request for test ID:", params.id, "with includeDeleted:", includeDeleted);
    
    const result = await implHandleGetTestById(TestBusinessLogicInstance, params.id, includeDeleted);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("Test GET by ID route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await req.json();
    console.log("sReceived PUT request for test ID:", params.id, "with body:", body);
    
    const result = await implHandleUpdateTest(params.id, body, TestBusinessLogicInstance);

    await eventService.sendTestEvent({
      type: "test_updated",
      payload: {
        test_id: params.id,
        user_id: "user_xxx"
      }
    });
    
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("Test PUT route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(req.url);
    const hardDelete = searchParams.get('hardDelete') === 'true';
    
    const result = await implHandleDeleteTest(params.id, TestBusinessLogicInstance, hardDelete);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("Test DELETE route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
} 