import { NextRequest, NextResponse } from "next/server";
import { TestBusinessLogicInstance } from "@/src/lib/repositories/tests";
import { implHandleRestoreTest } from "../../impl";
import { ERROR_CODES } from "@/src/app/api/error_codes";

export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const result = await implHandleRestoreTest(params.id, TestBusinessLogicInstance);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("Restore test error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}