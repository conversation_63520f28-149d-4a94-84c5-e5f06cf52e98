import { TestBusinessLogicInterface, Test, TestCreateInput, TestUpdateInput } from "@/src/lib/repositories/tests/interface";
import { ResponseWrapper } from "@/src/lib/types/responseWrapper";
import { TestCreateSchema, TestUpdateSchema } from "@/src/lib/validations/tests";
import { ERROR_CODES } from "@/src/app/api/error_codes";

// Create Test Implementation
export async function implHandleCreateTest(
  data: any,
  businessLogic: TestBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate input data
    const validationResult = TestCreateSchema.safeParse(data);
    
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err =>
        `${err.path.join('.')}: ${err.message}`
      );

      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          errors,
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    const test = await businessLogic.create(validationResult.data as TestCreateInput);

    return {
      status: 201,
      body: new ResponseWrapper("success", test),
    };
  } catch (error: any) {
    console.error("Create test error:", error);

    if (error.code === "DUPLICATE_TITLE") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to create test. Please try again."],
        [ERROR_CODES.CREATE_FAILED]
      ),
    };
  }
}

// Update Test Implementation
export async function implHandleUpdateTest(
  id: string,
  data: any,
  businessLogic: TestBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Check for empty update object
    if (!data || Object.keys(data).length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["No data provided for update"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // Validate input data
    const validationResult = TestUpdateSchema.safeParse(data);
    
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err =>
        `${err.path.join('.')}: ${err.message}`
      );

      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          errors,
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    const test = await businessLogic.update(id, validationResult.data);

    if (!test) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Test not found"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", test),
    };
  } catch (error: any) {
    console.error("Update test error:", error);

    if (error.code === "DUPLICATE_TITLE") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE]
        ),
      };
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to update test. Please try again."],
        [ERROR_CODES.UPDATE_FAILED]
      ),
    };
  }
}

// Delete Test Implementation
export async function implHandleDeleteTest(
  id: string,
  businessLogic: TestBusinessLogicInterface,
  hardDelete: boolean = false
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    console.log("Received request to delete test with ID:", id, "hardDelete:", hardDelete);
    
    const success = await businessLogic.delete(id, hardDelete);

    if (!success) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Test not found"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", { message: "Test deleted successfully" }),
    };
  } catch (error: any) {
    console.error("Delete test error:", error);

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to delete test. Please try again."],
        [ERROR_CODES.DELETE_FAILED]
      ),
    };
  }
}

// Get Test by ID Implementation
export async function implHandleGetTestById(
  businessLogic: TestBusinessLogicInterface,
  id: string,
  includeDeleted: boolean = false
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    const test = await businessLogic.getById(id, includeDeleted);

    if (!test) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Test not found"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", test),
    };
  } catch (error: any) {
    console.error("Get test error:", error);

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to fetch test. Please try again."],
        [ERROR_CODES.FETCH_FAILED]
      ),
    };
  }
}

interface GetAllResultPaginated<T> {
  items: T[];
  page: number;
  total: number;
}

// Get All Tests Implementation (handles all, search, and filtering)
export async function implHandleGetAllTests(
  businessLogic: TestBusinessLogicInterface,
  params?: {
    search?: string;
    includeDeleted?: boolean;
    page?: number;
    limit?: number;
    sorts?: {
      field: keyof Test | string;
      direction: "asc" | "desc";
    }[],
    filters?: {
      field: keyof Test | string;
      value: Test[keyof Test] | any;
    }[];
  }
): Promise<{
  status: number;
  body: ResponseWrapper<GetAllResultPaginated<Test>>;
}> {
  try {
    // Validate search parameter if provided
    if (params?.search !== undefined) {
      if (!params.search || params.search.trim() === '') {
        return {
          status: 400,
          body: new ResponseWrapper<GetAllResultPaginated<Test>>(
            "failed",
            undefined,
            ["Search keyword cannot be empty"],
            [ERROR_CODES.VALIDATION_FAILED]
          ),
        };
      }
    }

    // Validate filters if provided
    if (params?.filters && params.filters.length > 0) {
      for (const filter of params.filters) {
        if (!filter.field || filter.field.trim() === '') {
          return {
            status: 400,
            body: new ResponseWrapper<GetAllResultPaginated<Test>>(
              "failed",
              undefined,
              ["Filter field cannot be empty"],
              [ERROR_CODES.VALIDATION_FAILED]
            ),
          };
        }
      }
    }

    // Validate sorts if provided
    if (params?.sorts && params.sorts.length > 0) {
      for (const sort of params.sorts) {
        if (!sort.field || sort.field.trim() === '') {
          return {
            status: 400,
            body: new ResponseWrapper<GetAllResultPaginated<Test>>(
              "failed",
              undefined,
              ["Sort field cannot be empty"],
              [ERROR_CODES.VALIDATION_FAILED]
            ),
          };
        }
        if (!['asc', 'desc'].includes(sort.direction)) {
          return {
            status: 400,
            body: new ResponseWrapper<GetAllResultPaginated<Test>>(
              "failed",
              undefined,
              ["Sort direction must be 'asc' or 'desc'"],
              [ERROR_CODES.VALIDATION_FAILED]
            ),
          };
        }
      }
    }

    // Build query parameters for the repository
    const queryParams: any = {
      includeDeleted: params?.includeDeleted,
      page: params?.page,
      limit: params?.limit,
    };

    // Add search if provided
    if (params?.search) {
      queryParams.search = params.search.trim();
    }

    // Add filters if provided
    if (params?.filters && params.filters.length > 0) {
      queryParams.filters = params.filters;
    }

    // Add sorts if provided
    if (params?.sorts && params.sorts.length > 0) {
      queryParams.sorts = params.sorts;
    }

    const result = await businessLogic.getAll(queryParams);

    return {
      status: 200,
      body: new ResponseWrapper<GetAllResultPaginated<Test>>("success", { ...result, page: queryParams.page }),
    };
  } catch (error: any) {
    console.error("Get tests error:", error);

    return {
      status: 500,
      body: new ResponseWrapper<GetAllResultPaginated<Test>>(
        "failed",
        undefined,
        ["Failed to fetch tests. Please try again."],
        [ERROR_CODES.FETCH_FAILED]
      ),
    };
  }
}

// Bulk Create Tests Implementation
export async function implHandleBulkCreateTests(
  data: any[],
  businessLogic: TestBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate that data is an array
    if (!Array.isArray(data) || data.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Input must be a non-empty array"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // Validate each item in the array
    const validatedData: TestCreateInput[] = [];
    for (let i = 0; i < data.length; i++) {
      const validationResult = TestCreateSchema.safeParse(data[i]);
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(err =>
          `Item ${i}: ${err.path.join('.')}: ${err.message}`
        );
        return {
          status: 400,
          body: new ResponseWrapper(
            "failed",
            undefined,
            errors,
            [ERROR_CODES.VALIDATION_FAILED]
          ),
        };
      }
      validatedData.push(validationResult.data as TestCreateInput);
    }

    const tests = await businessLogic.bulkCreate(validatedData);

    return {
      status: 201,
      body: new ResponseWrapper("success", tests),
    };
  } catch (error: any) {
    console.error("Bulk create tests error:", error);

    if (error.code === "DUPLICATE_TITLES") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to bulk create tests. Please try again."],
        [ERROR_CODES.CREATE_FAILED]
      ),
    };
  }
}

// Bulk Update Tests Implementation
export async function implHandleBulkUpdateTests(
  updates: { id: string; data: any }[],
  businessLogic: TestBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate that updates is an array
    if (!Array.isArray(updates) || updates.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Updates must be a non-empty array"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // Validate each update in the array
    const validatedUpdates: { id: string; data: TestUpdateInput }[] = [];
    for (let i = 0; i < updates.length; i++) {
      const update = updates[i];

      if (!update.id || typeof update.id !== 'string') {
        return {
          status: 400,
          body: new ResponseWrapper(
            "failed",
            undefined,
            [`Update ${i}: ID is required and must be a string`],
            [ERROR_CODES.VALIDATION_FAILED]
          ),
        };
      }

      const validationResult = TestUpdateSchema.safeParse(update.data);
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(err =>
          `Update ${i}: ${err.path.join('.')}: ${err.message}`
        );
        return {
          status: 400,
          body: new ResponseWrapper(
            "failed",
            undefined,
            errors,
            [ERROR_CODES.VALIDATION_FAILED]
          ),
        };
      }

      validatedUpdates.push({
        id: update.id,
        data: validationResult.data
      });
    }

    const updatedCount = await businessLogic.bulkUpdate(validatedUpdates);

    return {
      status: 200,
      body: new ResponseWrapper("success", { updatedCount }),
    };
  } catch (error: any) {
    console.error("Bulk update tests error:", error);

    if (error.code === "DUPLICATE_TITLES") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to bulk update tests. Please try again."],
        [ERROR_CODES.UPDATE_FAILED]
      ),
    };
  }
}

// Bulk Delete Tests Implementation
export async function implHandleBulkDeleteTests(
  ids: string[],
  businessLogic: TestBusinessLogicInterface,
  hardDelete: boolean = false
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate that ids is an array
    if (!Array.isArray(ids) || ids.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["IDs must be a non-empty array"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // Validate each ID
    for (let i = 0; i < ids.length; i++) {
      const id = ids[i];
      if (!id || typeof id !== 'string' || id.trim() === '') {
        return {
          status: 400,
          body: new ResponseWrapper(
            "failed",
            undefined,
            [`ID at index ${i} is required`],
            [ERROR_CODES.VALIDATION_FAILED]
          ),
        };
      }
    }

    const deletedCount = await businessLogic.bulkDelete(ids, hardDelete);

    return {
      status: 200,
      body: new ResponseWrapper("success", { deletedCount }),
    };
  } catch (error: any) {
    console.error("Bulk delete tests error:", error);

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to bulk delete tests. Please try again."],
        [ERROR_CODES.DELETE_FAILED]
      ),
    };
  }
}

// Restore Test Implementation
export async function implHandleRestoreTest(
  id: string,
  businessLogic: TestBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate ID
    if (!id || typeof id !== 'string' || id.trim() === '') {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Test ID is required"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    const restored = await businessLogic.restore(id);

    if (!restored) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Test not found or cannot be restored"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", { restored: true }),
    };
  } catch (error: any) {
    console.error("Restore test error:", error);

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to restore test. Please try again."],
        [ERROR_CODES.UPDATE_FAILED]
      ),
    };
  }
} 