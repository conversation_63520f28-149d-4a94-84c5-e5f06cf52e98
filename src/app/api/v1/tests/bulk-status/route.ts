import { NextRequest, NextResponse } from "next/server";
import { TestBusinessLogicInstance } from "@/src/lib/repositories/tests";
import { ResponseWrapper } from "@/src/lib/types/responseWrapper";

const ERROR_CODES = {
  VALIDATION_FAILED: "ERROR_VALIDATION_FAILED",
  BULK_UPDATE_FAILED: "ERROR_BULK_UPDATE_FAILED",
  UNKNOWN_ERROR: "ERROR_UNKNOWN",
};

export async function PATCH(req: NextRequest) {
  try {
    const { ids, status } = await req.json();
    if (!Array.isArray(ids) || !["ACTIVE", "INACTIVE", "ARCHIVED"].includes(status)) {
      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          undefined,
          ["Invalid ids or status"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
        { status: 400 }
      );
    }
    await TestBusinessLogicInstance.bulkUpdateStatus(ids, status);
    return NextResponse.json(new ResponseWrapper("success", { success: true }));
  } catch (error) {
    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to update test statuses"],
        [ERROR_CODES.BULK_UPDATE_FAILED]
      ),
      { status: 500 }
    );
  }
}
