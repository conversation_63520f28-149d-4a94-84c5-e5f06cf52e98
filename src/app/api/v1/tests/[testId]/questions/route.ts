import { repo as testRepo } from "@/src/lib/repositories/tests";
import { ResponseWrapper } from "@/src/lib/types/responseWrapper";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  req: NextRequest,
  { params }: { params: { testId: string } }
) {
  try {
    const questions = await testRepo.getQuestionsOfTestWithId(params.testId);
    return NextResponse.json(new ResponseWrapper(
      "success",
      questions
    ));
  } catch (error) {
    console.error("Failed to fetch questions for test:", error);
    return NextResponse.json(
      new ResponseWrapper("failed", undefined, ["Failed to fetch questions for test"], ["ERROR_UNKNOWN"]),
      { status: 500 }
    );
  }
}


export async function PUT(
  req: NextRequest,
  { params }: { params: { testId: string } }
) {
  const testId = params.testId;

  try {
    const body = await req.json();
    const { added, removed } = body;

    if (!Array.isArray(added) || !Array.isArray(removed)) {
      return NextResponse.json({ error: "Invalid input format" }, { status: 400 });
    }

    await testRepo.updateQuestions(testId, {
      added: added,
      removed: removed,
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('[PUT /tests/:id/questions] Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
