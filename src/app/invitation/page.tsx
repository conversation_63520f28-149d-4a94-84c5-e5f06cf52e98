"use client"

import { useEffect, useState } from "react"
import { useSearchParams } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { CheckCircle2, Clock, XCircle, ArrowRight } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { CollaboratorsAPI } from "@/src/services/collaboratorsApi"
import { Invitation } from "@/src/lib/repositories/collaborators/CollaboratorsRepository"

type InvitationStatus = "loading" | "valid" | "expired" | "invalid" | "accepted" | "declined"

export default function InvitationPage() {
  const searchParams = useSearchParams()
  const code = searchParams.get("code")
  const [status, setStatus] = useState<InvitationStatus>("loading")
  const [invitation, setInvitation] = useState<Invitation | null>(null)
  const [userInfo, setUserInfo] = useState({ name: "", email: "", avatar: "" })
  const [isProcessing, setIsProcessing] = useState(false)

  useEffect(() => {
    const validateInvitation = async () => {
      if (!code) {
        setStatus("invalid")
        return
      }

      try {
        const response = await CollaboratorsAPI.GetInvitationByCode(code).request()
        setInvitation(response)
        setUserInfo(prev => ({ ...prev, email: response.email }))
        setStatus("valid")
      } catch (error: any) {
        console.error("Failed to validate invitation:", error)

        if (error.response?.status === 404) {
          setStatus("invalid")
        } else if (error.response?.data?.errorCodes?.includes("ERROR_INVITATION_EXPIRED")) {
          setStatus("expired")
        } else if (error.response?.data?.errorCodes?.includes("ERROR_INVITATION_ALREADY_PROCESSED")) {
          setStatus("accepted") // or declined, but we'll show as processed
        } else {
          setStatus("invalid")
        }
      }
    }

    validateInvitation()
  }, [code])

  const handleInvitationResponse = async (action: "accept" | "decline") => {
    if (!code) return

    setIsProcessing(true)
    try {
      const response = await CollaboratorsAPI.RespondToInvitation({
        code,
        action,
        userInfo: action === "accept" ? userInfo : undefined
      }).request()

      setStatus(action === "accept" ? "accepted" : "declined")
      console.log("Invitation response:", response)
    } catch (error) {
      console.error("Failed to respond to invitation:", error)
      alert("Failed to process invitation. Please try again.")
    } finally {
      setIsProcessing(false)
    }
  }

  const getRoleBadge = (role: string) => {
    switch (role) {
      case "team":
        return <Badge variant="secondary">Team Member</Badge>
      case "external":
        return <Badge variant="outline">External Collaborator</Badge>
      default:
        return null
    }
  }

  const renderContent = () => {
    switch (status) {
      case "loading":
        return (
          <Card className="max-w-md w-full">
            <CardHeader className="text-center">
              <CardTitle>Validating Invitation</CardTitle>
              <CardDescription>Please wait while we validate your invitation...</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center py-6">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
            </CardContent>
          </Card>
        )

      case "valid":
        return (
          <Card className="max-w-md w-full">
            <CardHeader>
              <CardTitle>Course Collaboration Invitation</CardTitle>
              <CardDescription>You've been invited to collaborate on courses.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="text-sm font-medium">Invitation Details:</div>
                <div className="text-sm text-muted-foreground">
                  Role: <Badge variant="secondary">{invitation?.role}</Badge>
                </div>
                <div className="text-sm text-muted-foreground">
                  Email: {invitation?.email}
                </div>
                <div className="text-sm text-muted-foreground">
                  Expires: {invitation?.expiresAt ? new Date(invitation.expiresAt).toLocaleDateString() : "N/A"}
                </div>
              </div>

              <div className="space-y-3">
                <div className="text-sm font-medium">Your Information:</div>
                <Input
                  placeholder="Your full name"
                  value={userInfo.name}
                  onChange={(e) => setUserInfo(prev => ({ ...prev, name: e.target.value }))}
                />
                <Input
                  placeholder="Your email"
                  type="email"
                  value={userInfo.email}
                  onChange={(e) => setUserInfo(prev => ({ ...prev, email: e.target.value }))}
                />
                <Input
                  placeholder="Avatar URL (optional)"
                  value={userInfo.avatar}
                  onChange={(e) => setUserInfo(prev => ({ ...prev, avatar: e.target.value }))}
                />
              </div>
            </CardContent>
            <CardFooter className="flex flex-col gap-2">
              <Button
                className="w-full"
                onClick={() => handleInvitationResponse("accept")}
                disabled={isProcessing || !userInfo.name.trim() || !userInfo.email.trim()}
              >
                {isProcessing ? "Processing..." : "Accept Invitation"}
              </Button>
              <Button
                variant="outline"
                className="w-full"
                onClick={() => handleInvitationResponse("decline")}
                disabled={isProcessing}
              >
                {isProcessing ? "Processing..." : "Decline"}
              </Button>
            </CardFooter>
          </Card>
        )

      case "expired":
        return (
          <Card className="max-w-md w-full">
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                <Clock className="h-12 w-12 text-amber-500" />
              </div>
              <CardTitle>Invitation Expired</CardTitle>
              <CardDescription>This invitation has expired and is no longer valid.</CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-muted-foreground mb-4">
                The invitation from {invitation?.from?.name || "the course creator"} has expired on{" "}
                {invitation?.expiresAt || "the expiration date"}.
              </p>
              <p className="text-sm">
                Please contact {invitation?.from?.email || "the course creator"} to request a new invitation.
              </p>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full" asChild>
                <Link href="/">Return to Home</Link>
              </Button>
            </CardFooter>
          </Card>
        )

      case "invalid":
        return (
          <Card className="max-w-md w-full">
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                <XCircle className="h-12 w-12 text-destructive" />
              </div>
              <CardTitle>Invalid Invitation</CardTitle>
              <CardDescription>The invitation code is invalid or has been revoked.</CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-muted-foreground mb-4">
                The invitation link you followed is not valid. This could be because:
              </p>
              <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside text-left max-w-xs mx-auto">
                <li>The invitation code is incorrect</li>
                <li>The invitation has been revoked</li>
                <li>The invitation has already been used</li>
              </ul>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full" asChild>
                <Link href="/">Return to Home</Link>
              </Button>
            </CardFooter>
          </Card>
        )

      case "accepted":
        return (
          <Card className="max-w-md w-full">
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                <CheckCircle2 className="h-12 w-12 text-green-500" />
              </div>
              <CardTitle>Invitation Accepted</CardTitle>
              <CardDescription>You have successfully accepted the invitation.</CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-muted-foreground mb-4">
                You now have access to {invitation?.coursesCount || "the"} courses from{" "}
                {invitation?.from?.name || "the course creator"} as a{" "}
                {invitation?.role === "team" ? "Team Member" : "External Collaborator"}.
              </p>
              <p className="text-sm">You can now access these courses from your dashboard.</p>
            </CardContent>
            <CardFooter>
              <Button className="w-full" asChild>
                <Link href="/dashboard/my-courses">
                  Go to My Courses
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardFooter>
          </Card>
        )

      case "declined":
        return (
          <Card className="max-w-md w-full">
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                <XCircle className="h-12 w-12 text-red-500" />
              </div>
              <CardTitle>Invitation Declined</CardTitle>
              <CardDescription>You have declined this collaboration invitation.</CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-muted-foreground mb-4">
                The invitation has been marked as declined.
              </p>
              <p className="text-sm">If you change your mind, please contact the inviter for a new invitation.</p>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full" asChild>
                <Link href="/">Return to Home</Link>
              </Button>
            </CardFooter>
          </Card>
        )

      default:
        return null
    }
  }

  return (
    <div className="min-h-screen flex flex-col">
      <header className="border-b">
        <div className="container flex h-16 items-center">
          <Link href="/" className="flex items-center gap-2 font-semibold">
            <Image src="/abstract-logo.png" alt="Logo" width={30} height={30} className="rounded" />
            <span>Course Platform</span>
          </Link>
        </div>
      </header>
      <main className="flex-1 flex items-center justify-center p-4">{renderContent()}</main>
      <footer className="border-t py-6">
        <div className="container flex flex-col items-center justify-center gap-2 text-center text-sm">
          <p className="text-muted-foreground">
            &copy; {new Date().getFullYear()} Course Platform. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  )
}
