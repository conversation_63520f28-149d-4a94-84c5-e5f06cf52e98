"use client"

import { useEffect, useState } from "react"
import { useSearchParams } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { CheckCircle2, Clock, XCircle, ArrowRight } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"

type InvitationStatus = "loading" | "valid" | "expired" | "invalid" | "accepted"

export default function InvitationPage() {
  const searchParams = useSearchParams()
  const code = searchParams.get("code")
  const [status, setStatus] = useState<InvitationStatus>("loading")
  const [invitation, setInvitation] = useState<any>(null)

  useEffect(() => {
    // Simulate API call to validate invitation code
    const validateInvitation = async () => {
      // In a real app, this would be an API call
      await new Promise((resolve) => setTimeout(resolve, 1500)) // Simulate network delay

      if (!code) {
        setStatus("invalid")
        return
      }

      // Mock validation logic based on the code
      if (code === "TEAM-123456") {
        setStatus("valid")
        setInvitation({
          from: {
            name: "Sarah Miller",
            email: "<EMAIL>",
            avatar: "/stylized-sm-logo.png",
          },
          role: "team",
          expiresAt: "2023-06-15",
          sentAt: "2023-06-01",
          coursesCount: 8,
        })
      } else if (code === "EXT-789012") {
        setStatus("valid")
        setInvitation({
          from: {
            name: "David Chen",
            email: "<EMAIL>",
            avatar: "/dc-skyline-night.png",
          },
          role: "external",
          expiresAt: "2023-06-20",
          sentAt: "2023-06-05",
          coursesCount: 5,
        })
      } else if (code === "EXPIRED-123") {
        setStatus("expired")
        setInvitation({
          from: {
            name: "Michael Wong",
            email: "<EMAIL>",
            avatar: "/intertwined-letters.png",
          },
          role: "team",
          expiresAt: "2023-05-15",
          sentAt: "2023-05-01",
          coursesCount: 3,
        })
      } else if (code === "ACCEPTED-456") {
        setStatus("accepted")
        setInvitation({
          from: {
            name: "Emily Rodriguez",
            email: "<EMAIL>",
            avatar: "/emergency-room-scene.png",
          },
          role: "external",
          expiresAt: "2023-06-30",
          sentAt: "2023-06-10",
          acceptedAt: "2023-06-12",
          coursesCount: 2,
        })
      } else {
        setStatus("invalid")
      }
    }

    validateInvitation()
  }, [code])

  const acceptInvitation = () => {
    // In a real app, this would be an API call
    setStatus("accepted")
  }

  const getRoleBadge = (role: string) => {
    switch (role) {
      case "team":
        return <Badge variant="secondary">Team Member</Badge>
      case "external":
        return <Badge variant="outline">External Collaborator</Badge>
      default:
        return null
    }
  }

  const renderContent = () => {
    switch (status) {
      case "loading":
        return (
          <Card className="max-w-md w-full">
            <CardHeader className="text-center">
              <CardTitle>Validating Invitation</CardTitle>
              <CardDescription>Please wait while we validate your invitation...</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center py-6">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
            </CardContent>
          </Card>
        )

      case "valid":
        return (
          <Card className="max-w-md w-full">
            <CardHeader>
              <CardTitle>Course Collaboration Invitation</CardTitle>
              <CardDescription>You've been invited to collaborate on courses.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={invitation.from.avatar || "/placeholder.svg"} alt={invitation.from.name} />
                  <AvatarFallback>
                    {invitation.from.name
                      .split(" ")
                      .map((n: string) => n[0])
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium">{invitation.from.name}</div>
                  <div className="text-sm text-muted-foreground">{invitation.from.email}</div>
                </div>
              </div>

              <div className="rounded-lg border p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <div className="font-medium">Invitation Details</div>
                  {getRoleBadge(invitation.role)}
                </div>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Role:</span>
                    <span>{invitation.role === "team" ? "Team Member" : "External Collaborator"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Courses:</span>
                    <span>{invitation.coursesCount} courses</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Sent on:</span>
                    <span>{invitation.sentAt}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Expires on:</span>
                    <span>{invitation.expiresAt}</span>
                  </div>
                </div>
              </div>

              <div className="rounded-lg border p-4">
                <div className="font-medium mb-2">Permissions</div>
                {invitation.role === "team" ? (
                  <ul className="text-sm space-y-1 list-disc list-inside text-muted-foreground">
                    <li>View all courses</li>
                    <li>Edit and update courses</li>
                    <li>Cannot delete courses</li>
                    <li>Add comments and feedback</li>
                  </ul>
                ) : (
                  <ul className="text-sm space-y-1 list-disc list-inside text-muted-foreground">
                    <li>View shared courses only</li>
                    <li>Cannot edit or update courses</li>
                    <li>Add comments and feedback</li>
                  </ul>
                )}
              </div>
            </CardContent>
            <CardFooter className="flex flex-col gap-2">
              <Button className="w-full" onClick={acceptInvitation}>
                Accept Invitation
              </Button>
              <Button variant="outline" className="w-full" asChild>
                <Link href="/">Decline</Link>
              </Button>
            </CardFooter>
          </Card>
        )

      case "expired":
        return (
          <Card className="max-w-md w-full">
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                <Clock className="h-12 w-12 text-amber-500" />
              </div>
              <CardTitle>Invitation Expired</CardTitle>
              <CardDescription>This invitation has expired and is no longer valid.</CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-muted-foreground mb-4">
                The invitation from {invitation?.from?.name || "the course creator"} has expired on{" "}
                {invitation?.expiresAt || "the expiration date"}.
              </p>
              <p className="text-sm">
                Please contact {invitation?.from?.email || "the course creator"} to request a new invitation.
              </p>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full" asChild>
                <Link href="/">Return to Home</Link>
              </Button>
            </CardFooter>
          </Card>
        )

      case "invalid":
        return (
          <Card className="max-w-md w-full">
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                <XCircle className="h-12 w-12 text-destructive" />
              </div>
              <CardTitle>Invalid Invitation</CardTitle>
              <CardDescription>The invitation code is invalid or has been revoked.</CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-muted-foreground mb-4">
                The invitation link you followed is not valid. This could be because:
              </p>
              <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside text-left max-w-xs mx-auto">
                <li>The invitation code is incorrect</li>
                <li>The invitation has been revoked</li>
                <li>The invitation has already been used</li>
              </ul>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full" asChild>
                <Link href="/">Return to Home</Link>
              </Button>
            </CardFooter>
          </Card>
        )

      case "accepted":
        return (
          <Card className="max-w-md w-full">
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                <CheckCircle2 className="h-12 w-12 text-green-500" />
              </div>
              <CardTitle>Invitation Accepted</CardTitle>
              <CardDescription>You have successfully accepted the invitation.</CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-muted-foreground mb-4">
                You now have access to {invitation?.coursesCount || "the"} courses from{" "}
                {invitation?.from?.name || "the course creator"} as a{" "}
                {invitation?.role === "team" ? "Team Member" : "External Collaborator"}.
              </p>
              <p className="text-sm">You can now access these courses from your dashboard.</p>
            </CardContent>
            <CardFooter>
              <Button className="w-full" asChild>
                <Link href="/dashboard/my-courses">
                  Go to My Courses
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardFooter>
          </Card>
        )

      default:
        return null
    }
  }

  return (
    <div className="min-h-screen flex flex-col">
      <header className="border-b">
        <div className="container flex h-16 items-center">
          <Link href="/" className="flex items-center gap-2 font-semibold">
            <Image src="/abstract-logo.png" alt="Logo" width={30} height={30} className="rounded" />
            <span>Course Platform</span>
          </Link>
        </div>
      </header>
      <main className="flex-1 flex items-center justify-center p-4">{renderContent()}</main>
      <footer className="border-t py-6">
        <div className="container flex flex-col items-center justify-center gap-2 text-center text-sm">
          <p className="text-muted-foreground">
            &copy; {new Date().getFullYear()} Course Platform. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  )
}
