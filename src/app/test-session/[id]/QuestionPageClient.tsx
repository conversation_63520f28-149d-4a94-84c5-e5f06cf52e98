/* eslint-disable @next/next/no-img-element */
"use client";

import { useState, useEffect, useMemo } from "react";
import MarkdownContent from "@/components/MarkdownContent";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { QuestionSwitcher } from "./QuestionSwitcher";
import { TimerAndSubmit } from "./TimerSubmit";
import {
  TestSessionAnswer,
  Uploader,
  type PluginProps,
  type TestSessionQuestion as TestSessionQuestion,
} from "./model";
import { useRouter } from "next/navigation";
import { ConfirmDialog } from "./ConfirmDialog";
import { AlertDialogWithReason } from "./AlertDialog";
import en from "./locales/en.json";
import { useLocalization } from "@/src/localization/functions/client";

import { TestSessionsAPI } from "@/src/services/testSessionApi";
import { TimeUpDialog } from "./TimeUpDialog";
import { pluginRegistry } from "@/components/QuestionDisplay";

export class DummyUploader implements Uploader {
  async upload(blob: Blob): Promise<string> {
    await new Promise((resolve) => setTimeout(resolve, 500));
    return "https://dummy-upload.com/media-url";
  }
}

export default function QuestionClient({
  id,
  questions: initialQuestions,
}: {
  id: string;
  questions: TestSessionQuestion[];
}) {
  const [timeRemaining, setTimeRemaining] = useState<number>(360);
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const [answers, setAnswers] = useState<
    Record<number, { questionId: string; answer: TestSessionAnswer }>
  >({});
  const [questions] = useState<TestSessionQuestion[]>(initialQuestions);
  const [showConfirmSubmit, setShowConfirmSubmit] = useState(false);
  const [showExitReason, setShowExitReason] = useState(false);
  const [showTimeUpDialog, setShowTimeUpDialog] = useState(false);

  const { t } = useLocalization("test-session", { en });

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeRemaining((prev) => (prev > 0 ? prev - 1 : 0));
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    if (timeRemaining === 0) {
      setShowTimeUpDialog(true);
    }
  }, [timeRemaining]);

  const currentQuestion = questions[currentIndex];
  const currentAnswer = answers[currentIndex];
  const handleAnswer = (answer: { questionId: string; answer: any }) =>
    setAnswers((prev) => ({ ...prev, [currentIndex]: answer }));

  const goToQuestion = (index: number) => setCurrentIndex(index);
  const goPrev = () => currentIndex > 0 && setCurrentIndex(currentIndex - 1);
  const goNext = () =>
    currentIndex < questions.length - 1 && setCurrentIndex(currentIndex + 1);

  const QuestionRenderer = pluginRegistry[currentQuestion.type];

  const router = useRouter();

  const confirmAndSubmit = () => {
    const unansweredCount = questions.filter((_, idx) => !answers[idx]).length;
    const isTimeRemaining = timeRemaining > 0;

    if (unansweredCount > 0 && isTimeRemaining) {
      setShowConfirmSubmit(true);
    } else {
      handleSubmit();
    }
  };

  const handleSubmit = async () => {
    try {
      if (Object.entries(answers || {})?.length <= 0) return handleExitWithReason('time out')
      const uploader = new DummyUploader();

      // Process all answers and convert to new format
      const processedAnswers: { questionId: string; answer: string | string[] }[] = [];

      for (const [indexStr, ans] of Object.entries(answers)) {
        if (ans) {
          let processedAnswer: any;
          
          if (
            ans &&
            typeof ans === "object" &&
            "getValue" in ans &&
            typeof ans.getValue === "function"
          ) {
            processedAnswer = await ans.getValue(uploader);
          } else {
            processedAnswer = ans.answer;
          }

          // Convert answer to string or array of strings as expected by new API
          let answerValue: string | string[];
          if (Array.isArray(processedAnswer)) {
            answerValue = processedAnswer.map(item => String(item));
          } else {
            answerValue = String(processedAnswer);
          }

          processedAnswers.push({
            questionId: ans.questionId,
            answer: answerValue,
          });
        }
      }

      const payload = {
        sessionId: id,
        answers: processedAnswers,
        timeTaken: 360 - timeRemaining,
      };

      const result = await TestSessionsAPI.SubmitAnswers(payload).request();
      console.log(result);
      
      if (result?.status === "success" && result?.data) {
        // The new API returns both session and testResult
        if (result.data.testResult?.id) {
          router.push(`/test-result/${result.data.testResult.id}`);
        } else {
          // Fallback to session ID if testResult not available
          router.push(`/test-result/${result.data.session?.id || id}`);
        }
      } else {
        throw new Error("Failed to submit test - invalid response");
      }
    } catch (error) {
      console.error("Submit error:", error);
      alert("Failed to submit test.");
    }
  };

  console.log(answers);
  console.log(initialQuestions);
  

  const handleExitWithReason = async (reason: string) => {
    try {
      const timeSpent = 360 - timeRemaining;

      const payload = {
        sessionId: id,
        exitReason: reason,
        timeSpent,
      };

      const result = await TestSessionsAPI.ExitSession(payload).request();

      if (result?.status === "success") {
        router.push("/tests");
      } else {
        throw new Error("Failed to exit session - invalid response");
      }
    } catch (err) {
      console.error("Exit error:", err);
      alert("Failed to exit test.");
    }
  };

  return (
    <div className="min-h-screen bg-white md:bg-gray-100 flex flex-col">
      <div className="relative bg-white md:rounded-3xl md:shadow-sm overflow-hidden p-4 md:p-8 min-h-[100vh] md:h-[800px]">
        <div className="flex flex-col md:flex-row gap-4 md:gap-8 h-full">
          <QuestionSwitcher
            questions={questions}
            currentIndex={currentIndex}
            answers={answers}
            goToQuestion={goToQuestion}
          />

          <div className="relative flex-1 order-3 md:order-2 flex flex-col h-full">
            <div className="flex justify-between items-center mb-4 md:mb-6">
              <h1 className="text-2xl md:text-3xl font-bold hidden md:block">
                {t("question_number", { number: currentIndex + 1 })}
              </h1>
              <TimerAndSubmit
                timeRemaining={timeRemaining}
                onSubmit={confirmAndSubmit}
                onExit={() => setShowExitReason(true)}
              />
            </div>

            <h1 className="text-2xl font-bold mb-2 md:hidden">
              {t("question_number", { number: currentIndex + 1 })}
            </h1>

            <div className="mb-6 flex flex-col space-y-4 items-stretch md:items-center md:overflow-y-scroll h-full">
              <div className="space-y-4 w-full md:w-[800px] md:mb-24">
                {QuestionRenderer ? (
                  <QuestionRenderer
                    key={currentQuestion.id}
                    question={currentQuestion}
                    answer={currentAnswer?.answer}
                    onAnswer={(answer) => {
                      handleAnswer({
                        questionId: currentQuestion.id,
                        answer: answer,
                      });
                    }}
                  />
                ) : (
                  <p className="text-red-500">
                    {t("unknown_question_type", {
                      type: currentQuestion.type,
                    })}
                  </p>
                )}
              </div>
            </div>

            <div className="md:absolute flex flex-col md:justify-end md:items-end right-2 bottom-2 md:opacity-50 md:hover:opacity-100">
              <div className="flex justify-between md:justify-end gap-3 mt-8 md:mt-12 mb-6">
                <Button
                  onClick={goPrev}
                  disabled={currentIndex === 0}
                  variant="ghost"
                  className="bg-gray-100 hover:bg-gray-200 px-4 md:px-6 flex space-x-2"
                >
                  <ChevronLeft className="h-4 w-4 mr-1" /> {t("previous")}
                </Button>
                {currentIndex !== questions.length - 1 && (
                  <Button
                    onClick={goNext}
                    disabled={currentIndex === questions.length - 1}
                    className="bg-green-600 hover:bg-green-700 px-4 md:px-6 flex-1 md:flex-none text-white"
                  >
                    {t("next_question")}
                    <ChevronRight className="h-4 w-4 ml-1 hidden md:inline" />
                  </Button>
                )}
                {currentIndex === questions.length - 1 && (
                  <Button
                    onClick={confirmAndSubmit}
                    className="bg-green-600 hover:bg-green-700 px-4 md:px-6 flex-1 md:flex-none text-white"
                  >
                    {t("submit")}
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <TimeUpDialog
        open={showTimeUpDialog}
        onConfirm={() => {
          setShowTimeUpDialog(false);
          handleSubmit();
        }}
      />

      <ConfirmDialog
        open={showConfirmSubmit}
        onClose={() => setShowConfirmSubmit(false)}
        onConfirm={() => {
          setShowConfirmSubmit(false);
          handleSubmit();
        }}
        title={t("submit_test_early_title")}
        description={t("submit_test_early_description")}
      />

      <AlertDialogWithReason
        open={showExitReason}
        onClose={() => setShowExitReason(false)}
        onConfirm={(reason: string) => {
          setShowExitReason(false);
          handleExitWithReason(reason);
        }}
      />
    </div>
  );
}
