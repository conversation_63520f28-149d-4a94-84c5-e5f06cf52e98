import { useEffect, useState } from "react";
import { createPortal } from "react-dom";
import { Button } from "@/components/ui/button";

export function AlertDialogWithReason({
  open,
  onClose,
  onConfirm,
}: {
  open: boolean;
  onClose: () => void;
  onConfirm: (reason: string) => void;
}) {
  const [reason, setReason] = useState("");

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") onClose();
    };
    if (open) {
      document.addEventListener("keydown", handleEscape);
    }
    return () => {
      document.removeEventListener("keydown", handleEscape);
    };
  }, [open, onClose]);

  if (!open) return null;

  return createPortal(
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 px-4">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-sm sm:max-w-md p-6 sm:p-6">
        <div className="mb-4">
          <h2 className="text-lg font-semibold">Exit Confirmation</h2>
        </div>
        <p className="text-sm text-gray-600">
          Are you sure you want to exit? Please enter a reason:
        </p>
        <textarea
          className="w-full border border-gray-300 p-2 mt-2 rounded text-sm"
          rows={3}
          value={reason}
          onChange={(e) => setReason(e.target.value)}
          placeholder="e.g. Emergency, Disconnected, etc."
        />
        <div className="flex flex-col-reverse sm:flex-row justify-end gap-2 mt-6">
          <Button variant="ghost" onClick={onClose} className="w-full sm:w-auto">
            Cancel
          </Button>
          <Button
            className="bg-red-600 text-white w-full sm:w-auto"
            onClick={() => {
              onConfirm(reason);
              setReason("");
            }}
            disabled={!reason.trim()}
          >
            Exit
          </Button>
        </div>
      </div>
    </div>,
    document.body
  );
}
