export interface BaseOption {
  id: string;
  text?: string;
  audio?: string;
  image?: string;
}

export interface TestSessionQuestion {
  id: string;
  type: string;
  question: string;
  image?: string;
  audio?: string;
  options?: BaseOption[];
  randomizeOptions?: boolean;
  data?: Record<string, any>;
  metadata?: Record<string, any>;
  internal?: Record<string, any>;
  sessionId: string;
  originalQuestionId: string;
  createdAt: Date;
}

export interface PluginProps {
  question: TestSessionQuestion;
  answer: TestSessionAnswer | undefined;
  onAnswer: (answer: TestSessionAnswer) => void;
}

// Define Uploader interface for plug-and-play upload services
export interface Uploader {
  upload(blob: Blob): Promise<string>;
}

// Interface for media answers that require upload
export interface MediaUpload {
  getValue(uploader: Uploader): Promise<string>;
}

// Allow TestSessionAnswer to include any value or a media answer
export type TestSessionAnswer = any | MediaUpload;

export class AudioAnswer implements MediaUpload {
  constructor(public localUrl: string, public blob: Blob) {}

  async getValue(uploader: Uploader): Promise<string> {
    return await uploader.upload(this.blob);
  }
}

export class VideoAnswer implements MediaUpload {
  constructor(public localUrl: string, public blob: Blob) {}

  async getValue(uploader: Uploader): Promise<string> {
    return await uploader.upload(this.blob);
  }
}

export class ScreenRecordInput implements MediaUpload {
  constructor(public localUrl: string, public blob: Blob) {}

  async getValue(uploader: Uploader): Promise<string> {
    return await uploader.upload(this.blob);
  }
}

export class WebCamInput implements MediaUpload {
  constructor(public localUrl: string, public blob: Blob) {}

  async getValue(uploader: Uploader): Promise<string> {
    return await uploader.upload(this.blob);
  }
}
