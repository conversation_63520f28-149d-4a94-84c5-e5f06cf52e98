import { useEffect } from "react";
import { createPortal } from "react-dom";
import { Button } from "@/components/ui/button";

export function ConfirmDialog({
  open,
  onClose,
  onConfirm,
  title,
  description,
}: {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  description: string;
}) {
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") onClose();
    };
    if (open) {
      document.addEventListener("keydown", handleEscape);
    }
    return () => {
      document.removeEventListener("keydown", handleEscape);
    };
  }, [open, onClose]);

  if (!open) return null;

  return createPortal(
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 px-4">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-sm sm:max-w-md p-6 sm:p-6">
        <div className="mb-4">
          <h2 className="text-lg font-semibold">{title}</h2>
        </div>
        <p className="mb-6 text-sm text-gray-600">{description}</p>
        <div className="flex flex-col-reverse sm:flex-row justify-end gap-2">
          <Button variant="ghost" onClick={onClose} className="w-full sm:w-auto">
            Cancel
          </Button>
          <Button
            className="bg-green-600 text-white hover:bg-green-700 w-full sm:w-auto"
            onClick={onConfirm}
          >
            Confirm
          </Button>
        </div>
      </div>
    </div>,
    document.body
  );
}
