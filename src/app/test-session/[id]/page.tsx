import Link from "next/link";
import QuestionClient from "./QuestionPageClient";
import { TestSessionDBRepository } from "@/src/lib/repositories/test-session";

export default async function QuestionPage({ params }: { params: { id: string } }) {
  const session = await TestSessionDBRepository.getById(params.id);

  if (!session) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center px-6 py-12 text-center">
        <div className="max-w-md">
          <div className="text-indigo-600 text-6xl mb-6">❌</div>
          <h1 className="text-2xl md:text-3xl font-semibold text-gray-800 mb-4">
            Test Session Not Found
          </h1>
          <p className="text-gray-600 mb-6">
            The test session may not exist or the ID might be incorrect.
          </p>
          <Link
            href="/"
            className="inline-block bg-indigo-600 text-white px-6 py-3 rounded-xl hover:bg-indigo-700 transition"
          >
            Back to Home
          </Link>
        </div>
      </div>
    );
  }

  if (session.status === "COMPLETED" || session.status === "EXPIRED" || session.status === "CANCELLED") {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center px-6 py-12 text-center">
        <div className="max-w-md">
          <div className="text-yellow-500 text-6xl mb-6">🔒</div>
          <h1 className="text-2xl md:text-3xl font-semibold text-gray-800 mb-4">
            Test Session Closed
          </h1>
          <p className="text-gray-600 mb-6">
            This test session is no longer accepting answers. Session status: {session.status.toLowerCase()}.
          </p>
          <Link
            href="/"
            className="inline-block bg-yellow-500 text-white px-6 py-3 rounded-xl hover:bg-yellow-600 transition"
          >
            Back to Home
          </Link>
        </div>
      </div>
    );
  }

  const questions = await TestSessionDBRepository.getQuestions(params.id);

  return <QuestionClient id={params.id} questions={questions} />;
}
