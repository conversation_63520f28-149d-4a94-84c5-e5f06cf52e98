"use client"

// components/TimerAndSubmit.tsx
import { Clock, LogOut } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"

interface Props {
  timeRemaining: number
  onSubmit: () => void
  onExit: () => void
}

export function TimerAndSubmit({ timeRemaining, onSubmit, onExit }: Props) {
  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`
  }

  return (
    <div className="flex items-center gap-4 md:gap-6">
      <div className="flex items-center gap-2">
        <Clock className="h-5 w-5 text-gray-600" />
        <span className="font-medium">{formatTime(timeRemaining)} remaining</span>
      </div>
      <Button
        onClick={onSubmit}
        className="bg-green-600 hover:bg-green-700 px-4 py-2 text-sm md:text-base text-white"
      >
        Submit
      </Button>
      <Button
        onClick={onExit}
        variant="ghost"
        className="text-white hover:text-white bg-red-500 hover:bg-red-600 gap-2 rounded-md flex space-x-2 p-2 w-fit"
        size="icon"
        aria-label="Exit Test"
      >
        <LogOut className="h-5 w-5 md:h-4 md:w-4" />
        <span className="md:inline">Exit Test</span>
      </Button>
    </div>
  )
}
