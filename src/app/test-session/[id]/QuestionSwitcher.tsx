"use client";

import { useEffect, useRef, useState } from "react";

interface Props {
  questions: { id: string }[];
  currentIndex: number;
  answers: Record<number, any>;
  goToQuestion: (index: number) => void;
}

export function QuestionSwitcher({
  questions,
  currentIndex,
  answers,
  goToQuestion,
}: Props) {
  const itemRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [isAutoScroll, setIsAutoScroll] = useState(false);

  useEffect(() => {
    if (!isAutoScroll) return;
    const currentEl = itemRefs.current[currentIndex];
    if (currentEl) {
      currentEl.scrollIntoView({
        behavior: "smooth",
        block: "nearest",
        inline: "center",
      });
    }
  }, [currentIndex, isAutoScroll]);

  return (
    <div
      className="
        w-full md:w-[80px] md:h-full
        overflow-x-auto md:overflow-y-auto
        p-2 md:px-0
        touch-auto
      "
    >
      <div
        className="
          flex flex-row md:flex-col items-center
          gap-2 md:gap-3
          pb-2 md:pb-0
          whitespace-nowrap
        "
      >
        {questions.map((q, index) => {
          const isAnswered = answers[index] !== undefined;
          const isActive = index === currentIndex;

          return (
            <div
              key={q.id}
              ref={(el) => {
                itemRefs.current[index] = el;
              }}
              onClick={() => {
                setIsAutoScroll(true);
                goToQuestion(index);
              }}
              className={`w-10 h-10 md:w-12 md:h-12 rounded-full md:rounded-lg flex items-center justify-center font-medium text-lg flex-shrink-0 cursor-pointer transition-colors
                ${
                  isActive
                    ? isAnswered
                      ? "bg-green-500 text-white"
                      : "bg-gray-900 text-white"
                    : "bg-gray-100 hover:bg-gray-200 text-gray-700"
                }
                ${isAnswered && !isActive ? "bg-green-500 text-white" : ""}
              `}
              title={isAnswered ? "Answered" : "Not Answered"}
            >
              {index + 1}
            </div>
          );
        })}
      </div>
    </div>
  );
}
