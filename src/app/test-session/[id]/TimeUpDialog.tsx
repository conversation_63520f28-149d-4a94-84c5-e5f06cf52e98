"use client";

import { useEffect } from "react";
import { createPortal } from "react-dom";
import { Button } from "@/components/ui/button";

export function TimeUpDialog({
  open,
  onConfirm,
  title = "⏰ Waktu Habis",
  description = "Waktu ujian kamu sudah selesai. Jawabanmu akan dikirim sekarang.",
}: {
  open: boolean;
  onConfirm: () => void;
  title?: string;
  description?: string;
}) {
  useEffect(() => {
    // prevent Escape key from closing
    const blockEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        e.preventDefault();
        e.stopPropagation();
      }
    };
    if (open) {
      document.addEventListener("keydown", blockEscape);
    }
    return () => {
      document.removeEventListener("keydown", blockEscape);
    };
  }, [open]);

  if (!open) return null;

  return createPortal(
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 px-4">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-sm sm:max-w-md p-6 sm:p-6 text-center">
        <h2 className="text-xl font-bold mb-4">{title}</h2>
        <p className="text-sm text-gray-700 mb-6">{description}</p>
        <Button
          className="bg-green-600 text-white hover:bg-green-700 w-full"
          onClick={onConfirm}
        >
          Kirim Jawaban
        </Button>
      </div>
    </div>,
    document.body
  );
}
