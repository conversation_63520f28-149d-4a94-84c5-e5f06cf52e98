"use client";

import React from "react";
import { Calendar<PERSON><PERSON>, ClockI<PERSON> } from "lucide-react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { cn } from "@/src/lib/utils";
import { useLocalization } from "@/src/hooks/useLocalization/client";
import { Test } from "@/src/lib/repositories/tests/TestsRepository";
import { locales } from "./locales";

interface TestCardProps {
  test: Test;
}

export function TestCard({ test }: TestCardProps) {
  const router = useRouter();
  const { t } = useLocalization("dashboard.test-builder", locales);

  const formatDate = (dateStr?: string | Date) => {
    if (!dateStr) return "-";
    const date = typeof dateStr === "string" ? new Date(dateStr) : dateStr;
    return `${date.toLocaleDateString()} ${date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}`;
  };

  return (
    <Card className="relative rounded-2xl gap-5 flex flex-col">
      <CardHeader className="p-0">
        <div className="flex w-full relative flex-row items-center pr-6 pt-4">
          <div
            className={cn(
              "px-6 py-2 rounded-r-full",
              test.status === "inprogress"
                ? "bg-sp-warning text-sp-neutral-800"
                : test.status === "done"
                  ? "bg-sp-success text-sp-neutral-100"
                  : test.status === "cancelled"
                    ? "bg-sp-error text-sp-neutral-100"
                    : test.status === "draft"
                      ? "bg-sp-info text-sp-neutral-100"
                      : ""
            )}
          >
            <p className="font-inter text-body-3 capitalize">{t(test.status)}</p>
          </div>
          <div className="ml-auto">
            <Avatar className="border border-sp-background">
              <AvatarImage
                src={test.creator.avatar || "https://github.com/shadcn.png"}
                alt={`@${test.creator.name}`}
              />
              <AvatarFallback className="uppercase">{test.creator.name.slice(0, 2)}</AvatarFallback>
            </Avatar>
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex flex-1 flex-col gap-5 px-6 pb-0">
        <div className="flex flex-col gap-2">
          <p className="font-inter text-body-1 text-sp-neutral-800">{test.title}</p>
          <p className="font-inter text-body-4 text-sp-neutral-600 font-light line-clamp-2">{test.description}</p>
        </div>

        <div className="flex flex-col gap-3">
          <p className="font-inter text-body-3 text-sp-neutral-800 capitalize">{t("details")}</p>
          <div className="flex flex-col gap-2">
            <div className="flex flex-row items-center gap-2 text-sp-neutral-600">
              <CalendarIcon className="w-5 h-5" />
              <p className="font-inter text-body-4 font-light">{test.createdAt.toDateString()}</p>
            </div>
            {test.availability?.startTime && test.availability?.endTime && (
              <div className="flex flex-row items-center gap-2 text-sp-neutral-600">
                <ClockIcon className="w-5 h-5" />
                <p className="font-inter text-body-4 font-light">
                  {formatDate(test.availability.startTime)} – {formatDate(test.availability.endTime)}
                </p>
              </div>
            )}
          </div>
        </div>

        <div className="flex flex-col gap-3">
          <p className="font-inter text-body-3 text-sp-neutral-800 capitalize">{t("attendees")}</p>
          <div className="flex flex-row items-center gap-2">
            {test.participants && (
              <div className="flex -space-x-2">
                {test.participants.map((a, idx) => (
                  <Avatar key={idx} className="border border-sp-background">
                    <AvatarImage src={a.avatar || "https://github.com/shadcn.png"} alt={`@${a.name}`} />
                    <AvatarFallback>{a.name.slice(0, 2)}</AvatarFallback>
                  </Avatar>
                ))}
              </div>
            )}
            <Button size="icon" variant="outline" className="h-10 w-10 border-dashed">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={2}
                stroke="currentColor"
                className="w-5 h-5 text-sp-neutral-600"
              >
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 4v16m8-8H4" />
              </svg>
            </Button>
          </div>
          <div className="flex flex-row items-center">
            <p className="font-inter text-body-3 text-sp-neutral-800 pr-2 border-r">
              {test.usersCount.toLocaleString()} participants
            </p>
            <p className="font-inter text-body-4 text-sp-neutral-600 pl-2 font-extralight">
              {test.totalQuestions || 0} Questions
            </p>
          </div>
        </div>
      </CardContent>

      <CardFooter className="flex flex-row gap-2 px-6 items-end">
        <Button variant="outline" onClick={() => router.push(`/dashboard/test-builder/t/${test.id}`)}>
          {t("btn_edit")}
        </Button>
        <Button className="w-full" onClick={() => router.push(`/dashboard/test-builder/t/${test.id}/questions`)}>
          {t("btn_manage_question")}
        </Button>
      </CardFooter>
    </Card>
  );
}
