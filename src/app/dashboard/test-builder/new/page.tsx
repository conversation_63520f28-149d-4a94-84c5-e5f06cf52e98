"use client";

import { useRouter } from "next/navigation";
import { TestsAPI } from "@/src/services/testsApi";
import { Test } from "@/src/lib/repositories/tests/TestsRepository";
import { TestEditor } from "../editor-client";

interface TestUpdateProps { }

const emptyTest: Test = {
  id: "",
  title: "",
  benefits: [],
  usersCount: 0,
  totalQuestions: 0,
  createdAt: new Date(),
  updatedAt: new Date(),
  status: "draft",
  creator: {
    id: "",
    name: "",
    avatar: ""
  }
};

export default function TestUpdate({ }: TestUpdateProps) {
  const router = useRouter();

  return (
    <TestEditor
      test={emptyTest}
      testSessions={[]}
      onSave={async (updatedTest) => {
        const createdTest = await TestsAPI.CreateTest(updatedTest).request();
        router.push(`/dashboard/test-builder/t/${createdTest.id}`);
        return createdTest;
      }}
    />
  );
}
