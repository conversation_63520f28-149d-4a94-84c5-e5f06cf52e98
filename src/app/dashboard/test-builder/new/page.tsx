"use client";

import { useRouter } from "next/navigation";
import { Test } from "@/src/lib/repositories/tests/interface";
import { TestEditor } from "../editor-client";
import { TestsAPI } from "@/src/services/testsApi";

interface TestUpdateProps { }

const emptyTest: Test = {
  id: "",
  title: "",
  description: "",
  benefits: [],
  usersCount: 0,
  totalQuestions: 0,
  questionIds: [],
  createdAt: new Date(),
  updatedAt: new Date(),
  access: "PRIVATE",
  status: "DRAFT",
  creator: {
    id: "",
    name: "",
    avatar: ""
  },
  categories: [],
  difficulties: [],
  successRate: 0,
  durationInSeconds: 1800,
  image: "",
  cover: "",
  mediaUrl: "",
  reason: "",
  premium: {
    isPremium: false,
    price: "",
    offerText: "",
    badges: []
  },
  topics: [],
  createdBy: ""
};

export default function TestUpdate({ }: TestUpdateProps) {
  const router = useRouter();

  return (
    <TestEditor
      test={emptyTest}
      testSessions={[]}
      onSave={async (updatedTest) => {
        const response = await TestsAPI.CreateTest({
          ...updatedTest,
          createdBy: 'current-user-id', // This should come from auth context
        }).request();
        
        router.push(`/dashboard/test-builder/t/${response.id}`);
        return response;
      }}
    />
  );
}
