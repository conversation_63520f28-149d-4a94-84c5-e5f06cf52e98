'use client';

import { useEffect, useState } from 'react';
import { TestsAPI } from '@/src/services/testsApi';
import { Search, Plus, Trash2, Trash } from 'lucide-react';

// Add type declaration for window
declare global {
  interface Window {
    searchTimeout: NodeJS.Timeout;
  }
}

function Spinner() {
  return (
    <div className="flex justify-center items-center py-6">
      <svg
        className="animate-spin h-8 w-8 text-blue-600"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        aria-label="Loading spinner"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
        />
      </svg>
    </div>
  );
}

export default function Page({ params }: { params: { testId: string } }) {
  const { testId } = params;

  const [testQuestions, setTestQuestions] = useState<
    Array<{ id: string; question: string; type: string }>
  >([]);
  const [selectedToRemove, setSelectedToRemove] = useState<string[]>([]);
  const [showModal, setShowModal] = useState(false);
  const [modalLoading, setModalLoading] = useState(false);
  const [allQuestions, setAllQuestions] = useState<
    Array<{ id: string; question: string; type: string, difficulty: string, tags: string[] }>
  >([]);
  const [selectedToAdd, setSelectedToAdd] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [mainSearchTerm, setMainSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [pageSize] = useState(10); // 10 items per page to match your pagination UI

  const loadAllQuestions = async (page = 1, search = '') => {
    setModalLoading(true);
    try {
      const queryParams = new URLSearchParams();
      queryParams.append('page', page.toString());
      queryParams.append('limit', '50'); // Load more questions for the modal
      if (search.trim()) {
        queryParams.append('search', search.trim());
      }

      const res = await fetch(`/api/v1/questions?${queryParams}`);
      const data = await res.json();
      console.log(data);

      if (data.status === 'success' && data.data) {
        setAllQuestions(data.data.items || []);
        setTotalItems(data.data.total);
      }
    } catch (error) {
      console.error('Error loading all questions:', error);
    } finally {
      setModalLoading(false);
    }
  };

  const handleOpenModal = () => {
    setShowModal(true);
  };

  // Add function to fetch test questions with pagination
  const fetchTestQuestionsWithPagination = async (page = 1, search = '') => {
    setLoading(true);
    try {
      // For now, we'll use the existing API since test questions don't have pagination
      // but we'll implement the pagination logic on the frontend
      await loadAllQuestions(1, searchTerm);
      const response = await TestsAPI.GetTestQuestions(testId).request();
      const allTestQuestions = response || [];

      // Apply search filter
      const filteredQuestions = search.trim()
        ? allTestQuestions.filter(q =>
          q.question.toLowerCase().includes(search.toLowerCase().trim())
        )
        : allTestQuestions;

      // Apply pagination
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedQuestions = filteredQuestions.slice(startIndex, endIndex);

      setTotalPages(Math.ceil(filteredQuestions.length / pageSize))
      setTestQuestions(paginatedQuestions);
      setCurrentPage(page);
    } catch (error) {
      console.error('Error loading test questions:', error);
    } finally {
      setLoading(false);
    }
  };

  // Update useEffect to use the new function
  useEffect(() => {
    fetchTestQuestionsWithPagination(currentPage, mainSearchTerm);
  }, [testId, currentPage, mainSearchTerm]);

  // Add search handler
  const handleMainSearch = (searchValue: string) => {
    setMainSearchTerm(searchValue);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Add pagination handlers
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const handleToggleRemove = (id: string) => {
    setSelectedToRemove((prev) =>
      prev.includes(id) ? prev.filter((qid) => qid !== id) : [...prev, id]
    );
  };

  const handleToggleAdd = (id: string) => {
    setSelectedToAdd((prev) =>
      prev.includes(id) ? prev.filter((qid) => qid !== id) : [...prev, id]
    );
  };

  const handleSave = async () => {
    if (selectedToAdd.length === 0 && selectedToRemove.length === 0) {
      alert('No changes to save.');
      return;
    }

    setSaving(true);
    try {
      await TestsAPI.UpdateTestQuestions(testId, {
        added: selectedToAdd,
        removed: selectedToRemove
      }).request();

      // Refresh the test questions with current pagination and search
      await fetchTestQuestionsWithPagination(currentPage, mainSearchTerm);
      setSelectedToAdd([]);
      setSelectedToRemove([]);
      alert('✅ Changes saved!');
    } catch (error) {
      console.error('Save error:', error);
      alert('❌ Failed to save changes.');
    } finally {
      setSaving(false);
      setShowModal(false);
      setSearchTerm('');
    }
  };

  const handleRemove = async (id: string) => {

    if (id === undefined || id === null) {
      alert('No questions selected for removal.');
      return;
    }
    setSaving(true);
    try {
      await TestsAPI.UpdateTestQuestions(testId, {
        added: [],
        removed: [id]
      }).request();
      // Refresh the test questions with current pagination and search
      await fetchTestQuestionsWithPagination(currentPage, mainSearchTerm);
      setSelectedToRemove([]);
      alert('✅ Questions removed successfully!');
    } catch (error) {
      console.error('Error removing questions:', error);
      alert('❌ Failed to remove questions.');
    } finally {
      setSaving(false);
    }
  };



  return (
    <div className="w-full max-w-6xl mx-auto bg-white rounded-xl shadow-sm">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <h1 className="text-xl font-semibold text-gray-900">List Questions</h1>
          <span className="px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded">
            {params.testId}
          </span>
        </div>

        <div className="flex items-center gap-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search question..."
              value={mainSearchTerm}
              onChange={(e) => handleMainSearch(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64"
            />
          </div>
          {selectedToRemove.length > 0 ? (
            <button
              onClick={handleOpenModal}
              className="flex items-center gap-2 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition text-sm font-medium"
            >
              <Trash className="w-4 h-4" />
              Remove Question
            </button>
          ) : (
            <button
              onClick={handleOpenModal}
              className="flex items-center gap-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition text-sm font-medium"
            >
              <Plus className="w-4 h-4" />
              Add Question
            </button>
          )}
        </div>
      </div>

      {/* Questions Table */}
      <div>
        {loading ? (
          <Spinner />
        ) : testQuestions.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">No questions yet.</p>
          </div>
        ) : (
          <div className="overflow-hidden">
            {/* Table Header */}
            <div className="grid grid-cols-12 gap-4 py-4 px-6 bg-gray-50 border-b border-gray-200 text-sm font-medium text-gray-700">
              <div className="col-span-1 flex items-center">
                <input
                  type="checkbox"
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  checked={selectedToRemove.length === testQuestions.length && testQuestions.length > 0}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedToRemove(testQuestions.map(q => q.id));
                    } else {
                      setSelectedToRemove([]);
                    }
                  }}
                />
              </div>
              <div className="col-span-1">No.</div>
              <div className="col-span-8">Questions</div>
              <div className="col-span-2 items-center justify-end text-right">Actions</div>
            </div>

            {/* Table Body */}
            <div className="divide-y divide-gray-200">
              {testQuestions.map((q, index) => {
                const isQueation = allQuestions.filter((tq) => tq.id === q.id)[0]

                return (
                  <div
                    key={q.id}
                    className={`grid grid-cols-12 gap-4 py-4 px-6 hover:bg-gray-50 transition-colors ${index % 2 === 1 ? 'bg-gray-50' : ''
                      }`}
                  >
                    <div className="col-span-1 flex items-center">
                      <input
                        type="checkbox"
                        className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        checked={selectedToRemove.includes(q.id)}
                        onChange={() => handleToggleRemove(q.id)}
                      />
                    </div>

                    <div className="col-span-1 flex items-center">
                      <span className="text-sm text-gray-600">{index + 1 + (currentPage - 1) * pageSize}.</span>
                    </div>

                    <div className="col-span-8 flex items-center gap-3">
                      <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs font-medium">
                          {q.type === 'multipleChoice' ? 'MC' :
                            q.type === 'singleChoice' ? 'SC' :
                              q.type === 'textInput' ? 'TI' : 'Q'}
                        </span>
                      </div>
                      {isQueation ? (
                        <div>
                          <p className="text-sm text-gray-900 line-clamp-2">
                            {isQueation?.question || '-'}
                          </p>
                          <div className="flex items-center gap-2 mt-1">
                            {isQueation?.tags?.map((a, b) => {
                              return (
                                <span key={b} className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded capitalize">
                                  {a}
                                </span>
                              )
                            })}
                          </div>
                        </div>
                      ) : (
                        <div className="text-sm text-gray-900">
                          -
                        </div>
                      )}
                    </div>

                    <div className="col-span-2 flex items-center justify-end">
                      <button
                        onClick={() => handleRemove(q.id)}
                        className={`p-1 rounded hover:bg-red-100 transition-colors text-gray-400`}
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between py-4 px-6 border-t border-gray-200">
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="flex items-center gap-2 py-2 text-sm text-gray-600 hover:text-gray-900 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              ← Previous
            </button>

            <div className="flex items-center gap-2">
              {/* Show page numbers */}
              {Array.from({ length: Math.min(totalPages, 10) }, (_, i) => {
                const pageNumber = i + 1;
                if (totalPages <= 10) {
                  return (
                    <button
                      key={pageNumber}
                      onClick={() => handlePageChange(pageNumber)}
                      className={`w-8 h-8 rounded text-sm font-medium transition-colors ${currentPage === pageNumber
                        ? 'bg-green-600 text-white'
                        : 'text-gray-600 hover:bg-gray-100'
                        }`}
                    >
                      {pageNumber}
                    </button>
                  );
                } else {
                  // Show smart pagination for many pages
                  if (pageNumber <= 3 || pageNumber > totalPages - 3 || Math.abs(pageNumber - currentPage) <= 1) {
                    return (
                      <button
                        key={pageNumber}
                        onClick={() => handlePageChange(pageNumber)}
                        className={`w-8 h-8 rounded text-sm font-medium transition-colors ${currentPage === pageNumber
                          ? 'bg-green-600 text-white'
                          : 'text-gray-600 hover:bg-gray-100'
                          }`}
                      >
                        {pageNumber}
                      </button>
                    );
                  } else if (pageNumber === 4 && currentPage > 5) {
                    return <span key="ellipsis1" className="text-gray-400 px-2">...</span>;
                  } else if (pageNumber === totalPages - 3 && currentPage < totalPages - 4) {
                    return <span key="ellipsis2" className="text-gray-400 px-2">...</span>;
                  }
                  return null;
                }
              })}
            </div>

            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600 hover:text-gray-900 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next →
            </button>
          </div>
        )}
      </div>

      {/* Modal for Adding Questions */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-start pt-20">
          <div className="bg-white rounded-xl shadow-xl w-full max-w-4xl max-h-[85vh] overflow-hidden">
            {/* Modal Header */}
            <div className="flex justify-between items-center p-6 border-b border-gray-200">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  Select new questions to add
                </h2>
              </div>

              {/* Search Input */}
              <div className="flex items-center gap-3">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Search questions..."
                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64"
                    value={searchTerm}
                    onChange={(e) => {
                      setSearchTerm(e.target.value);
                      // Debounce the search to avoid too many API calls
                      clearTimeout(window.searchTimeout);
                      window.searchTimeout = setTimeout(() => {
                        loadAllQuestions(1, e.target.value);
                      }, 500);
                    }}
                  />
                </div>
                <a
                  href="/dashboard/question-editor/new"
                  className="flex items-center gap-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition text-sm font-medium"
                >
                  Create Question
                </a>
              </div>
            </div>

            {/* Questions Table */}
            <div className="overflow-y-auto max-h-[60vh]">
              {modalLoading ? (
                <div className="p-6">
                  <Spinner />
                </div>
              ) : allQuestions.length === 0 ? (
                <div className="text-center py-12">
                  <p className="text-gray-500">No questions found.</p>
                </div>
              ) : (
                <div>
                  {/* Table Header */}
                  <div className="grid grid-cols-12 gap-4 p-4 bg-gray-50 border-b border-gray-200 text-sm font-medium text-gray-700 sticky top-0">
                    <div className="col-span-1 flex items-center">
                      <input
                        type="checkbox"
                        className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        checked={(() => {
                          const availableQuestions = allQuestions.filter(q => !testQuestions.some(tq => tq.id === q.id));
                          return selectedToAdd.length === availableQuestions.length && availableQuestions.length > 0;
                        })()}
                        onChange={(e) => {
                          const availableQuestions = allQuestions.filter(q => !testQuestions.some(tq => tq.id === q.id));
                          if (e.target.checked) {
                            setSelectedToAdd(availableQuestions.map(q => q.id));
                          } else {
                            setSelectedToAdd([]);
                          }
                        }}
                      />
                    </div>
                    <div className="col-span-1">No.</div>
                    <div className="col-span-6">Questions</div>
                    <div className="col-span-2">Type</div>
                    <div className="col-span-2">Difficulty</div>
                  </div>

                  {/* Table Body */}
                  <div>
                    {allQuestions.filter(q => {
                      const isAlreadyAdded = testQuestions.some((tq) => tq.id === q.id);
                      return !isAlreadyAdded;
                    }).map((q, index) => {
                      const getQuestionType = (type: string) => {
                        switch (type) {
                          case 'multipleChoice': return 'MCQ';
                          case 'singleChoice': return 'Single Choice';
                          case 'textInput': return 'Text';
                          case 'codeInput': return 'Code';
                          case 'imageBased': return 'Image';
                          case 'audioBased': return 'Audio';
                          case 'audioAnswer': return 'Audio Answer';
                          case 'voiceInput': return 'Voice';
                          case 'fileUpload': return 'File Upload';
                          default: return 'Text';
                        }
                      };

                      return (
                        <div
                          key={q.id}
                          className={`grid grid-cols-12 gap-4 p-4 border-b border-gray-100 hover:bg-gray-50 transition-colors ${index % 2 === 1 ? 'bg-gray-50' : ''
                            }`}
                        >
                          <div className="col-span-1 flex items-center">
                            <input
                              type="checkbox"
                              className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                              checked={selectedToAdd.includes(q.id)}
                              onChange={() => handleToggleAdd(q.id)}
                            />
                          </div>

                          <div className="col-span-1 flex items-center">
                            <span className="text-sm text-gray-600">{index + 1}.</span>
                          </div>

                          <div className="col-span-6 flex items-center gap-3">
                            <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center flex-shrink-0">
                              <span className="text-white text-xs font-medium">
                                {q.type === 'multipleChoice' ? 'MC' :
                                  q.type === 'singleChoice' ? 'SC' :
                                    q.type === 'textInput' ? 'TI' : 'Q'}
                              </span>
                            </div>
                            <div className="flex-1">
                              <p className="text-sm text-gray-900 line-clamp-2 leading-relaxed">
                                {q.question}
                              </p>
                              <div className="flex items-center gap-2 mt-1 flex-wrap">
                                {q.tags?.map((a, b) => {
                                  return (
                                    <span key={b} className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded capitalize">
                                      {a}
                                    </span>
                                  )
                                })}
                              </div>
                            </div>
                          </div>

                          <div className="col-span-2 flex items-center">
                            <span className="text-sm text-gray-900">
                              {getQuestionType(q.type)}
                            </span>
                          </div>

                          <div className="col-span-2 flex items-center">
                            <span className="text-sm text-gray-900 capitalize ">
                              {q.difficulty || '-'}
                            </span>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>

            {/* Modal Footer */}
            <div className="flex justify-end items-center gap-3 p-6 border-t border-gray-200 bg-white">
              <button
                onClick={() => {
                  setShowModal(false);
                  setSearchTerm('');
                  setSelectedToAdd([]);
                }}
                className="px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                disabled={selectedToAdd.length === 0 || saving}
                className="px-6 py-2 bg-gray-800 text-white rounded-lg hover:bg-gray-900 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {saving ? 'Adding...' : `Add ( ${selectedToAdd.length} ) Questions`}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
