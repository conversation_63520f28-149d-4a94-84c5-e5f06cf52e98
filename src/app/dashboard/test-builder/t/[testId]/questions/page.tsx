'use client';

import { useEffect, useState } from 'react';

function Spinner() {
  return (
    <div className="flex justify-center items-center py-6">
      <svg
        className="animate-spin h-8 w-8 text-blue-600"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        aria-label="Loading spinner"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
        />
      </svg>
    </div>
  );
}

export default function Page({ params }: { params: { testId: string } }) {
  const { testId } = params;

  const [testQuestions, setTestQuestions] = useState<
    Array<{ id: string; question: string; type: string }>
  >([]);
  const [selectedToRemove, setSelectedToRemove] = useState<string[]>([]);
  const [showModal, setShowModal] = useState(false);
  const [modalLoading, setModalLoading] = useState(false);
  const [allQuestions, setAllQuestions] = useState<
    Array<{ id: string; question: string; type: string }>
  >([]);
  const [selectedToAdd, setSelectedToAdd] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    const fetchTestQuestions = async () => {
      setLoading(true);
      try {
        const res = await fetch(`/api/v1/tests/${testId}/questions`);
        const body = await res.json();
        setTestQuestions(body.data || []);
      } catch (error) {
        console.error('Error loading test questions:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTestQuestions();
  }, [testId]);

  const loadAllQuestions = async () => {
    setModalLoading(true);
    try {
      const res = await fetch(`/api/v1/questions`);
      const data = await res.json();
      setAllQuestions(data.data || []);
    } catch (error) {
      console.error('Error loading all questions:', error);
    } finally {
      setModalLoading(false);
    }
  };

  const handleOpenModal = () => {
    setShowModal(true);
    loadAllQuestions();
  };

  const handleToggleRemove = (id: string) => {
    setSelectedToRemove((prev) =>
      prev.includes(id) ? prev.filter((qid) => qid !== id) : [...prev, id]
    );
  };

  const handleToggleAdd = (id: string) => {
    setSelectedToAdd((prev) =>
      prev.includes(id) ? prev.filter((qid) => qid !== id) : [...prev, id]
    );
  };

  const handleSave = async () => {
    if (selectedToAdd.length === 0 && selectedToRemove.length === 0) {
      alert('No changes to save.');
      return;
    }

    setSaving(true);
    try {
      const res = await fetch(`/api/v1/tests/${testId}/questions`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ added: selectedToAdd, removed: selectedToRemove }),
      });

      if (!res.ok) throw new Error('Failed to save.');

      const refreshed = await fetch(`/api/v1/tests/${testId}/questions`);
      const newData = await refreshed.json();
      setTestQuestions(newData.data || []);
      setSelectedToAdd([]);
      setSelectedToRemove([]);
      alert('✅ Changes saved!');
    } catch (error) {
      console.error('Save error:', error);
      alert('❌ Failed to save changes.');
    } finally {
      setSaving(false);
      setShowModal(false);
      setSearchTerm('');
    }
  };

  const filteredQuestions = allQuestions.filter((q) =>
    q.question.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="max-w-3xl mx-auto p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">📝 Test Questions</h1>
        <div className="space-x-2">
          <button
            onClick={handleOpenModal}
            className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition"
          >
            ➕ Add
          </button>
          <button
            onClick={handleSave}
            disabled={saving}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition disabled:opacity-50"
          >
            {saving ? 'Saving...' : '💾 Save'}
          </button>
        </div>
      </div>

      {/* Question List */}
      {loading ? (
        <Spinner />
      ) : testQuestions.length === 0 ? (
        <p>No questions yet.</p>
      ) : (
        <div className="space-y-3">
          {testQuestions.map((q, index) => (
            <div
              key={q.id}
              className={`flex items-center justify-between p-4 border rounded ${
                selectedToRemove.includes(q.id) ? 'bg-red-50' : ''
              }`}
            >
              <div>
                <p className="font-medium text-gray-800">
                  Q{index + 1}: {q.question}
                </p>
                <p className="text-sm text-gray-500">{q.type}</p>
              </div>
              <input
                type="checkbox"
                checked={selectedToRemove.includes(q.id)}
                onChange={() => handleToggleRemove(q.id)}
              />
            </div>
          ))}
        </div>
      )}

      {/* Modal for Adding Questions */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-start pt-20">
          <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Add Questions</h2>
              <button
                onClick={() => {
                  setShowModal(false);
                  setSearchTerm('');
                }}
                className="text-gray-600 hover:text-red-600"
              >
                ✖
              </button>
            </div>

            {/* Search Input */}
            <input
              type="text"
              placeholder="Search questions..."
              className="mb-4 w-full p-2 border rounded focus:outline-none focus:ring focus:ring-blue-300"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              autoFocus
            />

            {modalLoading ? (
              <Spinner />
            ) : filteredQuestions.length === 0 ? (
              <p>No questions found.</p>
            ) : (
              <div className="space-y-3 max-h-[60vh] overflow-y-auto">
                {filteredQuestions.map((q) => (
                  <label
                    key={q.id}
                    className="flex items-start space-x-3 border p-3 rounded-md hover:bg-gray-50 transition cursor-pointer"
                  >
                    <input
                      type="checkbox"
                      className="mt-1"
                      checked={selectedToAdd.includes(q.id)}
                      onChange={() => handleToggleAdd(q.id)}
                      disabled={testQuestions.some((tq) => tq.id === q.id)}
                    />
                    <div>
                      <p className="text-gray-800">{q.question}</p>
                      <p className="text-gray-500 text-sm">{q.type}</p>
                    </div>
                  </label>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
