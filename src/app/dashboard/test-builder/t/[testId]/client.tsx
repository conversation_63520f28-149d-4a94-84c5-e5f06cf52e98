"use client";

import { useRouter } from "next/navigation";
import { TestsAPI } from "@/src/services/testsApi";
import { Test } from "@/src/lib/repositories/tests/TestsRepository";
import { TestSession } from "@/src/lib/test-types";
import { TestEditor } from "../../editor-client";

interface TestUpdateProps {
  test: Test;
  sessions: TestSession[];
}

export function TestUpdate({ test, sessions }: TestUpdateProps) {
  const router = useRouter();

  return (
    <TestEditor
      test={test}
      testSessions={sessions}
      onSave={async (updatedTest) => {
        return await TestsAPI.UpdateTest(test.id, updatedTest).request();
      }}
      onDelete={async () => {
        await TestsAPI.DeleteTest(test.id).request();
        router.push("/dashboard/test-builder");
      }}
    />
  );
}
