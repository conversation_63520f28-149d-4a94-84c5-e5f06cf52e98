"use client";

import { useRouter } from "next/navigation";
import { Test } from "@/src/lib/repositories/tests/interface";
import { TestSession } from "@/src/lib/test-types";
import { TestEditor } from "../../editor-client";
import { TestsAPI } from "@/src/services/testsApi";

interface TestUpdateProps {
  test: Test;
  sessions: TestSession[];
}

export function TestUpdate({ test, sessions }: TestUpdateProps) {
  const router = useRouter();

  return (
    <TestEditor
      test={test}
      testSessions={sessions}
      onSave={async (updatedTest) => {
        const response = await TestsAPI.UpdateTest(test.id, {
          ...updatedTest,
          updatedBy: 'current-user-id', // This should come from auth context
        }).request();
        
        return response;
      }}
      onDelete={async () => {
        const response = await TestsAPI.DeleteTest(test.id).request();
        router.push("/dashboard/test-builder");
      }}
    />
  );
}
