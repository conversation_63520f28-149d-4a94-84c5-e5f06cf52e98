"use client";

import { useEffect, useMemo, useState } from "react";
import { useRouter } from "next/navigation";
import { Search, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useLocalization } from "@/src/localization/functions/client";
import en from "./locales/en.json";
import { TestCard } from "./DashboardCreatedTestCard";
import { Test } from "@/src/lib/repositories/tests/interface";
import { TestsAPI } from "@/src/services/testsApi";

export default function Page() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [tests, setTests] = useState<Test[]>([]);
  const [loading, setLoading] = useState(true);
  const { t } = useLocalization("dashboard.test-builder", { en });

  useEffect(() => {
    const fetchTests = async () => {
      try {
        const response = await TestsAPI.Tests({ page: 1, per_page: 50 }).request();
        setTests(response.items || []);
      } catch (err) {
        console.error(err);
      } finally {
        setLoading(false);
      }
    };
    fetchTests();
  }, []);

  const filteredTests = useMemo(
    () => tests.filter((test) => test.title.toLowerCase().includes(searchQuery.toLowerCase())),
    [tests, searchQuery]
  );

  return (
    <div className="min-h-screen bg-sp-neutral-100 rounded-3xl">
      <div className="container mx-auto p-4 gap-4 flex flex-col">
        <div className="flex justify-between items-center pb-4 border-b">
          <div className="flex flex-row items-center gap-4">
            <p className="font-inter text-body-3">{t("title")}</p>
            <span className="px-1 rounded-full bg-sp-success/10 border-sp-success border text-[10px] font-medium flex items-center justify-center text-sp-success">
              {filteredTests.length} {t("tests")}
            </span>
          </div>
          <div className="flex flex-row items-center gap-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                placeholder={t("searching_placeholder")}
                className="pl-10 rounded-full"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button onClick={() => router.push("/dashboard/test-builder/new")}>
              <Plus className="h-4 w-4 mr-2" />
              {t("create_tests")}
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTests.map((test) => (
            <TestCard key={test.id} test={test} />
          ))}
        </div>

        {loading ? (
          <div className="text-center py-12">
            <p className="text-gray-500">{"loading..."}</p>
          </div>
        ) : filteredTests.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">{"No tests found. Create a new test to get started."}</p>
          </div>
        ) : null}
      </div>
    </div>
  );
}
