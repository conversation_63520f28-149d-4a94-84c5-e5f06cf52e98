"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Monitor, Tablet, Smartphone, Pencil, Zap, Shield, Heart, Star } from "lucide-react"

interface LandingPageData {
  id: string
  name: string
  status: "published" | "DRAFT"
  components: any[]
  styles: {
    primaryColor: string
    secondaryColor: string
    accentColor: string
    fontFamily: string
    spacing: string
  }
}

const mockLandingPage: LandingPageData = {
  id: "1",
  name: "Product Launch Campaign",
  status: "published",
  components: [
    {
      id: "hero-1",
      type: "hero",
      props: {
        title: "Transform Your Business Today",
        subtitle: "Discover the power of our innovative solution",
        buttonText: "Get Started",
        backgroundImage: "/landing-page-template-1.png",
      },
    },
    {
      id: "features-1",
      type: "features",
      props: {
        title: "Why Choose Us",
        features: [
          { icon: "zap", title: "Fast", description: "Lightning-fast performance" },
          { icon: "shield", title: "Secure", description: "Bank-level security" },
          { icon: "heart", title: "Reliable", description: "99.9% uptime guarantee" },
        ],
      },
    },
    {
      id: "testimonials-1",
      type: "testimonials",
      props: {
        title: "What Our Customers Say",
        testimonials: [
          {
            content: "This product has transformed our business completely.",
            name: "John Doe",
            role: "CEO, Company Inc.",
          },
          {
            content: "Outstanding service and incredible results.",
            name: "Jane Smith",
            role: "Marketing Director",
          },
        ],
      },
    },
    {
      id: "cta-1",
      type: "cta",
      props: {
        title: "Ready to Get Started?",
        subtitle: "Join thousands of satisfied customers today",
        buttonText: "Get Started",
        secondaryButtonText: "Learn More",
      },
    },
  ],
  styles: {
    primaryColor: "#3b82f6",
    secondaryColor: "#64748b",
    accentColor: "#f59e0b",
    fontFamily: "Inter",
    spacing: "normal",
  },
}

export default function LandingPagePreview() {
  const params = useParams()
  const router = useRouter()
  const [landingPage, setLandingPage] = useState<LandingPageData | null>(null)
  const [previewMode, setPreviewMode] = useState<"desktop" | "tablet" | "mobile">("desktop")
  const [showControls, setShowControls] = useState(true)

  useEffect(() => {
    // In a real app, fetch the landing page data
    // For now, use mock data
    setLandingPage(mockLandingPage)
  }, [params.id])

  if (!landingPage) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
          <p>Loading preview...</p>
        </div>
      </div>
    )
  }

  const getPreviewWidth = () => {
    switch (previewMode) {
      case "mobile":
        return "max-w-sm"
      case "tablet":
        return "max-w-2xl"
      default:
        return "w-full"
    }
  }

  const toggleControls = () => {
    setShowControls(!showControls)
  }

  return (
    <div className="min-h-screen bg-gray-100 flex flex-col">
      {/* Controls */}
      {showControls && (
        <div className="bg-background border-b p-2 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" asChild>
              <Link href={`/dashboard/landing-builder/${params.id}`}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Editor
              </Link>
            </Button>
            <h1 className="font-medium">{landingPage.name} - Preview</h1>
          </div>

          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1 border rounded-md p-1">
              <Button
                variant={previewMode === "desktop" ? "default" : "ghost"}
                size="sm"
                onClick={() => setPreviewMode("desktop")}
              >
                <Monitor className="h-4 w-4" />
              </Button>
              <Button
                variant={previewMode === "tablet" ? "default" : "ghost"}
                size="sm"
                onClick={() => setPreviewMode("tablet")}
              >
                <Tablet className="h-4 w-4" />
              </Button>
              <Button
                variant={previewMode === "mobile" ? "default" : "ghost"}
                size="sm"
                onClick={() => setPreviewMode("mobile")}
              >
                <Smartphone className="h-4 w-4" />
              </Button>
            </div>

            <Button variant="outline" size="sm" asChild>
              <Link href={`/dashboard/landing-builder/${params.id}`}>
                <Pencil className="h-4 w-4 mr-2" />
                Edit
              </Link>
            </Button>
          </div>
        </div>
      )}

      {/* Preview */}
      <div className="flex-1 overflow-auto p-0 flex justify-center items-start">
        <div
          className={`${getPreviewWidth()} transition-all duration-300 bg-white shadow-md`}
          style={{ minHeight: "100vh" }}
        >
          {/* Render the landing page components here */}
          <div style={{ fontFamily: landingPage.styles.fontFamily }}>
            {landingPage.components.map((component) => {
              switch (component.type) {
                case "hero":
                  return (
                    <div
                      key={component.id}
                      className="relative min-h-[500px] flex items-center justify-center text-white"
                      style={{
                        backgroundImage: component.props.backgroundImage
                          ? `url(${component.props.backgroundImage})`
                          : "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                        backgroundSize: "cover",
                        backgroundPosition: "center",
                      }}
                    >
                      <div className="absolute inset-0 bg-black/40" />
                      <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
                        <h1 className="text-4xl md:text-6xl font-bold mb-6">{component.props.title}</h1>
                        <p className="text-xl md:text-2xl mb-8 opacity-90">{component.props.subtitle}</p>
                        <Button
                          size="lg"
                          className="bg-white text-gray-900 hover:bg-gray-100"
                          style={{ backgroundColor: landingPage.styles.accentColor, color: "white" }}
                        >
                          {component.props.buttonText}
                        </Button>
                      </div>
                    </div>
                  )

                case "features":
                  return (
                    <div key={component.id} className="py-16 px-4">
                      <div className="max-w-6xl mx-auto">
                        <div className="text-center mb-12">
                          <h2 className="text-3xl md:text-4xl font-bold mb-4">{component.props.title}</h2>
                          {component.props.subtitle && (
                            <p className="text-xl text-gray-600 max-w-2xl mx-auto">{component.props.subtitle}</p>
                          )}
                        </div>
                        <div className="grid md:grid-cols-3 gap-8">
                          {component.props.features.map((feature: any, index: number) => {
                            const IconComponent =
                              feature.icon === "zap"
                                ? Zap
                                : feature.icon === "shield"
                                  ? Shield
                                  : feature.icon === "heart"
                                    ? Heart
                                    : Star

                            return (
                              <div key={index} className="text-center p-6 border rounded-lg shadow-sm">
                                <div
                                  className="w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4"
                                  style={{ backgroundColor: landingPage.styles.primaryColor }}
                                >
                                  <IconComponent className="h-6 w-6 text-white" />
                                </div>
                                <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                                <p className="text-gray-600">{feature.description}</p>
                              </div>
                            )
                          })}
                        </div>
                      </div>
                    </div>
                  )

                case "testimonials":
                  return (
                    <div key={component.id} className="py-16 px-4 bg-gray-50">
                      <div className="max-w-4xl mx-auto text-center">
                        <h2 className="text-3xl font-bold mb-12">{component.props.title}</h2>
                        <div className="grid md:grid-cols-2 gap-8">
                          {component.props.testimonials.map((testimonial: any, index: number) => (
                            <div key={index} className="p-6 bg-white rounded-lg shadow-sm">
                              <div className="flex mb-4">
                                {Array.from({ length: 5 }).map((_, i) => (
                                  <Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                                ))}
                              </div>
                              <p className="text-gray-600 mb-4">"{testimonial.content}"</p>
                              <div className="flex items-center">
                                <div className="w-10 h-10 bg-gray-300 rounded-full mr-3" />
                                <div>
                                  <p className="font-semibold">{testimonial.name}</p>
                                  <p className="text-sm text-gray-500">{testimonial.role}</p>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )

                case "cta":
                  return (
                    <div
                      key={component.id}
                      className="py-16 px-4 text-center"
                      style={{ backgroundColor: landingPage.styles.primaryColor }}
                    >
                      <div className="max-w-2xl mx-auto text-white">
                        <h2 className="text-3xl md:text-4xl font-bold mb-4">{component.props.title}</h2>
                        <p className="text-xl mb-8 opacity-90">{component.props.subtitle}</p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                          <Button size="lg" className="bg-white text-gray-900 hover:bg-gray-100">
                            {component.props.buttonText}
                          </Button>
                          {component.props.secondaryButtonText && (
                            <Button
                              size="lg"
                              variant="outline"
                              className="border-white text-white hover:bg-white hover:text-gray-900"
                            >
                              {component.props.secondaryButtonText}
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  )

                default:
                  return null
              }
            })}
          </div>
        </div>
      </div>

      {/* Toggle controls button */}
      <Button
        variant="secondary"
        size="sm"
        className="fixed bottom-4 right-4 z-50 rounded-full h-10 w-10 p-0 shadow-lg"
        onClick={toggleControls}
      >
        {showControls ? <ArrowLeft className="h-5 w-5" /> : <Pencil className="h-5 w-5" />}
      </Button>
    </div>
  )
}
