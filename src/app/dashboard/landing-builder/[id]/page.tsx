"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { useParams } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  ArrowLeft,
  Eye,
  Save,
  Smartphone,
  Tablet,
  Monitor,
  Undo,
  Redo,
  Play,
  Trash2,
  Plus,
  Move,
  Settings,
  X,
  ChevronUp,
  ChevronDown,
  MessageSquare,
  Star,
  Zap,
  Shield,
  Heart,
  Mail,
  CreditCard,
} from "lucide-react"

interface LandingPageData {
  id: string
  name: string
  status: "published" | "DRAFT"
  components: LandingComponent[]
  styles: {
    primaryColor: string
    secondaryColor: string
    accentColor: string
    fontFamily: string
    spacing: string
  }
}

interface LandingComponent {
  id: string
  type: string
  props: any
}

const mockLandingPage: LandingPageData = {
  id: "1",
  name: "Product Launch Campaign",
  status: "DRAFT",
  components: [
    {
      id: "hero-1",
      type: "hero",
      props: {
        title: "Transform Your Business Today",
        subtitle: "Discover the power of our innovative solution",
        buttonText: "Get Started",
        backgroundImage: "/landing-page-template-1.png",
      },
    },
    {
      id: "features-1",
      type: "features",
      props: {
        title: "Why Choose Us",
        features: [
          { icon: "zap", title: "Fast", description: "Lightning-fast performance" },
          { icon: "shield", title: "Secure", description: "Bank-level security" },
          { icon: "heart", title: "Reliable", description: "99.9% uptime guarantee" },
        ],
      },
    },
  ],
  styles: {
    primaryColor: "#3b82f6",
    secondaryColor: "#64748b",
    accentColor: "#f59e0b",
    fontFamily: "Inter",
    spacing: "normal",
  },
}

const componentCategories = [
  {
    name: "Layout",
    components: [
      {
        type: "hero",
        name: "Hero Section",
        description: "Eye-catching header with title and CTA",
        icon: Zap,
        defaultProps: {
          title: "Your Amazing Headline",
          subtitle: "A compelling subtitle that explains your value proposition",
          buttonText: "Get Started",
          backgroundImage: "/landing-page-template-1.png",
        },
      },
      {
        type: "features",
        name: "Feature Grid",
        description: "Showcase your key features",
        icon: Star,
        defaultProps: {
          title: "Amazing Features",
          subtitle: "Everything you need to succeed",
          features: [
            { icon: "zap", title: "Fast", description: "Lightning-fast performance" },
            { icon: "shield", title: "Secure", description: "Bank-level security" },
            { icon: "heart", title: "Reliable", description: "99.9% uptime guarantee" },
          ],
        },
      },
    ],
  },
  {
    name: "Content",
    components: [
      {
        type: "testimonials",
        name: "Testimonials",
        description: "Customer reviews and social proof",
        icon: MessageSquare,
        defaultProps: {
          title: "What Our Customers Say",
          testimonials: [
            {
              content: "This product has transformed our business completely.",
              name: "John Doe",
              role: "CEO, Company Inc.",
              avatar: "",
            },
            {
              content: "Outstanding service and incredible results.",
              name: "Jane Smith",
              role: "Marketing Director",
              avatar: "",
            },
          ],
        },
      },
      {
        type: "pricing",
        name: "Pricing Table",
        description: "Display your pricing plans",
        icon: CreditCard,
        defaultProps: {
          title: "Choose Your Plan",
          plans: [
            {
              name: "Basic",
              price: "$9",
              period: "month",
              features: ["Feature 1", "Feature 2", "Feature 3"],
            },
            {
              name: "Pro",
              price: "$19",
              period: "month",
              features: ["Everything in Basic", "Feature 4", "Feature 5"],
              popular: true,
            },
          ],
        },
      },
    ],
  },
  {
    name: "Conversion",
    components: [
      {
        type: "cta",
        name: "Call to Action",
        description: "Drive conversions with compelling CTAs",
        icon: Mail,
        defaultProps: {
          title: "Ready to Get Started?",
          subtitle: "Join thousands of satisfied customers today",
          buttonText: "Get Started",
          secondaryButtonText: "Learn More",
        },
      },
      {
        type: "contact",
        name: "Contact Form",
        description: "Capture leads with a contact form",
        icon: Mail,
        defaultProps: {
          title: "Get in Touch",
          subtitle: "We'd love to hear from you",
          fields: ["name", "email", "message"],
        },
      },
    ],
  },
]

const iconMap = {
  zap: Zap,
  shield: Shield,
  heart: Heart,
  star: Star,
  mail: Mail,
  creditCard: CreditCard,
  messageSquare: MessageSquare,
}

export default function LandingBuilderEditor() {
  const params = useParams()
  const [landingPage, setLandingPage] = useState<LandingPageData>(mockLandingPage)
  const [previewMode, setPreviewMode] = useState<"desktop" | "tablet" | "mobile">("desktop")
  const [activeTab, setActiveTab] = useState("editor")
  const [isSaving, setIsSaving] = useState(false)
  const [selectedComponentId, setSelectedComponentId] = useState<string | null>(null)
  const [draggedComponent, setDraggedComponent] = useState<any | null>(null)
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null)
  const [history, setHistory] = useState<LandingPageData[]>([mockLandingPage])
  const [historyIndex, setHistoryIndex] = useState(0)
  const [searchTerm, setSearchTerm] = useState("")

  const editorRef = useRef<HTMLDivElement>(null)

  // Find the selected component
  const selectedComponent = landingPage.components.find((comp) => comp.id === selectedComponentId)

  // Filter components based on search term
  const filteredCategories = componentCategories
    .map((category) => ({
      ...category,
      components: category.components.filter(
        (component) =>
          component.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          component.description.toLowerCase().includes(searchTerm.toLowerCase()),
      ),
    }))
    .filter((category) => category.components.length > 0)

  // Add to history when landingPage changes
  useEffect(() => {
    if (JSON.stringify(landingPage) !== JSON.stringify(history[historyIndex])) {
      const newHistory = history.slice(0, historyIndex + 1)
      newHistory.push(JSON.parse(JSON.stringify(landingPage)))
      setHistory(newHistory)
      setHistoryIndex(newHistory.length - 1)
    }
  }, [landingPage])

  const handleSave = async () => {
    setIsSaving(true)
    // Simulate save operation
    await new Promise((resolve) => setTimeout(resolve, 1000))
    setIsSaving(false)
  }

  const handlePublish = async () => {
    await handleSave()
    setLandingPage((prev) => ({ ...prev, status: "published" }))
  }

  const handleUndo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1)
      setLandingPage(JSON.parse(JSON.stringify(history[historyIndex - 1])))
    }
  }

  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1)
      setLandingPage(JSON.parse(JSON.stringify(history[historyIndex + 1])))
    }
  }

  const getPreviewWidth = () => {
    switch (previewMode) {
      case "mobile":
        return "max-w-sm"
      case "tablet":
        return "max-w-2xl"
      default:
        return "w-full"
    }
  }

  const handleDragStart = (component: any) => {
    setDraggedComponent(component)
  }

  const handleDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault()
    setDragOverIndex(index)
  }

  const handleDrop = (e: React.DragEvent, index: number) => {
    e.preventDefault()

    if (!draggedComponent) return

    // If it's a new component being added
    if (!draggedComponent.id) {
      const newComponent = {
        id: `${draggedComponent.type}-${Date.now()}`,
        type: draggedComponent.type,
        props: { ...draggedComponent.defaultProps },
      }

      const newComponents = [...landingPage.components]
      newComponents.splice(index, 0, newComponent)

      setLandingPage({
        ...landingPage,
        components: newComponents,
      })

      // Select the newly added component
      setSelectedComponentId(newComponent.id)
    }
    // If it's reordering an existing component
    else {
      const currentIndex = landingPage.components.findIndex((c) => c.id === draggedComponent.id)
      if (currentIndex === -1) return

      const newComponents = [...landingPage.components]
      const [movedComponent] = newComponents.splice(currentIndex, 1)

      // Adjust index if moving from above to below
      const adjustedIndex = index > currentIndex ? index - 1 : index
      newComponents.splice(adjustedIndex, 0, movedComponent)

      setLandingPage({
        ...landingPage,
        components: newComponents,
      })
    }

    setDraggedComponent(null)
    setDragOverIndex(null)
  }

  const handleDeleteComponent = (id: string) => {
    setLandingPage({
      ...landingPage,
      components: landingPage.components.filter((c) => c.id !== id),
    })

    if (selectedComponentId === id) {
      setSelectedComponentId(null)
    }
  }

  const handleMoveComponent = (id: string, direction: "up" | "down") => {
    const currentIndex = landingPage.components.findIndex((c) => c.id === id)
    if (currentIndex === -1) return

    if (direction === "up" && currentIndex === 0) return
    if (direction === "down" && currentIndex === landingPage.components.length - 1) return

    const newComponents = [...landingPage.components]
    const [movedComponent] = newComponents.splice(currentIndex, 1)

    const newIndex = direction === "up" ? currentIndex - 1 : currentIndex + 1
    newComponents.splice(newIndex, 0, movedComponent)

    setLandingPage({
      ...landingPage,
      components: newComponents,
    })
  }

  const updateComponentProps = (id: string, newProps: any) => {
    setLandingPage({
      ...landingPage,
      components: landingPage.components.map((component) =>
        component.id === id ? { ...component, props: { ...component.props, ...newProps } } : component,
      ),
    })
  }

  // Render the component editor based on the selected component type
  const renderComponentEditor = () => {
    if (!selectedComponent) {
      return (
        <div className="p-6 text-center text-muted-foreground">
          <Settings className="h-12 w-12 mx-auto mb-4 opacity-20" />
          <h3 className="text-lg font-medium mb-2">No Component Selected</h3>
          <p className="text-sm">
            Click on a component in the editor to customize it, or drag a new component from the library.
          </p>
        </div>
      )
    }

    switch (selectedComponent.type) {
      case "hero":
        return (
          <div className="p-4 space-y-4">
            <h3 className="font-semibold text-lg">Hero Section</h3>

            <div className="space-y-2">
              <Label htmlFor="hero-title">Title</Label>
              <Input
                id="hero-title"
                value={selectedComponent.props.title || ""}
                onChange={(e) => updateComponentProps(selectedComponent.id, { title: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="hero-subtitle">Subtitle</Label>
              <Textarea
                id="hero-subtitle"
                value={selectedComponent.props.subtitle || ""}
                onChange={(e) => updateComponentProps(selectedComponent.id, { subtitle: e.target.value })}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="hero-button">Button Text</Label>
              <Input
                id="hero-button"
                value={selectedComponent.props.buttonText || ""}
                onChange={(e) => updateComponentProps(selectedComponent.id, { buttonText: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="hero-bg">Background Image URL</Label>
              <Input
                id="hero-bg"
                value={selectedComponent.props.backgroundImage || ""}
                onChange={(e) => updateComponentProps(selectedComponent.id, { backgroundImage: e.target.value })}
              />
              <div className="mt-2 flex justify-center">
                {selectedComponent.props.backgroundImage && (
                  <div className="relative w-full h-32 bg-gray-100 rounded overflow-hidden">
                    <img
                      src={selectedComponent.props.backgroundImage || "/placeholder.svg"}
                      alt="Background preview"
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
        )

      case "features":
        return (
          <div className="p-4 space-y-4">
            <h3 className="font-semibold text-lg">Features Section</h3>

            <div className="space-y-2">
              <Label htmlFor="features-title">Title</Label>
              <Input
                id="features-title"
                value={selectedComponent.props.title || ""}
                onChange={(e) => updateComponentProps(selectedComponent.id, { title: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="features-subtitle">Subtitle</Label>
              <Textarea
                id="features-subtitle"
                value={selectedComponent.props.subtitle || ""}
                onChange={(e) => updateComponentProps(selectedComponent.id, { subtitle: e.target.value })}
                rows={2}
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between mb-2">
                <Label>Features</Label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newFeatures = [...(selectedComponent.props.features || [])]
                    newFeatures.push({ icon: "star", title: "New Feature", description: "Description" })
                    updateComponentProps(selectedComponent.id, { features: newFeatures })
                  }}
                >
                  <Plus className="h-3 w-3 mr-1" /> Add Feature
                </Button>
              </div>

              <div className="space-y-3 max-h-[300px] overflow-y-auto pr-2">
                {selectedComponent.props.features?.map((feature: any, index: number) => (
                  <Card key={index} className="p-3">
                    <div className="flex justify-between items-start mb-2">
                      <div className="font-medium text-sm">Feature {index + 1}</div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={() => {
                          const newFeatures = [...selectedComponent.props.features]
                          newFeatures.splice(index, 1)
                          updateComponentProps(selectedComponent.id, { features: newFeatures })
                        }}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>

                    <div className="space-y-2">
                      <div className="grid grid-cols-4 gap-2 items-center">
                        <Label htmlFor={`feature-icon-${index}`} className="text-xs">
                          Icon
                        </Label>
                        <div className="col-span-3">
                          <select
                            id={`feature-icon-${index}`}
                            value={feature.icon}
                            onChange={(e) => {
                              const newFeatures = [...selectedComponent.props.features]
                              newFeatures[index] = { ...feature, icon: e.target.value }
                              updateComponentProps(selectedComponent.id, { features: newFeatures })
                            }}
                            className="w-full p-1 text-xs border rounded"
                          >
                            <option value="zap">Zap</option>
                            <option value="shield">Shield</option>
                            <option value="heart">Heart</option>
                            <option value="star">Star</option>
                          </select>
                        </div>
                      </div>

                      <div className="grid grid-cols-4 gap-2 items-center">
                        <Label htmlFor={`feature-title-${index}`} className="text-xs">
                          Title
                        </Label>
                        <Input
                          id={`feature-title-${index}`}
                          value={feature.title}
                          onChange={(e) => {
                            const newFeatures = [...selectedComponent.props.features]
                            newFeatures[index] = { ...feature, title: e.target.value }
                            updateComponentProps(selectedComponent.id, { features: newFeatures })
                          }}
                          className="col-span-3 h-7 text-xs"
                        />
                      </div>

                      <div className="grid grid-cols-4 gap-2 items-center">
                        <Label htmlFor={`feature-desc-${index}`} className="text-xs">
                          Description
                        </Label>
                        <Textarea
                          id={`feature-desc-${index}`}
                          value={feature.description}
                          onChange={(e) => {
                            const newFeatures = [...selectedComponent.props.features]
                            newFeatures[index] = { ...feature, description: e.target.value }
                            updateComponentProps(selectedComponent.id, { features: newFeatures })
                          }}
                          className="col-span-3 h-16 text-xs min-h-0"
                          rows={2}
                        />
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        )

      case "testimonials":
        return (
          <div className="p-4 space-y-4">
            <h3 className="font-semibold text-lg">Testimonials Section</h3>

            <div className="space-y-2">
              <Label htmlFor="testimonials-title">Title</Label>
              <Input
                id="testimonials-title"
                value={selectedComponent.props.title || ""}
                onChange={(e) => updateComponentProps(selectedComponent.id, { title: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between mb-2">
                <Label>Testimonials</Label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newTestimonials = [...(selectedComponent.props.testimonials || [])]
                    newTestimonials.push({
                      content: "This product is amazing!",
                      name: "New Customer",
                      role: "Position",
                      avatar: "",
                    })
                    updateComponentProps(selectedComponent.id, { testimonials: newTestimonials })
                  }}
                >
                  <Plus className="h-3 w-3 mr-1" /> Add Testimonial
                </Button>
              </div>

              <div className="space-y-3 max-h-[300px] overflow-y-auto pr-2">
                {selectedComponent.props.testimonials?.map((testimonial: any, index: number) => (
                  <Card key={index} className="p-3">
                    <div className="flex justify-between items-start mb-2">
                      <div className="font-medium text-sm">Testimonial {index + 1}</div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={() => {
                          const newTestimonials = [...selectedComponent.props.testimonials]
                          newTestimonials.splice(index, 1)
                          updateComponentProps(selectedComponent.id, { testimonials: newTestimonials })
                        }}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>

                    <div className="space-y-2">
                      <div>
                        <Label htmlFor={`testimonial-content-${index}`} className="text-xs">
                          Content
                        </Label>
                        <Textarea
                          id={`testimonial-content-${index}`}
                          value={testimonial.content}
                          onChange={(e) => {
                            const newTestimonials = [...selectedComponent.props.testimonials]
                            newTestimonials[index] = { ...testimonial, content: e.target.value }
                            updateComponentProps(selectedComponent.id, { testimonials: newTestimonials })
                          }}
                          className="mt-1 text-xs"
                          rows={3}
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <Label htmlFor={`testimonial-name-${index}`} className="text-xs">
                            Name
                          </Label>
                          <Input
                            id={`testimonial-name-${index}`}
                            value={testimonial.name}
                            onChange={(e) => {
                              const newTestimonials = [...selectedComponent.props.testimonials]
                              newTestimonials[index] = { ...testimonial, name: e.target.value }
                              updateComponentProps(selectedComponent.id, { testimonials: newTestimonials })
                            }}
                            className="mt-1 h-7 text-xs"
                          />
                        </div>

                        <div>
                          <Label htmlFor={`testimonial-role-${index}`} className="text-xs">
                            Role
                          </Label>
                          <Input
                            id={`testimonial-role-${index}`}
                            value={testimonial.role}
                            onChange={(e) => {
                              const newTestimonials = [...selectedComponent.props.testimonials]
                              newTestimonials[index] = { ...testimonial, role: e.target.value }
                              updateComponentProps(selectedComponent.id, { testimonials: newTestimonials })
                            }}
                            className="mt-1 h-7 text-xs"
                          />
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        )

      case "cta":
        return (
          <div className="p-4 space-y-4">
            <h3 className="font-semibold text-lg">Call to Action</h3>

            <div className="space-y-2">
              <Label htmlFor="cta-title">Title</Label>
              <Input
                id="cta-title"
                value={selectedComponent.props.title || ""}
                onChange={(e) => updateComponentProps(selectedComponent.id, { title: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="cta-subtitle">Subtitle</Label>
              <Textarea
                id="cta-subtitle"
                value={selectedComponent.props.subtitle || ""}
                onChange={(e) => updateComponentProps(selectedComponent.id, { subtitle: e.target.value })}
                rows={2}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="cta-button">Primary Button Text</Label>
              <Input
                id="cta-button"
                value={selectedComponent.props.buttonText || ""}
                onChange={(e) => updateComponentProps(selectedComponent.id, { buttonText: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="cta-secondary-button">Secondary Button Text (optional)</Label>
              <Input
                id="cta-secondary-button"
                value={selectedComponent.props.secondaryButtonText || ""}
                onChange={(e) => updateComponentProps(selectedComponent.id, { secondaryButtonText: e.target.value })}
                placeholder="Leave empty to hide secondary button"
              />
            </div>
          </div>
        )

      default:
        return (
          <div className="p-4">
            <h3 className="font-semibold text-lg mb-4">Component Properties</h3>
            <p className="text-muted-foreground">This component type doesn't have a specialized editor yet.</p>
          </div>
        )
    }
  }

  // Render a component in the editor
  const renderComponent = (component: LandingComponent, index: number) => {
    const isSelected = component.id === selectedComponentId
    const isOver = dragOverIndex === index

    let content

    switch (component.type) {
      case "hero":
        content = (
          <div
            className="relative min-h-[300px] flex items-center justify-center text-white"
            style={{
              backgroundImage: component.props.backgroundImage
                ? `url(${component.props.backgroundImage})`
                : "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              backgroundSize: "cover",
              backgroundPosition: "center",
            }}
          >
            <div className="absolute inset-0 bg-black/40" />
            <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
              <h1 className="text-3xl md:text-4xl font-bold mb-4">
                {component.props.title || "Your Amazing Headline"}
              </h1>
              <p className="text-xl mb-6 opacity-90">
                {component.props.subtitle || "A compelling subtitle that explains your value proposition"}
              </p>
              <Button
                size="lg"
                className="bg-white text-gray-900 hover:bg-gray-100"
                style={{ backgroundColor: landingPage.styles.accentColor, color: "white" }}
              >
                {component.props.buttonText || "Get Started"}
              </Button>
            </div>
          </div>
        )
        break

      case "features":
        content = (
          <div className="py-12 px-4">
            <div className="max-w-6xl mx-auto">
              <div className="text-center mb-10">
                <h2 className="text-2xl md:text-3xl font-bold mb-3">{component.props.title || "Amazing Features"}</h2>
                {component.props.subtitle && (
                  <p className="text-lg text-gray-600 max-w-2xl mx-auto">{component.props.subtitle}</p>
                )}
              </div>
              <div className="grid md:grid-cols-3 gap-6">
                {(component.props.features || []).map((feature: any, idx: number) => {
                  const IconComponent = iconMap[feature.icon as keyof typeof iconMap] || Star
                  return (
                    <Card key={idx} className="text-center p-5">
                      <CardContent className="pt-5">
                        <div
                          className="w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-3"
                          style={{ backgroundColor: landingPage.styles.primaryColor }}
                        >
                          <IconComponent className="h-5 w-5 text-white" />
                        </div>
                        <h3 className="text-lg font-semibold mb-2">{feature.title}</h3>
                        <p className="text-gray-600 text-sm">{feature.description}</p>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </div>
          </div>
        )
        break

      case "testimonials":
        content = (
          <div className="py-12 px-4 bg-gray-50">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-2xl font-bold mb-10">{component.props.title || "What Our Customers Say"}</h2>
              <div className="grid md:grid-cols-2 gap-6">
                {(component.props.testimonials || []).map((testimonial: any, idx: number) => (
                  <Card key={idx} className="p-5">
                    <CardContent className="pt-5">
                      <div className="flex mb-3">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        ))}
                      </div>
                      <p className="text-gray-600 mb-4 text-sm">"{testimonial.content}"</p>
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-gray-300 rounded-full mr-3" />
                        <div className="text-left">
                          <p className="font-semibold text-sm">{testimonial.name}</p>
                          <p className="text-xs text-gray-500">{testimonial.role}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        )
        break

      case "cta":
        content = (
          <div className="py-12 px-4 text-center" style={{ backgroundColor: landingPage.styles.primaryColor }}>
            <div className="max-w-2xl mx-auto text-white">
              <h2 className="text-2xl md:text-3xl font-bold mb-3">
                {component.props.title || "Ready to Get Started?"}
              </h2>
              <p className="text-lg mb-6 opacity-90">
                {component.props.subtitle || "Join thousands of satisfied customers today"}
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button size="lg" className="bg-white text-gray-900 hover:bg-gray-100">
                  {component.props.buttonText || "Get Started"}
                </Button>
                {component.props.secondaryButtonText && (
                  <Button
                    size="lg"
                    variant="outline"
                    className="border-white text-white hover:bg-white hover:text-gray-900"
                  >
                    {component.props.secondaryButtonText}
                  </Button>
                )}
              </div>
            </div>
          </div>
        )
        break

      default:
        content = (
          <div className="p-6 border-2 border-dashed border-gray-300 text-center">
            <p className="text-gray-500">Unknown component type: {component.type}</p>
          </div>
        )
    }

    return (
      <div key={component.id}>
        {/* Drop zone above component */}
        <div
          className={`h-4 relative ${isOver && dragOverIndex === index ? "bg-blue-200" : ""}`}
          onDragOver={(e) => handleDragOver(e, index)}
          onDrop={(e) => handleDrop(e, index)}
        >
          {isOver && dragOverIndex === index && (
            <div className="absolute inset-0 border-t-2 border-blue-500 z-10"></div>
          )}
        </div>

        {/* Component with controls */}
        <div
          className={`group relative ${isSelected ? "ring-2 ring-blue-500" : "hover:ring-2 hover:ring-blue-200"}`}
          onClick={() => setSelectedComponentId(component.id)}
          draggable
          onDragStart={() => handleDragStart(component)}
        >
          {/* Component controls */}
          <div
            className={`absolute top-2 right-2 z-20 flex gap-1 ${isSelected ? "opacity-100" : "opacity-0 group-hover:opacity-100"} transition-opacity`}
          >
            <Button
              variant="secondary"
              size="sm"
              className="h-7 w-7 p-0 bg-white/90 backdrop-blur-sm"
              onClick={(e) => {
                e.stopPropagation()
                handleMoveComponent(component.id, "up")
              }}
              disabled={index === 0}
            >
              <ChevronUp className="h-4 w-4" />
            </Button>
            <Button
              variant="secondary"
              size="sm"
              className="h-7 w-7 p-0 bg-white/90 backdrop-blur-sm"
              onClick={(e) => {
                e.stopPropagation()
                handleMoveComponent(component.id, "down")
              }}
              disabled={index === landingPage.components.length - 1}
            >
              <ChevronDown className="h-4 w-4" />
            </Button>
            <Button
              variant="secondary"
              size="sm"
              className="h-7 w-7 p-0 bg-white/90 backdrop-blur-sm"
              onClick={(e) => {
                e.stopPropagation()
                handleDeleteComponent(component.id)
              }}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>

          {/* Component content */}
          {content}

          {/* Drag handle */}
          <div
            className={`absolute top-2 left-2 z-20 cursor-move ${isSelected ? "opacity-100" : "opacity-0 group-hover:opacity-100"} transition-opacity`}
          >
            <Button
              variant="secondary"
              size="sm"
              className="h-7 w-7 p-0 bg-white/90 backdrop-blur-sm"
              onMouseDown={(e) => {
                e.stopPropagation()
                // This is just to show the drag handle is clickable
              }}
            >
              <Move className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Drop zone below the last component */}
        {index === landingPage.components.length - 1 && (
          <div
            className={`h-16 relative ${isOver && dragOverIndex === index + 1 ? "bg-blue-200" : ""}`}
            onDragOver={(e) => handleDragOver(e, index + 1)}
            onDrop={(e) => handleDrop(e, index + 1)}
          >
            {isOver && dragOverIndex === index + 1 && (
              <div className="absolute inset-0 border-t-2 border-blue-500 z-10"></div>
            )}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex h-14 items-center px-4">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/dashboard/landing-builder">
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <div>
              <h1 className="font-semibold">{landingPage.name}</h1>
              <div className="flex items-center space-x-2">
                <Badge variant={landingPage.status === "published" ? "default" : "secondary"}>
                  {landingPage.status}
                </Badge>
              </div>
            </div>
          </div>

          <div className="ml-auto flex items-center space-x-2">
            <div className="flex items-center space-x-1 border rounded-md p-1">
              <Button
                variant={previewMode === "desktop" ? "default" : "ghost"}
                size="sm"
                onClick={() => setPreviewMode("desktop")}
              >
                <Monitor className="h-4 w-4" />
              </Button>
              <Button
                variant={previewMode === "tablet" ? "default" : "ghost"}
                size="sm"
                onClick={() => setPreviewMode("tablet")}
              >
                <Tablet className="h-4 w-4" />
              </Button>
              <Button
                variant={previewMode === "mobile" ? "default" : "ghost"}
                size="sm"
                onClick={() => setPreviewMode("mobile")}
              >
                <Smartphone className="h-4 w-4" />
              </Button>
            </div>

            <Separator orientation="vertical" className="h-6" />

            <Button variant="ghost" size="sm" onClick={handleUndo} disabled={historyIndex <= 0}>
              <Undo className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={handleRedo} disabled={historyIndex >= history.length - 1}>
              <Redo className="h-4 w-4" />
            </Button>

            <Separator orientation="vertical" className="h-6" />

            <Button variant="outline" size="sm" asChild>
              <Link href={`/dashboard/landing-builder/${params.id}/preview`}>
                <Eye className="mr-2 h-4 w-4" />
                Preview
              </Link>
            </Button>

            <Button size="sm" onClick={handleSave} disabled={isSaving}>
              <Save className="mr-2 h-4 w-4" />
              {isSaving ? "Saving..." : "Save"}
            </Button>

            <Button size="sm" onClick={handlePublish} disabled={isSaving}>
              <Play className="mr-2 h-4 w-4" />
              Publish
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Sidebar - Component Library */}
        <div className="w-64 border-r bg-muted/30 flex flex-col">
          <div className="p-4 border-b">
            <Input
              placeholder="Search components..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
          </div>

          <ScrollArea className="flex-1">
            <div className="p-4">
              {filteredCategories.map((category) => (
                <div key={category.name} className="mb-6">
                  <h3 className="font-semibold text-sm mb-3">{category.name}</h3>
                  <div className="space-y-2">
                    {category.components.map((component) => (
                      <Card
                        key={component.type}
                        className="cursor-move hover:shadow-md transition-shadow"
                        draggable
                        onDragStart={() => handleDragStart(component)}
                      >
                        <CardContent className="p-3 flex items-center space-x-3">
                          <component.icon className="h-8 w-8 p-1.5 bg-primary/10 text-primary rounded" />
                          <div>
                            <h4 className="text-sm font-medium">{component.name}</h4>
                            <p className="text-xs text-muted-foreground">{component.description}</p>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>

        {/* Center - Editor Canvas */}
        <div className="flex-1 overflow-auto bg-gray-50" ref={editorRef}>
          <div className="p-8 flex justify-center">
            <div className={`${getPreviewWidth()} transition-all duration-300`}>
              <Card className="shadow-lg overflow-hidden">
                <CardContent className="p-0">
                  {landingPage.components.length === 0 ? (
                    <div
                      className="min-h-[400px] flex items-center justify-center text-center p-8"
                      onDragOver={(e) => {
                        e.preventDefault()
                        setDragOverIndex(0)
                      }}
                      onDrop={(e) => handleDrop(e, 0)}
                    >
                      <div>
                        <h3 className="text-lg font-semibold mb-2">Start Building Your Landing Page</h3>
                        <p className="text-gray-600 mb-4">Drag components from the sidebar to get started</p>
                        <Badge variant="outline">Tip: Drag components here to add them</Badge>
                      </div>
                    </div>
                  ) : (
                    <div style={{ fontFamily: landingPage.styles.fontFamily }}>
                      {landingPage.components.map((component, index) => renderComponent(component, index))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        {/* Right Sidebar - Component Editor */}
        <div className="w-80 border-l bg-background">
          <ScrollArea className="h-full">{renderComponentEditor()}</ScrollArea>
        </div>
      </div>
    </div>
  )
}
