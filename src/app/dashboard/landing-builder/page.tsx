"use client"

import { useState } from "react"
import { useSearchParams } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { ComingSoonOverlay } from "@/components/coming-soon-overlay"
import { ConfirmDialog } from "@/components/confirm-dialog"
import { Plus, Search, MoreVertical, Copy, Trash2, Eye, Edit, Users, MousePointer, TrendingUp } from "lucide-react"

interface LandingPage {
  id: string
  name: string
  description: string
  thumbnail: string
  status: "published" | "DRAFT"
  createdAt: string
  updatedAt: string
  analytics: {
    visitors: number
    conversions: number
    conversionRate: number
  }
}

const mockLandingPages: LandingPage[] = [
  {
    id: "1",
    name: "Product Launch Campaign",
    description: "Landing page for our new SaaS product launch",
    thumbnail: "/landing-page-template-1.png",
    status: "published",
    createdAt: "2024-01-15",
    updatedAt: "2024-01-20",
    analytics: {
      visitors: 2847,
      conversions: 142,
      conversionRate: 4.99,
    },
  },
  {
    id: "2",
    name: "Course Enrollment Page",
    description: "Educational course sign-up and enrollment",
    thumbnail: "/landing-page-template-2.png",
    status: "DRAFT",
    createdAt: "2024-01-10",
    updatedAt: "2024-01-18",
    analytics: {
      visitors: 1523,
      conversions: 89,
      conversionRate: 5.84,
    },
  },
  {
    id: "3",
    name: "Newsletter Signup",
    description: "Simple newsletter subscription landing page",
    thumbnail: "/landing-page-builder-preview.png",
    status: "published",
    createdAt: "2024-01-05",
    updatedAt: "2024-01-15",
    analytics: {
      visitors: 4521,
      conversions: 678,
      conversionRate: 15.0,
    },
  },
]

export default function LandingBuilderPage() {
  const searchParams = useSearchParams()
  const accountType = searchParams.get("account_type")
  const [searchTerm, setSearchTerm] = useState("")
  const [deleteId, setDeleteId] = useState<string | null>(null)

  const filteredPages = mockLandingPages.filter(
    (page) =>
      page.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      page.description.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const handleDuplicate = (page: LandingPage) => {
    console.log("Duplicating page:", page.name)
    // Implementation for duplicating page
  }

  const handleDelete = (id: string) => {
    console.log("Deleting page:", id)
    setDeleteId(null)
    // Implementation for deleting page
  }

  return (
    <div className="space-y-6">
      {accountType === "free" && (
        <ComingSoonOverlay
          title="Landing Page Builder"
          description="Create stunning landing pages with our drag-and-drop builder"
          icon={<Plus className="h-8 w-8 text-primary" />}
          features={[
            "Drag-and-drop page builder",
            "Professional templates",
            "Custom domain support",
            "Analytics integration",
            "A/B testing tools",
          ]}
          regularPrice="$29"
          specialPrice="$14.50"
          ctaText="Upgrade to Basic Plan"
          imageSrc="/landing-page-builder-preview.png"
          imageAlt="Landing page builder interface"
        />
      )}

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Landing Pages</h1>
          <p className="text-muted-foreground">Create and manage your marketing landing pages</p>
        </div>
        <Button asChild>
          <Link href="/dashboard/landing-builder/new">
            <Plus className="mr-2 h-4 w-4" />
            Create Landing Page
          </Link>
        </Button>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search landing pages..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      {filteredPages.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-16">
            <div className="text-center">
              <h3 className="text-lg font-semibold">No landing pages found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm ? "Try adjusting your search terms" : "Create your first landing page to get started"}
              </p>
              <Button asChild>
                <Link href="/dashboard/landing-builder/new">
                  <Plus className="mr-2 h-4 w-4" />
                  Create Landing Page
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredPages.map((page) => (
            <Card key={page.id} className="group hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <CardTitle className="text-lg">{page.name}</CardTitle>
                    <CardDescription>{page.description}</CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant={page.status === "published" ? "default" : "secondary"}>{page.status}</Badge>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/dashboard/landing-builder/${page.id}`}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/dashboard/landing-builder/${page.id}/preview`}>
                            <Eye className="mr-2 h-4 w-4" />
                            Preview
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDuplicate(page)}>
                          <Copy className="mr-2 h-4 w-4" />
                          Duplicate
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => setDeleteId(page.id)} className="text-destructive">
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="aspect-video relative rounded-lg overflow-hidden bg-muted">
                  <Image src={page.thumbnail || "/placeholder.svg"} alt={page.name} fill className="object-cover" />
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors" />
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                    <div className="flex space-x-2">
                      <Button size="sm" asChild>
                        <Link href={`/dashboard/landing-builder/${page.id}`}>
                          <Edit className="h-4 w-4" />
                        </Link>
                      </Button>
                      <Button size="sm" variant="secondary" asChild>
                        <Link href={`/dashboard/landing-builder/${page.id}/preview`}>
                          <Eye className="h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div className="text-center">
                    <div className="flex items-center justify-center mb-1">
                      <Users className="h-4 w-4 text-blue-500" />
                    </div>
                    <div className="font-semibold">{page.analytics.visitors.toLocaleString()}</div>
                    <div className="text-muted-foreground">Visitors</div>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center mb-1">
                      <MousePointer className="h-4 w-4 text-green-500" />
                    </div>
                    <div className="font-semibold">{page.analytics.conversions}</div>
                    <div className="text-muted-foreground">Conversions</div>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center mb-1">
                      <TrendingUp className="h-4 w-4 text-purple-500" />
                    </div>
                    <div className="font-semibold">{page.analytics.conversionRate}%</div>
                    <div className="text-muted-foreground">Rate</div>
                  </div>
                </div>

                <div className="text-xs text-muted-foreground">
                  Updated {new Date(page.updatedAt).toLocaleDateString()}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      <ConfirmDialog
        open={!!deleteId}
        onOpenChange={() => setDeleteId(null)}
        title="Delete Landing Page"
        description="Are you sure you want to delete this landing page? This action cannot be undone."
        onConfirm={() => deleteId && handleDelete(deleteId)}
      />
    </div>
  )
}
