"use client"

import { useState, Suspense } from "react"
import Link from "next/link"
import Image from "next/image"
import { usePathname, useRouter } from "next/navigation"
import {
  LayoutDashboard, ClipboardList, Settings, LogOut, Menu, ChevronDownCircle,
  User, Bell, Search, GraduationCap, Presentation, CheckCircle2, Award,
  BookOpen, Layout, Database, FileStack, BarChart, HomeIcon, ArrowDownCircleIcon
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel,
  DropdownMenuSeparator, DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { CommandPalette } from "@/components/command-palette"
import { cn } from "@/src/lib/utils"
import { FooterLoginPage } from "@/components/layout/login/footer"
import {
  Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList,
  BreadcrumbPage, BreadcrumbSeparator
} from "@/components/ui/breadcrumb"
import {
  Tooltip, TooltipContent, TooltipProvider, TooltipTrigger
} from "@/components/ui/tooltip"
import { useLocalization } from "@/src/hooks/useLocalization/client"
import en from "./locales/en.json"
import ja from "./locales/ja.json"
import { DashboardHeader } from "./DashboardHeader"
import { AuthAPI } from "@/src/services/authApi"
import { secureStorage, StorageKeys } from "@/src/utils/SecureStorage"

interface SidebarNavProps {
  isCollapsed: boolean
  links: {
    title: string
    label?: string
    icon: React.ReactNode
    variant: string
    href: string
  }[]
}

function SidebarNav({ links, isCollapsed }: SidebarNavProps) {
  const pathname = usePathname()

  return (
    <div data-collapsed={isCollapsed} className="group flex flex-col overflow-y-auto">
      <nav className="grid gap-1 group-[[data-collapsed=true]]:justify-center">
        {links.map((link, index) => {
          const isActive = pathname === link.href
          return (
            <TooltipProvider key={index} delayDuration={100}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link
                    href={link.href}
                    className={cn(
                      `flex items-center justify-between rounded-full text-sm font-medium relative`,
                      isCollapsed ? "py-3 px-3 justify-center" : "py-4 px-5",
                      isActive
                        ? "bg-sp-secondary-1 text-sp-neutral-100"
                        : "text-sp-neutral-600 hover:bg-muted hover:text-foreground"
                    )}
                  >
                    <div className="flex flex-row items-center gap-4">
                      {link.icon}
                      {!isCollapsed && <span className="line-clamp-1">{link.title}</span>}
                    </div>
                    {link.label && (
                      <span className={cn(
                        "rounded-full px-2 py-0.5 text-xs line-clamp-1",
                        isActive
                          ? "bg-sp-neutral-100/10 text-sp-neutral-100"
                          : "bg-primary/10 text-primary"
                      )}>
                        {link.label}
                      </span>
                    )}
                  </Link>
                </TooltipTrigger>
                <TooltipContent side="right" className={cn("ml-2", isCollapsed ? "" : "opacity-0")}>
                  <p>{link.title}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )
        })}
      </nav>
    </div>
  )
}

export default function DashboardLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const { t } = useLocalization("dashboard-layout", { en, ja })
  const [isCollapsed, setIsCollapsed] = useState(false)
  const router = useRouter()

  const links = [
    { title: t("dashboard"), icon: <LayoutDashboard className="h-5 w-5" />, variant: "default", href: "/dashboard" },
    { title: t("analytics"), icon: <BarChart className="h-5 w-5" />, variant: "ghost", href: "/dashboard/analytics", label: "Basic" },
    { title: t("questionBanks"), icon: <Database className="h-5 w-5" />, variant: "ghost", href: "/dashboard/question-banks" },
    { title: t("testBuilder"), icon: <FileStack className="h-5 w-5" />, variant: "ghost", href: "/dashboard/test-builder" },
    { title: t("myTests"), icon: <ClipboardList className="h-5 w-5" />, variant: "ghost", href: "/dashboard/my-tests", label: "8" },
    { title: t("notifications"), icon: <Bell className="h-5 w-5" />, variant: "ghost", href: "/dashboard/notifications", label: "3" },
    { title: t("landingBuilder"), icon: <Layout className="h-5 w-5" />, variant: "ghost", href: "/dashboard/landing-builder", label: "New" },
    { title: t("myCourses"), icon: <GraduationCap className="h-5 w-5" />, variant: "ghost", href: "/dashboard/my-courses" },
    { title: t("createdCourses"), icon: <Presentation className="h-5 w-5" />, variant: "ghost", href: "/dashboard/created-courses" },
    { title: t("settings"), icon: <Settings className="h-5 w-5" />, variant: "ghost", href: "/dashboard/settings/appearance" }
  ]

  const performLogout = async () => {
    try {
      await AuthAPI.Logout().request()
    } catch (error) {
      console.error("Failed to logout:", error)
    } finally {
      secureStorage.removeItem(StorageKeys.UserToken)
      secureStorage.removeItem(StorageKeys.RefreshToken)
      secureStorage.removeItem(StorageKeys.CookieToken)
      router.push("/tests")
    }
  }

  const handleLogout = async () => {
    await performLogout();
  }

  return (
    <Suspense fallback={null}>
      <div className="flex flex-col w-full bg-muted/40 gap-3 bg-sp-background min-h-screen">
        <div className="flex">
          {/* Sidebar */}
          <div className="hidden md:flex py-6 pl-6 h-screen sticky top-0">
            <div className={`flex flex-col bg-sp-neutral-100 rounded-[20px] shadow-xl gap-[30px] px-4 py-6 ${isCollapsed ? "w-14" : "w-60"} transition-width duration-200`}>
              <div className={cn("flex items-center", isCollapsed ? "justify-center" : "justify-between")}>
                {!isCollapsed && (
                  <Link href="/dashboard" className="flex items-center gap-2 font-semibold">
                    <Image src="/images/logo.png" alt="Logo" width={82} height={82} />
                  </Link>
                )}
                <button onClick={() => setIsCollapsed(!isCollapsed)} className="text-sp-neutral-600">
                  <ChevronDownCircle className={cn("h-5 w-5", isCollapsed ? '-rotate-90' : 'rotate-90')} />
                </button>
              </div>
              <SidebarNav links={links} isCollapsed={isCollapsed} />
            </div>
          </div>

          {/* Main content */}
          <div className="flex flex-col flex-1 gap-4 pt-6">
            {/* Header */}
            <DashboardHeader handleLogout={handleLogout} />

            <main className="flex-1 px-6">{children}</main>
            <div className="px-6">
              <FooterLoginPage />
            </div>
          </div>
          <CommandPalette />
        </div>
      </div>
    </Suspense>
  )
}
