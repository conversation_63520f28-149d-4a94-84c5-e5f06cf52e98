"use client"

import { useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import { SummaryCards } from "@/components/layout/analytics/Summary"
import Detailed from "@/components/layout/analytics/Detailed"
import UpgradeOverlay from "@/components/layout/analytics/UpgradeOverlay"
import { StatsItem } from "@/src/lib/repositories/analytics/summary/AnalyticsSummaryRepository"
import { AnalyticsAPI } from "@/src/services/analyticsApi"
import HeaderAnalytics from "@/components/layout/analytics/HeaderAnalytics"

export default function AnalyticsPage() {
  const searchParams = useSearchParams()
  const accountType = searchParams.get("account_type")
  const [timeRange, setTimeRange] = useState("30days")
  const [showUpgradeOverlay, setShowUpgradeOverlay] = useState(false)
  const [summaryAnalytics, setSummaryAnalytics] = useState<StatsItem[]>([])
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (accountType === "free") {
      setShowUpgradeOverlay(true)
    }
  }, [accountType])

  const fetchTests = async () => {
    try {
      const data = await AnalyticsAPI.AnalyticsSummary({ filters: {}, page: 1 }).request();
      setSummaryAnalytics(data);
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false)
    }
  };

  useEffect(() => {
    fetchTests();
  }, [timeRange]);

  return (
    <div className="relative">
      {/* Main analytics content */}
      <div className={showUpgradeOverlay ? "blur-sm pointer-events-none" : ""}>
        <HeaderAnalytics setTimeRange={setTimeRange} timeRange={timeRange} />

        {/* Summary cards */}
        <SummaryCards data={summaryAnalytics} timeRange={timeRange} />

        {/* Detailed analytics */}
        <Detailed />
      </div>

      {/* Upgrade overlay for free users */}
      <UpgradeOverlay show={showUpgradeOverlay} />
    </div>
  )
}
