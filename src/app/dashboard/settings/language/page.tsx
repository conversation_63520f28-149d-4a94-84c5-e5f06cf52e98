"use client"

import { useState, useEffect } from "react"
import { Globe, Check } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import { SettingsAPI } from "@/src/services/settingsApi"
import { UserSettings } from "@/src/lib/repositories/settings/SettingsRepository"

export default function LanguagePage() {
  const [settings, setSettings] = useState<UserSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const { toast } = useToast()

  // Get current user ID (in real app, this would come from auth context)
  const userId = "default-user"

  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      setLoading(true)
      const data = await SettingsAPI.UserSettings(userId).request()
      setSettings(data)
    } catch (error) {
      console.error("Failed to fetch settings:", error)
      toast({
        title: "Error",
        description: "Failed to load language settings",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const updateLanguageSettings = async (updates: Partial<UserSettings>) => {
    if (!settings) return

    try {
      setSaving(true)
      const updatedSettings = await SettingsAPI.UpdateLanguageSettings(userId, updates).request()
      setSettings(updatedSettings)
      toast({
        title: "Success",
        description: "Language settings updated successfully",
      })
    } catch (error) {
      console.error("Failed to update settings:", error)
      toast({
        title: "Error",
        description: "Failed to update language settings",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  if (loading || !settings) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col gap-2">
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Globe className="h-8 w-8" />
            Language & Region
          </h1>
          <p className="text-muted-foreground">
            {loading ? "Loading..." : "Failed to load settings"}
          </p>
        </div>
      </div>
    )
  }

  const languages = [
    {
      code: "en",
      name: "English",
      nativeName: "English",
      flag: "🇺🇸",
      available: true
    },
    {
      code: "es",
      name: "Spanish",
      nativeName: "Español",
      flag: "🇪🇸",
      available: true
    },
    {
      code: "fr",
      name: "French",
      nativeName: "Français",
      flag: "🇫🇷",
      available: false
    },
    {
      code: "de",
      name: "German",
      nativeName: "Deutsch",
      flag: "🇩🇪",
      available: false
    },
    {
      code: "ja",
      name: "Japanese",
      nativeName: "日本語",
      flag: "🇯🇵",
      available: false
    },
    {
      code: "zh",
      name: "Chinese",
      nativeName: "中文",
      flag: "🇨🇳",
      available: false
    }
  ]

  const regions = [
    { code: "US", name: "United States" },
    { code: "GB", name: "United Kingdom" },
    { code: "CA", name: "Canada" },
    { code: "AU", name: "Australia" },
    { code: "ES", name: "Spain" },
    { code: "MX", name: "Mexico" },
  ]

  const dateFormats = [
    { value: "MM/DD/YYYY", label: "MM/DD/YYYY", example: "12/31/2023" },
    { value: "DD/MM/YYYY", label: "DD/MM/YYYY", example: "31/12/2023" },
    { value: "YYYY-MM-DD", label: "YYYY-MM-DD", example: "2023-12-31" },
    { value: "DD MMM YYYY", label: "DD MMM YYYY", example: "31 Dec 2023" },
  ]

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold flex items-center gap-2">
          <Globe className="h-8 w-8" />
          Language & Region
        </h1>
        <p className="text-muted-foreground">
          Set your preferred language and regional formatting options.
        </p>
      </div>

      <div className="grid gap-6">
        {/* Language Selection */}
        <Card>
          <CardHeader>
            <CardTitle>Language</CardTitle>
            <CardDescription>
              Choose your preferred language for the dashboard interface.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <RadioGroup
              value={settings.language}
              onValueChange={(value) => updateLanguageSettings({ language: value })}
              className="space-y-3"
              disabled={saving}
            >
              {languages.map((lang) => (
                <div key={lang.code} className="flex items-center space-x-3">
                  <RadioGroupItem
                    value={lang.code}
                    id={lang.code}
                    disabled={!lang.available}
                  />
                  <Label
                    htmlFor={lang.code}
                    className={`flex items-center gap-3 flex-1 cursor-pointer ${
                      !lang.available ? "opacity-50" : ""
                    }`}
                  >
                    <span className="text-2xl">{lang.flag}</span>
                    <div className="flex-1">
                      <div className="font-medium">{lang.name}</div>
                      <div className="text-sm text-muted-foreground">{lang.nativeName}</div>
                    </div>
                    {lang.available ? (
                      settings.language === lang.code && <Check className="h-4 w-4 text-primary" />
                    ) : (
                      <Badge variant="secondary">Coming Soon</Badge>
                    )}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </CardContent>
        </Card>

        {/* Region Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Region</CardTitle>
            <CardDescription>
              Select your region for localized content and formatting.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="region">Region</Label>
              <Select
                value={settings.region}
                onValueChange={(value) => updateLanguageSettings({ region: value })}
                disabled={saving}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select your region" />
                </SelectTrigger>
                <SelectContent>
                  {regions.map((regionOption) => (
                    <SelectItem key={regionOption.code} value={regionOption.code}>
                      {regionOption.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Format Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Format Settings</CardTitle>
            <CardDescription>
              Customize how dates, times, and numbers are displayed.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="date-format">Date Format</Label>
              <Select
                value={settings.dateFormat}
                onValueChange={(value) => updateLanguageSettings({ dateFormat: value })}
                disabled={saving}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select date format" />
                </SelectTrigger>
                <SelectContent>
                  {dateFormats.map((format) => (
                    <SelectItem key={format.value} value={format.value}>
                      <div className="flex items-center justify-between w-full">
                        <span>{format.label}</span>
                        <span className="text-muted-foreground ml-4">{format.example}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="time-format">Time Format</Label>
              <Select
                value={settings.timeFormat}
                onValueChange={(value: "12" | "24") => updateLanguageSettings({ timeFormat: value })}
                disabled={saving}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select time format" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="12">12-hour (2:30 PM)</SelectItem>
                  <SelectItem value="24">24-hour (14:30)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Translation Status */}
        <Card>
          <CardHeader>
            <CardTitle>Translation Status</CardTitle>
            <CardDescription>
              Current translation progress for different languages.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span>🇺🇸</span>
                  <span className="font-medium">English</span>
                </div>
                <Badge variant="secondary" className="bg-green-500/10 text-green-500">
                  100% Complete
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span>🇪🇸</span>
                  <span className="font-medium">Spanish</span>
                </div>
                <Badge variant="secondary" className="bg-blue-500/10 text-blue-500">
                  85% Complete
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span>🇫🇷</span>
                  <span className="font-medium">French</span>
                </div>
                <Badge variant="secondary">
                  Coming Soon
                </Badge>
              </div>
            </div>

            <Separator />

            <div className="text-sm text-muted-foreground">
              Want to help translate the dashboard into your language? 
              <Button variant="link" className="p-0 h-auto font-normal">
                Contact us
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Save Changes */}
        <div className="flex justify-end">
          <Button onClick={fetchSettings} disabled={saving}>
            {saving ? "Saving..." : "Refresh Settings"}
          </Button>
        </div>
      </div>
    </div>
  )
}
