"use client"

import { useState, useEffect } from "react"
import { Shield, Key, Smartphone, AlertTriangle } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import { SettingsAPI } from "@/src/services/settingsApi"
import { UserSettings } from "@/src/lib/repositories/settings/SettingsRepository"
import { AuthAPI } from "@/src/services/authApi"

export default function SecurityPage() {
  const [settings, setSettings] = useState<UserSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [currentPassword, setCurrentPassword] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const { toast } = useToast()

  // Get current user ID (in real app, this would come from auth context)
  const userId = "default-user"

  useEffect(() => {
    fetchSettings()
  }, [])

  const validatePassword = () => {
    if (!currentPassword || !newPassword || !confirmPassword) {
      return "All fields are required"
    }
    if (newPassword.length < 8) {
      return "New password must be at least 8 characters"
    }
    if (newPassword !== confirmPassword) {
      return "Passwords do not match"
    }
    return null
  }

  const handlePasswordUpdate = async () => {
    const validationError = validatePassword()
    if (validationError) {
      toast({
        title: "Error",
        description: validationError,
        variant: "destructive",
      })
      return
    }

    try {
      setSaving(true)
      await AuthAPI.ChangePassword({ currentPassword, newPassword }).request()
      toast({
        title: "Success",
        description: "Password updated successfully",
      })
    } catch (error) {
      console.error("Failed to update password:", error)
      toast({
        title: "Error",
        description: "Failed to update password",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  const fetchSettings = async () => {
    try {
      setLoading(true)
      const data = await SettingsAPI.UserSettings(userId).request()
      setSettings(data)
    } catch (error) {
      console.error("Failed to fetch settings:", error)
      toast({
        title: "Error",
        description: "Failed to load security settings",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const updateSecuritySettings = async (updates: Partial<UserSettings>) => {
    if (!settings) return

    try {
      setSaving(true)
      const updatedSettings = await SettingsAPI.UpdateSecuritySettings(userId, updates).request()
      setSettings(updatedSettings)
      toast({
        title: "Success",
        description: "Security settings updated successfully",
      })
    } catch (error) {
      console.error("Failed to update settings:", error)
      toast({
        title: "Error",
        description: "Failed to update security settings",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  if (loading || !settings) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col gap-2">
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Shield className="h-8 w-8" />
            Security
          </h1>
          <p className="text-muted-foreground">
            {loading ? "Loading..." : "Failed to load settings"}
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold flex items-center gap-2">
          <Shield className="h-8 w-8" />
          Security
        </h1>
        <p className="text-muted-foreground">
          Manage your account security settings and authentication methods.
        </p>
      </div>

      <div className="grid gap-6">
        {/* Password */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Key className="h-5 w-5" />
              Password
            </CardTitle>
            <CardDescription>
              Update your password to keep your account secure.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="current-password">Current Password</Label>
              <Input
                id="current-password"
                type="password"
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
                placeholder="Enter your current password"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="new-password">New Password</Label>
              <Input
                id="new-password"
                type="password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                placeholder="Enter your new password"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirm-password">Confirm New Password</Label>
              <Input
                id="confirm-password"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="Confirm your new password"
              />
            </div>

            <Button onClick={handlePasswordUpdate} disabled={saving}>Update Password</Button>
          </CardContent>
        </Card>

        {/* Two-Factor Authentication */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Smartphone className="h-5 w-5" />
              Two-Factor Authentication
              {settings.twoFactorEnabled && (
                <Badge variant="secondary" className="bg-green-500/10 text-green-500">
                  Enabled
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              Add an extra layer of security to your account with two-factor authentication.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="two-factor">Enable Two-Factor Authentication</Label>
                <div className="text-sm text-muted-foreground">
                  Use an authenticator app to generate verification codes
                </div>
              </div>
              <Switch
                id="two-factor"
                checked={settings.twoFactorEnabled}
                onCheckedChange={(checked) => updateSecuritySettings({ twoFactorEnabled: checked })}
                disabled={saving}
              />
            </div>

            {settings.twoFactorEnabled && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Two-factor authentication is enabled. You'll need your authenticator app to sign in.
                </AlertDescription>
              </Alert>
            )}

            {!settings.twoFactorEnabled && (
              <div className="space-y-4">
                <Separator />
                <div className="text-sm text-muted-foreground">
                  <p className="mb-2">To enable two-factor authentication:</p>
                  <ol className="list-decimal list-inside space-y-1">
                    <li>Download an authenticator app like Google Authenticator or Authy</li>
                    <li>Click "Enable Two-Factor Authentication" above</li>
                    <li>Scan the QR code with your authenticator app</li>
                    <li>Enter the verification code to confirm setup</li>
                  </ol>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Active Sessions */}
        <Card>
          <CardHeader>
            <CardTitle>Active Sessions</CardTitle>
            <CardDescription>
              Manage devices and browsers where you're currently signed in.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <div className="font-medium">Current Session</div>
                  <div className="text-sm text-muted-foreground">
                    Chrome on Windows • Last active: Now
                  </div>
                </div>
                <Badge variant="secondary">Current</Badge>
              </div>

              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <div className="font-medium">Mobile Device</div>
                  <div className="text-sm text-muted-foreground">
                    Safari on iPhone • Last active: 2 hours ago
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  Sign Out
                </Button>
              </div>
            </div>

            <Separator />

            <Button variant="outline" className="w-full">
              Sign Out All Other Sessions
            </Button>
          </CardContent>
        </Card>

        {/* Account Deletion */}
        <Card className="border-destructive/20">
          <CardHeader>
            <CardTitle className="text-destructive">Danger Zone</CardTitle>
            <CardDescription>
              Irreversible actions that will affect your account.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Alert className="border-destructive/20">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Deleting your account will permanently remove all your data, including courses, tests, and collaborations. This action cannot be undone.
              </AlertDescription>
            </Alert>

            <div className="mt-4">
              <Button variant="destructive" className="w-full">
                Delete Account
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
