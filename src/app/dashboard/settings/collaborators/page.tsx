"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON>, UserPlus } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

import { useLocalization } from "@/src/localization/functions/client"
import en from "../locales/en.json"
import { CollaboratorUser, Invitation, RoleType } from "@/src/lib/repositories/collaborators/CollaboratorsRepository"
import { CollaboratorsAPI } from "@/src/services/collaboratorsApi"
import TabsCollaborators from "@/components/layout/collaborators/TabsCollaborators"
import TabsInvitations from "@/components/layout/collaborators/TabsInvitations"

export default function CollaboratorsPage() {
  const [collaborators, setCollaborators] = useState<CollaboratorUser[]>([])
  const [invitations, setInvitations] = useState<Invitation[]>([])
  const [activeTab, setActiveTab] = useState<string | "collaborators" | "invitations">("collaborators")
  const [newInviteEmail, setNewInviteEmail] = useState("")
  const [newInviteRole, setNewInviteRole] = useState<RoleType>("team")
  const [inviteDialogOpen, setInviteDialogOpen] = useState(false)
  const [generatedLink, setGeneratedLink] = useState("")
  const [linkCopied, setLinkCopied] = useState(false)
  const [loading, setLoading] = useState(true);
  const { t } = useLocalization("public-test", { en });

  const fetchDataCollaborators = async () => {
    try {
      const data = await CollaboratorsAPI.Collaborators({page: 1}).request();
      setCollaborators(data);
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false)
    }
  };

  const fetchDataCollaboratorInvitations = async () => {
    try {
      const data = await CollaboratorsAPI.Invitations({page: 1}).request();
      setInvitations(data);
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false)
    }
  };

  useEffect(() => {
    if (!activeTab) return;
    if (activeTab == 'collaborators') fetchDataCollaborators();
    else if (activeTab == 'invitations') fetchDataCollaboratorInvitations()
  }, [activeTab]);

  const generateInvitationLink = async () => {
    try {
      setLoading(true)
      if (!newInviteEmail || !newInviteRole) return

      const code = `${newInviteRole.toUpperCase()}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`

      // Add to invitations
      const newInvitation: Invitation = {
        id: `inv-${Date.now()}`,
        email: newInviteEmail,
        role: newInviteRole,
        status: "pending",
        expiresAt: new Date(), // 14 days from now
        sentAt: new Date(),
        code,
      }
      const data = await CollaboratorsAPI.CreateInvitation(newInvitation).request();
      const link = `${window.location.origin}/invitation?code=${code}`
      setGeneratedLink(link)
      setInvitations([...invitations, data])
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false)
    }
  }

  const copyToClipboard = () => {
    navigator.clipboard.writeText(generatedLink)
    setLinkCopied(true)
    setTimeout(() => setLinkCopied(false), 2000)
  }

  const resendInvitation = async (invitation: Invitation) => {
    try {
      console.log(`Resending invitation ${invitation.id}`)
      setLoading(true)
      if (!invitation.id) return

      const code = `${newInviteRole.toUpperCase()}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`

      // Add to invitations
      const newInvitation: Invitation = {
        ...invitation,
        expiresAt: new Date(),
        sentAt: new Date(),
        code,
      }

      const data = await CollaboratorsAPI.UpdateInvitation(invitation.id, newInvitation).request();
      setInvitations(invitations.map((val) => val.id === invitation.id ? data : val))
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false)
    }
  }

  const cancelInvitation = async (invitationId: string) => {
    try {
      setLoading(true)
      if (!invitationId) return

      await CollaboratorsAPI.DeleteInvitation(invitationId).request();
      setInvitations(invitations.filter((invitation) => invitation.id !== invitationId))
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false)
    }
  }

  const removeCollaborator = async (collaboratorId: string) => {
    try {
      setLoading(true)
      if (!collaboratorId) return

      await CollaboratorsAPI.DeleteCollaborator(collaboratorId).request();
      setCollaborators(collaborators.filter((collaborator) => collaborator.id !== collaboratorId))
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false)
    }
  }

  const changeCollaboratorRole = async (collaborator: CollaboratorUser) => {
    try {
      setLoading(true)
      if (!collaborator.id) return

      const newRole = collaborator.role == 'external' ? 'team' : 'external'

      // Add to invitations
      const newCollaborator: CollaboratorUser = {
        ...collaborator,
        role: newRole
      }

      const data = await CollaboratorsAPI.UpdateCollaborator(collaborator.id, newCollaborator).request();
      setCollaborators(
        collaborators.map((val) =>
          val.id === collaborator.id ? data : val,
        ),
      )
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false)
    }
  }

  const getRoleBadge = (role: string) => {
    switch (role) {
      case "admin":
        return <Badge className="bg-primary">Admin</Badge>
      case "team":
        return <Badge variant="secondary">Team</Badge>
      case "external":
        return <Badge variant="outline">External</Badge>
      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-6">
        <div className="flex flex-col gap-2">
          <h1 className="text-3xl font-bold">{t("collaborator_management.title")}</h1>
          <p className="text-muted-foreground">{t("collaborator_management.description")}</p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="flex items-center justify-between">
            <TabsList>
              <TabsTrigger value="collaborators">
                {t("tabs.collaborators")}
              </TabsTrigger>
              <TabsTrigger value="invitations">
                {t("tabs.pendingInvitations")}
              </TabsTrigger>
            </TabsList>

            <Dialog open={inviteDialogOpen} onOpenChange={setInviteDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <UserPlus className="mr-2 h-4 w-4" />
                  {t("buttons.inviteCollaborator")}
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>{t("dialog.title")}</DialogTitle>
                  <DialogDescription>
                    {t("dialog.description")}
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid gap-2">
                    <label htmlFor="email" className="text-sm font-medium">
                      {t("form.email")}
                    </label>
                    <Input
                      id="email"
                      type="email"
                      placeholder={t("form.emailPlaceholder")}
                      value={newInviteEmail}
                      onChange={(e) => setNewInviteEmail(e.target.value)}
                    />
                  </div>
                  <div className="grid gap-2">
                    <label htmlFor="role" className="text-sm font-medium">
                      {t("form.role")}
                    </label>
                    <Select value={newInviteRole} onValueChange={(props: RoleType) => setNewInviteRole(props)}>
                      <SelectTrigger id="role">
                        <SelectValue placeholder={t("form.rolePlaceholder")} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="team">{t("roles.team")}</SelectItem>
                        <SelectItem value="external">{t("roles.external")}</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-muted-foreground">
                      {newInviteRole === "team"
                        ? t("roleDescriptions.team")
                        : t("roleDescriptions.external")}
                    </p>
                  </div>

                  {generatedLink && (
                    <div className="mt-2 space-y-2">
                      <label className="text-sm font-medium">
                        {t("form.invitationLink")}
                      </label>
                      <div className="flex items-center gap-2">
                        <Input value={generatedLink} readOnly className="flex-1" />
                        <TooltipProvider>
                          <Tooltip open={linkCopied}>
                            <TooltipTrigger asChild>
                              <Button size="icon" variant="outline" onClick={copyToClipboard}>
                                <Copy className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{linkCopied ? t("tooltip.copied") : t("tooltip.copy")}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {t("form.linkExpiration")}
                      </p>
                    </div>
                  )}
                </div>
                <DialogFooter>
                  {!generatedLink ? (
                    <Button onClick={generateInvitationLink} disabled={!newInviteEmail || !newInviteRole}>
                      {t("buttons.generateLink")}
                    </Button>
                  ) : (
                    <Button onClick={() => {
                      setGeneratedLink("")
                      setInviteDialogOpen(false)
                    }}>
                      {t("buttons.done")}
                    </Button>
                  )}
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          <TabsContent value="collaborators" className="mt-6">
            <TabsCollaborators collaborators={collaborators} getRoleBadge={getRoleBadge} changeCollaboratorRole={changeCollaboratorRole} removeCollaborator={removeCollaborator} />
          </TabsContent>

          <TabsContent value="invitations" className="mt-6">
            <TabsInvitations invitations={invitations} getRoleBadge={getRoleBadge} cancelInvitation={cancelInvitation} resendInvitation={resendInvitation} handleNewInvitation={() => {
              setActiveTab("collaborators")
              setInviteDialogOpen(true)
            }} />
          </TabsContent>
        </Tabs>

        <Card>
          <CardHeader>
            <CardTitle>{t("permissions.title")}</CardTitle>
            <CardDescription>{t("permissions.description")}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-3">
              {/* Admin */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Badge className="bg-primary">{t("admin.name")}</Badge>
                  <span className="font-medium">{t("admin.you")}</span>
                </div>
                <ul className="text-sm space-y-1 list-disc list-inside text-muted-foreground">
                  <li>{t("admin_abilities.create")}</li>
                  <li>{t("admin_abilities.edit")}</li>
                  <li>{t("admin_abilities.delete")}</li>
                  <li>{t("admin_abilities.manage")}</li>
                  <li>{t("admin_abilities.fullAccess")}</li>
                </ul>
              </div>

              {/* Team Member */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">{t("team.name")}</Badge>
                </div>
                <ul className="text-sm space-y-1 list-disc list-inside text-muted-foreground">
                  <li>{t("team_abilities.view")}</li>
                  <li>{t("team_abilities.edit")}</li>
                  <li>{t("team_abilities.noDelete")}</li>
                  <li>{t("team_abilities.comment")}</li>
                  <li>{t("team_abilities.limitedAdmin")}</li>
                </ul>
              </div>

              {/* External Collaborator */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Badge variant="outline">{t("external.name")}</Badge>
                </div>
                <ul className="text-sm space-y-1 list-disc list-inside text-muted-foreground">
                  <li>{t("external_abilities.viewShared")}</li>
                  <li>{t("external_abilities.noEdit")}</li>
                  <li>{t("external_abilities.comment")}</li>
                  <li>{t("external_abilities.noAdmin")}</li>
                  <li>{t("external_abilities.limitedScope")}</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card> 
      </div>
    </div>
  )
}
