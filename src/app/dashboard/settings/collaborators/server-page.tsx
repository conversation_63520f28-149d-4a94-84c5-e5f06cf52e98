import { repo } from "@/src/lib/repositories/collaborators";
import { CollaboratorUser, Invitation } from "@/src/lib/repositories/collaborators/CollaboratorsRepository";
import { getCurrentUser } from "@/src/lib/auth/server-auth";
import CollaboratorsClientPage from "./client-page";

// Server Component to fetch data
export default async function CollaboratorsServerPage() {
  try {
    // Get current user for invitation filtering
    const currentUser = await getCurrentUser();
    const userEmail = currentUser?.email || "";
    const userId = currentUser?.id || "";

    // Fetch collaborators and invitations on the server
    const [collaborators, outgoingInvitations, incomingInvitations] = await Promise.all([
      repo.getAll({ page: 1, pageSize: 50 }),
      repo.getOutgoingInvitations(userId, { page: 1, pageSize: 50 }),
      repo.getIncomingInvitations(userEmail, { page: 1, pageSize: 50 })
    ]);

    // Pass the data to the client component
    return (
      <CollaboratorsClientPage
        initialCollaborators={collaborators}
        initialOutgoingInvitations={outgoingInvitations}
        initialIncomingInvitations={incomingInvitations}
        currentUser={currentUser}
      />
    );
  } catch (error) {
    console.error("Failed to fetch collaborators data:", error);

    // Return client component with empty data on error
    return (
      <CollaboratorsClientPage
        initialCollaborators={[]}
        initialOutgoingInvitations={[]}
        initialIncomingInvitations={[]}
        currentUser={null}
      />
    );
  }
}
