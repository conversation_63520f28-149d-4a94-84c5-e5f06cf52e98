"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Copy, Check, UserPlus, Users, Mail } from "lucide-react"

import { useLocalization } from "@/src/localization/functions/client"
import en from "../locales/en.json"
import { CollaboratorUser, Invitation, RoleType } from "@/src/lib/repositories/collaborators/CollaboratorsRepository"
import { User } from "@/src/lib/types/base"
import { CollaboratorsAPI } from "@/src/services/collaboratorsApi"
import TabsCollaborators from "@/components/layout/collaborators/TabsCollaborators"
import TabsInvitations from "@/components/layout/collaborators/TabsInvitations"

interface CollaboratorsClientPageProps {
  initialCollaborators: CollaboratorUser[];
  initialOutgoingInvitations: Invitation[];
  initialIncomingInvitations: Invitation[];
  currentUser: User | null;
}

export default function CollaboratorsClientPage({
  initialCollaborators,
  initialOutgoingInvitations,
  initialIncomingInvitations,
  currentUser
}: CollaboratorsClientPageProps) {
  const [collaborators, setCollaborators] = useState<CollaboratorUser[]>(initialCollaborators)
  const [outgoingInvitations, setOutgoingInvitations] = useState<Invitation[]>(initialOutgoingInvitations)
  const [incomingInvitations, setIncomingInvitations] = useState<Invitation[]>(initialIncomingInvitations)
  const [activeTab, setActiveTab] = useState<string | "collaborators" | "invitations-out" | "invitations-in">("collaborators")
  const [newInviteEmail, setNewInviteEmail] = useState("")
  const [newInviteRole, setNewInviteRole] = useState<RoleType>("team")
  const [inviteDialogOpen, setInviteDialogOpen] = useState(false)
  const [generatedLink, setGeneratedLink] = useState("")
  const [linkCopied, setLinkCopied] = useState(false)
  const [loading, setLoading] = useState(false);
  const { t } = useLocalization("public-test", { en });

  const fetchDataCollaborators = async () => {
    try {
      setLoading(true)
      const data = await CollaboratorsAPI.Collaborators({page: 1}).request();
      setCollaborators(data);
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false)
    }
  };

  const fetchOutgoingInvitations = async () => {
    try {
      setLoading(true)
      if (currentUser?.id) {
        const data = await CollaboratorsAPI.OutgoingInvitations(currentUser.id, {page: 1}).request();
        setOutgoingInvitations(data);
      }
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false)
    }
  };

  const fetchIncomingInvitations = async () => {
    try {
      setLoading(true)
      if (currentUser?.email) {
        const data = await CollaboratorsAPI.IncomingInvitations(currentUser.email, {page: 1}).request();
        setIncomingInvitations(data);
      }
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false)
    }
  };

  // Only fetch if we need fresh data (e.g., after tab change and no initial data)
  useEffect(() => {
    if (!activeTab) return;

    // Only fetch if we don't have initial data or need to refresh
    if (activeTab === 'collaborators' && collaborators.length === 0) {
      fetchDataCollaborators();
    } else if (activeTab === 'invitations-out' && outgoingInvitations.length === 0) {
      fetchOutgoingInvitations();
    } else if (activeTab === 'invitations-in' && incomingInvitations.length === 0) {
      fetchIncomingInvitations();
    }
  }, [activeTab]);

  const generateInvitationLink = async () => {
    try {
      setLoading(true)
      if (!newInviteEmail || !newInviteRole) return

      // Create invitation - let backend generate the code
      const newInvitation: Partial<Invitation> = {
        email: newInviteEmail,
        role: newInviteRole,
        status: "pending",
        invitedBy: currentUser?.id,
      }
      const data = await CollaboratorsAPI.CreateInvitation(newInvitation).request();
      const link = `${window.location.origin}/invitation?code=${data.code}`
      setGeneratedLink(link)
      setOutgoingInvitations([...outgoingInvitations, data])
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false)
    }
  }

  const copyToClipboard = () => {
    navigator.clipboard.writeText(generatedLink)
    setLinkCopied(true)
    setTimeout(() => setLinkCopied(false), 2000)
  }

  const resendInvitation = async (invitation: Invitation) => {
    try {
      console.log(`Resending invitation ${invitation.id}`)
      setLoading(true)
      if (!invitation.id) return

      // Update invitation with new expiry and sent date - let backend generate new code
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 7); // 7 days from now

      const updatedInvitation: Partial<Invitation> = {
        expiresAt,
        sentAt: new Date(),
        status: "pending", // Reset to pending if it was declined
      }

      const data = await CollaboratorsAPI.UpdateInvitation(invitation.id, updatedInvitation).request();
      // Update the appropriate invitation list based on who sent it
      if (data.invitedBy === currentUser?.id) {
        setOutgoingInvitations(outgoingInvitations.map((val) => val.id === invitation.id ? data : val))
      } else {
        setIncomingInvitations(incomingInvitations.map((val) => val.id === invitation.id ? data : val))
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false)
    }
  }

  const cancelInvitation = async (invitation: Invitation) => {
    try {
      setLoading(true)
      if (!invitation.id) return

      await CollaboratorsAPI.DeleteInvitation(invitation.id).request();
      // Remove from the appropriate invitation list based on who sent it
      if (invitation.invitedBy === currentUser?.id) {
        setOutgoingInvitations(outgoingInvitations.filter((val) => val.id !== invitation.id))
      } else {
        setIncomingInvitations(incomingInvitations.filter((val) => val.id !== invitation.id))
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false)
    }
  }

  const removeCollaborator = async (collaborator: CollaboratorUser) => {
    try {
      setLoading(true)
      if (!collaborator.id) return

      await CollaboratorsAPI.DeleteCollaborator(collaborator.id).request();
      setCollaborators(collaborators.filter((val) => val.id !== collaborator.id))
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false)
    }
  }

  const changeCollaboratorRole = async (collaborator: CollaboratorUser, newRole: RoleType) => {
    try {
      setLoading(true)
      if (!collaborator.id) return

      const updatedCollaborator: Partial<CollaboratorUser> = {
        ...collaborator,
        role: newRole,
      }

      const data = await CollaboratorsAPI.UpdateCollaborator(collaborator.id, updatedCollaborator).request();
      setCollaborators(collaborators.map((val) => val.id === collaborator.id ? data : val))
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false)
    }
  }

  const getRoleBadge = (role: string) => {
    const roleType = role as RoleType;
    switch (roleType) {
      case "admin":
        return <Badge variant="destructive">{t("roles.admin")}</Badge>
      case "team":
        return <Badge variant="default">{t("roles.team")}</Badge>
      case "external":
        return <Badge variant="secondary">{t("roles.external")}</Badge>
      default:
        return <Badge variant="outline">{role}</Badge>
    }
  }

  // Wrapper functions to match component interfaces
  const handleChangeCollaboratorRole = (collaborator: CollaboratorUser) => {
    // This would typically open a role selection dialog
    // For now, we'll just cycle through roles as an example
    const roles: RoleType[] = ["team", "admin", "external"];
    const currentIndex = roles.indexOf(collaborator.role);
    const nextRole = roles[(currentIndex + 1) % roles.length];
    changeCollaboratorRole(collaborator, nextRole);
  }

  const handleRemoveCollaborator = (id: string) => {
    const collaborator = collaborators.find(c => c.id === id);
    if (collaborator) {
      removeCollaborator(collaborator);
    }
  }

  const handleResendInvitation = (invitation: Invitation) => {
    resendInvitation(invitation);
  }

  const handleCancelInvitation = (id: string) => {
    const invitation = [...outgoingInvitations, ...incomingInvitations].find(i => i.id === id);
    if (invitation) {
      cancelInvitation(invitation);
    }
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">{t("titles.collaborators")}</h1>
        <p className="text-muted-foreground mt-2">{t("descriptions.collaborators")}</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("stats.totalCollaborators")}</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{collaborators.length}</div>
            <p className="text-xs text-muted-foreground">{t("stats.activeMembers")}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("stats.pendingInvitations")}</CardTitle>
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{[...outgoingInvitations, ...incomingInvitations].filter(inv => inv.status === "pending").length}</div>
            <p className="text-xs text-muted-foreground">{t("stats.awaitingResponse")}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("stats.totalInvitations")}</CardTitle>
            <UserPlus className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{outgoingInvitations.length + incomingInvitations.length}</div>
            <p className="text-xs text-muted-foreground">{t("stats.allTime")}</p>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="flex items-center justify-between">
          <TabsList className="grid w-full grid-cols-4 lg:w-[600px]">
            <TabsTrigger value="collaborators">{t("tabs.collaborators")}</TabsTrigger>
            <TabsTrigger value="invitations-out">Sent Invitations</TabsTrigger>
            <TabsTrigger value="invitations-in">Received Invitations</TabsTrigger>
          </TabsList>

          <Dialog open={inviteDialogOpen} onOpenChange={setInviteDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <UserPlus className="mr-2 h-4 w-4" />
                {t("buttons.inviteCollaborator")}
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>{t("dialogs.inviteCollaborator.title")}</DialogTitle>
                <DialogDescription>{t("dialogs.inviteCollaborator.description")}</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="email">{t("labels.email")}</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder={t("placeholders.email")}
                    value={newInviteEmail}
                    onChange={(e) => setNewInviteEmail(e.target.value)}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="role">{t("labels.role")}</Label>
                  <Select value={newInviteRole} onValueChange={(value: RoleType) => setNewInviteRole(value)}>
                    <SelectTrigger>
                      <SelectValue placeholder={t("placeholders.selectRole")} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="team">{t("roles.team")}</SelectItem>
                      <SelectItem value="external">{t("roles.external")}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                {generatedLink && (
                  <div className="grid gap-2">
                    <Label>{t("labels.invitationLink")}</Label>
                    <div className="flex gap-2">
                      <Input value={generatedLink} readOnly className="flex-1" />
                      <Button size="sm" variant="outline" onClick={copyToClipboard}>
                        {linkCopied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>
                )}
              </div>
              <DialogFooter>
                {!generatedLink ? (
                  <Button onClick={generateInvitationLink} disabled={!newInviteEmail || !newInviteRole}>
                    {t("buttons.generateLink")}
                  </Button>
                ) : (
                  <Button onClick={() => {
                    setGeneratedLink("")
                    setInviteDialogOpen(false)
                  }}>
                    {t("buttons.done")}
                  </Button>
                )}
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        <TabsContent value="collaborators" className="mt-6">
          <TabsCollaborators
            collaborators={collaborators}
            getRoleBadge={getRoleBadge}
            changeCollaboratorRole={handleChangeCollaboratorRole}
            removeCollaborator={handleRemoveCollaborator}
          />
        </TabsContent>

        <TabsContent value="invitations-out" className="mt-6">
          <TabsInvitations
            invitations={outgoingInvitations}
            getRoleBadge={getRoleBadge}
            cancelInvitation={handleCancelInvitation}
            resendInvitation={handleResendInvitation}
            handleNewInvitation={() => {
              setActiveTab("collaborators")
              setInviteDialogOpen(true)
            }}
          />
        </TabsContent>

        <TabsContent value="invitations-in" className="mt-6">
          <TabsInvitations
            invitations={incomingInvitations}
            getRoleBadge={getRoleBadge}
            cancelInvitation={handleCancelInvitation}
            resendInvitation={handleResendInvitation}
            handleNewInvitation={() => {
              setActiveTab("collaborators")
              setInviteDialogOpen(true)
            }}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}
