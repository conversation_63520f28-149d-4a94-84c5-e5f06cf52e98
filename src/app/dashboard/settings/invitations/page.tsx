"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON>, Check, Clock, Copy, ExternalLink, X } from "lucide-react"
import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON>ltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

// Mock data for received invitations
const initialReceivedInvitations = [
  {
    id: "rec-1",
    from: {
      name: "<PERSON>",
      email: "<EMAIL>",
      avatar: "/stylized-sm-logo.png",
    },
    role: "team",
    status: "pending",
    expiresAt: "2023-06-15",
    sentAt: "2023-06-01",
    code: "TEAM-123456",
    coursesCount: 8,
  },
  {
    id: "rec-2",
    from: {
      name: "<PERSON>",
      email: "<EMAIL>",
      avatar: "/dc-skyline-night.png",
    },
    role: "external",
    status: "pending",
    expiresAt: "2023-06-20",
    sentAt: "2023-06-05",
    code: "EXT-789012",
    coursesCount: 5,
  },
]

// Mock data for sent invitations
const initialSentInvitations = [
  {
    id: "sent-1",
    to: "<EMAIL>",
    role: "team",
    status: "pending",
    expiresAt: "2023-06-15",
    sentAt: "2023-06-01",
    code: "TEAM-654321",
  },
  {
    id: "sent-2",
    to: "<EMAIL>",
    role: "external",
    status: "accepted",
    expiresAt: "2023-06-20",
    sentAt: "2023-06-05",
    acceptedAt: "2023-06-07",
    code: "EXT-210987",
  },
  {
    id: "sent-3",
    to: "<EMAIL>",
    role: "team",
    status: "expired",
    expiresAt: "2023-05-30",
    sentAt: "2023-05-16",
    code: "TEAM-135790",
  },
]

export default function InvitationsPage() {
  const [receivedInvitations, setReceivedInvitations] = useState(initialReceivedInvitations)
  const [sentInvitations, setSentInvitations] = useState(initialSentInvitations)
  const [activeTab, setActiveTab] = useState("received")

  const acceptInvitation = (invitationId: string) => {
    setReceivedInvitations(
      receivedInvitations.map((invitation) =>
        invitation.id === invitationId ? { ...invitation, status: "accepted" } : invitation,
      ),
    )
  }

  const declineInvitation = (invitationId: string) => {
    setReceivedInvitations(
      receivedInvitations.map((invitation) =>
        invitation.id === invitationId ? { ...invitation, status: "declined" } : invitation,
      ),
    )
  }

  const copyInvitationLink = (code: string) => {
    const link = `${window.location.origin}/invitation?code=${code}`
    navigator.clipboard.writeText(link)
  }

  const getRoleBadge = (role: string) => {
    switch (role) {
      case "admin":
        return <Badge className="bg-primary">Admin</Badge>
      case "team":
        return <Badge variant="secondary">Team</Badge>
      case "external":
        return <Badge variant="outline">External</Badge>
      default:
        return null
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return (
          <Badge variant="outline" className="bg-yellow-500/10 text-yellow-500">
            Pending
          </Badge>
        )
      case "accepted":
        return (
          <Badge variant="outline" className="bg-green-500/10 text-green-500">
            Accepted
          </Badge>
        )
      case "declined":
        return (
          <Badge variant="outline" className="bg-red-500/10 text-red-500">
            Declined
          </Badge>
        )
      case "expired":
        return (
          <Badge variant="outline" className="bg-gray-500/10 text-gray-500">
            Expired
          </Badge>
        )
      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold">Invitations</h1>
        <p className="text-muted-foreground">Manage invitations to collaborate on courses.</p>
      </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList>
            <TabsTrigger value="received">Received Invitations</TabsTrigger>
            <TabsTrigger value="sent">Sent Invitations</TabsTrigger>
          </TabsList>

          <TabsContent value="received" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Invitations Received</CardTitle>
                <CardDescription>Invitations from other users to collaborate on their courses.</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {receivedInvitations.length === 0 ? (
                    <div className="text-center py-12 text-muted-foreground">
                      <p>No invitations received.</p>
                    </div>
                  ) : (
                    receivedInvitations.map((invitation) => (
                      <div
                        key={invitation.id}
                        className={`flex flex-col md:flex-row md:items-center justify-between rounded-lg border p-4 gap-4 ${
                          invitation.status === "accepted"
                            ? "bg-green-500/5 border-green-500/20"
                            : invitation.status === "declined"
                              ? "bg-red-500/5 border-red-500/20"
                              : ""
                        }`}
                      >
                        <div className="flex items-center gap-4">
                          <Avatar>
                            <AvatarImage
                              src={invitation.from.avatar || "/placeholder.svg"}
                              alt={invitation.from.name}
                            />
                            <AvatarFallback>
                              {invitation.from.name
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{invitation.from.name}</div>
                            <div className="text-sm text-muted-foreground">{invitation.from.email}</div>
                            <div className="flex items-center gap-2 mt-1">
                              {getRoleBadge(invitation.role)}
                              {getStatusBadge(invitation.status)}
                            </div>
                          </div>
                        </div>
                        <div className="flex flex-col md:items-end gap-2">
                          <div className="text-sm text-muted-foreground flex items-center gap-1">
                            <Calendar className="h-3.5 w-3.5" />
                            Sent on {invitation.sentAt}
                          </div>
                          <div className="text-sm text-muted-foreground flex items-center gap-1">
                            <Clock className="h-3.5 w-3.5" />
                            Expires on {invitation.expiresAt}
                          </div>
                          <div className="text-sm">Access to {invitation.coursesCount} courses</div>
                        </div>
                        {invitation.status === "pending" && (
                          <div className="flex gap-2 mt-2 md:mt-0">
                            <Button
                              variant="outline"
                              className="flex-1 md:flex-initial"
                              onClick={() => declineInvitation(invitation.id)}
                            >
                              <X className="mr-2 h-4 w-4" />
                              Decline
                            </Button>
                            <Button className="flex-1 md:flex-initial" onClick={() => acceptInvitation(invitation.id)}>
                              <Check className="mr-2 h-4 w-4" />
                              Accept
                            </Button>
                          </div>
                        )}
                        {invitation.status === "accepted" && (
                          <Button variant="outline" asChild>
                            <Link href="/dashboard/my-courses">
                              View Courses
                              <ExternalLink className="ml-2 h-4 w-4" />
                            </Link>
                          </Button>
                        )}
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="sent" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Invitations Sent</CardTitle>
                <CardDescription>Invitations you've sent to others to collaborate on your courses.</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {sentInvitations.length === 0 ? (
                    <div className="text-center py-12 text-muted-foreground">
                      <p>No invitations sent.</p>
                    </div>
                  ) : (
                    sentInvitations.map((invitation) => (
                      <div
                        key={invitation.id}
                        className={`flex flex-col md:flex-row md:items-center justify-between rounded-lg border p-4 gap-4 ${
                          invitation.status === "accepted"
                            ? "bg-green-500/5 border-green-500/20"
                            : invitation.status === "expired"
                              ? "bg-gray-500/5 border-gray-500/20"
                              : ""
                        }`}
                      >
                        <div>
                          <div className="font-medium">{invitation.to}</div>
                          <div className="flex items-center gap-2 mt-1">
                            {getRoleBadge(invitation.role)}
                            {getStatusBadge(invitation.status)}
                          </div>
                        </div>
                        <div className="flex flex-col md:items-end gap-1">
                          <div className="text-sm text-muted-foreground flex items-center gap-1">
                            <Calendar className="h-3.5 w-3.5" />
                            Sent on {invitation.sentAt}
                          </div>
                          {invitation.status === "accepted" && invitation.acceptedAt && (
                            <div className="text-sm text-muted-foreground flex items-center gap-1">
                              <Check className="h-3.5 w-3.5 text-green-500" />
                              Accepted on {invitation.acceptedAt}
                            </div>
                          )}
                          {invitation.status === "pending" && (
                            <div className="text-sm text-muted-foreground flex items-center gap-1">
                              <Clock className="h-3.5 w-3.5" />
                              Expires on {invitation.expiresAt}
                            </div>
                          )}
                        </div>
                        <div className="flex gap-2 mt-2 md:mt-0">
                          {invitation.status === "pending" && (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="outline"
                                    size="icon"
                                    onClick={() => copyInvitationLink(invitation.code)}
                                  >
                                    <Copy className="h-4 w-4" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>Copy invitation link</TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}
                          <Button variant="outline" asChild className="flex-1 md:flex-initial">
                            <Link href="/dashboard/settings/collaborators">
                              Manage
                              <ExternalLink className="ml-2 h-4 w-4" />
                            </Link>
                          </Button>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full" asChild>
                  <Link href="/dashboard/settings/collaborators">
                    Manage Collaborators
                    <ExternalLink className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
    </div>
  )
}
