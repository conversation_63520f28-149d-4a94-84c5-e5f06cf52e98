"use client"

import <PERSON> from "next/link"
import { usePathname } from "next/navigation"
import {
  Settings,
  Users,
  Mail,
  Bell,
  Shield,
  Palette,
  Globe
} from "lucide-react"
import { cn } from "@/src/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"

const settingsNavigation = [
  {
    title: "Team",
    items: [
      {
        title: "Collaborators",
        href: "/dashboard/settings/collaborators",
        icon: Users,
        description: "Manage team members and their permissions"
      },
      {
        title: "Invitations", 
        href: "/dashboard/settings/invitations",
        icon: Mail,
        description: "View and manage pending invitations"
      }
    ]
  },
  {
    title: "Account",
    items: [
      {
        title: "Notifications",
        href: "/dashboard/settings/notifications",
        icon: Bell,
        description: "Configure notification preferences"
      },
      {
        title: "Security",
        href: "/dashboard/settings/security", 
        icon: Shield,
        description: "Manage security settings and authentication"
      }
    ]
  },
  {
    title: "Preferences",
    items: [
      {
        title: "Appearance",
        href: "/dashboard/settings/appearance",
        icon: Palette,
        description: "Customize theme and display preferences"
      },
      {
        title: "Language",
        href: "/dashboard/settings/language",
        icon: Globe,
        description: "Set your preferred language"
      }
    ]
  }
]

interface SettingsLayoutProps {
  children: React.ReactNode
}

export default function SettingsLayout({ children }: SettingsLayoutProps) {
  const pathname = usePathname()
  const isSettingsRoot = pathname === "/dashboard/settings"

  // Don't show sidebar on the main settings page
  if (isSettingsRoot) {
    return <>{children}</>
  }

  return (
    <div className="container py-8">
      <div className="flex flex-col lg:flex-row gap-8">
        {/* Sidebar */}
        <div className="lg:w-80 flex-shrink-0">
          <div className="sticky top-8">
            {/* Settings Header */}
            <div className="flex items-center gap-2 mb-6">
              <Settings className="h-6 w-6" />
              <h2 className="text-xl font-semibold">Settings</h2>
            </div>

            {/* Navigation */}
            <div className="space-y-6">
              {settingsNavigation.map((section) => (
                <div key={section.title}>
                  <h3 className="text-sm font-medium text-muted-foreground mb-3 px-3">
                    {section.title}
                  </h3>
                  <div className="space-y-1">
                    {section.items.map((item) => {
                      const Icon = item.icon
                      const isActive = pathname === item.href
                      
                      return (
                        <Link key={item.href} href={item.href}>
                          <div className={cn(
                            "flex items-center gap-3 px-3 py-2 rounded-lg text-sm transition-colors hover:bg-muted",
                            isActive && "bg-muted font-medium text-primary"
                          )}>
                            <Icon className="h-4 w-4" />
                            {item.title}
                          </div>
                        </Link>
                      )
                    })}
                  </div>
                </div>
              ))}
            </div>

            <Separator className="my-6" />

            {/* Quick Info */}
            <Card>
              <CardContent className="p-4">
                <div className="text-sm">
                  <div className="font-medium mb-1">Need Help?</div>
                  <div className="text-muted-foreground mb-3">
                    Check our documentation for detailed guides.
                  </div>
                  <Button variant="outline" size="sm" className="w-full">
                    View Documentation
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 min-w-0">
          {children}
        </div>
      </div>
    </div>
  )
}
