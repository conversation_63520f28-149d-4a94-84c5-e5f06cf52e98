"use client"

import { useState, useEffect } from "react"
import { Bell, Mail, MessageSquare, Calendar } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import { SettingsAPI } from "@/src/services/settingsApi"
import { UserSettings } from "@/src/lib/repositories/settings/SettingsRepository"

export default function NotificationsPage() {
  const [settings, setSettings] = useState<UserSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const { toast } = useToast()

  // Get current user ID (in real app, this would come from auth context)
  const userId = "default-user"

  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      setLoading(true)
      const data = await SettingsAPI.UserSettings(userId).request()
      setSettings(data)
    } catch (error) {
      console.error("Failed to fetch settings:", error)
      toast({
        title: "Error",
        description: "Failed to load notification settings",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const updateNotificationSettings = async (updates: Partial<UserSettings>) => {
    if (!settings) return

    try {
      setSaving(true)
      const updatedSettings = await SettingsAPI.UpdateNotificationSettings(userId, updates).request()
      setSettings(updatedSettings)
      toast({
        title: "Success",
        description: "Notification settings updated successfully",
      })
    } catch (error) {
      console.error("Failed to update settings:", error)
      toast({
        title: "Error",
        description: "Failed to update notification settings",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col gap-2">
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Bell className="h-8 w-8" />
            Notifications
          </h1>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  if (!settings) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col gap-2">
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Bell className="h-8 w-8" />
            Notifications
          </h1>
          <p className="text-muted-foreground">Failed to load settings</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold flex items-center gap-2">
          <Bell className="h-8 w-8" />
          Notifications
        </h1>
        <p className="text-muted-foreground">
          Configure how and when you receive notifications.
        </p>
      </div>

      <div className="grid gap-6">
        {/* Email Notifications */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Email Notifications
            </CardTitle>
            <CardDescription>
              Manage email notification preferences for different types of activities.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="email-notifications">Email Notifications</Label>
                <div className="text-sm text-muted-foreground">
                  Receive notifications via email
                </div>
              </div>
              <Switch
                id="email-notifications"
                checked={settings.emailNotifications}
                onCheckedChange={(checked) => updateNotificationSettings({ emailNotifications: checked })}
                disabled={saving}
              />
            </div>

            <Separator />

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="course-updates">Course Updates</Label>
                  <div className="text-sm text-muted-foreground">
                    New content, assignments, and course announcements
                  </div>
                </div>
                <Switch
                  id="course-updates"
                  checked={settings.courseUpdates}
                  onCheckedChange={(checked) => updateNotificationSettings({ courseUpdates: checked })}
                  disabled={!settings.emailNotifications || saving}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="test-reminders">Test Reminders</Label>
                  <div className="text-sm text-muted-foreground">
                    Upcoming tests and assignment deadlines
                  </div>
                </div>
                <Switch
                  id="test-reminders"
                  checked={settings.testReminders}
                  onCheckedChange={(checked) => updateNotificationSettings({ testReminders: checked })}
                  disabled={!settings.emailNotifications || saving}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="collaborator-activity">Collaborator Activity</Label>
                  <div className="text-sm text-muted-foreground">
                    When team members make changes to shared courses
                  </div>
                </div>
                <Switch
                  id="collaborator-activity"
                  checked={settings.collaboratorActivity}
                  onCheckedChange={(checked) => updateNotificationSettings({ collaboratorActivity: checked })}
                  disabled={!settings.emailNotifications || saving}
                />
              </div>
            </div>

            <Separator />

            <div className="space-y-2">
              <Label htmlFor="frequency">Email Frequency</Label>
              <Select
                value={settings.notificationFrequency}
                onValueChange={(value: "immediate" | "daily" | "weekly") => updateNotificationSettings({ notificationFrequency: value })}
                disabled={!settings.emailNotifications || saving}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select frequency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="immediate">Immediate</SelectItem>
                  <SelectItem value="daily">Daily Digest</SelectItem>
                  <SelectItem value="weekly">Weekly Summary</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Push Notifications */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Push Notifications
            </CardTitle>
            <CardDescription>
              Receive real-time notifications in your browser.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="push-notifications">Browser Notifications</Label>
                <div className="text-sm text-muted-foreground">
                  Show notifications in your browser when the app is open
                </div>
              </div>
              <Switch
                id="push-notifications"
                checked={settings.pushNotifications}
                onCheckedChange={(checked) => updateNotificationSettings({ pushNotifications: checked })}
                disabled={saving}
              />
            </div>
          </CardContent>
        </Card>

        {/* Calendar Integration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Calendar Integration
            </CardTitle>
            <CardDescription>
              Sync test dates and deadlines with your calendar.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm text-muted-foreground">
              Calendar integration is coming soon. You'll be able to sync test dates and assignment deadlines with your preferred calendar application.
            </div>
            <Button variant="outline" disabled>
              Connect Calendar
            </Button>
          </CardContent>
        </Card>

        {/* Save Changes */}
        <div className="flex justify-end">
          <Button onClick={fetchSettings} disabled={saving}>
            {saving ? "Saving..." : "Refresh Settings"}
          </Button>
        </div>
      </div>
    </div>
  )
}
