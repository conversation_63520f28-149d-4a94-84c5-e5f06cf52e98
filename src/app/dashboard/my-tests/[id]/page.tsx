"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { ChevronLeft, Lock, Share2, } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import DetailTestOverview from "@/components/layout/my-test/DetailTestOverview"
import DetailTestAnalytics from "@/components/layout/my-test/DetailTestAnalytics"
import { MyTestDetail } from "@/src/lib/repositories/my-tests/MyTestsRepository"
import { MyTestsAPI } from "@/src/services/myTestsApi"

import { useLocalization } from "@/src/localization/functions/client";
import en from "../locales/en.json"

export default function TestDetail({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [showProModal, setShowProModal] = useState(false)
  const [testDetails, setTestDetails] = useState<MyTestDetail>()
  const [loading, setLoading] = useState(true);
  const { t } = useLocalization("public-test", { en });

  useEffect(() => {
    const fetchTests = async () => {
      try {
        const data = await MyTestsAPI.MyTestDetailFull(params.id).request();
        setTestDetails(data);
      } catch (err) {
        console.error(err);
      } finally {
        setLoading(false)
      }
    };
    fetchTests();
  }, []);

  // Get test details from mock data
  const test = testDetails

  if (!test) {
    return (
      <div className="flex flex-col items-center justify-center h-[50vh]">
        <h2 className="text-xl font-semibold mb-2">{t("test_not_found")}</h2>
        <p className="text-muted-foreground mb-4">{t("test_not_found_detail")}</p>
        <Button onClick={() => router.push("/dashboard/my-tests")}>
          <ChevronLeft className="mr-2 h-4 w-4" />
          {t("back_to_my_tests")}
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row gap-4 md:items-center md:justify-between">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" onClick={() => router.push("/dashboard/my-tests")}>
            <ChevronLeft className="h-4 w-4 mr-1" />
            {t("btn_back")}
          </Button>
          <h2 className="text-2xl font-bold tracking-tight">{test.title}</h2>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Share2 className="h-4 w-4 mr-2" />
            {t("btn_share_results")}
          </Button>
          <Button size="sm">{t("btn_retake_test")}</Button>
        </div>
      </div>

      {/* Test overview */}
      <DetailTestOverview test={test} />

      {/* Detailed analysis */}
      <DetailTestAnalytics test={test} setShowProModal={setShowProModal} />

      {/* Pro upgrade alert */}
      <Alert className="shadow-none bg-sp-neutral-100 border-none rounded-3xl p-4">
        <Lock className="h-4 w-4" />
        <AlertTitle>{t("unlock_premium_features")}</AlertTitle>
        <AlertDescription>
          {t("upgrade_to_pro")}
          <Button variant="link" className="p-0 h-auto font-normal" onClick={() => setShowProModal(true)}>
            {t("btn_learn_more")}
          </Button>
        </AlertDescription>
      </Alert>
    </div>
  )
}
