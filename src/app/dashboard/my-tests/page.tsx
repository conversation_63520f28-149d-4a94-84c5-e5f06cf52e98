import TabsMyTestMain from "@/components/layout/my-test/TabsMyTestMain"
import { useLocalization } from "@/src/hooks/useLocalization/server";
import { locales } from "./locales";

export default async function MyTests() {
  const { t } = await useLocalization("dashboard.my-test", locales);

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row gap-4 md:items-center md:justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">{t("my_tests")}</h2>
          <p className="text-muted-foreground">{t("my_tests_detail")}</p>
        </div>
      </div>

      <TabsMyTestMain />
    </div>
  )
}
