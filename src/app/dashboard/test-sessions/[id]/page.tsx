"use client"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import {
  ArrowLeft,
  Calendar,
  CheckCircle2,
  XCircle,
  AlertCircle,
  FileText,
  Mic,
  Video,
  Eye,
  Edit,
  BarChart3,
  Mail,
  Timer,
} from "lucide-react"

// Mock data for test session
const mockTestSession = {
  id: "session-1",
  testId: "1",
  testTitle: "Web Development Fundamentals",
  student: {
    id: "student-001",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "https://github.com/shadcn.png",
  },
  startedAt: "2024-01-15T10:30:00Z",
  completedAt: "2024-01-15T10:45:00Z",
  status: "needs-review" as const,
  totalQuestions: 10,
  answeredQuestions: 10,
  reviewableQuestions: 3,
  autoGradedQuestions: 7,
  score: null, // Will be calculated after review
  timeSpent: 900, // 15 minutes in seconds
  questions: [
    {
      id: "q1",
      text: "What is the difference between let and var in JavaScript?",
      type: "multiple-choice",
      answer: "B",
      correctAnswer: "B",
      isCorrect: true,
      score: 1,
      maxScore: 1,
      timeSpent: 45,
      status: "auto-graded",
    },
    {
      id: "q2",
      text: "Explain the concept of closures in JavaScript with an example.",
      type: "freetext",
      answer: "A closure is a function that has access to variables in its outer scope...",
      correctAnswer: null,
      isCorrect: null,
      score: null,
      maxScore: 2,
      timeSpent: 180,
      status: "needs-review",
    },
    {
      id: "q3",
      text: "Which HTML tag is used for creating hyperlinks?",
      type: "multiple-choice",
      answer: "A",
      correctAnswer: "A",
      isCorrect: true,
      score: 1,
      maxScore: 1,
      timeSpent: 30,
      status: "auto-graded",
    },
    {
      id: "q4",
      text: "Record a 2-minute explanation of how CSS Grid works.",
      type: "video",
      answer: "video-recording-url",
      correctAnswer: null,
      isCorrect: null,
      score: null,
      maxScore: 3,
      timeSpent: 240,
      status: "needs-review",
    },
    {
      id: "q5",
      text: "What is the purpose of the DOCTYPE declaration?",
      type: "multiple-choice",
      answer: "C",
      correctAnswer: "B",
      isCorrect: false,
      score: 0,
      maxScore: 1,
      timeSpent: 60,
      status: "auto-graded",
    },
    {
      id: "q6",
      text: "Explain your approach to responsive web design.",
      type: "voice",
      answer: "audio-recording-url",
      correctAnswer: null,
      isCorrect: null,
      score: null,
      maxScore: 2,
      timeSpent: 120,
      status: "needs-review",
    },
    {
      id: "q7",
      text: "Which CSS property is used to change text color?",
      type: "multiple-choice",
      answer: "A",
      correctAnswer: "A",
      isCorrect: true,
      score: 1,
      maxScore: 1,
      timeSpent: 25,
      status: "auto-graded",
    },
    {
      id: "q8",
      text: "What is the box model in CSS?",
      type: "multiple-choice",
      answer: "B",
      correctAnswer: "B",
      isCorrect: true,
      score: 1,
      maxScore: 1,
      timeSpent: 40,
      status: "auto-graded",
    },
    {
      id: "q9",
      text: "How do you center a div horizontally and vertically?",
      type: "multiple-choice",
      answer: "C",
      correctAnswer: "C",
      isCorrect: true,
      score: 1,
      maxScore: 1,
      timeSpent: 50,
      status: "auto-graded",
    },
    {
      id: "q10",
      text: "What is semantic HTML?",
      type: "multiple-choice",
      answer: "A",
      correctAnswer: "A",
      isCorrect: true,
      score: 1,
      maxScore: 1,
      timeSpent: 35,
      status: "auto-graded",
    },
  ],
}

function getQuestionTypeIcon(type: string) {
  switch (type) {
    case "voice":
      return <Mic className="h-4 w-4" />
    case "video":
      return <Video className="h-4 w-4" />
    case "freetext":
      return <FileText className="h-4 w-4" />
    default:
      return <CheckCircle2 className="h-4 w-4" />
  }
}

function getStatusIcon(status: string, isCorrect?: boolean | null) {
  if (status === "needs-review") {
    return <AlertCircle className="h-4 w-4 text-orange-500" />
  }
  if (status === "auto-graded") {
    return isCorrect ? (
      <CheckCircle2 className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    )
  }
  return <AlertCircle className="h-4 w-4 text-gray-400" />
}

function formatTime(seconds: number) {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleString("en-US", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  })
}

export default function TestSessionDetailPage() {
  const params = useParams()
  const router = useRouter()
  const sessionId = params.id

  const session = mockTestSession
  const autoGradedScore = session.questions
    .filter((q) => q.status === "auto-graded")
    .reduce((sum, q) => sum + (q.score || 0), 0)
  const autoGradedMaxScore = session.questions
    .filter((q) => q.status === "auto-graded")
    .reduce((sum, q) => sum + q.maxScore, 0)
  const totalMaxScore = session.questions.reduce((sum, q) => sum + q.maxScore, 0)
  const needsReviewQuestions = session.questions.filter((q) => q.status === "needs-review")
  const completionPercentage = (session.answeredQuestions / session.totalQuestions) * 100

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col gap-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" asChild>
              <Link href={`/dashboard/test-builder/${session.testId}`}>
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Test
              </Link>
            </Button>
            <span className="text-muted-foreground">|</span>
            <h1 className="text-2xl font-semibold">{session.testTitle}</h1>
            <Badge
              className={
                session.status === "needs-review"
                  ? "bg-orange-100 text-orange-800"
                  : session.status === "completed"
                    ? "bg-green-100 text-green-800"
                    : "bg-blue-100 text-blue-800"
              }
            >
              {session.status === "needs-review" ? "Needs Review" : "Completed"}
            </Badge>
          </div>

          <div className="flex gap-2">
            {needsReviewQuestions.length > 0 && (
              <Button asChild>
                <Link href={`/dashboard/test-sessions/${sessionId}/review`}>
                  <Edit className="h-4 w-4 mr-1" />
                  Review Answers ({needsReviewQuestions.length})
                </Link>
              </Button>
            )}
            <Button variant="outline">
              <BarChart3 className="h-4 w-4 mr-1" />
              Export Report
            </Button>
          </div>
        </div>

        {/* Main content */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Left column - Student info and summary */}
          <div className="md:col-span-1 space-y-6">
            {/* Student info */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Student Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={session.student.avatar || "/placeholder.svg"} alt={session.student.name} />
                    <AvatarFallback>{session.student.name.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-semibold text-lg">{session.student.name}</p>
                    <p className="text-sm text-muted-foreground flex items-center gap-1">
                      <Mail className="h-3 w-3" />
                      {session.student.email}
                    </p>
                  </div>
                </div>

                <div className="space-y-3 pt-2 border-t">
                  <div className="flex items-center gap-2 text-sm">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="font-medium">Started</p>
                      <p className="text-muted-foreground">{formatDate(session.startedAt)}</p>
                    </div>
                  </div>
                  {session.completedAt && (
                    <div className="flex items-center gap-2 text-sm">
                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                      <div>
                        <p className="font-medium">Completed</p>
                        <p className="text-muted-foreground">{formatDate(session.completedAt)}</p>
                      </div>
                    </div>
                  )}
                  <div className="flex items-center gap-2 text-sm">
                    <Timer className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="font-medium">Time Spent</p>
                      <p className="text-muted-foreground">{formatTime(session.timeSpent)}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Test summary */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Test Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium">Progress</span>
                    <span className="text-sm text-muted-foreground">
                      {session.answeredQuestions}/{session.totalQuestions}
                    </span>
                  </div>
                  <Progress value={completionPercentage} className="h-2" />
                </div>

                <div className="grid grid-cols-2 gap-4 pt-2">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">{session.autoGradedQuestions}</p>
                    <p className="text-xs text-muted-foreground">Auto-graded</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-orange-600">{session.reviewableQuestions}</p>
                    <p className="text-xs text-muted-foreground">Need review</p>
                  </div>
                </div>

                {autoGradedMaxScore > 0 && (
                  <div className="pt-2 border-t">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Auto-graded Score</span>
                      <span className="text-lg font-bold">
                        {autoGradedScore}/{autoGradedMaxScore}
                      </span>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      {Math.round((autoGradedScore / autoGradedMaxScore) * 100)}% on auto-graded questions
                    </p>
                  </div>
                )}

                <div className="pt-2 border-t">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Total Possible Score</span>
                    <span className="text-lg font-bold">{totalMaxScore} points</span>
                  </div>
                  {session.reviewableQuestions > 0 && (
                    <p className="text-xs text-muted-foreground mt-1">
                      Final score pending review of {session.reviewableQuestions} questions
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right column - Questions list */}
          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">DashboardQuestion Responses</CardTitle>
                <CardDescription>Review individual question responses and scores</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {session.questions.map((question, index) => (
                    <div key={question.id} className="border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <span className="text-sm font-medium text-muted-foreground">Q{index + 1}</span>
                            {getQuestionTypeIcon(question.type)}
                            <Badge variant="outline" className="text-xs">
                              {question.type}
                            </Badge>
                            {getStatusIcon(question.status, question.isCorrect)}
                            <span className="text-xs text-muted-foreground ml-auto">
                              {formatTime(question.timeSpent)}
                            </span>
                          </div>
                          <p className="text-sm mb-3">{question.text}</p>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4">
                              {question.status === "auto-graded" && (
                                <div className="text-sm">
                                  <span
                                    className={`font-medium ${question.isCorrect ? "text-green-600" : "text-red-600"}`}
                                  >
                                    {question.score}/{question.maxScore} points
                                  </span>
                                  <span className="text-muted-foreground ml-2">
                                    ({question.isCorrect ? "Correct" : "Incorrect"})
                                  </span>
                                </div>
                              )}
                              {question.status === "needs-review" && (
                                <div className="text-sm">
                                  <span className="font-medium text-orange-600">Pending Review</span>
                                  <span className="text-muted-foreground ml-2">Max {question.maxScore} points</span>
                                </div>
                              )}
                            </div>
                            <div className="flex gap-2">
                              {question.status === "needs-review" && (
                                <Button size="sm" asChild>
                                  <Link
                                    href={`/dashboard/test-sessions/${sessionId}/question/${question.id}/answer/ans-${question.id}/review`}
                                  >
                                    <Edit className="h-4 w-4 mr-1" />
                                    Review
                                  </Link>
                                </Button>
                              )}
                              <Button variant="outline" size="sm">
                                <Eye className="h-4 w-4 mr-1" />
                                View
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
