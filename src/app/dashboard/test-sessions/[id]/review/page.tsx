"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { ArrowLeft, CheckCircle2, AlertCircle, FileText, Mic, Video, Edit, Eye, Clock } from "lucide-react"

// Mock data for test session review
const mockTestSession = {
  id: "session-1",
  testId: "1",
  testTitle: "Web Development Fundamentals",
  student: {
    id: "student-001",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "https://github.com/shadcn.png",
  },
  startedAt: "2024-01-15T10:30:00Z",
  completedAt: "2024-01-15T10:45:00Z",
  status: "needs-review" as const,
  totalQuestions: 10,
  reviewableQuestions: [
    {
      id: "q2",
      questionNumber: 2,
      text: "Explain the concept of closures in JavaScript with an example.",
      type: "freetext",
      maxScore: 2,
      timeSpent: 180,
      status: "needs-review",
      answerId: "ans-q2",
    },
    {
      id: "q4",
      questionNumber: 4,
      text: "Record a 2-minute explanation of how CSS Grid works.",
      type: "video",
      maxScore: 3,
      timeSpent: 240,
      status: "needs-review",
      answerId: "ans-q4",
    },
    {
      id: "q6",
      questionNumber: 6,
      text: "Explain your approach to responsive web design.",
      type: "voice",
      maxScore: 2,
      timeSpent: 120,
      status: "needs-review",
      answerId: "ans-q6",
    },
  ],
  reviewProgress: {
    completed: 0,
    total: 3,
  },
}

function getQuestionTypeIcon(type: string) {
  switch (type) {
    case "voice":
      return <Mic className="h-4 w-4" />
    case "video":
      return <Video className="h-4 w-4" />
    case "freetext":
      return <FileText className="h-4 w-4" />
    default:
      return <CheckCircle2 className="h-4 w-4" />
  }
}

function getTypeColor(type: string) {
  switch (type) {
    case "voice":
      return "bg-purple-100 text-purple-800"
    case "video":
      return "bg-blue-100 text-blue-800"
    case "freetext":
      return "bg-green-100 text-green-800"
    default:
      return "bg-gray-100 text-gray-800"
  }
}

function formatTime(seconds: number) {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`
}

export default function TestSessionReviewPage() {
  const params = useParams()
  const router = useRouter()
  const sessionId = params.id

  const [reviewProgress, setReviewProgress] = useState(mockTestSession.reviewProgress)
  const session = mockTestSession
  const progressPercentage = (reviewProgress.completed / reviewProgress.total) * 100

  const handleStartReview = () => {
    // Navigate to the first question that needs review
    const firstQuestion = session.reviewableQuestions[0]
    if (firstQuestion) {
      router.push(
        `/dashboard/test-sessions/${sessionId}/question/${firstQuestion.id}/answer/${firstQuestion.answerId}/review`,
      )
    }
  }

  const handleContinueReview = () => {
    // Find the next question that needs review
    const nextQuestion = session.reviewableQuestions.find((q) => q.status === "needs-review")
    if (nextQuestion) {
      router.push(
        `/dashboard/test-sessions/${sessionId}/question/${nextQuestion.id}/answer/${nextQuestion.answerId}/review`,
      )
    }
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col gap-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" asChild>
              <Link href={`/dashboard/test-sessions/${sessionId}`}>
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Session
              </Link>
            </Button>
            <span className="text-muted-foreground">|</span>
            <h1 className="text-2xl font-semibold">Review Session</h1>
            <Badge className="bg-orange-100 text-orange-800">
              {reviewProgress.total - reviewProgress.completed} questions pending
            </Badge>
          </div>

          <div className="flex gap-2">
            {reviewProgress.completed < reviewProgress.total ? (
              <Button onClick={reviewProgress.completed === 0 ? handleStartReview : handleContinueReview}>
                <Edit className="h-4 w-4 mr-1" />
                {reviewProgress.completed === 0 ? "Start Review" : "Continue Review"}
              </Button>
            ) : (
              <Button variant="outline" disabled>
                <CheckCircle2 className="h-4 w-4 mr-1" />
                Review Complete
              </Button>
            )}
          </div>
        </div>

        {/* Main content */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Left column - Session info and progress */}
          <div className="md:col-span-1 space-y-6">
            {/* Session info */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Session Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-semibold text-lg">{session.testTitle}</h3>
                  <p className="text-sm text-muted-foreground">Test Session Review</p>
                </div>

                <div className="flex items-center gap-3 pt-2 border-t">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={session.student.avatar || "/placeholder.svg"} alt={session.student.name} />
                    <AvatarFallback>{session.student.name.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">{session.student.name}</p>
                    <p className="text-sm text-muted-foreground">{session.student.email}</p>
                  </div>
                </div>

                <div className="space-y-2 pt-2 border-t">
                  <div className="flex justify-between text-sm">
                    <span>Completed:</span>
                    <span>{new Date(session.completedAt).toLocaleDateString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Total Questions:</span>
                    <span>{session.totalQuestions}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Need Review:</span>
                    <span className="font-medium text-orange-600">{session.reviewableQuestions.length}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Review progress */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Review Progress</CardTitle>
                <CardDescription>Track your review completion</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium">Completed</span>
                    <span className="text-sm text-muted-foreground">
                      {reviewProgress.completed}/{reviewProgress.total}
                    </span>
                  </div>
                  <Progress value={progressPercentage} className="h-2" />
                  <p className="text-xs text-muted-foreground mt-1">{Math.round(progressPercentage)}% complete</p>
                </div>

                {reviewProgress.completed === reviewProgress.total ? (
                  <div className="flex items-center gap-2 p-3 bg-green-50 rounded-lg">
                    <CheckCircle2 className="h-5 w-5 text-green-600" />
                    <div>
                      <p className="text-sm font-medium text-green-800">Review Complete!</p>
                      <p className="text-xs text-green-600">All questions have been reviewed</p>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center gap-2 p-3 bg-orange-50 rounded-lg">
                    <AlertCircle className="h-5 w-5 text-orange-600" />
                    <div>
                      <p className="text-sm font-medium text-orange-800">
                        {reviewProgress.total - reviewProgress.completed} questions remaining
                      </p>
                      <p className="text-xs text-orange-600">Continue reviewing to complete the session</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Right column - Questions to review */}
          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Questions Requiring Review</CardTitle>
                <CardDescription>These questions need human evaluation due to their subjective nature</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {session.reviewableQuestions.map((question, index) => (
                    <div key={question.id} className="border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-3">
                            <span className="text-sm font-medium text-muted-foreground">
                              DashboardQuestion {question.questionNumber}
                            </span>
                            {getQuestionTypeIcon(question.type)}
                            <Badge variant="outline" className={`text-xs ${getTypeColor(question.type)}`}>
                              {question.type}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {question.maxScore} points
                            </Badge>
                            <div className="flex items-center gap-1 text-xs text-muted-foreground ml-auto">
                              <Clock className="h-3 w-3" />
                              {formatTime(question.timeSpent)}
                            </div>
                          </div>

                          <p className="text-sm mb-4 leading-relaxed">{question.text}</p>

                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              {question.status === "needs-review" ? (
                                <div className="flex items-center gap-1">
                                  <AlertCircle className="h-4 w-4 text-orange-500" />
                                  <span className="text-sm font-medium text-orange-600">Pending Review</span>
                                </div>
                              ) : (
                                <div className="flex items-center gap-1">
                                  <CheckCircle2 className="h-4 w-4 text-green-500" />
                                  <span className="text-sm font-medium text-green-600">Reviewed</span>
                                </div>
                              )}
                            </div>

                            <div className="flex gap-2">
                              <Button size="sm" asChild disabled={question.status !== "needs-review"}>
                                <Link
                                  href={`/dashboard/test-sessions/${sessionId}/question/${question.id}/answer/${question.answerId}/review`}
                                >
                                  <Edit className="h-4 w-4 mr-1" />
                                  Review
                                </Link>
                              </Button>
                              <Button variant="outline" size="sm">
                                <Eye className="h-4 w-4 mr-1" />
                                Preview
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {session.reviewableQuestions.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    <CheckCircle2 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No questions require review</p>
                    <p className="text-sm">All questions in this session were automatically graded</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
