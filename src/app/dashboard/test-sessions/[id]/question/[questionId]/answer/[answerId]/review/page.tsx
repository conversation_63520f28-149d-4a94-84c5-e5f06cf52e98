"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import {
  CheckCircle2,
  XCircle,
  ArrowLeft,
  ThumbsUp,
  ThumbsDown,
  Clock,
  Calendar,
  FileText,
  ChevronLeft,
  ChevronRight,
  Mic,
  Video,
  Play,
  Pause,
  Volume2,
  Download,
  Eye,
} from "lucide-react"

// Mock data for the answer review
const mockAnswer = {
  id: "ans-123",
  questionId: "q-456",
  testSessionId: "session-789",
  student: {
    id: "student-001",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "https://github.com/shadcn.png",
  },
  type: "freetext", // freetext, voice, video
  content: {
    text: "The key differences between REST and GraphQL APIs are:\n\n1. Data fetching: REST requires multiple endpoints for different resources, while GraphQL uses a single endpoint.\n\n2. Over-fetching/under-fetching: REST often returns more data than needed or requires multiple requests, while GraphQL allows clients to specify exactly what data they need.\n\n3. Versioning: REST typically requires explicit versioning in the URL, while GraphQL can evolve without breaking existing queries.\n\n4. Caching: REST has built-in caching mechanisms, while GraphQL requires additional implementation for efficient caching.",
    audioUrl: "/placeholder-audio.mp3",
    videoUrl: "/placeholder-video.mp4",
    duration: 180, // seconds for audio/video
  },
  submittedAt: "2023-11-15T14:30:00Z",
  status: "pending", // pending, correct, incorrect, partial
  score: null,
  feedback: "",
  timeSpent: 240, // seconds
}

const mockQuestion = {
  id: "q-456",
  testSessionId: "session-789",
  text: "Explain the key differences between REST and GraphQL APIs. Include at least three points of comparison. You may provide your answer in text, voice recording, or video format.",
  type: "freetext", // freetext, voice, video, multimodal
  maxScore: 10,
  rubric: [
    "Correctly identifies data fetching differences (2 points)",
    "Explains over-fetching/under-fetching issues (2 points)",
    "Discusses versioning approaches (2 points)",
    "Compares caching mechanisms (2 points)",
    "Provides clear examples and communication (2 points)",
  ],
  expectedAnswer:
    "REST and GraphQL differ in several key ways:\n\n1. Data fetching: REST uses multiple endpoints for different resources, while GraphQL uses a single endpoint where clients can specify their data requirements.\n\n2. Over-fetching/under-fetching: REST often returns more data than needed (over-fetching) or requires multiple requests to get all needed data (under-fetching). GraphQL allows clients to request exactly what they need in a single request.\n\n3. Versioning: REST APIs typically require explicit versioning in the URL or headers. GraphQL can evolve the schema without breaking existing queries by adding fields without removing old ones.\n\n4. Caching: REST has built-in HTTP caching mechanisms. GraphQL requires more complex caching implementation but offers more flexibility.\n\n5. Error handling: REST uses HTTP status codes, while GraphQL returns errors as part of the response payload alongside successful data.",
}

const mockTestSession = {
  id: "session-789",
  testTitle: "Web Development Fundamentals Assessment",
  studentName: "Alex Johnson",
  totalQuestions: 15,
  pendingReviews: 8,
  completedAt: "2023-11-15T15:45:00Z",
}

// Navigation data for previous/next answers
const navigationData = [
  { id: "ans-122", student: "Emma Wilson", status: "pending", type: "voice" },
  { id: "ans-123", student: "Alex Johnson", status: "pending", type: "freetext" }, // Current
  { id: "ans-124", student: "Michael Brown", status: "pending", type: "video" },
  { id: "ans-125", student: "Sophia Davis", status: "pending", type: "freetext" },
]

function MediaPlayer({ type, content }: { type: string; content: any }) {
  const [isPlaying, setIsPlaying] = useState(false)

  if (type === "freetext") {
    return (
      <div className="bg-muted/50 p-4 rounded-md">
        <div className="flex items-center gap-2 mb-2">
          <FileText className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">Text Response</span>
        </div>
        <div className="whitespace-pre-line text-sm">{content.text}</div>
      </div>
    )
  }

  if (type === "voice") {
    return (
      <div className="bg-muted/50 p-4 rounded-md">
        <div className="flex items-center gap-2 mb-3">
          <Mic className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">Voice Recording</span>
          <Badge variant="outline" className="ml-auto">
            {Math.floor(content.duration / 60)}:{(content.duration % 60).toString().padStart(2, "0")}
          </Badge>
        </div>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsPlaying(!isPlaying)}
            className="flex items-center gap-2"
          >
            {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            {isPlaying ? "Pause" : "Play"}
          </Button>
          <div className="flex-1 bg-muted rounded-full h-2">
            <div className="bg-primary h-2 rounded-full w-1/3"></div>
          </div>
          <Button variant="ghost" size="sm">
            <Volume2 className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <Download className="h-4 w-4" />
          </Button>
        </div>
        {content.text && (
          <div className="mt-3 pt-3 border-t">
            <p className="text-xs text-muted-foreground mb-1">Auto-generated transcript:</p>
            <p className="text-sm italic">{content.text}</p>
          </div>
        )}
      </div>
    )
  }

  if (type === "video") {
    return (
      <div className="bg-muted/50 p-4 rounded-md">
        <div className="flex items-center gap-2 mb-3">
          <Video className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">Video Recording</span>
          <Badge variant="outline" className="ml-auto">
            {Math.floor(content.duration / 60)}:{(content.duration % 60).toString().padStart(2, "0")}
          </Badge>
        </div>
        <div className="relative bg-black rounded-md aspect-video mb-3">
          <div className="absolute inset-0 flex items-center justify-center">
            <Button
              variant="secondary"
              size="lg"
              onClick={() => setIsPlaying(!isPlaying)}
              className="flex items-center gap-2"
            >
              {isPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
              {isPlaying ? "Pause" : "Play Video"}
            </Button>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Eye className="h-4 w-4 mr-1" />
            Full Screen
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-1" />
            Download
          </Button>
        </div>
        {content.text && (
          <div className="mt-3 pt-3 border-t">
            <p className="text-xs text-muted-foreground mb-1">Auto-generated transcript:</p>
            <p className="text-sm italic">{content.text}</p>
          </div>
        )}
      </div>
    )
  }

  return null
}

function getTypeIcon(type: string) {
  switch (type) {
    case "voice":
      return <Mic className="h-4 w-4" />
    case "video":
      return <Video className="h-4 w-4" />
    default:
      return <FileText className="h-4 w-4" />
  }
}

export default function TestSessionAnswerReviewPage() {
  const params = useParams()
  const testSessionId = params.id
  const questionId = params.questionId
  const answerId = params.answerId

  const [answer, setAnswer] = useState<any>(mockAnswer)
  const [feedback, setFeedback] = useState("")
  const [score, setScore] = useState<number>(0)

  const handleSaveReview = () => {
    setAnswer({
      ...answer,
      status: score !== null ? (score >= 6 ? "correct" : score >= 4 ? "partial" : "incorrect") : "pending",
      score,
      feedback,
    })
    // In a real app, this would save to the backend
    alert("Review saved successfully!")
  }

  const currentIndex = navigationData.findIndex((item) => item.id === answerId)
  const prevAnswer = currentIndex > 0 ? navigationData[currentIndex - 1] : null
  const nextAnswer = currentIndex < navigationData.length - 1 ? navigationData[currentIndex + 1] : null

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col gap-6">
        {/* Header with navigation */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" asChild>
              <Link href={`/dashboard/test-sessions/${testSessionId}/review`}>
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Session Review
              </Link>
            </Button>
            <span className="text-muted-foreground">|</span>
            <h1 className="text-xl font-semibold">{mockTestSession.testTitle}</h1>
            <Badge variant="outline" className="ml-2">
              {mockTestSession.pendingReviews} pending reviews
            </Badge>
          </div>

          <div className="flex items-center gap-2">
            {prevAnswer && (
              <Button variant="outline" size="sm" asChild>
                <Link
                  href={`/dashboard/test-sessions/${testSessionId}/question/${questionId}/answer/${prevAnswer.id}/review`}
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Previous
                </Link>
              </Button>
            )}
            {nextAnswer && (
              <Button variant="outline" size="sm" asChild>
                <Link
                  href={`/dashboard/test-sessions/${testSessionId}/question/${questionId}/answer/${nextAnswer.id}/review`}
                >
                  Next
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Link>
              </Button>
            )}
          </div>
        </div>

        {/* Main content */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Left column - Student info and question */}
          <div className="md:col-span-1 space-y-6">
            {/* Student info card */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Student Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={answer.student.avatar || "/placeholder.svg"} alt={answer.student.name} />
                    <AvatarFallback>{answer.student.name.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">{answer.student.name}</p>
                    <p className="text-sm text-muted-foreground">{answer.student.email}</p>
                  </div>
                </div>

                <div className="space-y-2 pt-2">
                  <div className="flex items-center gap-2 text-sm">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span>Submitted: {new Date(answer.submittedAt).toLocaleString()}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span>
                      Time spent: {Math.floor(answer.timeSpent / 60)}m {answer.timeSpent % 60}s
                    </span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    {getTypeIcon(answer.type)}
                    <span>Response type: {answer.type.charAt(0).toUpperCase() + answer.type.slice(1)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* DashboardQuestion card */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">DashboardQuestion</CardTitle>
                <CardDescription>Requires human review for subjective assessment</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="whitespace-pre-line">{mockQuestion.text}</p>
              </CardContent>
              <CardFooter className="border-t pt-4 text-sm text-muted-foreground">
                Max score: {mockQuestion.maxScore} points
              </CardFooter>
            </Card>

            {/* Rubric card */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Grading Rubric</CardTitle>
                <CardDescription>Check off criteria as you review the response</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {mockQuestion.rubric.map((item, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <div className="h-5 w-5 flex-shrink-0 mt-0.5">
                        <input
                          type="checkbox"
                          id={`rubric-${index}`}
                          className="h-4 w-4"
                          onChange={(e) => {
                            const newScore = (score === null ? 0 : score) + (e.target.checked ? 2 : -2)
                            setScore(Math.max(0, Math.min(mockQuestion.maxScore, newScore)))
                          }}
                        />
                      </div>
                      <label htmlFor={`rubric-${index}`} className="text-sm">
                        {item}
                      </label>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* Right column - Answer and review */}
          <div className="md:col-span-2 space-y-6">
            {/* Answer tabs */}
            <Tabs defaultValue="student-answer">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="student-answer">Student Response</TabsTrigger>
                <TabsTrigger value="expected-answer">Expected Answer</TabsTrigger>
              </TabsList>
              <TabsContent value="student-answer">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      {getTypeIcon(answer.type)}
                      Student Response
                    </CardTitle>
                    <CardDescription>Review the student's {answer.type} response below</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <MediaPlayer type={answer.type} content={answer.content} />
                  </CardContent>
                </Card>
              </TabsContent>
              <TabsContent value="expected-answer">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Expected Answer</CardTitle>
                    <CardDescription>Reference this model answer when grading</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="bg-muted/50 p-4 rounded-md whitespace-pre-line">{mockQuestion.expectedAnswer}</div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>

            {/* Review form */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Human Review</CardTitle>
                <CardDescription>Provide feedback and score for this subjective response</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Score</label>
                  <div className="flex items-center gap-2">
                    <input
                      type="number"
                      min="0"
                      max={mockQuestion.maxScore}
                      value={score === null ? "" : score}
                      onChange={(e) =>
                        setScore(
                          e.target.value === ""
                            ? 0
                            : Math.min(mockQuestion.maxScore, Math.max(0, Number.parseInt(e.target.value))),
                        )
                      }
                      className="w-20 h-10 rounded-md border border-input bg-background px-3 py-2 text-sm"
                    />
                    <span className="text-sm text-muted-foreground">/ {mockQuestion.maxScore}</span>

                    <div className="ml-auto flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="gap-1"
                        onClick={() => {
                          setScore(mockQuestion.maxScore)
                          setFeedback(feedback || "Excellent response! All key points covered clearly.")
                        }}
                      >
                        <ThumbsUp className="h-4 w-4" />
                        Excellent
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="gap-1"
                        onClick={() => {
                          setScore(Math.floor(mockQuestion.maxScore * 0.6))
                          setFeedback(feedback || "Good response with room for improvement.")
                        }}
                      >
                        <ThumbsDown className="h-4 w-4" />
                        Needs Work
                      </Button>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Detailed Feedback</label>
                  <Textarea
                    placeholder="Provide detailed feedback on the student's response, including strengths and areas for improvement..."
                    className="min-h-32"
                    value={feedback}
                    onChange={(e) => setFeedback(e.target.value)}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Consider communication clarity, content accuracy, and completeness in your feedback.
                  </p>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between border-t pt-4">
                <div className="flex items-center">
                  {answer.status !== "pending" && (
                    <div className="flex items-center gap-1">
                      {answer.status === "correct" ? (
                        <CheckCircle2 className="h-4 w-4 text-green-500" />
                      ) : answer.status === "partial" ? (
                        <div className="h-4 w-4 rounded-full bg-yellow-500" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-500" />
                      )}
                      <span className="text-sm font-medium capitalize">{answer.status}</span>
                      {answer.score !== null && (
                        <span className="text-sm text-muted-foreground ml-2">
                          Score: {answer.score}/{mockQuestion.maxScore}
                        </span>
                      )}
                    </div>
                  )}
                </div>
                <div className="flex gap-2">
                  <Button variant="outline">Skip for Now</Button>
                  <Button onClick={handleSaveReview}>Save Review</Button>
                </div>
              </CardFooter>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
