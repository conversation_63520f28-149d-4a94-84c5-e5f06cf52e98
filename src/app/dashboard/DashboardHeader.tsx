"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import {
  Menu,
  Search,
  Bell,
  User,
  Settings,
  LogOut,
  ArrowDownCircleIcon,
  HomeIcon,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { useLocalization } from "@/src/hooks/useLocalization/client"
import en from "./locales/en.json"
import ja from "./locales/ja.json"
import { useLocalizationContext } from "@/src/hooks/useLocalization/localization-context"

const languageOptions = [
  { code: "en", label: "English", flag: "🇺🇸" },
  { code: "ja", label: "日本語", flag: "🇯🇵" },
]

export function DashboardHeader({ handleLogout }: { handleLogout: () => void }) {
  const { locale, setLocale } = useLocalizationContext();

  const { t } = useLocalization("dashboard-layout", { en, ja })

  function changeLocale(newLocale: string) {
    if (newLocale === locale) return;

    setLocale(newLocale)
  }

  const links = [
    { title: t("dashboard"), href: "/dashboard" },
    { title: t("analytics"), href: "/dashboard/analytics" },
  ]

  return (
    <header className="z-10 sticky top-0 flex bg-sp-background h-14 items-center gap-4 justify-between px-6">
      <NavSheet links={links} />
      <Breadcrumbs title={t("dashboard")} />
      <Actions
        t={t}
        locale={locale}
        onChangeLocale={changeLocale}
        handleLogout={handleLogout}
      />
    </header>
  )
}

// --- Components below ---

function NavSheet({ links }: { links: { title: string; href: string }[] }) {
  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="outline" size="icon" className="md:hidden mr-2">
          <Menu className="h-4 w-4" />
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="w-64 p-0 pt-10">
        <nav>
          <ul>
            {links.map((link) => (
              <li key={link.href}>
                <Link href={link.href} className="block p-2 hover:bg-gray-200">
                  {link.title}
                </Link>
              </li>
            ))}
          </ul>
        </nav>
      </SheetContent>
    </Sheet>
  )
}

function Breadcrumbs({ title }: { title: string }) {
  return (
    <div className="hidden md:flex w-full">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/">
              <HomeIcon className="w-5 h-5 text-sp-neutral-600" />
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{title}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    </div>
  )
}

function Actions({
  t,
  locale,
  onChangeLocale,
  handleLogout,
}: {
  t: (key: string) => string
  locale: string
  onChangeLocale: (locale: string) => void
  handleLogout: () => void
}) {
  return (
    <div className="flex flex-row items-center gap-4 w-full justify-end">
      <SearchButton t={t} />
      <NotificationsDropdown t={t} />
      <LanguageDropdown locale={locale} onChangeLocale={onChangeLocale} />
      <UserDropdown t={t} handleLogout={handleLogout} />
    </div>
  )
}

function SearchButton({ t }: { t: (key: string) => string }) {
  return (
    <Button
      variant="outline"
      className="relative bg-[#F0EEF6] w-full justify-start"
      onClick={() =>
        document.dispatchEvent(
          new KeyboardEvent("keydown", { key: "k", metaKey: true, bubbles: true }),
        )
      }
    >
      <Search className="ml-4 mr-2 h-4 w-4" />
      <span className="mr-1 text-body-5 font-inter text-sp-neutral-600">{t("search")}</span>
      <kbd className="ml-auto mr-4 inline-flex h-5 items-center gap-1 border bg-muted px-1.5 text-[10px] text-muted-foreground">
        <span className="text-xs">⌘</span>K
      </kbd>
    </Button>
  )
}

function NotificationsDropdown({ t }: { t: (key: string) => string }) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon" className="relative bg-[#F0EEF6] w-24">
          <Bell className="h-4 w-4" />
          <span className="absolute -top-1 -right-1 h-4 w-fit px-2 rounded-full bg-red-500 text-[10px] flex items-center justify-center text-white">
            30
          </span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-80" align="end">
        <div className="flex items-center justify-between px-4 py-2 border-b">
          <h3 className="font-medium">{t("notifications")}</h3>
          <Button variant="ghost" size="sm" className="text-xs">
            {t("markAllAsRead")}
          </Button>
        </div>
        <div className="max-h-[300px] overflow-y-auto">
          <div className="px-4 py-3 border-b">
            <p className="text-sm font-medium">{t("testCompleted")}</p>
            <p className="text-xs">{t("youScored")} 85%</p>
            <p className="text-xs text-muted-foreground mt-1">2h ago</p>
          </div>
        </div>
        <div className="p-2 border-t text-center">
          <Button variant="ghost" size="sm" className="w-full text-xs" asChild>
            <Link href="/dashboard/notifications">{t("viewAll")}</Link>
          </Button>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

function UserDropdown({
  t,
  handleLogout,
}: {
  t: (key: string) => string
  handleLogout: () => void
}) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="flex flow-row items-center bg-sp-neutral-100 gap-2 border rounded-full p-1 cursor-pointer w-96">
          <Avatar className="h-8 w-8">
            <AvatarImage src="https://github.com/shadcn.png" alt="@shadcn" />
            <AvatarFallback>CN</AvatarFallback>
          </Avatar>
          <div className="flex flex-col items-start">
            <p className="font-clash text-body-4 text-sp-secondary-1">Achmad Q</p>
            <p className="text-[10px] text-sp-neutral-600">{t("freeAccount")}</p>
          </div>
          <div className="flex-grow"></div>
          <ArrowDownCircleIcon className="w-6 h-6 text-sp-neutral-600" />
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56">
        <DropdownMenuLabel className="font-normal">
          <span className="text-sm font-medium leading-none">Achmad Q</span>
          <span className="text-sm"><EMAIL></span>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem>
          <User className="mr-2 h-4 w-4" />
          <span>{t("profile")}</span>
        </DropdownMenuItem>
        <DropdownMenuItem>
          <Settings className="mr-2 h-4 w-4" />
          <span>{t("settings")}</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleLogout}>
          <LogOut className="mr-2 h-4 w-4" />
          <span>{t("logout")}</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

function LanguageDropdown({
  locale,
  onChangeLocale,
}: {
  locale: string
  onChangeLocale: (locale: string) => void
}) {
  const current = languageOptions.find((l) => l.code === locale)

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm">
          <span className="mr-1 text-3xl">{current?.flag || "🇺🇳"}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" side="bottom" className="w-36">
        {languageOptions.map(({ code, label, flag }) => (
          <DropdownMenuItem
            key={code}
            onClick={() => onChangeLocale(code)}
            className={code === locale ? "font-bold" : ""}
          >
            <span className="mr-2">{flag}</span>
            {label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}