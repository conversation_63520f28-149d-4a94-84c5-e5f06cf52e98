"use client";

import React, { SetStateAction, useState } from "react";
import { X, Save } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { QuestionList } from "@/components/question-list";
import { QuestionEditor } from "@/components/question-editor";
import { QuestionPreview } from "./others";
import { pluginRegistry } from "@/components/QuestionDisplay";
import { MultipleChoiceQuestion, Question } from "@/src/lib/repositories/questions/interface";
import { useLocalization } from "@/src/localization/functions/client";
import { locales } from "./locales";

interface Props {
  questions: Question[];
  selectedQuestion: number;
  setSelectedQuestion: React.Dispatch<React.SetStateAction<number>>;
  handleQuestionChange: (updated: Partial<Question>) => void;
  handleSaveQuestion: () => Promise<void>;
  showSuccessMessage: boolean;
  successMessage: string;
  setShowSuccessMessage: React.Dispatch<React.SetStateAction<boolean>>;
  showMessage: (message: string) => void;
  setQuestions: React.Dispatch<SetStateAction<Question[]>>
}

export default function QuestionEditorPageClient({
  questions,
  selectedQuestion,
  setSelectedQuestion,
  handleQuestionChange,
  handleSaveQuestion,
  showSuccessMessage,
  successMessage,
  setShowSuccessMessage,
  showMessage,
  setQuestions,
}: Props) {
  const { t } = useLocalization("question-editor", locales);

  const [tab, setTab] = useState<"preview" | "editor">("editor");
  const [leftSidebarVisible, setLeftSidebarVisible] = useState(false);
  const [showBulkDialog, setShowBulkDialog] = useState(false);

  const currentQuestion = questions[selectedQuestion] ?? null;

  const handleUpdateQuestion = () => {
    showMessage(t("questionManagement.updateSuccessful"));
  };

  const onBulkInsert = () => {
    setShowBulkDialog(true);
  };

  const onExport = () => {
    const data = JSON.stringify(questions, null, 2);
    const blob = new Blob([data], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `questions-${formatDateForFilename()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const onDeleteAll = () => {
    const sampleQuestion: Partial<MultipleChoiceQuestion> = {
      type: "multipleChoice",
      question: "",
      difficulty: "easy",
      tags: [],
      options: [],
      correctAnswer: "",
      isActive: true,
    };

    if (confirm(t("questionManagement.confirmDeleteAll"))) {
      setQuestions([sampleQuestion as MultipleChoiceQuestion]);
      setSelectedQuestion(0);
    }
  };

  const handleBulkInsert = (importedQuestions: Question[]) => {
    setQuestions([...questions, ...importedQuestions]);
    setShowBulkDialog(false);
    showMessage(t("questionManagement.bulkInsertSuccessful"));
  };

  return (
    <div className="min-h-screen flex flex-col bg-white">
      {/* HEADER */}
      <div className="border-b p-4 flex justify-between items-center">
        <h1 className="text-xl font-bold">{t("questionManagement.title")}</h1>
        <div className="flex gap-2">
          <Button onClick={onExport}>{t("questionManagement.exportJSON")}</Button>
          <Button onClick={onBulkInsert}>{t("questionManagement.showBulkInsert")}</Button>
          <Button onClick={onDeleteAll}>{t("questionManagement.deleteAllQuestions")}</Button>
          <Button onClick={handleSaveQuestion}>
            <Save className="mr-2 h-4 w-4" /> {t("questionManagement.save")}
          </Button>
          <Button variant={tab === "editor" ? "default" : "outline"} onClick={() => setTab("editor")}>
            {t("questionManagement.edit")}
          </Button>
          <Button variant={tab === "preview" ? "default" : "outline"} onClick={() => setTab("preview")}>
            {t("questionManagement.preview")}
          </Button>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* LEFT SIDEBAR */}
        <div className={`transition-all duration-300 ${leftSidebarVisible ? "w-80" : "w-0"} border-r`}>
          {leftSidebarVisible && (
            <QuestionList
              questions={questions}
              selectedQuestion={selectedQuestion}
              onSelectQuestion={setSelectedQuestion}
              onDeleteQuestion={(index) => {
                const updated = [...questions];
                updated.splice(index, 1);
                setQuestions(updated);
                setSelectedQuestion(Math.max(0, updated.length - 1));
              }}
            />
          )}
        </div>

        {/* MAIN PANEL */}
        <div className="flex-1 overflow-y-auto p-4">
          {tab === "editor" && currentQuestion && (
            <QuestionEditor
              question={currentQuestion}
              onChange={(updatedOld) => handleQuestionChange(updatedOld)}
              onUpdate={handleUpdateQuestion}
              pluginRegistry={pluginRegistry}
            />
          )}
          {tab === "preview" && currentQuestion && <QuestionPreview question={currentQuestion} />}
          {!currentQuestion && <p className="text-gray-500">{t("questionManagement.noQuestionsAvailable")}</p>}
        </div>
      </div>

      {/* BULK DIALOG */}
      {/* {showBulkDialog && (
        <BulkInsertDialog
          onClose={() => setShowBulkDialog(false)}
          onSubmit={handleBulkInsert}
        />
      )} */}

      {showSuccessMessage && (
        <div className="fixed bottom-4 right-4">
          <Alert className="bg-green-600 text-white">
            <AlertDescription className="flex justify-between items-center">
              {successMessage}
              <Button
                onClick={() => setShowSuccessMessage(false)}
                className="text-white bg-transparent hover:bg-transparent"
              >
                <X className="h-4 w-4" />
              </Button>
            </AlertDescription>
          </Alert>
        </div>
      )}
    </div>
  );
}

// Helpers
function formatDateForFilename() {
  return new Date().toISOString().split("T")[0];
}
