"use client";

import { useState } from "react";
import { QuestionsAPI } from "@/src/services/questionsApi";
import { Question } from "@/src/lib/repositories/questions/interface";
import { useLocalization } from "@/src/localization/functions/client";
import { locales } from "../../locales";
import QuestionEditorPageClient from "../../client";

export default function QuestionManagementPage({ question }: { question: Question }) {
  const { t } = useLocalization("question-editor", locales);

  const [questions, setQuestions] = useState<Question[]>([question]);
  const [selectedQuestion, setSelectedQuestion] = useState(0);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");

  const showMessage = (message: string) => {
    setSuccessMessage(message);
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
  };

  const handleQuestionChange = (updated: Partial<Question>) => {
    const updatedList = [...questions];
    updatedList[selectedQuestion] = {
      ...updatedList[selectedQuestion],
      ...updated,
    } as Question;
    setQuestions(updatedList);
  };

  console.log("Current question:", questions[selectedQuestion]);
  
  const handleSaveQuestion = async () => {
    const question = questions[selectedQuestion];
    try {
      if (!question.id) {
        alert(t("questionManagement.invalidQuestionId") || "Invalid question ID.");
        return;
      }
      // Only update existing questions
      const response = await QuestionsAPI.UpdateQuestion(question.id, {
        ...question,
        updatedBy: 'current-user-id', // This should come from auth context
      }).request();
      showMessage(t("questionManagement.updateSuccessful"));

      const updatedList = [...questions];
      updatedList[selectedQuestion] = response;
      setQuestions(updatedList);
    } catch (err) {
      console.error("Update question failed:", err);
      alert(t("questionManagement.saveFailed") || "Failed to update question.");
    }
  };

  return (
    <QuestionEditorPageClient
      questions={questions}
      selectedQuestion={selectedQuestion}
      setSelectedQuestion={setSelectedQuestion}
      handleQuestionChange={handleQuestionChange}
      handleSaveQuestion={handleSaveQuestion}
      showSuccessMessage={showSuccessMessage}
      successMessage={successMessage}
      setShowSuccessMessage={setShowSuccessMessage}
      showMessage={showMessage}
      setQuestions={setQuestions}
    />
  );
}
