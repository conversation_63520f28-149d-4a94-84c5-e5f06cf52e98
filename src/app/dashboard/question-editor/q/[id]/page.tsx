import { QuestionBusinessLogicInstance } from "@/src/lib/repositories/questions";
import QuestionManagementPage from "./client";
import NotFoundLayout from "@/components/not-found-redirect";

export default async function Page({ params }: { params: { id: string } }) {
  const question = await QuestionBusinessLogicInstance.getById(params.id);

  if (!question) {
    return (
      <NotFoundLayout
        title="Question Not Found"
        description="The question you're looking for doesn't exist or has been removed."
        redirectHref="/dashboard"
        redirectLabel="Go to Dashboard"
      />
    );
  }

  return <QuestionManagementPage question={question} />;
}
