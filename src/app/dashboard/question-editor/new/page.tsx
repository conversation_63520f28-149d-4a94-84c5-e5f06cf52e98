"use client";

import { useState } from "react";
import { QuestionsAPI } from "@/src/services/questionsApi";
import { MultipleChoiceQuestion, Question } from "@/src/lib/repositories/questions/interface";
import { useLocalization } from "@/src/localization/functions/client";
import { locales } from "../../locales";
import QuestionEditorPageClient from "../client";
import { useRouter } from "next/navigation";

export default function QuestionManagementPage() {
    const { t } = useLocalization("question-editor", locales);

    const router = useRouter();

    const sampleQuestion: Partial<MultipleChoiceQuestion> = {
        type: "multipleChoice",
        question: "What is the capital of France?",
        difficulty: "easy",
        internal: {},
        createdAt: new Date(),
        updatedAt: new Date(),
        tags: ["geography", "basics"],
        options: [
            { id: "a", text: "Paris" },
            { id: "b", text: "Berlin" },
            { id: "c", text: "Madrid" },
            { id: "d", text: "Rome" },
        ],
        correctAnswers: ["a"],
    };

    const [questions, setQuestions] = useState<Partial<Question>[]>([sampleQuestion]);
    const [selectedQuestion, setSelectedQuestion] = useState(0);
    const [showSuccessMessage, setShowSuccessMessage] = useState(false);
    const [successMessage, setSuccessMessage] = useState("");

    const showMessage = (message: string) => {
        setSuccessMessage(message);
        setShowSuccessMessage(true);
        setTimeout(() => setShowSuccessMessage(false), 3000);
    };

    const handleQuestionChange = (updated: Partial<Question>) => {
        const updatedList = [...questions];
        updatedList[selectedQuestion] = {
            ...updatedList[selectedQuestion],
            ...updated,
        } as Question;
        setQuestions(updatedList);
    };

    const handleSaveQuestion = async () => {
        const question = questions[selectedQuestion];
        try {
            // Always create new question

            const response = await QuestionsAPI.CreateQuestion({
                ...question,
                createdBy: "currentUserId", // Replace with actual user ID
            }).request();
            showMessage(t("questionManagement.createSuccessful"));

            router.push(`/dashboard/question-editor/q/${response.id}`)
        } catch (err) {
            console.error("Save question failed:", err);
            alert(t("questionManagement.saveFailed") || "Failed to save question.");
        }
    };

    return (
        <QuestionEditorPageClient
            //@ts-ignore
            questions={questions}
            selectedQuestion={selectedQuestion}
            setSelectedQuestion={setSelectedQuestion}
            handleQuestionChange={handleQuestionChange}
            handleSaveQuestion={handleSaveQuestion}
            showSuccessMessage={showSuccessMessage}
            successMessage={successMessage}
            setShowSuccessMessage={setShowSuccessMessage}
            showMessage={showMessage}
            //@ts-ignore
            setQuestions={setQuestions}
        />
    );
}
