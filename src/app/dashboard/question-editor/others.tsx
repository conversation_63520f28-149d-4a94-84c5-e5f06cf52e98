"use client";

import type React from "react";

import { useState } from "react";
import {
  X,
  ChevronLeft,
  ChevronRight,
  Settings,
  List,
  Save,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { QuestionList } from "@/components/question-list";
import { QuestionEditor } from "@/components/question-editor";
import { BulkInsertDialog } from "@/components/bulk-insert-dialog";
import { ConfirmDialog } from "@/components/confirm-dialog";

import { useLocalization } from "@/src/localization/functions/client"
import en from "./locales/en.json"
import { pluginRegistry } from "@/components/QuestionDisplay";
import { Question } from "@/src/lib/repositories/questions/interface";
import { mapQuestionToTestSessionQuestion } from "@/src/lib/repositories/test-session/mapQuestionToTestSessionQuestion";
import { scoreAnswer } from "@/src/lib/repositories/test-session/scoreAnswer";
import { QuestionsAPI } from "@/src/services/questionsApi";

type LeftSidebarProps = {
  visible: boolean;
  questions: Question[];
  selectedIndex: number;
  onSelect: (index: number) => void;
  onDelete: (index: number) => void;
  toggle: () => void;
};

export function LeftSidebar({
  visible,
  questions,
  selectedIndex,
  onSelect,
  onDelete,
  toggle,
  t,
}: LeftSidebarProps & { t: (key: string) => string }) {
  return (
    <div className={`transition-all duration-300 ease-in-out ${visible ? "w-80" : "w-0"} border-r relative overflow-hidden`}>
      {/* Toggle button - visible even when sidebar is hidden */}
      <button
        onClick={toggle}
        className={`absolute top-2 right-[-12px] z-10 bg-white border border-gray-300 rounded-full w-6 h-6 flex items-center justify-center shadow ${visible ? "" : "right-[-12px]"
          }`}
        title={visible ? t("hideSidebar") : t("showSidebar")}
      >
        {visible ? "<" : ">"}
      </button>

      {/* Render content only if visible */}
      {visible && (
        <div className="h-full overflow-y-auto">
          <QuestionList
            questions={questions}
            selectedQuestion={selectedIndex}
            onSelectQuestion={onSelect}
            onDeleteQuestion={onDelete}
          />
        </div>
      )}
    </div>
  );
}


export function QuestionPreview({ question }: { question: Question | null }) {
  const { t } = useLocalization("question-editor", { en })
  const [answer, setAnswer] = useState<any>([])
  const [scoringResult, setScoringResult] = useState<any>(null)

  if (!question) {
    return (
      <div className="flex-1 overflow-y-auto p-4 text-sm text-gray-600">
        {t("questionManagement.noQuestionsAvailable")}
      </div>
    )
  }

  const testSessionQuestion = mapQuestionToTestSessionQuestion(question, "preview")
  const QuestionRenderer = pluginRegistry[testSessionQuestion.type]

  if (!QuestionRenderer) {
    return (
      <div className="flex-1 overflow-y-auto p-4 text-sm text-gray-600">
        {t("questionManagement.unsupportedQuestionType", {
          type: testSessionQuestion.type,
        })}
      </div>
    )
  }

  return (
    <div className="flex-1 overflow-y-auto p-4">
      <div className="p-4 border rounded-md space-y-4">
        <QuestionRenderer
          key={testSessionQuestion.id}
          question={testSessionQuestion}
          answer={answer}
          onAnswer={(submittedAnswer) => {
            setAnswer(submittedAnswer)
            setScoringResult(scoreAnswer(submittedAnswer, testSessionQuestion))
          }}
        />

        {scoringResult !== null && (
          <div
            className={`text-sm font-medium ${scoringResult ? "text-green-600" : "text-red-600"
              }`}
          >
            {JSON.stringify(scoringResult)}
          </div>
        )}
      </div>
    </div>
  )
}