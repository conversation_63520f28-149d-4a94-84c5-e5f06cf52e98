"use client";

import React from "react";
import {
  <PERSON><PERSON>erP<PERSON>,
  <PERSON><PERSON><PERSON>,
  Lightbulb,
  Hourglass,
  Folder,
  Clock,
  Tag,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, <PERSON>H<PERSON>er, CardT<PERSON>le, CardContent } from "@/components/ui/card";
import { useLocalization } from "@/src/localization/functions/client";
import { locales } from "../locales";

const sampleGroups = [
  {
    id: "sample-1",
    name: "HTML & CSS Basics",
    description: "Fundamental questions about HTML structure and CSS styling.",
    updatedAt: new Date("2024-06-01T10:00:00Z"),
    tags: ["HTML", "CSS", "Beginner"],
  },
  {
    id: "sample-2",
    name: "JavaScript Logic",
    description: "Core questions on conditions, loops, and logic building.",
    updatedAt: new Date("2024-06-05T14:30:00Z"),
    tags: ["JavaScript", "Logic", "Intermediate"],
  },
  {
    id: "sample-3",
    name: "React Components",
    description: "Test knowledge of React components, props, and hooks.",
    updatedAt: new Date("2024-06-10T08:45:00Z"),
    tags: ["React", "Hooks", "Frontend"],
  },
];

function formatDate(date: Date): string {
  return new Date(date).toLocaleDateString(undefined, {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
}

export default function QuestionGroup() {
  const { t } = useLocalization("question-group", locales);

  return (
    <div className="min-h-screen bg-muted/50 p-6 flex flex-col items-center">
      <Card className="max-w-2xl w-full p-8 shadow-xl bg-white mb-10">
        <div className="flex flex-col items-center text-center">
          <div className="bg-primary/10 text-primary rounded-full p-4 mb-4">
            <FolderPlus className="w-8 h-8" />
          </div>
          <h1 className="text-2xl font-bold mb-2">{t("heading")}</h1>
          <p className="text-muted-foreground mb-6 max-w-md">
            {t("description")}
          </p>

          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 w-full mb-6">
            <div className="flex flex-col items-center bg-muted rounded-lg p-4">
              <Lightbulb className="h-6 w-6 text-yellow-500 mb-2" />
              <span className="font-medium">{t("feature.organize.title")}</span>
              <p className="text-xs text-muted-foreground mt-1 text-center">
                {t("feature.organize.desc")}
              </p>
            </div>
            <div className="flex flex-col items-center bg-muted rounded-lg p-4">
              <Sparkles className="h-6 w-6 text-purple-500 mb-2" />
              <span className="font-medium">{t("feature.reuse.title")}</span>
              <p className="text-xs text-muted-foreground mt-1 text-center">
                {t("feature.reuse.desc")}
              </p>
            </div>
            <div className="flex flex-col items-center bg-muted rounded-lg p-4">
              <Hourglass className="h-6 w-6 text-blue-500 mb-2" />
              <span className="font-medium">{t("feature.coming.title")}</span>
              <p className="text-xs text-muted-foreground mt-1 text-center">
                {t("feature.coming.desc")}
              </p>
            </div>
          </div>

          <Button disabled className="opacity-70 cursor-not-allowed">
            🚧 {t("button.comingSoon")}
          </Button>
          <p className="text-sm text-muted-foreground mt-3">{t("footer.note")}</p>
        </div>
      </Card>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl w-full">
        {sampleGroups.map((group) => (
          <Card key={group.id} className="bg-white shadow-sm border">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Folder className="h-5 w-5 text-primary" />
                  <CardTitle className="text-base font-medium line-clamp-1">{group.name}</CardTitle>
                </div>
                <Badge variant="outline" className="text-xs text-blue-700 border-blue-200">
                  {t("sampleGroup")}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="text-sm text-muted-foreground space-y-3">
              <p>{group.description}</p>
              <div className="flex items-center gap-2 text-xs text-gray-500">
                <Clock className="h-3 w-3" />
                {formatDate(group.updatedAt)}
              </div>
              <div className="flex flex-wrap gap-2 pt-2">
                {group.tags.map((tag) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    <Tag className="h-3 w-3 mr-1" />
                    {tag}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
