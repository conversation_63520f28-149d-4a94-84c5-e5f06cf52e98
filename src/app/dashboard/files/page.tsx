/* eslint-disable @next/next/no-img-element */
"use client";

import type React from "react";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Header } from "@/components/mvp/header";
import { useAuth } from "@/src/lib/auth-utils";
import type { MediaFile } from "@/src/lib/types";
import {
  Copy,
  Trash2,
  FileImage,
  FileAudio,
  FileVideo,
  File,
} from "lucide-react";

export default function FilesPage() {
  const router = useRouter();
  const { getCurrentUser, isChecked } = useAuth();
  const [files, setFiles] = useState<MediaFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("all");
  const [copied, setCopied] = useState<string | null>(null);

  useEffect(() => {
    if (isChecked) {
      setLoading(false);
    }
  }, [isChecked]);

  useEffect(() => {
    if (!loading) {
      const user = getCurrentUser();
      if (user) {
        // Load files from localStorage
        const storedFiles = localStorage.getItem("mediaFiles");
        if (storedFiles) {
          const parsedFiles = JSON.parse(storedFiles);
          // Filter files by current user
          setFiles(
            parsedFiles.filter((file: MediaFile) => file.createdBy === user.id)
          );
        }
      }
    }
  }, [getCurrentUser, loading]);

  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const user = getCurrentUser();
    if (!user) return;

    const files = event.target.files;
    if (!files || files.length === 0) return;

    setUploading(true);

    try {
      const newFiles: MediaFile[] = [];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const reader = new FileReader();

        // Convert file to base64
        const base64Promise = new Promise<string>((resolve) => {
          reader.onload = () => {
            const base64 = reader.result as string;
            resolve(base64);
          };
          reader.readAsDataURL(file);
        });

        const base64 = await base64Promise;

        const newFile: MediaFile = {
          id: Date.now().toString() + i,
          name: file.name,
          type: file.type,
          url: base64,
          createdBy: user.id,
          createdAt: Date.now(),
        };

        newFiles.push(newFile);
      }

      // Update state and localStorage
      const updatedFiles: MediaFile[] = [];
      for (let i = 0; i < files.length; i++) {
        // updatedFiles.push(files[i]);
      }
      for (let i = 0; i < newFiles.length; i++) {
        updatedFiles.push(newFiles[i]);
      }
      setFiles(updatedFiles);

      const allFiles = JSON.parse(localStorage.getItem("mediaFiles") || "[]");
      localStorage.setItem(
        "mediaFiles",
        JSON.stringify([...allFiles, ...newFiles])
      );

      // Reset file input
      event.target.value = "";
    } catch (error) {
      console.error("Error uploading files:", error);
      alert("Error uploading files. Please try again.");
    } finally {
      setUploading(false);
    }
  };

  const handleDeleteFile = (id: string) => {
    if (!confirm("Are you sure you want to delete this file?")) return;

    const updatedFiles = files.filter((file) => file.id !== id);
    setFiles(updatedFiles);

    // Update localStorage
    const allFiles = JSON.parse(localStorage.getItem("mediaFiles") || "[]");
    localStorage.setItem(
      "mediaFiles",
      JSON.stringify(allFiles.filter((file: MediaFile) => file.id !== id))
    );
  };

  const copyToClipboard = (url: string, id: string) => {
    navigator.clipboard.writeText(url);
    setCopied(id);
    setTimeout(() => setCopied(null), 2000);
  };

  const filteredFiles =
    activeTab === "all"
      ? files
      : files.filter((file) => {
          if (activeTab === "images") return file.type.startsWith("image/");
          if (activeTab === "audio") return file.type.startsWith("audio/");
          if (activeTab === "video") return file.type.startsWith("video/");
          return true;
        });

  const getFileIcon = (type: string) => {
    if (type.startsWith("image/")) return <FileImage className="w-6 h-6" />;
    if (type.startsWith("audio/")) return <FileAudio className="w-6 h-6" />;
    if (type.startsWith("video/")) return <FileVideo className="w-6 h-6" />;
    return <File className="w-6 h-6" />;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p>Loading...</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      <main className="container p-4 mx-auto">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold">Media Files</h1>
          <div>
            <Input
              type="file"
              id="file-upload"
              className="hidden"
              multiple
              onChange={handleFileUpload}
              accept="image/*,audio/*,video/*"
            />
            <Label htmlFor="file-upload" asChild>
              <Button disabled={uploading}>
                {uploading ? "Uploading..." : "Upload Files"}
              </Button>
            </Label>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
          <TabsList>
            <TabsTrigger value="all">All Files</TabsTrigger>
            <TabsTrigger value="images">Images</TabsTrigger>
            <TabsTrigger value="audio">Audio</TabsTrigger>
            <TabsTrigger value="video">Video</TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab}>
            {filteredFiles.length === 0 ? (
              <div className="flex flex-col items-center justify-center p-8 text-center border rounded-lg bg-background">
                <h3 className="mb-2 text-xl font-medium">No files yet</h3>
                <p className="mb-4 text-muted-foreground">
                  Upload files to use in your questions
                </p>
                <Label htmlFor="file-upload" asChild>
                  <Button>Upload Files</Button>
                </Label>
              </div>
            ) : (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {filteredFiles.map((file) => (
                  <Card key={file.id} className="overflow-hidden">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg flex items-center gap-2">
                        {getFileIcon(file.type)}
                        <span className="truncate">{file.name}</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="p-4">
                      {file.type.startsWith("image/") ? (
                        <div className="relative aspect-video bg-gray-100 dark:bg-gray-800 rounded-md overflow-hidden">
                          <img
                            src={file.url || "/placeholder.svg"}
                            alt={file.name}
                            className="object-contain w-full h-full"
                          />
                        </div>
                      ) : file.type.startsWith("audio/") ? (
                        <div className="p-4 bg-gray-100 dark:bg-gray-800 rounded-md">
                          <audio controls className="w-full">
                            <source src={file.url} type={file.type} />
                            Your browser does not support the audio element.
                          </audio>
                        </div>
                      ) : file.type.startsWith("video/") ? (
                        <div className="relative aspect-video bg-gray-100 dark:bg-gray-800 rounded-md overflow-hidden">
                          <video controls className="w-full h-full">
                            <source src={file.url} type={file.type} />
                            Your browser does not support the video element.
                          </video>
                        </div>
                      ) : (
                        <div className="p-4 bg-gray-100 dark:bg-gray-800 rounded-md text-center">
                          <p>File preview not available</p>
                        </div>
                      )}
                    </CardContent>
                    <CardFooter className="flex justify-between border-t bg-muted/50 p-3">
                      <Button
                        variant="outline"
                        size="sm"
                        className="gap-1"
                        onClick={() => copyToClipboard(file.url, file.id)}
                      >
                        <Copy className="w-4 h-4" />
                        {copied === file.id ? "Copied!" : "Copy URL"}
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        className="gap-1"
                        onClick={() => handleDeleteFile(file.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                        Delete
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
}
