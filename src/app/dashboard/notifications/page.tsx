import { repo } from "@/src/lib/repositories/notifications"
import { NotificationResponse } from "@/src/lib/repositories/notifications/NotificationsRepository"
import NotificationsClient from "./client"

export default async function NotificationsPage() {
  // Fetch initial notifications data server-side
  const initialData: NotificationResponse = await repo.getAll({
    page: 1,
    pageSize: 50 // Load more initially for better UX
  })

  return (
    <NotificationsClient initialData={initialData} />
  )
}

