"use client"

import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON>, useState } from "react"
import {
  <PERSON>,
  Calendar,
  CheckCircle2,
  Trash2,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"

import { NotificationResponse, NotificationType } from "@/src/lib/repositories/notifications/NotificationsRepository"
import { NotificationsAPI } from "@/src/services/notificationsApi"
import { NotificationItem } from "@/components/layout/notifications/NotificationItem"
import ProUpgradeModal from "@/components/layout/notifications/ProUpgradeModal"
import SettingNotif from "@/components/layout/notifications/SettingNotif"

import { useLocalization } from "@/src/localization/functions/client"
import en from "./locales/en.json"

export type TabConfig = {
  id: string;
  label: string;
};

const tabConfig: TabConfig[] = [
  { id: "test", label: "Tests" },
  { id: "course", label: "Courses" },
  { id: "badge", label: "Badges" },
];

type SwitchesState = {
  [key: string]: boolean;
};

interface NotificationsClientProps {
  initialData: NotificationResponse;
}

export default function NotificationsClient({ initialData }: NotificationsClientProps) {
  const [activeTab, setActiveTab] = useState("all")
  const [timeFilter, setTimeFilter] = useState("all-time")
  const [notificationList, setNotificationList] = useState<NotificationType[]>(initialData.items)
  const [loading, setLoading] = useState(false);
  const [totalItems, setTotalItems] = useState(initialData.total);

  const { t } = useLocalization("public-test", { en });

  const initialState: SwitchesState = tabConfig.reduce((acc, tab) => {
    acc[tab.id] = true;
    return acc;
  }, {} as SwitchesState);

  const [switches, setSwitches] = useState<SwitchesState>(initialState);

  const toggleSwitch = (key: string) => {
    setSwitches((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const fetchData = async () => {
    try {
      setLoading(true);
      const searchParams: any = {};

      if (activeTab === "unread") {
        searchParams.read = false;
      } else if (activeTab !== "all") {
        searchParams.type = activeTab;
      }

      if (timeFilter && timeFilter !== "all-time") {
        searchParams.time = timeFilter;
      }

      const data = await NotificationsAPI.Notifications({ filters: searchParams, page: 1 }).request();
      setNotificationList(data.items);
      setTotalItems(data.total)
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Fetch data when tab or filter changes (client-side updates)
  useEffect(() => {
    // Only fetch if not initial load (activeTab and timeFilter have changed from defaults)
    if (activeTab !== "all" || timeFilter !== "all-time") {
      fetchData();
    }
  }, [activeTab, timeFilter]);

  // Add the state for the upgrade modal
  const [showUpgradeModal, setShowUpgradeModal] = useState(false)

  // Group notifications by date
  const { today, thisWeek, earlier } = useMemo(() => {
    const now = new Date();
    const oneDay = 24 * 60 * 60 * 1000;

    const today = notificationList.filter((n) => {
      const diff = now.getTime() - new Date(n.date).getTime();
      return diff < oneDay;
    });

    const thisWeek = notificationList.filter((n) => {
      const diff = now.getTime() - new Date(n.date).getTime();
      return diff >= oneDay && diff < 7 * oneDay;
    });

    const earlier = notificationList.filter((n) => {
      const diff = now.getTime() - new Date(n.date).getTime();
      return diff >= 7 * oneDay;
    });

    return { today: notificationList, thisWeek, earlier }
  }, [notificationList])

  // Mark all as read
  const markAllAsRead = async () => {
    try {
      setLoading(true)
      const data = await NotificationsAPI.MarkAllAsRead().request();
      setNotificationList(data)
      fetchData()
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false)
    }
  }

  const bulkDelete = async () => {
    try {
      setLoading(true)
      await NotificationsAPI.DeleteAllRead().request();
      fetchData()
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false)
    }
  }

  // Mark single notification as read
  const markAsRead = async (id: string) => {
    try {
      setLoading(true)

      const newData: NotificationType = {
        ...notificationList.filter(a => a.id == id)[0],
        read: true
      }
      const data = await NotificationsAPI.UpdateNotification(id, newData).request();
      setNotificationList(notificationList.map((n) => (n.id === id ? data : n)))
      fetchData()
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false)
    }
  }

  // Delete notification
  const deleteNotification = async (id: string) => {
    try {
      setLoading(true)
      await NotificationsAPI.DeleteNotification(id).request();
      setNotificationList(notificationList.filter((n) => n.id !== id))
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false)
    }
  }

  return (
    <>
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row gap-4 md:items-center md:justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">
              {t('notifications.title')}
            </h2>
            <p className="text-muted-foreground">
              {t('notifications.description')}
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={markAllAsRead} disabled={loading}>
              {t('notifications.markAllAsRead')}
            </Button>
            <SettingNotif tabConfig={tabConfig} switches={switches} toggleSwitch={toggleSwitch} />
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-4 items-start">
          <div className="w-full md:w-3/4">
            <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="space-y-4">
              <div className="flex justify-between items-center">
                <TabsList>
                  <TabsTrigger value="all" className="relative">
                    {t('tabs.all')}
                    <Badge className="ml-1 bg-primary text-primary-foreground">{totalItems.all}</Badge>
                  </TabsTrigger>
                  <TabsTrigger value="unread" className="relative">
                    {t('tabs.unread')}
                    <Badge className="ml-1 bg-primary text-primary-foreground">
                      {totalItems.unread}
                    </Badge>
                  </TabsTrigger>
                  {tabConfig.map(
                    ({ id, label }) =>
                      switches[id] && (
                        <TabsTrigger key={id} value={id}>
                          {t(`tabs.${id}`)}
                        </TabsTrigger>
                      )
                  )}
                </TabsList>

                <div className="flex items-center">
                  <Select value={timeFilter} onValueChange={setTimeFilter}>
                    <SelectTrigger className="w-[160px]">
                      <Calendar className="h-4 w-4 mr-2" />
                      <SelectValue placeholder={t('filters.placeholder')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all-time">{t('filters.allTime')}</SelectItem>
                      <SelectItem value="today">{t('filters.today')}</SelectItem>
                      <SelectItem value="this-week">{t('filters.thisWeek')}</SelectItem>
                      <SelectItem value="this-month">{t('filters.thisMonth')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <TabsContent value={activeTab} className="space-y-6">
                {loading && (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                    <p className="text-muted-foreground mt-2">Loading notifications...</p>
                  </div>
                )}

                {!loading && notificationList.length === 0 ? (
                  <div className="text-center py-12 border rounded-lg">
                    <Bell className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-1">{t('empty.title')}</h3>
                    <p className="text-muted-foreground">
                      {t('empty.description', {
                        type: activeTab !== 'all' ? activeTab : '',
                      })}
                    </p>
                  </div>
                ) : !loading && (
                  <>
                    {today.length > 0 && (
                      <div className="space-y-2">
                        <h3 className="text-sm font-medium text-muted-foreground">
                          {t('sections.today')}
                        </h3>
                        <Card>
                          <CardContent className="p-0 divide-y">
                            {today.map((notification) => (
                              <NotificationItem
                                key={notification.id}
                                notification={notification}
                                onMarkAsRead={markAsRead}
                                onDelete={deleteNotification}
                              />
                            ))}
                          </CardContent>
                        </Card>
                      </div>
                    )}

                    {thisWeek.length > 0 && (
                      <div className="space-y-2">
                        <h3 className="text-sm font-medium text-muted-foreground">
                          {t('sections.thisWeek')}
                        </h3>
                        <Card>
                          <CardContent className="p-0 divide-y">
                            {thisWeek.map((notification) => (
                              <NotificationItem
                                key={notification.id}
                                notification={notification}
                                onMarkAsRead={markAsRead}
                                onDelete={deleteNotification}
                              />
                            ))}
                          </CardContent>
                        </Card>
                      </div>
                    )}

                    {earlier.length > 0 && (
                      <div className="space-y-2">
                        <h3 className="text-sm font-medium text-muted-foreground">
                          {t('sections.earlier')}
                        </h3>
                        <Card>
                          <CardContent className="p-0 divide-y">
                            {earlier.map((notification) => (
                              <NotificationItem
                                key={notification.id}
                                notification={notification}
                                onMarkAsRead={markAsRead}
                                onDelete={deleteNotification}
                              />
                            ))}
                          </CardContent>
                        </Card>
                      </div>
                    )}
                  </>
                )}
              </TabsContent>
            </Tabs>
          </div>

          <div className="w-full md:w-1/4 space-y-4">
            <Card>
              <CardContent className="p-4">
                <h3 className="font-medium mb-2">{t("Notification_Summary")}</h3>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">{t("Total")}</span>
                    <Badge variant="outline">{totalItems.all}</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">{t("Unread")}</span>
                    <Badge variant="outline" className="bg-primary/10 text-primary">
                      {totalItems.unread}
                    </Badge>
                  </div>
                  <Separator className="my-2" />
                  <div className="flex justify-between items-center">
                    <span className="text-sm">{t("Tests")}</span>
                    <Badge variant="outline">{totalItems.tests}</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">{t("Courses")}</span>
                    <Badge variant="outline">{totalItems.courses}</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">{t("Badges")}</span>
                    <Badge variant="outline">{totalItems.badges}</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">{t("Other")}</span>
                    <Badge variant="outline">
                      {
                        notificationList.filter(
                          (n) => !n.type.includes("test") && !n.type.includes("course") && !n.type.includes("badge"),
                        ).length
                      }
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <h3 className="font-medium mb-2">{t("Quick_Actions")}</h3>
                <div className="space-y-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-start"
                    onClick={markAllAsRead}
                    disabled={loading}
                  >
                    <CheckCircle2 className="h-4 w-4 mr-2" />
                    {t("Mark_all_as_read")}
                  </Button>
                  <Button
                    onClick={bulkDelete}
                    variant="outline"
                    size="sm"
                    className="w-full justify-start text-red-500 hover:text-red-600"
                    disabled={loading}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    {t("Clear_all_notifications")}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Pro upgrade modal */}
        <ProUpgradeModal
          isOpen={showUpgradeModal}
          onClose={() => setShowUpgradeModal(false)}
          onUpgrade={() => {
            console.log("User clicked upgrade!");
            // handle actual upgrade logic here
          }}
        />
      </div>
    </>
  )
}
