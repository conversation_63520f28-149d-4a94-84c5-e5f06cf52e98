import MyCourseBody from "@/components/layout/my-courses/my-course-body"
import { localize } from "@/src/localization/functions/server"
import en from "./locales/en.json"
import MyCourseFooter from "@/components/layout/my-courses/my-course-footer";

export default async function MyCoursesPage() {
  const { t } = await localize("public-test", { en });

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-6">
        <div className="flex flex-col gap-2">
          <h1 className="text-3xl font-bold">{t("my_courses")}</h1>
          <p className="text-muted-foreground">{t("my_courses_detail")}</p>
        </div>

        <MyCourseBody />
        <MyCourseFooter />
      </div>
    </div>
  )
}
