"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import {
  ArrowLeft,
  BookOpen,
  Clock,
  Play,
  MessageSquare,
  Star,
  StarHalf,
  Share2,
  Download,
  FileText,
  CheckCircle2,
  ChevronLeft,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { VideoPlayer } from "../../../../../components/layout/my-courses/video-player"
import { TimestampComments } from "../../../../../components/layout/my-courses/timestamp-comments"
import { CourseNotes } from "../../../../../components/layout/my-courses/course-notes"
import { CourseFeedback } from "../../../../../components/layout/my-courses/course-feedback"
import { MCQTests } from "../../../../../components/layout/my-courses/mcq-tests"
import { CourseRecommendations } from "../../../../../components/layout/my-courses/course-recommendations"
import { MyCourseDetail } from "@/src/lib/repositories/my-courses/MyCoursesRepository"
import { MyCoursesAPI } from "@/src/services/myCoursesApi"

import { useLocalization } from "@/src/hooks/useLocalization/client";
import en from "../locales/en.json"
import { useRouter } from "next/navigation"

export default function CourseDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [myCoursesDetails, setMyCoursesDetails] = useState<MyCourseDetail>()
  const [loading, setLoading] = useState(true);
  const { t } = useLocalization("public-test", { en });
  const [activeTab, setActiveTab] = useState("content")
  const [currentVideoId, setCurrentVideoId] = useState("video-1")
  const [expandedSections, setExpandedSections] = useState<string[]>(["section-1"])

  useEffect(() => {
    const fetchData = async () => {
      try {
        const data = await MyCoursesAPI.MyCourseDetail(params.id).request();
        setMyCoursesDetails(data as MyCourseDetail);
      } catch (err) {
        console.error(err);
      } finally {
        setLoading(false)
      }
    };
    fetchData();
  }, []);

  // Mock data for a course
  const course = myCoursesDetails

  if (!course) {
    return (
      <div className="flex flex-col items-center justify-center h-[50vh]">
        <h2 className="text-xl font-semibold mb-2">{t("my_courses_not_found")}</h2>
        <p className="text-muted-foreground mb-4">{t("my_courses_not_found_detail")}</p>
        <Button onClick={() => router.push("/dashboard/my-courses")}>
          <ChevronLeft className="mr-2 h-4 w-4" />
          {t("back_to_my_courses")}
        </Button>
      </div>
    )
  }

  // Find current video
  const currentVideo = course.sections.flatMap((section) => section.videos).find((video) => video.id === currentVideoId)

  // Find current section
  const currentSection = course.sections.find((section) => section.videos.some((video) => video.id === currentVideoId))

  const toggleSection = (sectionId: string) => {
    setExpandedSections((prev) =>
      prev.includes(sectionId) ? prev.filter((id) => id !== sectionId) : [...prev, sectionId],
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/dashboard/my-courses">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold md:text-3xl">{course.title}</h1>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <span>{t("by")} {course.instructor}</span>
              <span>•</span>
              <div className="flex items-center">
                <Star className="h-4 w-4 fill-primary text-primary" />
                <Star className="h-4 w-4 fill-primary text-primary" />
                <Star className="h-4 w-4 fill-primary text-primary" />
                <Star className="h-4 w-4 fill-primary text-primary" />
                <StarHalf className="h-4 w-4 fill-primary text-primary" />
                <span className="ml-1">
                  {course.rating} ({course.totalReviews} {t("reviews")})
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="grid gap-6 lg:grid-cols-3">
          <div className="lg:col-span-2">
            <div className="rounded-lg overflow-hidden border bg-card">
              <VideoPlayer videoId={currentVideoId} />
              <div className="p-4">
                <div className="flex flex-wrap items-center justify-between gap-2">
                  <h2 className="text-xl font-semibold">{currentVideo?.title}</h2>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">
                      <Download className="mr-1 h-4 w-4" />
                      {t("resources")}
                    </Button>
                    <Button variant="outline" size="sm">
                      <Share2 className="mr-1 h-4 w-4" />
                      {t("share")}
                    </Button>
                  </div>
                </div>
                <div className="mt-2 flex items-center gap-4">
                  <div className="flex items-center gap-1">
                    <Badge variant="outline" className="text-xs font-normal">
                      {currentSection?.title}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <Clock className="h-4 w-4" />
                    <span>{currentVideo?.duration}</span>
                  </div>
                </div>
              </div>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-6">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="content">{t("content")}</TabsTrigger>
                <TabsTrigger value="comments">{t("comments")}</TabsTrigger>
                <TabsTrigger value="notes">{t("notes")}</TabsTrigger>
                <TabsTrigger value="tests">{t("tests")}</TabsTrigger>
              </TabsList>
              <TabsContent value="content" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Course Content</CardTitle>
                    <CardDescription>
                      {course.completedLessons} of {course.totalLessons} lessons completed (
                      {course.completionPercentage}%)
                    </CardDescription>
                    <Progress value={course.completionPercentage} className="h-2" />
                  </CardHeader>
                  <CardContent className="p-0">
                    <Accordion
                      type="multiple"
                      value={expandedSections}
                      onValueChange={setExpandedSections}
                      className="w-full"
                    >
                      {course.sections.map((section) => (
                        <AccordionItem key={section.id} value={section.id} className="border-0 px-6">
                          <AccordionTrigger className="py-4 hover:no-underline">
                            <div className="flex flex-1 flex-col items-start text-left">
                              <div className="font-medium">{section.title}</div>
                              <div className="text-xs text-muted-foreground">
                                {section.videos.length} lessons •{" "}
                                {section.videos.reduce((total, video) => {
                                  const [mins, secs] = video.duration.split(":")
                                  return total + Number.parseInt(mins) * 60 + Number.parseInt(secs)
                                }, 0) / 60}{" "}
                                min
                              </div>
                            </div>
                          </AccordionTrigger>
                          <AccordionContent className="pb-6">
                            <div className="space-y-1">
                              {section.description && (
                                <p className="text-sm text-muted-foreground mb-3">{section.description}</p>
                              )}
                              {section.videos.map((video) => (
                                <div
                                  key={video.id}
                                  className={`flex cursor-pointer items-center gap-3 rounded-md p-2 ${
                                    currentVideoId === video.id ? "bg-primary/10 text-primary" : "hover:bg-muted"
                                  }`}
                                  onClick={() => setCurrentVideoId(video.id)}
                                >
                                  <div
                                    className={`flex h-8 w-8 items-center justify-center rounded-full ${
                                      video.completed
                                        ? "bg-primary/20 text-primary"
                                        : currentVideoId === video.id
                                          ? "bg-primary/20 text-primary"
                                          : "bg-muted text-muted-foreground"
                                    }`}
                                  >
                                    {video.completed ? (
                                      <CheckCircle2 className="h-4 w-4" />
                                    ) : (
                                      <Play className="h-4 w-4" />
                                    )}
                                  </div>
                                  <div className="flex-1">
                                    <div className="text-sm font-medium">{video.title}</div>
                                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                      <Clock className="h-3 w-3" />
                                      <span>{video.duration}</span>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </AccordionContent>
                        </AccordionItem>
                      ))}
                    </Accordion>
                  </CardContent>
                </Card>
              </TabsContent>
              <TabsContent value="comments" className="mt-4">
                <TimestampComments videoId={currentVideoId} />
              </TabsContent>
              <TabsContent value="notes" className="mt-4">
                <CourseNotes videoId={currentVideoId} />
              </TabsContent>
              <TabsContent value="tests" className="mt-4">
                <MCQTests courseId={params.id} />
              </TabsContent>
            </Tabs>
          </div>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>{t("your_progress")}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>{t("course_completion")}</span>
                    <span className="font-medium">{course.completionPercentage}%</span>
                  </div>
                  <Progress value={course.completionPercentage} className="h-2" />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="rounded-lg border bg-card p-3 text-center">
                    <div className="text-2xl font-bold">{course.completedLessons}</div>
                    <div className="text-xs text-muted-foreground">{t("lessons_completed")}</div>
                  </div>
                  <div className="rounded-lg border bg-card p-3 text-center">
                    <div className="text-2xl font-bold">{course.totalLessons - course.completedLessons}</div>
                    <div className="text-xs text-muted-foreground">{t("lessons_remaining")}</div>
                  </div>
                </div>
                <Button className="w-full">{t("continue_learning")}</Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>{t("course_information")}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <Avatar>
                    <AvatarImage src={course.instructorAvatar || "/placeholder.svg"} alt={course.instructor} />
                    <AvatarFallback>
                      {course.instructor
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">{course.instructor}</div>
                    <div className="text-sm text-muted-foreground">{t("instructor")}</div>
                  </div>
                </div>
                <Separator />
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <BookOpen className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      {course.totalLessons} {t("lessons")} ({course.estimatedHours} {t("hours")})
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{t("certificate_of_completion")}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{t("last_updated")}</span>
                  </div>
                </div>
                <Separator />
                <div className="space-y-2">
                  <h4 className="font-medium">{t("about_this_course")}</h4>
                  <p className="text-sm text-muted-foreground">{course.summary}</p>
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  {t("contact_instructor")}
                </Button>
              </CardFooter>
            </Card>

            <CourseFeedback courseId={params.id} />

            <CourseRecommendations courseId={params.id} />
          </div>
        </div>
      </div>
    </div>
  )
}
