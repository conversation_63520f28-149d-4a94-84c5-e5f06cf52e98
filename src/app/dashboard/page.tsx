import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger } from "@/components/ui/tabs"
import DashboardSummary from "@/components/layout/dashboard/DashboardSummary"
import TabsContentOverview from "@/components/layout/dashboard/TabsContentOverview"
import { TabsContentProgress } from "@/components/layout/dashboard/TabsContentProgress"
import { TabsContentRecommendations } from "@/components/layout/dashboard/TabsContentRecommendations"

export default function Dashboard() {
  return (
    <div className="space-y-8">
      {/* Welcome section with quick stats */}
      <DashboardSummary />

      {/* Main content tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="progress">Progress</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <TabsContentOverview />
        </TabsContent>

        <TabsContent value="progress" className="space-y-6">
          <TabsContentProgress />
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-6">
          <TabsContentRecommendations />
        </TabsContent>
      </Tabs>
    </div>
  )
}
