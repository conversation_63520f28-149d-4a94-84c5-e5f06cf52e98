'use client'

import Link from "next/link"
import { PlusCircle } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { CourseCard } from "../../../../components/layout/created-courses-2/course-card"
import { useEffect, useState } from "react"
import { Course, CourseResponse } from "@/src/lib/repositories/courses/CoursesRepository"
import { CoursesAPI } from "@/src/services/coursesApi"

import { useLocalization } from "@/src/hooks/useLocalization/client"
import en from "./locales/en.json"

export default function CreatedCoursesPage() {
  const [courses, setCourses] = useState<Course[]>([])
  const [loading, setLoading] = useState(true);
  const { t } = useLocalization("public-test", { en });
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const pageSize = 6; // or whatever number you want

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await CoursesAPI.Courses({ page, per_page: pageSize }).request();
        setCourses(response.items);
        setTotalPages(response.total_pages);
      } catch (err) {
        console.error(err);
      } finally {
        setLoading(false)
      }
    };
    fetchData();
  }, [page]);

  return (
    <div className="container py-8">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold">{t("your_created_courses")}</h1>
        <Button asChild>
          <Link href="/dashboard/created-courses-2/new">
            <PlusCircle className="mr-2 h-4 w-4" />
            {t("create_new_course")}
          </Link>
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {courses.map((course) => (
          <CourseCard key={course.id} course={course} />
        ))}
      </div>

      <div className="mt-6">
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                aria-disabled={page <= 1}
                tabIndex={page <= 1 ? -1 : undefined}
                className={
                  page <= 1 ? "pointer-events-none opacity-50" : undefined
                }
                href="#"
                onClick={() => setPage(prev => Math.max(prev - 1, 1))} />
            </PaginationItem>

            {Array.from({ length: totalPages }, (_, i) => (
              <PaginationItem key={i}>
                <PaginationLink
                  href="#"
                  isActive={page === i + 1}
                  onClick={() => setPage(i + 1)}
                >
                  {i + 1}
                </PaginationLink>
              </PaginationItem>
            ))}

            <PaginationItem>
              <PaginationNext
                aria-disabled={page >= totalPages}
                tabIndex={page >= totalPages ? -1 : undefined}
                className={
                  page >= totalPages ? "pointer-events-none opacity-50" : undefined
                }
                href="#"
                onClick={() => setPage(prev => Math.min(prev + 1, totalPages))} />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  )
}
