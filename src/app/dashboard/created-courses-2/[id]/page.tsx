"use client"

import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON>, useState } from "react"
import Link from "next/link"
import { ArrowLeft, ChevronLeft, Save } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { CourseContentEditor } from "../../../../../components/layout/created-courses-2/course-content-editor"
import { CourseRichTextEditor } from "../../../../../components/layout/created-courses-2/course-rich-text-editor"
import { CourseComments } from "../../../../../components/layout/created-courses-2/course-comments"
import { CourseAnalytics } from "../../../../../components/layout/created-courses-2/course-analytics"
import { useRouter } from "next/navigation"
import { CourseDetail } from "@/src/lib/repositories/courses/CoursesRepository"
import { CoursesAPI } from "@/src/services/coursesApi"
import { useLocalization } from "@/src/hooks/useLocalization/client"
import en from "../locales/en.json"

export default function CourseEditPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [coursesDetails, setCoursesDetails] = useState<CourseDetail>({
    id: params.id,
    title: "",
    description: "",
    sections: [],
    completionRate: 0,
    course_id: params.id,
    summary: "",
    students: 0,
    engagement: 0,
  })
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("details")
  const [changeSection, setChangeSection] = useState(false)
  const { t } = useLocalization("public-test", { en });

  const isNewCourse = params.id === "new"
  const pageTitle = isNewCourse ? "Create New Course" : "Edit Course"

  const fetchData = async () => {
    try {
      const data = await CoursesAPI.CourseDetail(params.id).request();
      setCoursesDetails(data);
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false)
    }
  };

  useEffect(() => {
    if (isNewCourse) {
      return setCoursesDetails({
        id: params.id,
        title: "",
        description: "",
        sections: [],
        completionRate: 0,
        course_id: params.id,
        summary: "",
        students: 0,
        engagement: 0,
      });
    }
    if (params.id.length > 0) fetchData();
  }, [params.id]);

  const addCourse = async () => {
    try {
      setLoading(true)
      const { title, description, summary } = coursesDetails
      const newCourse = {
        title,
        description,
        summary,
      }

      const newDataCourses = await CoursesAPI.CreateCourse(newCourse).request();
      setCoursesDetails(newDataCourses as CourseDetail);
      router.push("/dashboard/created-courses-2")
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false)
    }
  }

  const saveEditedCourse = async (courseId: string) => {
    try {
      setLoading(true)
      const newDataCourses = await CoursesAPI.UpdateCourse(courseId, coursesDetails).request();
      setCoursesDetails(newDataCourses as CourseDetail);

      if (!changeSection) router.push("/dashboard/created-courses-2")
    } catch (err) {
      console.error(err);
    } finally {
      setChangeSection(false)
      setLoading(false)
    }
  }

  // Mock data for a course
  const course = useMemo(() => coursesDetails,[coursesDetails, changeSection])

  useEffect(() => {
    if (changeSection) saveEditedCourse(course.id)
  }, [changeSection])

  if (!course && !isNewCourse) {
    return (
      <div className="flex flex-col items-center justify-center h-[50vh]">
        <h2 className="text-xl font-semibold mb-2">{t("my_courses_not_found")}</h2>
        <p className="text-muted-foreground mb-4">{t("my_courses_not_found_detail")}</p>
        <Button onClick={() => router.push("/dashboard/courses")}>
          <ChevronLeft className="mr-2 h-4 w-4" />
          {t("back_to_courses")}
        </Button>
      </div>
    )
  }
  return (
    <div className="container py-8">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/dashboard/created-courses-2">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-3xl font-bold">{pageTitle}</h1>
        </div>
        <Button
          onClick={() => isNewCourse ? addCourse() : saveEditedCourse(course.id)}
        >
          <Save className="mr-2 h-4 w-4" />
          {t("save_changes")}
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="details">{t("course_details")}</TabsTrigger>
          <TabsTrigger value="content">{t("content")}</TabsTrigger>
          <TabsTrigger value="comments">{t("comments")}</TabsTrigger>
          <TabsTrigger value="analytics">{t("analytics")}</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{t("basic_information")}</CardTitle>
              <CardDescription>{t("edit_basic_details")}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">{t("course_title")}</Label>
                <Input
                  id="title"
                  defaultValue={course.title}
                  onChange={value => setCoursesDetails(old => ({ ...old, title: value.target.value }))}
                  placeholder={t("enter_course_title")}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="summary">{t("short_summary")}</Label>
                <Textarea
                  id="summary"
                  defaultValue={course.summary}
                  onChange={value => setCoursesDetails(old => ({ ...old, summary: value.target.value }))}
                  placeholder={t("write_brief_summary")}
                  className="min-h-[100px]"
                />
              </div>
              <div className="space-y-2">
                <Label>{t("detailed_description")}</Label>
                <CourseRichTextEditor setCoursesDetails={setCoursesDetails} coursesDetails={coursesDetails}/>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="content">
          <CourseContentEditor
            setCoursesDetails={setCoursesDetails}
            setChangeSection={setChangeSection}
            sections={course.sections || []}
          />
        </TabsContent>

        <TabsContent value="comments">
          <CourseComments courseId={params.id} />
        </TabsContent>

        <TabsContent value="analytics">
          <CourseAnalytics courseId={params.id} />
        </TabsContent>
      </Tabs>
    </div>
  )
}
