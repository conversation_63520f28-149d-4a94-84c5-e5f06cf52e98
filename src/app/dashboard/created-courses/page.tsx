import type React from "react"
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>rk<PERSON>, Star, Lightbulb, <PERSON>Tool, Presentation } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export default function CreatedCourses() {
  return (
    <div className="space-y-8">
      <div className="flex flex-col md:flex-row gap-4 md:items-center md:justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Created Courses</h2>
          <p className="text-muted-foreground">Create and manage your own courses</p>
        </div>
      </div>

      {/* Coming Soon / Pro Upgrade Card */}
      <Card className="border-0 bg-gradient-to-r from-amber-500/90 to-orange-500 text-white">
        <CardContent className="p-8">
          <div className="flex flex-col md:flex-row items-center gap-6">
            <div className="relative h-32 w-32 flex-shrink-0">
              <div className="absolute inset-0 rounded-full bg-white/20 animate-pulse"></div>
              <div className="absolute inset-2 rounded-full bg-white/30 flex items-center justify-center">
                <Presentation className="h-12 w-12 text-white" />
              </div>
            </div>

            <div className="flex-1 text-center md:text-left">
              <Badge className="bg-white/20 text-white hover:bg-white/30 border-0 mb-2">
                <Sparkles className="h-3 w-3 mr-1" />
                Coming Soon
              </Badge>
              <h2 className="text-2xl font-bold mb-2">Course Creator Studio</h2>
              <p className="text-white/80 mb-4 max-w-2xl">
                Our course creation platform is coming soon! Upgrade to Pro now to lock in lifetime pricing and get
                early access to our powerful course creation tools. Pro creators will enjoy advanced analytics,
                marketing tools, and higher revenue shares.
              </p>
              <div className="flex flex-wrap gap-3 justify-center md:justify-start">
                <Button variant="secondary" size="lg" className="gap-2">
                  <Star className="h-4 w-4" />
                  Upgrade to Pro
                </Button>
                <Button
                  variant="outline"
                  className="bg-white/10 text-white border-white/20 hover:bg-white/20 hover:text-white"
                >
                  Learn More
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Pro Features Preview */}
      <div className="grid md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Powerful Course Builder</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
              <PenTool className="h-6 w-6 text-primary" />
            </div>
            <p className="text-muted-foreground">
              Intuitive drag-and-drop course builder with support for video, quizzes, assignments, and more.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Advanced Analytics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
              <Lock className="h-6 w-6 text-primary" />
            </div>
            <p className="text-muted-foreground">
              Track student progress, engagement, and performance with detailed analytics and insights.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Marketing Tools</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
              <Lock className="h-6 w-6 text-primary" />
            </div>
            <p className="text-muted-foreground">
              Promote your courses with built-in marketing tools, landing pages, and email campaigns.
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Why Create Courses */}
      <Card>
        <CardHeader>
          <CardTitle>Why Create Courses?</CardTitle>
          <CardDescription>Share your knowledge and earn revenue</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex gap-3">
                <div className="h-10 w-10 rounded-full bg-green-100 text-green-700 flex items-center justify-center flex-shrink-0">
                  <Lightbulb className="h-5 w-5" />
                </div>
                <div>
                  <h3 className="font-medium">Share Your Expertise</h3>
                  <p className="text-sm text-muted-foreground">
                    Help others learn and grow by sharing your knowledge and experience in your area of expertise.
                  </p>
                </div>
              </div>

              <div className="flex gap-3">
                <div className="h-10 w-10 rounded-full bg-blue-100 text-blue-700 flex items-center justify-center flex-shrink-0">
                  <Users className="h-5 w-5" />
                </div>
                <div>
                  <h3 className="font-medium">Build Your Audience</h3>
                  <p className="text-sm text-muted-foreground">
                    Connect with learners worldwide and build a community around your content and teaching style.
                  </p>
                </div>
              </div>

              <div className="flex gap-3">
                <div className="h-10 w-10 rounded-full bg-purple-100 text-purple-700 flex items-center justify-center flex-shrink-0">
                  <Award className="h-5 w-5" />
                </div>
                <div>
                  <h3 className="font-medium">Establish Authority</h3>
                  <p className="text-sm text-muted-foreground">
                    Position yourself as an authority in your field and enhance your professional reputation.
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex gap-3">
                <div className="h-10 w-10 rounded-full bg-amber-100 text-amber-700 flex items-center justify-center flex-shrink-0">
                  <DollarSign className="h-5 w-5" />
                </div>
                <div>
                  <h3 className="font-medium">Generate Revenue</h3>
                  <p className="text-sm text-muted-foreground">
                    Earn income from your courses with our fair revenue sharing model and multiple monetization options.
                  </p>
                </div>
              </div>

              <div className="flex gap-3">
                <div className="h-10 w-10 rounded-full bg-red-100 text-red-700 flex items-center justify-center flex-shrink-0">
                  <Zap className="h-5 w-5" />
                </div>
                <div>
                  <h3 className="font-medium">Flexible Creation</h3>
                  <p className="text-sm text-muted-foreground">
                    Create courses on your own schedule with our easy-to-use tools and templates.
                  </p>
                </div>
              </div>

              <div className="flex gap-3">
                <div className="h-10 w-10 rounded-full bg-teal-100 text-teal-700 flex items-center justify-center flex-shrink-0">
                  <BarChart className="h-5 w-5" />
                </div>
                <div>
                  <h3 className="font-medium">Detailed Analytics</h3>
                  <p className="text-sm text-muted-foreground">
                    Get insights into how your courses are performing and how students are engaging with your content.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-center">
          <Button size="lg">
            Join the Creator Waitlist
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}

function Users(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
      <circle cx="9" cy="7" r="4" />
      <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
      <path d="M16 3.13a4 4 0 0 1 0 7.75" />
    </svg>
  )
}

function Award(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <circle cx="12" cy="8" r="7" />
      <polyline points="8.21 13.89 7 23 12 20 17 23 15.79 13.88" />
    </svg>
  )
}

function DollarSign(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <line x1="12" x2="12" y1="2" y2="22" />
      <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
    </svg>
  )
}

function Zap(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2" />
    </svg>
  )
}

function BarChart(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <line x1="12" x2="12" y1="20" y2="10" />
      <line x1="18" x2="18" y1="20" y2="4" />
      <line x1="6" x2="6" y1="20" y2="16" />
    </svg>
  )
}
