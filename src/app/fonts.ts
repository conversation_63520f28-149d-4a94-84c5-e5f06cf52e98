import localFont from "next/font/local";

const inter = localFont({
  src: [
    {
      path: "./../../public/fonts/inter/Inter_24pt-Regular.ttf",
      weight: "400",
      style: "normal",
    },
    {
      path: "./../../public/fonts/inter/Inter_24pt-Medium.ttf",
      weight: "500",
      style: "medium",
    },
    {
      path: "./../../public/fonts/inter/Inter_24pt-SemiBold.ttf",
      weight: "600",
      style: "semibold",
    },
    {
      path: "./../../public/fonts/inter/Inter_24pt-Bold.ttf",
      weight: "700",
      style: "bold",
    },
  ],
  variable: "--font-inter",
});


const clash = localFont({
  src: [
    {
      path: "./../../public/fonts/clash/ClashDisplay-Regular.otf",
      weight: "400",
      style: "normal",
    },
    {
      path: "./../../public/fonts/clash/ClashDisplay-Medium.otf",
      weight: "500",
      style: "medium",
    },
    {
      path: "./../../public/fonts/clash/ClashDisplay-SemiBold.otf",
      weight: "600",
      style: "semibold",
    },
    {
      path: "./../../public/fonts/clash/ClashDisplay-Bold.otf",
      weight: "700",
      style: "bold",
    },
  ],
  variable: "--font-clash",
});

export { inter, clash };
