import type React from "react"
import type { Metadata } from "next"

import "@/src/app/globals.css"
import "react-modern-drawer/dist/index.css"
import { cn } from "@/src/lib/utils"
import { clash, inter } from "./fonts"
import { getServerLocale } from '@/src/hooks/useLocalization/server'
import { LocalizationProvider } from "@/src/hooks/useLocalization/localization-context"
import { Toaster } from "@/components/ui/toaster"
import { Toaster as ToasterSonner } from "@/components/ui/sonner"

export const metadata: Metadata = {
  title: "Skill Pintar",
  description: "Belajar skill baru dengan Skill Pintar",
  // themeColor: [
  //   { media: "(prefers-color-scheme: light)", color: "white" },
  //   { media: "(prefers-color-scheme: dark)", color: "black" },
  // ],
  icons: {
    icon: "/favicon.ico",
    shortcut: "/favicon-16x16.png",
    apple: "/apple-touch-icon.png",
  },
}

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {

  const locale = await getServerLocale()

  return (
    <html
      lang="en"
      suppressHydrationWarning
    // className={cn(`${inter.variable} ${clash.variable}`)}
    >
      <body>
        <LocalizationProvider initialLocale={locale}>
          {children}
        </LocalizationProvider>
        <Toaster />
        <ToasterSonner richColors />
      </body>
    </html>
  )
}
