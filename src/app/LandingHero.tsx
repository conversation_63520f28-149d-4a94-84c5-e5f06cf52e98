import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowR<PERSON>, PhoneCall, Play } from "lucide-react";

export const LandingHero = () => {
  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100 py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          <div>
            <Badge className="mb-4 px-2 w-fit bg-green-100 text-green-800">
              Limited Time Offer
            </Badge>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
              Prove Your Skills,{" "}
              <span className="text-green-600">Boost Your Career</span>
            </h1>
            <p className="text-lg md:text-xl text-gray-600 mb-8">
              Take verified skill tests, build a professional profile, and
              showcase your expertise to potential employers. Accelerate your
              career growth today.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 mb-8">
              <Link href="/tests" passHref>
                <Button className="bg-green-600 hover:bg-green-700 text-white h-12 px-6 text-base flex items-center gap-2">
                  Start Testing Now <ArrowRight className="h-4 w-4" />
                </Button>
              </Link>
              <Link href="https://wa.me/6282243905005" passHref>
                <Button
                  variant="outline"
                  className="h-12 px-6 text-base flex items-center gap-2"
                >
                  <PhoneCall className="h-4 w-4 text-green-600" /> Book a Demo
                </Button>
              </Link>
            </div>
            <div className="flex items-center text-sm text-gray-500">
              <div className="flex -space-x-2 mr-3">
                <Image
                  src="/placeholder.svg"
                  alt="User 1"
                  width={32}
                  height={32}
                  className="rounded-full border-2 border-white"
                />
                <Image
                  src="/placeholder.svg"
                  alt="User 2"
                  width={32}
                  height={32}
                  className="rounded-full border-2 border-white"
                />
                <Image
                  src="/placeholder.svg"
                  alt="User 3"
                  width={32}
                  height={32}
                  className="rounded-full border-2 border-white"
                />
              </div>
              <span>
                <span className="font-medium text-green-600">5,000+</span>{" "}
                professionals already testing their skills
              </span>
            </div>
          </div>

          <div className="relative">
            <div className="absolute -top-16 -right-16 w-32 h-32 bg-green-400 rounded-full opacity-20 blur-2xl"></div>
            <div className="absolute -bottom-8 -left-8 w-24 h-24 bg-orange-400 rounded-full opacity-20 blur-xl"></div>
            <div className="relative bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-100">
              <Image
                src="/images/landing-hero.png"
                alt="SkillPintar Platform"
                width={600}
                height={400}
                className="w-full h-auto"
              />
              <div className="absolute top-4 right-4 bg-green-600 text-white text-xs px-3 py-1 rounded-full">
                Professional Profile
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Price Badge */}
      <div className="absolute top-10 right-10 md:top-20 md:right-20 animate-pulse">
        <div className="relative">
          <div className="absolute inset-0 bg-green-400 rounded-full blur-xl opacity-30 transform scale-110"></div>
          <div className="relative bg-white rounded-2xl shadow-lg p-4 border border-green-100">
            <div className="text-center">
              <p className="text-sm text-gray-500 line-through">
                IDR 50,000/mo
              </p>
              <p className="text-2xl font-bold text-green-600">IDR 10,000/mo</p>
              <p className="text-xs text-gray-500">Limited time offer</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
