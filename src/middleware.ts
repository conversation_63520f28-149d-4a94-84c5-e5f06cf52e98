import { NextRequest, NextResponse } from 'next/server';
import { queryValidation } from '@/middleware_functions/query-params-validation';
import { matchPattern } from '@/src/utils/wildcardPatternMatching';
import { ResponseWrapper } from '@/src/lib/types/responseWrapper';

// Define secured pages (default: public)
const SECURED_PAGES = [
  '/dashboard',
  '/dashboard/*'
];

// Define public API routes (default: secured)
const PUBLIC_API_ROUTES = [
  '/api/v1/auth/login',
  '/api/v1/auth/reset-password',
  '/api/v1/auth/verify-email',
  '/api/v1/auth/register',
  '/api/v1/auth/forgot-password',
  '/api/v1/auth/resend-verification',
  '/api/v1/public/*'
];

export async function middleware(request: NextRequest) {
  // 1. Validate query parameters
  const invalidQueryResponse = queryValidation(request);
  if (invalidQueryResponse) return invalidQueryResponse;

  const { pathname } = request.nextUrl;

  const isApiRoute = pathname.startsWith('/api');

  // 2. Match secured/public routes
  const isPublicApi = PUBLIC_API_ROUTES.some(route => matchPattern(route, pathname));
  const isSecuredPage = SECURED_PAGES.some(route => matchPattern(route, pathname));

  // Determine if route needs protection
  const requiresAuth = isApiRoute ? !isPublicApi : isSecuredPage;

  if (!requiresAuth) {
    return NextResponse.next(); // No auth needed
  }

  // 3. Extract token
  const authHeader = request.headers.get('authorization');
  const bearerToken = authHeader?.startsWith('Bearer ') ? authHeader.split(' ')[1] : null;
  const cookieToken = request.cookies.get('token')?.value;
  const token = bearerToken || cookieToken;

  if (!token) {
    // 4. Token missing → differentiate between API and page
    if (isApiRoute) {
      return NextResponse.json(
        new ResponseWrapper(
          'failed',
          undefined,
          ['api.error.requires_token'],
          ['ERROR_UNAUTHORIZED']
        ),
        { status: 401 }
      );
    }

    // Redirect to login for page
    const loginUrl = new URL('/auth/login', request.url);
    return NextResponse.redirect(loginUrl);
  }

  // 5. Token exists, forward it
  const response = NextResponse.next();
  response.headers.set('x-internal-token', token);
  return response;
}
