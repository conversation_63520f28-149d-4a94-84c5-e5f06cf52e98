import type { Config } from "tailwindcss";

const config: Config = {
  darkMode: ["class"],
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic":
          "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      colors: {
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))",
        },
        sidebar: {
          DEFAULT: "hsl(var(--sidebar-background))",
          foreground: "hsl(var(--sidebar-foreground))",
          primary: "hsl(var(--sidebar-primary))",
          "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
          border: "hsl(var(--sidebar-border))",
          ring: "hsl(var(--sidebar-ring))",
        },
        sp: {
          primary: "#44A14C",
          secondary: "#3D8F44",
          "secondary-1":"#172029",
          "neutral-800": "#202020",
          "neutral-700": "#595959",
          "neutral-600": "#808080",
          "neutral-500": "#AFAFAF",
          "neutral-400": "#A5D9AA",
          "neutral-300": "#C9E8CC",
          "neutral-200": "#E2F3E4",
          "neutral-100": "#FFFFFF",
          error: "#CB5B5B",
          warning: "#FFCC00",
          info: "#0063F7",
          success: "#06C270",
          background:"#F6F5FA"
        },
      },
      fontFamily: {
        inter: "var(--font-inter)",
        clash: "var(--font-clash)",
      },
      fontSize: {
        title: [
          "4.5rem", // 72px
          {
            lineHeight: "6.75rem", // 108px
            fontWeight: "700",
          },
        ],
        "heading-1": [
          "3.5rem", // 56px
          {
            lineHeight: "5.25rem", // 84px
            fontWeight: "600",
          },
        ],
        "heading-2": [
          "3rem", // 48px
          {
            lineHeight: "4.5rem", // 72px
            fontWeight: "600",
          },
        ],
        "heading-3": [
          "2.5rem", // 40px
          {
            lineHeight: "3.75rem", // 60px
            fontWeight: "600",
          },
        ],
        "heading-4": [
          "2rem", // 32px
          {
            lineHeight: "3rem", // 48px
            fontWeight: "600",
          },
        ],
        "body-1": [
          "1.5rem", // 24px
          {
            lineHeight: "2.25rem", // 36px
            fontWeight: "500",
          },
        ],
        "body-2": [
          "1.25rem", // 20px
          {
            lineHeight: "1.875rem", // 30px
            fontWeight: "500",
          },
        ],
        "body-3": [
          "1rem", // 16px
          {
            lineHeight: "1.5rem", // 24px
            fontWeight: "500",
          },
        ],
        "body-4": [
          "0.875rem", // 14px
          {
            lineHeight: "1.125rem", // 18px
            fontWeight: "500",
          },
        ],
        "body-5": [
          "0.75rem", // 12px
          {
            lineHeight: "0.875rem", // 14px
            fontWeight: "500",
          },
        ],
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-gradients"), require("tailwindcss-animate")],
};
export default config;
