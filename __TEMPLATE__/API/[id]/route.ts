import { TEMPLATE_CAMELCASEDsBusinessLogic } from "@/lib/repositories/businessLogics";
import { NextRequest, NextResponse } from "next/server";
import { implHandleGetTEMPLATE_CAPITALIZED, implHandleUpdateTEMPLATE_CAPITALIZED, implHandleDeleteTEMPLATE_CAPITALIZED } from "../impl";
import { ResponseWrapper } from "@/lib/types/responseWrapper";
import { ERROR_CODES } from "@/app/api/error_codes";
import { buildSessionContext } from "@/app/api/sharedFunction";
import { TEMPLATE_CAMELCASEDAccessManager } from "@/lib/repositories/accessManagers";

export async function GET(
  req: NextRequest,
  ctx: { params: Promise<{ id: string }> }
) {
  try {
    const { context, response } = await buildSessionContext(req);
    if (response) {
      return response;
    }
    const { id } = await ctx.params;
    const result = await implHandleGetTEMPLATE_CAPITALIZED(id, TEMPLATE_CAMELCASEDsBusinessLogic, context, TEMPLATE_CAMELCASEDAccessManager(context.organization?.id || context.user.id));
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("TEMPLATE_CAPITALIZED GET route error:", error);
    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        undefined,
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR]
      ),
      { status: 500 }
    );
  }
}

export async function PUT(
  req: NextRequest,
  ctx: { params: Promise<{ id: string }> }
) {
  try {
    const { context, response } = await buildSessionContext(req);
    if (response) {
      return response;
    }

    const { id } = await ctx.params;
    const body = await req.json();
    const result = await implHandleUpdateTEMPLATE_CAPITALIZED(id, body, TEMPLATE_CAMELCASEDsBusinessLogic, context, TEMPLATE_CAMELCASEDAccessManager(context.organization?.id || context.user.id));
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("TEMPLATE_CAPITALIZED PUT route error:", error);
    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        undefined,
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR]
      ),
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  ctx: { params: Promise<{ id: string }> }
) {
  try {
    const { context, response } = await buildSessionContext(req);
    if (response) {
      return response;
    }

    const { id } = await ctx.params;
    const result = await implHandleDeleteTEMPLATE_CAPITALIZED(id, TEMPLATE_CAMELCASEDsBusinessLogic, false, context, TEMPLATE_CAMELCASEDAccessManager(context.organization?.id || context.user.id));
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("TEMPLATE_CAPITALIZED DELETE route error:", error);
    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        undefined,
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR]
      ),
      { status: 500 }
    );
  }
}
