import { TEMPLATE_CAPITALIZEDBusinessLogicInterface } from "@/lib/repositories/TEMPLATE_KEBABCASEDs/interface";
import { ResponseWrapper } from "@/lib/types/responseWrapper";
import { TEMPLATE_CAPITALIZEDUpdateSchema } from "@/lib/validations/TEMPLATE_KEBABCASED";
import { ERROR_CODES } from "@/app/api/error_codes";
import { GroupRoleResourceAccessManager } from "@/lib/repositories/AccessManager";
import { SessionContext } from "@/lib/repositories/auth/types";
import { TEMPLATE_CAPITALIZEDAccessResource } from "./access_manager_resource";
import { TEMPLATE_CAPITALIZEDResponses } from "@/lib/utils/api-response-helper";

// Update TEMPLATE_CAPITALIZED Implementation
export async function implHandleUpdateTEMPLATE_CAPITALIZED(
  id: string,
  data: any,
  businessLogic: TEMPLATE_CAPITALIZEDBusinessLogicInterface,
  context?: SessionContext,
  accessManager?: GroupRoleResourceAccessManager<TEMPLATE_CAPITALIZEDAccessResource>,
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {

  const isAuthorized = await accessManager?.isAllowed({
    resource: "PUT /TEMPLATE_KEBABCASEDs/*",
    action: "update",
    userId: context?.user.id ?? "",
  });

  if (!isAuthorized) {
    return TEMPLATE_CAPITALIZEDResponses.unauthorizedRole();
  }

  try {
    // Validate input data
    const validationResult = TEMPLATE_CAPITALIZEDUpdateSchema.safeParse(data);
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err =>
        `${err.path.join('.')}: ${err.message}`
      );

      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          errors,
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    const TEMPLATE_CAMELCASEDs = await businessLogic.update(id, validationResult.data);

    if (!TEMPLATE_CAMELCASEDs) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["TEMPLATE_CAPITALIZED not found"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", TEMPLATE_CAMELCASEDs),
    };
  } catch (error: any) {
    console.error("Update TEMPLATE_CAMELCASEDs error:", error);

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    if (error.code === "DUPLICATE_PHONE" || error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE]
        ),
      };
    }

    if (error.code === "INVALID_UPDATE_DATA") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    if (error.code === "INVALID_EMAIL" || error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to update TEMPLATE_CAMELCASEDs. Please try again."],
        [ERROR_CODES.UPDATE_FAILED]
      ),
    };
  }
}
