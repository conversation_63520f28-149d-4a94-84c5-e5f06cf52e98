import { TEMPLATE_CAPITALIZEDBusinessLogicInterface } from "@/lib/repositories/TEMPLATE_KEBABCASEDs/interface";
import { ResponseWrapper } from "@/lib/types/responseWrapper";
import { TEMPLATE_CAPITALIZEDCreateSchema } from "@/lib/validations/TEMPLATE_KEBABCASED";
import { ERROR_CODES } from "@/app/api/error_codes";
import { TEMPLATE_CAPITALIZEDResponses, createValidationErrorResponse } from "@/lib/utils/api-response-helper";
import { GroupRoleResourceAccessManager } from "@/lib/repositories/AccessManager";
import { SessionContext } from "@/lib/repositories/auth/types";
import { TEMPLATE_CAPITALIZEDAccessResource } from "./access_manager_resource";

// Create TEMPLATE_CAPITALIZED Implementation
export async function implHandleCreateTEMPLATE_CAPITALIZED(
  data: any,
  businessLogic: TEMPLATE_CAPITALIZEDBusinessLogicInterface,
  context?: SessionContext,
  accessManager?: GroupRoleResourceAccessManager<TEMPLATE_CAPITALIZEDAccessResource>,
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {

  const isAuthorized = await accessManager?.isAllowed({
    resource: "POST /TEMPLATE_KEBABCASEDs",
    action: "create",
    userId: context?.user.id ?? "",
  });

  if (!isAuthorized) {
    return TEMPLATE_CAPITALIZEDResponses.unauthorizedRole();
  }

  try {
    // Validate input data
    const validationResult = TEMPLATE_CAPITALIZEDCreateSchema.safeParse(data);
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err =>
        `${err.path.join('.')}: ${err.message}`
      );

      return createValidationErrorResponse(errors);
    }

    // Transform notes to include createdAt if not present
    const createData = {
      ...validationResult.data,
      notes: validationResult.data.notes?.map(note => ({
        text: note.text,
        createdAt: new Date().toISOString()
      }))
    };

    const TEMPLATE_CAMELCASEDs = await businessLogic.create(createData);

    return TEMPLATE_CAPITALIZEDResponses.created(TEMPLATE_CAMELCASEDs);
  } catch (error: any) {
    console.error("Create TEMPLATE_CAMELCASEDs error:", error);

    if (error.code === "DUPLICATE_PHONE") {
      return TEMPLATE_CAPITALIZEDResponses.duplicatePhone();
    }

    if (error.code === "DUPLICATE_NAME") {
      return TEMPLATE_CAPITALIZEDResponses.duplicateName();
    }

    if (error.code === "INVALID_EMAIL") {
      return createValidationErrorResponse([error.message]);
    }

    return TEMPLATE_CAPITALIZEDResponses.createFailed();
  }
}
