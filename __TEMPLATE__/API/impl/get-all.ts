import { TEMPLATE_CAPITALIZEDBusinessLogicInterface, TEMPLATE_CAPITALIZED } from "@/lib/repositories/TEMPLATE_KEBABCASEDs/interface";
import { ResponseWrapper } from "@/lib/types/responseWrapper";
import { GetAllResultPaginated, TEMPLATE_CAPITALIZEDQueryParams } from "./types";
import { TEMPLATE_CAPITALIZEDResponses, SearchResponses, createSuccessResponse } from "@/lib/utils/api-response-helper";
import { SessionContext } from "@/lib/repositories/auth/types";
import { GroupRoleResourceAccessManager } from "@/lib/repositories/AccessManager";
import { TEMPLATE_CAPITALIZEDAccessResource } from "./access_manager_resource";

// Get All TEMPLATE_CAPITALIZEDs Implementation (handles all, search, and tag filtering)
export async function implHandleGetAllTEMPLATE_CAPITALIZEDs(
  businessLogic: TEMPLATE_CAPITALIZEDBusinessLogicInterface,
  params?: TEMPLATE_CAPITALIZEDQueryParams,
  context?: SessionContext,
  accessManager?: GroupRoleResourceAccessManager<TEMPLATE_CAPITALIZEDAccessResource>,
): Promise<{
  status: number;
  body: ResponseWrapper<GetAllResultPaginated<TEMPLATE_CAPITALIZED>>;
}> {

  const isAuthorized = await accessManager?.isAllowed({
    resource: "GET /TEMPLATE_KEBABCASEDs",
    action: "read",
    userId: context?.user.id ?? "",
  });

  if (!isAuthorized) {
    return TEMPLATE_CAPITALIZEDResponses.unauthorizedRole();
  }


  try {
    // Validate search parameter if provided
    if (params?.search !== undefined) {
      if (!params.search || params.search.trim() === '') {
        return SearchResponses.emptyKeyword();
      }
    }

    // Validate filters if provided
    if (params?.filters && params.filters.length > 0) {
      for (const filter of params.filters) {
        if (!filter.field || filter.field.trim() === '') {
          return SearchResponses.emptyFilterField();
        }
      }
    }

    // Validate sorts if provided
    if (params?.sorts && params.sorts.length > 0) {
      for (const sort of params.sorts) {
        if (!sort.field || sort.field.trim() === '') {
          return SearchResponses.emptySortField();
        }
        if (!['asc', 'desc'].includes(sort.direction)) {
          return SearchResponses.invalidSortDirection();
        }
      }
    }

    // Build query parameters for the repository
    const queryParams: any = {
      includeDeleted: params?.includeDeleted,
      page: params?.page,
      limit: params?.limit,
    };

    // Add search if provided
    if (params?.search) {
      queryParams.search = params.search.trim();
    }

    // Add filters if provided
    if (params?.filters && params.filters.length > 0) {
      queryParams.filters = params.filters;
    }

    // Add sorts if provided
    if (params?.sorts && params.sorts.length > 0) {
      queryParams.sorts = params.sorts;
    }

    const result = await businessLogic.getAll(queryParams);

    return createSuccessResponse<GetAllResultPaginated<TEMPLATE_CAPITALIZED>>({ ...result, page: queryParams.page });
  } catch (error: any) {
    console.error("Get TEMPLATE_CAMELCASEDs error:", error);

    return TEMPLATE_CAPITALIZEDResponses.fetchFailed();
  }
}
