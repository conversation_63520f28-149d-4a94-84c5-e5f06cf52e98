import { TEMPLATE_CAPITALIZEDBusinessLogicInterface } from "@/lib/repositories/TEMPLATE_KEBABCASEDs/interface";
import { ResponseWrapper } from "@/lib/types/responseWrapper";
import { ERROR_CODES } from "@/app/api/error_codes";
import { GroupRoleResourceAccessManager } from "@/lib/repositories/AccessManager";
import { SessionContext } from "@/lib/repositories/auth/types";
import { TEMPLATE_CAPITALIZEDAccessResource } from "./access_manager_resource";
import { TEMPLATE_CAPITALIZEDResponses } from "@/lib/utils/api-response-helper";

// Delete TEMPLATE_CAPITALIZED Implementation
export async function implHandleDeleteTEMPLATE_CAPITALIZED(
  id: string,
  businessLogic: TEMPLATE_CAPITALIZEDBusinessLogicInterface,
  hardDelete: boolean = false,
  context?: SessionContext,
  accessManager?: GroupRoleResourceAccessManager<TEMPLATE_CAPITALIZEDAccessResource>,
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {

  const isAuthorized = await accessManager?.isAllowed({
    resource: "DELETE /TEMPLATE_KEBABCASEDs/*",
    action: "delete",
    userId: context?.user.id ?? "",
  });

  if (!isAuthorized) {
    return TEMPLATE_CAPITALIZEDResponses.unauthorizedRole();
  }

  try {
    await businessLogic.delete(id, hardDelete);

    return {
      status: 200,
      body: new ResponseWrapper("success", { message: "TEMPLATE_CAPITALIZED deleted successfully" }),
    };
  } catch (error: any) {
    console.error("Delete TEMPLATE_CAMELCASEDs error:", error);

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to delete TEMPLATE_CAMELCASEDs. Please try again."],
        [ERROR_CODES.DELETE_FAILED]
      ),
    };
  }
}
