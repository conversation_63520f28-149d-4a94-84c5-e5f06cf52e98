import { TEMPLATE_CAPITALIZED } from "@/lib/repositories/TEMPLATE_KEBABCASEDs/interface";

// Common types used across TEMPLATE_CAMELCASED implementations
export interface GetAllResultPaginated<T> {
  items: T[];
  page: number;
  total: number;
}

// ============================================================================
// BULK OPERATIONS TYPES
// ============================================================================

export interface BulkImportRequest {
  operation: 'import';
  data: Array<{
    name: string;
    phone: string;
    email?: string;
    tags?: string[];
    notes?: Array<{ text: string; createdAt: string }>;
  }>;
}

export interface BulkUpdateRequest {
  operation: 'update';
  data: Array<{
    id: string;
    name: string;
    phone: string;
    email?: string;
    tags?: string[];
    notes?: Array<{ text: string; createdAt: string }>;
  }>;
}

export interface BulkDeleteRequest {
  operation: 'delete';
  ids: string[];
}

export interface BulkOperationResult {
  total: number;
  successful: number;
  failed: number;
  errors: Array<{
    row?: number;
    id?: string;
    field?: string;
    message: string;
  }>;
}

// ============================================================================
// STATS OPERATIONS TYPES
// ============================================================================

export interface TEMPLATE_CAPITALIZEDsStatsData {
  totalTEMPLATE_CAPITALIZEDs: number;
  activeTEMPLATE_CAPITALIZEDs: number;
  deletedTEMPLATE_CAPITALIZEDs: number;
  TEMPLATE_CAMELCASEDsWithEmail: number;
  TEMPLATE_CAMELCASEDsWithTags: number;
  recentTEMPLATE_CAPITALIZEDs: number; // last 7 days
  statusBreakdown: Array<{
    status: string;
    count: number;
    percentage: number;
  }>;
  tagBreakdown: Array<{
    tag: string;
    count: number;
    percentage: number;
  }>;
  createdByBreakdown: Array<{
    createdBy: string;
    count: number;
    percentage: number;
  }>;
  dailyStats: Array<{
    date: string;
    created: number;
    deleted: number;
  }>;
}

// ============================================================================
// QUERY PARAMETERS TYPES
// ============================================================================

export interface TEMPLATE_CAPITALIZEDQueryParams {
  search?: string;
  includeDeleted?: boolean;
  page?: number;
  limit?: number;
  sorts?: {
    field: keyof TEMPLATE_CAPITALIZED | string;
    direction: "asc" | "desc";
  }[];
  filters?: {
    field: keyof TEMPLATE_CAPITALIZED | string;
    value: TEMPLATE_CAPITALIZED[keyof TEMPLATE_CAPITALIZED] | any;
  }[];
}

export interface TEMPLATE_CAPITALIZEDStatsParams {
  search?: string;
  includeDeleted?: boolean;
  filters?: {
    field: string;
    value: any;
  }[];
  dateFrom?: string;
  dateTo?: string;
}
