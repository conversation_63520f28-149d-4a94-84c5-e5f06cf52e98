import { TEMPLATE_CAPITALIZEDBusinessLogicInterface } from "@/lib/repositories/TEMPLATE_KEBABCASEDs/interface";
import { ResponseWrapper } from "@/lib/types/responseWrapper";
import { ERROR_CODES } from "@/app/api/error_codes";
import { GroupRoleResourceAccessManager } from "@/lib/repositories/AccessManager";
import { SessionContext } from "@/lib/repositories/auth/types";
import { TEMPLATE_CAPITALIZEDResponses } from "@/lib/utils/api-response-helper";
import { TEMPLATE_CAPITALIZEDAccessResource } from "./access_manager_resource";

// Get TEMPLATE_CAPITALIZED by ID Implementation
export async function implHandleGetTEMPLATE_CAPITALIZED(
  id: string,
  businessLogic: TEMPLATE_CAPITALIZEDBusinessLogicInterface,
  context?: SessionContext,
  accessManager?: GroupRoleResourceAccessManager<TEMPLATE_CAPITALIZEDAccessResource>,
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  const isAuthorized = await accessManager?.isAllowed({
    resource: "GET /TEMPLATE_KEBABCASEDs",
    action: "read",
    userId: context?.user.id ?? "",
  });

  if (!isAuthorized) {
    return TEMPLATE_CAPITALIZEDResponses.unauthorizedRole();
  }

  try {
    const TEMPLATE_CAMELCASEDs = await businessLogic.getById(id);

    if (!TEMPLATE_CAMELCASEDs) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["TEMPLATE_CAPITALIZED not found"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", TEMPLATE_CAMELCASEDs),
    };
  } catch (error: any) {
    console.error("Get TEMPLATE_CAMELCASEDs error:", error);

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to fetch TEMPLATE_CAMELCASEDs. Please try again."],
        [ERROR_CODES.FETCH_FAILED]
      ),
    };
  }
}
