import { BulkImportRequest, BulkUpdateRequest, BulkDeleteRequest } from "./types";

// Validation helpers for bulk operations
export function implValidateBulkImportRequest(body: any): body is BulkImportRequest {
  return (
    body &&
    body.operation === 'import' &&
    Array.isArray(body.data) &&
    body.data.length > 0 &&
    body.data.every((item: any) =>
      typeof item === 'object' &&
      typeof item.name === 'string' &&
      typeof item.phone === 'string'
    )
  );
}

export function implValidateBulkUpdateRequest(body: any): body is BulkUpdateRequest {
  return (
    body &&
    body.operation === 'update' &&
    Array.isArray(body.data) &&
    body.data.length > 0 &&
    body.data.every((item: any) =>
      typeof item === 'object' &&
      typeof item.id === 'string' &&
      typeof item.name === 'string' &&
      typeof item.phone === 'string'
    )
  );
}

export function implValidateBulkDeleteRequest(body: any): body is BulkDeleteRequest {
  return (
    body &&
    body.operation === 'delete' &&
    Array.isArray(body.ids) &&
    body.ids.length > 0 &&
    body.ids.every((id: any) => typeof id === 'string')
  );
}
