import { TEMPLATE_CAPITALIZEDBusinessLogicInterface } from "@/lib/repositories/TEMPLATE_KEBABCASEDs/interface";
import { ResponseWrapper } from "@/lib/types/responseWrapper";
import { ERROR_CODES } from "@/app/api/error_codes";
import { TEMPLATE_CAPITALIZEDsStatsData, TEMPLATE_CAPITALIZEDStatsParams } from "./types";
import { GroupRoleResourceAccessManager } from "@/lib/repositories/AccessManager";
import { SessionContext } from "@/lib/repositories/auth/types";
import { TEMPLATE_CAPITALIZEDAccessResource } from "./access_manager_resource";
import { TEMPLATE_CAPITALIZEDResponses } from "@/lib/utils/api-response-helper";

// Get TEMPLATE_CAPITALIZEDs Stats Implementation
export async function implHandleGetTEMPLATE_CAPITALIZEDsStats(
  businessLogic: TEMPLATE_CAPITALIZEDBusinessLogicInterface,
  params?: TEMPLATE_CAPITALIZEDStatsParams,
  context?: SessionContext,
  accessManager?: GroupRoleResourceAccessManager<TEMPLATE_CAPITALIZEDAccessResource>,
): Promise<{
  status: number;
  body: ResponseWrapper<TEMPLATE_CAPITALIZEDsStatsData>;
}> {

  const isAuthorized = await accessManager?.isAllowed({
    resource: "GET /TEMPLATE_KEBABCASEDs/stats",
    action: "read",
    userId: context?.user.id ?? "",
  });

  if (!isAuthorized) {
    return TEMPLATE_CAPITALIZEDResponses.unauthorizedRole()
  }

  try {
    // Get all TEMPLATE_CAMELCASEDs with filters
    const allTEMPLATE_CAPITALIZEDsResult = await businessLogic.getAll({
      search: params?.search,
      includeDeleted: params?.includeDeleted,
      filters: params?.filters,
      limit: 10000 // Get all for stats calculation
    });

    const TEMPLATE_CAMELCASEDs = allTEMPLATE_CAPITALIZEDsResult.items;
    const totalTEMPLATE_CAPITALIZEDs = TEMPLATE_CAMELCASEDs.length;

    // Calculate basic stats
    const activeTEMPLATE_CAPITALIZEDs = TEMPLATE_CAMELCASEDs.filter(c => !c.deletedAt).length;
    const deletedTEMPLATE_CAPITALIZEDs = TEMPLATE_CAMELCASEDs.filter(c => c.deletedAt).length;
    const TEMPLATE_CAMELCASEDsWithEmail = TEMPLATE_CAMELCASEDs.filter(c => c.email && c.email.trim() !== '').length;
    const TEMPLATE_CAMELCASEDsWithTags = TEMPLATE_CAMELCASEDs.filter(c => c.tags && c.tags.length > 0).length;

    // Recent TEMPLATE_CAMELCASEDs (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const recentTEMPLATE_CAPITALIZEDs = TEMPLATE_CAMELCASEDs.filter(c => new Date(c.createdAt) >= sevenDaysAgo).length;

    // Status breakdown
    const statusBreakdown = [
      {
        status: 'Active',
        count: activeTEMPLATE_CAPITALIZEDs,
        percentage: totalTEMPLATE_CAPITALIZEDs > 0 ? Math.round((activeTEMPLATE_CAPITALIZEDs / totalTEMPLATE_CAPITALIZEDs) * 100) : 0
      },
      {
        status: 'Deleted',
        count: deletedTEMPLATE_CAPITALIZEDs,
        percentage: totalTEMPLATE_CAPITALIZEDs > 0 ? Math.round((deletedTEMPLATE_CAPITALIZEDs / totalTEMPLATE_CAPITALIZEDs) * 100) : 0
      }
    ];

    // Tag breakdown
    const tagCounts: Record<string, number> = {};
    TEMPLATE_CAMELCASEDs.forEach(TEMPLATE_CAMELCASED => {
      if (TEMPLATE_CAMELCASED.tags && TEMPLATE_CAMELCASED.tags.length > 0) {
        TEMPLATE_CAMELCASED.tags.forEach(tag => {
          tagCounts[tag] = (tagCounts[tag] || 0) + 1;
        });
      } else {
        tagCounts['No Tag'] = (tagCounts['No Tag'] || 0) + 1;
      }
    });

    const tagBreakdown = Object.entries(tagCounts)
      .map(([tag, count]) => ({
        tag,
        count,
        percentage: totalTEMPLATE_CAPITALIZEDs > 0 ? Math.round((count / totalTEMPLATE_CAPITALIZEDs) * 100) : 0
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10); // Top 10 tags

    // Created by breakdown
    const createdByCounts: Record<string, number> = {};
    TEMPLATE_CAMELCASEDs.forEach(TEMPLATE_CAMELCASED => {
      const createdBy = TEMPLATE_CAMELCASED.createdBy || 'System';
      createdByCounts[createdBy] = (createdByCounts[createdBy] || 0) + 1;
    });

    const createdByBreakdown = Object.entries(createdByCounts)
      .map(([createdBy, count]) => ({
        createdBy,
        count,
        percentage: totalTEMPLATE_CAPITALIZEDs > 0 ? Math.round((count / totalTEMPLATE_CAPITALIZEDs) * 100) : 0
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10); // Top 10 creators

    // Daily stats for the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const dailyStats: Array<{ date: string; created: number; deleted: number }> = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];

      const created = TEMPLATE_CAMELCASEDs.filter(c =>
        new Date(c.createdAt).toISOString().split('T')[0] === dateStr
      ).length;

      const deleted = TEMPLATE_CAMELCASEDs.filter(c =>
        c.deletedAt && new Date(c.deletedAt).toISOString().split('T')[0] === dateStr
      ).length;

      dailyStats.push({ date: dateStr, created, deleted });
    }

    const statsData: TEMPLATE_CAPITALIZEDsStatsData = {
      totalTEMPLATE_CAPITALIZEDs,
      activeTEMPLATE_CAPITALIZEDs,
      deletedTEMPLATE_CAPITALIZEDs,
      TEMPLATE_CAMELCASEDsWithEmail,
      TEMPLATE_CAMELCASEDsWithTags,
      recentTEMPLATE_CAPITALIZEDs,
      statusBreakdown,
      tagBreakdown,
      createdByBreakdown,
      dailyStats
    };

    return {
      status: 200,
      body: new ResponseWrapper("success", statsData),
    };
  } catch (error: any) {
    console.error("Get TEMPLATE_CAMELCASEDs stats error:", error);

    return {
      status: 500,
      body: new ResponseWrapper<TEMPLATE_CAPITALIZEDsStatsData>(
        "failed",
        undefined,
        ["Failed to fetch TEMPLATE_CAMELCASEDs statistics. Please try again."],
        [ERROR_CODES.FETCH_FAILED]
      ),
    };
  }
}
