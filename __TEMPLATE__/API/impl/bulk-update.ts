import { TEMPLATE_CAPITALIZEDBusinessLogicInterface } from "@/lib/repositories/TEMPLATE_KEBABCASEDs/interface";
import { ResponseWrapper } from "@/lib/types/responseWrapper";
import { ERROR_CODES } from "@/app/api/error_codes";
import { BulkUpdateRequest, BulkOperationResult } from "./types";
import { GroupRoleResourceAccessManager } from "@/lib/repositories/AccessManager";
import { SessionContext } from "@/lib/repositories/auth/types";
import { TEMPLATE_CAPITALIZEDResponses } from "@/lib/utils/api-response-helper";
import { TEMPLATE_CAPITALIZEDAccessResource } from "./access_manager_resource";

// Bulk update implementation
export async function implBulkUpdateTEMPLATE_CAPITALIZEDs(
  request: BulkUpdateRequest,
  businessLogic: TEMPLATE_CAPITALIZEDBusinessLogicInterface,
  context?: SessionContext,
  accessManager?: GroupRoleResourceAccessManager<TEMPLATE_CAPITALIZEDAccessResource>,
): Promise<{
  status: number;
  body: ResponseWrapper<BulkOperationResult>;
}> {

  const isAuthorized = await accessManager?.isAllowed({
    resource: "PUT /bulk",
    action: "update",
    userId: context?.user.id ?? "",
  });

  if (!isAuthorized) {
    return TEMPLATE_CAPITALIZEDResponses.unauthorizedRole();
  }

  try {
    const { data } = request;

    if (!Array.isArray(data)) {
      return {
        status: 400,
        body: new ResponseWrapper<BulkOperationResult>(
          "failed",
          undefined,
          ["Invalid data format - expected array"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    if (data.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper<BulkOperationResult>(
          "failed",
          undefined,
          ["No data provided for update"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // Validate all data and prepare updates
    const validatedUpdates: Array<{ id: string; data: any }> = [];
    const validationErrors: Array<{ row: number; field: string; message: string }> = [];

    for (let i = 0; i < data.length; i++) {
      const TEMPLATE_CAMELCASEDData = data[i];

      // Validate required fields
      if (!TEMPLATE_CAMELCASEDData.id) {
        validationErrors.push({
          row: i,
          field: 'id',
          message: 'ID is required for updates'
        });
        continue;
      }

      // Prepare update data (only include fields that exist in TEMPLATE_CAPITALIZEDUpdateInput)
      const updateData = {
        name: TEMPLATE_CAMELCASEDData.name,
        phone: TEMPLATE_CAMELCASEDData.phone,
        email: TEMPLATE_CAMELCASEDData.email,
        tags: TEMPLATE_CAMELCASEDData.tags,
        notes: TEMPLATE_CAMELCASEDData.notes?.map(note => ({
          text: note.text,
          createdAt: note.createdAt || new Date().toISOString()
        }))
      };

      validatedUpdates.push({
        id: TEMPLATE_CAMELCASEDData.id,
        data: updateData
      });
    }

    const results: BulkOperationResult = {
      total: data.length,
      successful: 0,
      failed: validationErrors.length,
      errors: validationErrors
    };

    // If we have valid updates, perform bulk update
    if (validatedUpdates.length > 0) {
      try {
        const updatedCount = await businessLogic.bulkUpdate(validatedUpdates);
        results.successful = updatedCount;

        // If some updates failed (updatedCount < validatedUpdates.length)
        if (updatedCount < validatedUpdates.length) {
          const failedCount = validatedUpdates.length - updatedCount;
          results.failed += failedCount;
          results.errors.push({
            row: 0,
            field: 'general',
            message: `${failedCount} TEMPLATE_CAMELCASEDs could not be updated (may not exist or have validation errors)`
          });
        }
      } catch (error: any) {
        // If bulk operation fails, mark all remaining as failed
        results.failed = data.length;
        results.successful = 0;
        results.errors = [{
          row: 0,
          field: 'general',
          message: error instanceof Error ? error.message : 'Bulk update operation failed'
        }];
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", results),
    };
  } catch (error: any) {
    console.error("Bulk update TEMPLATE_CAMELCASEDs error:", error);

    return {
      status: 500,
      body: new ResponseWrapper<BulkOperationResult>(
        "failed",
        undefined,
        ["Failed to update TEMPLATE_CAMELCASEDs. Please try again."],
        [ERROR_CODES.UPDATE_FAILED]
      ),
    };
  }
}
