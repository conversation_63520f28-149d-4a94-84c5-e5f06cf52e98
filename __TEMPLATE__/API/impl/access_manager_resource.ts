import { AccessRule, GroupRoleResourceAccessManager } from "@/lib/repositories/AccessManager";

// Use wildcards only; no ":id"
export const AccessManagerResource = [
  "* /TEMPLATE_KEBABCASEDs*",
  "GET /TEMPLATE_KEBABCASEDs",
  "GET /TEMPLATE_KEBABCASEDs/stats",
  // replaced all ":id" with "*"
  "GET /TEMPLATE_KEBABCASEDs/*",
  "POST /TEMPLATE_KEBABCASEDs",
  "PUT /TEMPLATE_KEBABCASEDs/*",
  "DELETE /TEMPLATE_KEBABCASEDs/*",
  "DELETE /bulk",
  "POST /bulk",
  "PUT /bulk",
] as const;

export type TEMPLATE_CAPITALIZEDAccessResource = typeof AccessManagerResource[number];


export async function setInitialTEMPLATE_CAPITALIZEDAccessOnRegister(
  groupId: string,
  accessManager: GroupRoleResourceAccessManager<TEMPLATE_CAPITALIZEDAccessResource>
): Promise<void> {
  await accessManager.appendRules([
    // Admin – full access
    {
      resource: "* /TEMPLATE_KEBABCASEDs*",
      group: groupId,
      role: "admin",
      actions: ["read", "create", "update", "delete"],
    },

    {
      resource: "GET /TEMPLATE_KEBABCASEDs",
      group: groupId,
      role: "member",
      actions: ["read"],
    },
    // changed from "GET /TEMPLATE_KEBABCASEDs/:id" to wildcard
    {
      resource: "GET /TEMPLATE_KEBABCASEDs/*",
      group: groupId,
      role: "member",
      actions: ["read"],
    },
    // changed from "PUT /TEMPLATE_KEBABCASEDs/:id" to wildcard
    {
      resource: "PUT /TEMPLATE_KEBABCASEDs/*",
      group: groupId,
      role: "member",
      actions: ["update"],
    },
  ]);
}
