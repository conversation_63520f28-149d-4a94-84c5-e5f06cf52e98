import { NextRequest, NextResponse } from "next/server";
import { TEMPLATE_CAMELCASEDsBusinessLogic } from "@/lib/repositories/businessLogics";
import { implHandleGetAllTEMPLATE_CAPITALIZEDs, implHandleCreateTEMPLATE_CAPITALIZED } from "./impl";
import { ERROR_CODES } from "@/app/api/error_codes";
import { TEMPLATE_CAMELCASEDsSearchConfig } from "../search-configs/entities/TEMPLATE_KEBABCASEDs";
import { ResponseWrapper } from "@/lib/types/responseWrapper";
import { buildSessionContext } from "@/src/app/api/sharedFunction";
import { TEMPLATE_CAMELCASEDAccessManager } from "@/lib/repositories/accessManagers";

export async function GET(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req);
    if (response) {
      return response;
    }

    const { searchParams } = new URL(req.url);

    const search = searchParams.get('search') || undefined;
    const includeDeleted = searchParams.get('includeDeleted') === 'true';
    const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : undefined;
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined;

    const { result: searchConfigResult, errors: searchConfigErrors } = TEMPLATE_CAMELCASEDsSearchConfig.parseParams(searchParams);

    if (searchConfigErrors) {
      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          undefined,
          searchConfigErrors,
          [ERROR_CODES.VALIDATION_FAILED]
        ),
        { status: 400 }
      );
    }

    const sorts = searchConfigResult?.sorts || [];
    const filters = searchConfigResult?.filters || [];

    const params = {
      search,
      includeDeleted,
      page,
      limit,
      sorts,
      filters
    };

    const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(TEMPLATE_CAMELCASEDsBusinessLogic, params, context, TEMPLATE_CAMELCASEDAccessManager(context.organization?.id || context.user.id));
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("TEMPLATE_CAPITALIZEDs GET route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {

    const { context, response } = await buildSessionContext(req);
    if (response) {
      return response;
    }

    const body = await req.json();
    const result = await implHandleCreateTEMPLATE_CAPITALIZED(body, TEMPLATE_CAMELCASEDsBusinessLogic, context, TEMPLATE_CAMELCASEDAccessManager(context.organization?.id || context.user.id));
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("TEMPLATE_CAPITALIZEDs POST route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}
