import { NextRequest, NextResponse } from 'next/server'
import { TEMPLATE_CAMELCASEDsBusinessLogic } from "@/lib/repositories/businessLogics";
import {
  implValidateBulkImportRequest,
  implBulkImportTEMPLATE_CAPITALIZEDs,
  implValidateBulkUpdateRequest,
  implBulkUpdateTEMPLATE_CAPITALIZEDs,
  implValidateBulkDeleteRequest,
  implBulkDeleteTEMPLATE_CAPITALIZEDs
} from '../impl';
import { buildSessionContext } from '@/app/api/sharedFunction';
import { TEMPLATE_CAMELCASEDAccessManager } from '@/lib/repositories/accessManagers';

export async function POST(req: NextRequest) {
  try {

    const { context, response } = await buildSessionContext(req);
    if (response) {
      return response;
    }

    const body = await req.json()

    // Validate req format
    if (!implValidateBulkImportRequest(body)) {
      return NextResponse.json({
        status: 400,
        body: {
          status: 'failed',
          data: null,
          messages: ['Invalid operation or data format']
        }
      }, { status: 400 })
    }

    // Execute bulk import
    const result = await implBulkImportTEMPLATE_CAPITALIZEDs(body, TEMPLATE_CAMELCASEDsBusinessLogic, context, TEMPLATE_CAMELCASEDAccessManager(context.organization?.id || context.user.id));

    return NextResponse.json({
      status: result.status,
      body: result.body
    })

  } catch (error) {
    console.error('Bulk import error:', error)
    return NextResponse.json({
      status: 500,
      body: {
        status: 'failed',
        data: null,
        messages: ['Internal server error during bulk import']
      }
    }, { status: 500 })
  }
}

export async function PUT(req: NextRequest) {
  try {

    const { context, response } = await buildSessionContext(req);
    if (response) {
      return response;
    }

    const body = await req.json()

    // Validate req format
    if (!implValidateBulkUpdateRequest(body)) {
      return NextResponse.json({
        status: 400,
        body: {
          status: 'failed',
          data: null,
          messages: ['Invalid operation or data format']
        }
      }, { status: 400 })
    }

    // Execute bulk update
    const result = await implBulkUpdateTEMPLATE_CAPITALIZEDs(body, TEMPLATE_CAMELCASEDsBusinessLogic, context, TEMPLATE_CAMELCASEDAccessManager(context.organization?.id || context.user.id));

    return NextResponse.json({
      status: result.status,
      body: result.body
    })

  } catch (error) {
    console.error('Bulk update error:', error)
    return NextResponse.json({
      status: 500,
      body: {
        status: 'failed',
        data: null,
        messages: ['Internal server error during bulk update']
      }
    }, { status: 500 })
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req);
    if (response) {
      return response;
    }

    const body = await req.json()

    // Validate req format
    if (!implValidateBulkDeleteRequest(body)) {
      return NextResponse.json({
        status: 400,
        body: {
          status: 'failed',
          data: null,
          messages: ['Invalid operation or IDs format']
        }
      }, { status: 400 })
    }

    // Execute bulk delete
    const result = await implBulkDeleteTEMPLATE_CAPITALIZEDs(body, TEMPLATE_CAMELCASEDsBusinessLogic, false, context, TEMPLATE_CAMELCASEDAccessManager(context.organization?.id || context.user.id));

    return NextResponse.json({
      status: result.status,
      body: result.body
    })

  } catch (error) {
    console.error('Bulk delete error:', error)
    return NextResponse.json({
      status: 500,
      body: {
        status: 'failed',
        data: null,
        messages: ['Internal server error during bulk delete']
      }
    }, { status: 500 })
  }
}
