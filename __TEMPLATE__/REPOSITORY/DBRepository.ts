import { TEMPLATE_CAPITALIZED, TEMPLATE_CAPITALIZEDCreateInput, TEMPLATE_CAPITALIZEDUpdateInput } from "./interface";
import { BaseDbRepository, BaseQueryParams } from "../BaseDBRepository";

export type TEMPLATE_CAPITALIZEDDbQueryParams = BaseQueryParams<TEMPLATE_CAPITALIZED>;

export interface TEMPLATE_CAPITALIZEDDBRepository
  extends BaseDbRepository<TEMPLATE_CAPITALIZED, TEMPLATE_CAPITALIZEDCreateInput, TEMPLATE_CAPITALIZEDUpdateInput, TEMPLATE_CAPITALIZEDDbQueryParams> {}
