// TEMPLATE_CAMELCASED.ts

export interface TEMPLATE_CAPITALIZED {
  id: string;
  name: string;
  phone: string;
  email?: string;
  tags?: string[];
  notes?: { text: string; createdAt: string }[];

  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  createdBy?: string;
  updatedBy?: string;
}

export interface TEMPLATE_CAPITALIZEDCreateInput {
  name: string;
  phone: string;
  email?: string;
  tags?: string[];
  notes?: { text: string; createdAt: string }[];
  createdBy?: string;
}

export interface TEMPLATE_CAPITALIZEDUpdateInput {
  name?: string;
  phone?: string;
  email?: string;
  tags?: string[];
  notes?: { text: string; createdAt: string }[];
  updatedBy?: string;
}

export interface TEMPLATE_CAPITALIZEDQueryParams {
  search?: string;
  filters?: { field: keyof TEMPLATE_CAPITALIZED | string; value: any }[];
  sorts?: { field: keyof TEMPLATE_CAPITALIZED | string; direction: "asc" | "desc" }[];
  page?: number;
  limit?: number;
  includeDeleted?: boolean;
}

export interface TEMPLATE_CAPITALIZEDBusinessLogicInterface {
  getById(id: string, includeDeleted?: boolean): Promise<TEMPLATE_CAPITALIZED | null>;
  getAll(params?: TEMPLATE_CAPITALIZEDQueryParams): Promise<{
    items: TEMPLATE_CAPITALIZED[];
    total: number;
  }>;
  create(data: TEMPLATE_CAPITALIZEDCreateInput): Promise<TEMPLATE_CAPITALIZED>;
  update(id: string, data: TEMPLATE_CAPITALIZEDUpdateInput): Promise<TEMPLATE_CAPITALIZED | null>;
  delete(id: string, hardDelete?: boolean): Promise<boolean>;
  restore(id: string): Promise<boolean>;

  bulkCreate(data: TEMPLATE_CAPITALIZEDCreateInput[]): Promise<TEMPLATE_CAPITALIZED[]>;
  bulkUpdate(updates: { id: string; data: TEMPLATE_CAPITALIZEDUpdateInput }[]): Promise<number>;
  bulkDelete(ids: string[], hardDelete?: boolean): Promise<number>;
}
