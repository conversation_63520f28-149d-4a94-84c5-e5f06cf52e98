import {
  TEMPLATE_CAPITALIZED,
  TEMPLATE_CAPITALIZEDCreateInput,
  TEMPLATE_CAPITALIZEDUpdateInput,
  TEMPLATE_CAPITALIZEDQueryParams,
  TEMPLATE_CAPITALIZEDBusinessLogicInterface,
} from "./interface";
import { createError } from "@/lib/utils/common";
import { TEMPLATE_CAPITALIZEDDBRepository } from "./DBRepository";

export class TEMPLATE_CAPITALIZEDBusinessLogic implements TEMPLATE_CAPITALIZEDBusinessLogicInterface {
  constructor(private readonly db: TEMPLATE_CAPITALIZEDDBRepository) {}

  private validateId(id: string) {
    if (!id || !id.trim()) throw createError("TEMPLATE_CAPITALIZED ID is required", "INVALID_ID");
  }

  private trimCreateInput(data: TEMPLATE_CAPITALIZEDCreateInput): TEMPLATE_CAPITALIZEDCreateInput {
    return {
      ...data,
      name: data.name.trim(),
      phone: data.phone?.trim() ?? "",
      tags: data.tags?.map(t => t.trim()) ?? [],
    };
  }

  private trimUpdateInput(data: TEMPLATE_CAPITALIZEDUpdateInput): TEMPLATE_CAPITALIZEDUpdateInput {
    return {
      ...data,
      name: data.name?.trim(),
      phone: data.phone?.trim(),
      tags: data.tags?.map(t => t.trim())
    };
  }

  async getById(id: string, includeDeleted = false): Promise<TEMPLATE_CAPITALIZED | null> {
    this.validateId(id);
    return this.db.getById(id, includeDeleted);
  }

  async getAll(params?: TEMPLATE_CAPITALIZEDQueryParams): Promise<{ items: TEMPLATE_CAPITALIZED[]; total: number }> {
    // Optionally validate filters/sorts here if needed
    return this.db.getAll(params);
  }

  async create(data: TEMPLATE_CAPITALIZEDCreateInput): Promise<TEMPLATE_CAPITALIZED> {
    const trimmedData = this.trimCreateInput(data);

    // Check for duplicate name
    const existing = await this.db.getCount({ filters: [{ field: "name", value: trimmedData.name }] });
    if (existing.total > 0) {
      throw createError("TEMPLATE_CAPITALIZED with the same name already exists", "DUPLICATE_NAME");
    }

    return this.db.create(trimmedData);
  }

  async update(id: string, data: TEMPLATE_CAPITALIZEDUpdateInput): Promise<TEMPLATE_CAPITALIZED | null> {
    this.validateId(id);

    if (!data || Object.keys(data).length === 0) {
      throw createError("No data provided for update", "INVALID_UPDATE_DATA");
    }

    const existingRule = await this.db.getById(id);
    if (!existingRule) throw createError("TEMPLATE_CAPITALIZED not found", "NOT_FOUND");

    if (data.name && data.name.trim() !== existingRule.name) {
      const duplicates = await this.db.getAll({ filters: [{ field: "name", value: data.name.trim() }] });
      if (duplicates.items.some(r => r.id !== id)) {
        throw createError("Another TEMPLATE_CAPITALIZED with this name exists", "DUPLICATE_NAME");
      }
    }

    const trimmedData = this.trimUpdateInput(data);
    return this.db.update(id, trimmedData);
  }

  async delete(id: string, hardDelete = false): Promise<boolean> {
    this.validateId(id);

    const existingRule = await this.db.getById(id);
    if (!existingRule) throw createError("TEMPLATE_CAPITALIZED not found", "NOT_FOUND");

    return this.db.delete(id, hardDelete);
  }

  async restore(id: string): Promise<boolean> {
    this.validateId(id);

    const TEMPLATE_CAMELCASED = await this.db.getById(id, true);
    if (!TEMPLATE_CAMELCASED || !TEMPLATE_CAMELCASED.deletedAt) return false;

    // Check for conflicts by name
    const conflict = await this.db.getAll({ filters: [{ field: "name", value: TEMPLATE_CAMELCASED.name }] });
    if (conflict.items.length > 0) return false;

    return this.db.restore(id);
  }

  async bulkCreate(data: TEMPLATE_CAPITALIZEDCreateInput[]): Promise<TEMPLATE_CAPITALIZED[]> {
    if (!Array.isArray(data) || data.length === 0) {
      throw createError("Input must be a non-empty array", "INVALID_BULK_CREATE_DATA");
    }

    for (const entry of data) {
      this.trimCreateInput(entry); // Will throw if invalid
      const existing = await this.db.getAll({ filters: [{ field: "name", value: entry.name.trim() }] });
      if (existing.items.length > 0) {
        throw createError(`Duplicate name found: ${entry.name}`, "DUPLICATE_NAME");
      }
    }

    const trimmedData = data.map(d => this.trimCreateInput(d));
    return this.db.bulkCreate(trimmedData);
  }

  async bulkUpdate(updates: { id: string; data: TEMPLATE_CAPITALIZEDUpdateInput }[]): Promise<number> {
    if (!Array.isArray(updates) || updates.length === 0) {
      throw createError("Input must be a non-empty array", "INVALID_BULK_UPDATE_DATA");
    }

    for (const { id, data } of updates) {
      this.validateId(id);

      if (!data || Object.keys(data).length === 0) {
        throw createError(`No data provided for update of ID ${id}`, "INVALID_UPDATE_DATA");
      }

      const existingRule = await this.db.getById(id);
      if (!existingRule) throw createError(`Rule with ID ${id} not found`, "NOT_FOUND");

      if (data.name && data.name.trim() !== existingRule.name) {
        const duplicates = await this.db.getAll({ filters: [{ field: "name", value: data.name.trim() }] });
        if (duplicates.items.some(r => r.id !== id)) {
          throw createError(`Duplicate name in update: ${data.name}`, "DUPLICATE_NAME");
        }
      }

      this.trimUpdateInput(data); // Will throw if invalid
    }

    const trimmedUpdates = updates.map(({ id, data }) => ({
      id,
      data: this.trimUpdateInput(data),
    }));

    return this.db.bulkUpdate(trimmedUpdates);
  }

  async bulkDelete(ids: string[], hardDelete = false): Promise<number> {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw createError("IDs must be a non-empty array", "INVALID_BULK_DELETE_DATA");
    }

    for (const id of ids) {
      this.validateId(id);
      const TEMPLATE_CAMELCASED = await this.db.getById(id);
      if (!TEMPLATE_CAMELCASED) throw createError(`Rule with ID ${id} not found`, "NOT_FOUND");
    }

    return this.db.bulkDelete(ids, hardDelete);
  }

  async search(query: string): Promise<TEMPLATE_CAPITALIZED[]> {
    if (!query || !query.trim()) {
      return [];
    }

    const searchTerm = query.trim();
    const result = await this.db.getAll({
      filters: [
        { field: "name", value: { $regex: searchTerm, $options: "i" } }
      ]
    });

    return result.items;
  }
}
