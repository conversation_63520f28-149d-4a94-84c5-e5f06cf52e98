import { SearchConfigApiResponseVisitor } from '../SearchConfigApiResponseVisitor'

export abstract class Sort {
  protected field: string
  protected label: string
  protected defaultDirection: 'asc' | 'desc'

  constructor(field: string, label: string, defaultDirection: 'asc' | 'desc' = 'asc') {
    this.field = field
    this.label = label
    this.defaultDirection = defaultDirection
  }

  abstract buildForApiResponse(visitor: SearchConfigApiResponseVisitor): void

  parseFromQuery(query: URLSearchParams): { field: string, direction: 'asc' | 'desc' } | null {
    const value = query.get("sort." + this.field)
    if (value) {
      const direction = value.toLowerCase() === 'desc' ? 'desc' : 'asc'
      return { field: this.field, direction }
    }
    return null
  }
}
