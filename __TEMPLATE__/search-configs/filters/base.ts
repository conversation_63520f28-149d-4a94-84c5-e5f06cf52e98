import { SearchConfigApiResponseVisitor } from "../SearchConfigApiResponseVisitor"

// ✨ Base interfaces for search filters
export interface MongoSearchQuery {
  [key: string]: any
}

export interface ApiFilterResponse {
  id: string
  name: string
  field: string
  type: string
  options?: Array<{ value: string; label: string; description?: string; disabled?: boolean }>
  validation?: {
    required?: boolean
    minLength?: number
    maxLength?: number
    pattern?: string
    minItems?: number
    maxItems?: number
    min?: number
    max?: number
    minDate?: string
    maxDate?: string
    maxRange?: number
    domain?: string
    allowedDomains?: string[]
    blockedDomains?: string[]
  }
  // Additional properties for different filter types
  placeholder?: string
  searchType?: string
  requireVerified?: boolean
  checkMultipleFields?: string[]
  matchAll?: boolean
  caseSensitive?: boolean
  exactMatch?: boolean
  allowCustom?: boolean
  defaultValue?: string | string[]
  allowSingleDate?: boolean
  includeTime?: boolean
  defaultRange?: string
}

export interface QueryBuilder {
  addDateRange(field: string, start: Date, end: Date): void
  addStringFilter(field: string, value: string, operator?: string): void
  addBooleanFilter(field: string, value: boolean): void
  addMultipleSelectFilter(field: string, values: string[]): void
  addRegexFilter(field: string, regex: RegExp): void
  addNotRegexFilter(field: string, regex: RegExp): void
}

export interface QueryValueHolder {
  visit(queryBuilder: QueryBuilder): void
}

// ✨ Base SearchFilter class
export interface SearchFilter {
  field: string
  label: string

  buildForApiResponse(visitor: SearchConfigApiResponseVisitor): void
  parseFromQuery(query: URLSearchParams): { field: string, value: QueryValueHolder } | null
}
