import { SearchConfigApiResponseVisitor } from '../SearchConfigApiResponseVisitor'
import { QueryValueHolder, QueryBuilder, SearchFilter } from './base'

export interface StringSearchOptions {
  validations?: Array<(value: string) => void>
}

class StringQueryValueHolder implements QueryValueHolder {
  constructor(private field: string, private operator: string, private value: string) { }

  visit(queryBuilder: QueryBuilder) {
    queryBuilder.addStringFilter(this.field, this.value, this.operator)
  }
}

// ✨ StringSearch filter for text-based searches
export class StringSearch implements SearchFilter {
  readonly field: string
  readonly name: string
  readonly label: string
  private options: StringSearchOptions

  private operators = [
    'contains',
    'equals',
    'startsWith',
    'endsWith'
  ]

  constructor(
    field: string,
    label: string,
    options: StringSearchOptions = {}
  ) {
    this.field = field
    this.name = field
    this.label = label
    this.options = options
  }

  buildForApiResponse(visitor: SearchConfigApiResponseVisitor) {
    visitor.addFilter({
      id: this.field,
      field: this.field,
      name: this.label,
      type: "text",
    })
  }

  parseFromQuery(query: URLSearchParams): { field: string, value: QueryValueHolder } | null {
    const value = query.get(`filter.${this.field}`)
    if (!value) return null

    const [operator, actualValue] = value.split(':')

    this.options.validations?.forEach(validationFn => validationFn(actualValue))

    return { field: this.field, value: new StringQueryValueHolder(this.field, operator, actualValue) }
  }
}
