import { SearchConfigApiResponseVisitor } from '../SearchConfigApiResponseVisitor'
import { QueryValueHolder, QueryBuilder, SearchFilter } from './base'

export interface DateRangeSearchOptions {
  validations?: Array<(value: string) => void>
  dateRangeMapper: (value: string) => { start: Date, end: Date }
}

const generateId = () => Math.random().toString(36).substring(2, 11)


class DateRangeQueryValueHolder implements QueryValueHolder {
  constructor(private field: string, private start: Date, private end: Date) {}

  visit(queryBuilder: QueryBuilder) {
    queryBuilder.addDateRange(this.field, this.start, this.end)
  }
}

export class DateRangeSearch implements SearchFilter {
  readonly field: string
  readonly value: string
  readonly label: string
  private options: DateRangeSearchOptions

  constructor(field: string, value: string, label: string, options: DateRangeSearchOptions) {
    this.field = field
    this.value = value
    this.label = label
    this.options = options
  }

  buildForApiResponse(visitor: SearchConfigApiResponseVisitor) {
    visitor.addDateFilterOption({
      id: generateId(),
      value: this.value,
      label: this.label,
      type: 'date',
    })
  }

  parseFromQuery(query: URLSearchParams): { field: string, value: QueryValueHolder } | null {
    const value = query.get("filter.date_range")
    if (!value) return null

    if (value !== this.value) {
      return null
    }

    this.options.validations?.forEach(validationFn => validationFn(value))

    const range = this.options.dateRangeMapper(value)
      return { field: this.field, value: new DateRangeQueryValueHolder(this.field, range.start, range.end) }
  }
}
