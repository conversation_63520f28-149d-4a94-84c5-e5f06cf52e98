import { SearchFilter, QueryValueHolder } from "../filters/base"
import { SearchConfigApiResponseVisitor } from "../SearchConfigApiResponseVisitor"
import { Sort } from "../sorts/interface"
import { MongoQueryBuilder } from "./mongoQueryBuilder"

export class BaseSearchConfig {
    constructor(
        protected filters: SearchFilter[],
        protected sorts: Sort[],
        public searchableFields: string[],
        protected dateFilters: any[]
    ) {
        this.filters = filters
        this.sorts = sorts
        this.searchableFields = searchableFields
        this.dateFilters = dateFilters
    }

    buildForApiResponse(visitor: SearchConfigApiResponseVisitor) {
        for (const filter of this.filters) {
            filter.buildForApiResponse(visitor)
        }

        for (const sort of this.sorts) {
            sort.buildForApiResponse(visitor)
        }

        for (const dateFilter of this.dateFilters) {
            dateFilter.buildForApiResponse(visitor)
        }
    }

    buildMongoQuery(params: {
        sorts: { field: string, direction: 'asc' | 'desc' }[],
        filters: { field: string, value: QueryValueHolder | any }[]
    }): { query: any, sort: any } {
        const mongoQueryBuilder = new MongoQueryBuilder()


        for (const filter of params.filters) {
            const value = filter.value

            if (typeof value === 'object' && 'visit' in value) {
                value.visit(mongoQueryBuilder)
            } else {
                mongoQueryBuilder.addStringFilter(filter.field, value?.toString?.() || '')
            }
        }

        const query = mongoQueryBuilder.getQuery()
        const sort: Record<string, 1 | -1> = {}

        for (const s of params.sorts) {
            sort[s.field] = s.direction === 'asc' ? 1 : -1
        }

        return { query, sort }
    }

    parseParams(searchParams: URLSearchParams): {
        result?: { sorts: { field: string, direction: 'asc' | 'desc' }[], filters: { field: string, value: QueryValueHolder }[] },
        errors?: string[]
    } {
        try {
            const params: { sorts: { field: string, direction: 'asc' | 'desc' }[], filters: { field: string, value: QueryValueHolder }[] } = {
                sorts: [],
                filters: []
            }

            for (const filter of this.filters) {
                const parsed = filter.parseFromQuery(searchParams)
                if (parsed) {
                    params.filters.push(parsed)
                }
            }

            for (const dateFilter of this.dateFilters) {
                const parsed = dateFilter.parseFromQuery(searchParams)
                if (parsed) {
                    params.filters.push(parsed)
                }
            }

            for (const sort of this.sorts) {
                const parsed = sort.parseFromQuery(searchParams)
                if (parsed) {
                    params.sorts = params.sorts || []
                    params.sorts.push(parsed)
                }
            }

            return { result: params }
        } catch (error: any) {
            return { errors: [error.message] }
        }
    }
}