import { SearchConfigApiResponseVisitor } from '../SearchConfigApiResponseVisitor'
import { aiRuleSearchConfig } from './aiRule'
import { contactsSearchConfig } from './contacts'
import { SearchConfigEntity } from './interface'
import { workflowExecutionsSearchConfig } from './workflowExecutions'

const entities: Record<string, SearchConfigEntity> = {
  contacts: contactsSearchConfig,
  workflowExecutions: workflowExecutionsSearchConfig,
  aiRules: aiRuleSearchConfig
}

export function hasEntity(entityName: string): boolean {
  return entityName in entities
}

export function getRegisteredEntities(): string[] {
  return Object.keys(entities)
}

export function buildApiResponse(entityName: string): any {
  const entity = entities[entityName]
  const visitor = new SearchConfigApiResponseVisitor()

  entity.buildForApiResponse(visitor)

  return visitor.getResult()
}
