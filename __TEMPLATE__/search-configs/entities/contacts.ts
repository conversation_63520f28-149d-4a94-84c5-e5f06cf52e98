import MESSAGE_KEYS from '@/app/api/message_keys'
import { BooleanSearch } from '../filters/BooleanSearch'
import { IsGmailSearch } from '../filters/IsGmailSearch'
import { MultipleSelectSearch } from '../filters/MultipleSelectSearch'
import { StringSearch } from '../filters/StringSearch'
import { StringSort } from '../sorts/StringSort'
import {
  DateRangeSearchLastMonth,
  DateRangeSearchLastWeek,
  DateRangeSearchLastYear,
  DateRangeSearchThisMonth,
  DateRangeSearchThisWeek,
  DateRangeSearchThisYear,
  DateRangeSearchToday,
  DateRangeSearchYesterday
} from './common'
import { ERROR_KEYS } from './errors'
import { SearchConfigEntity } from './interface'
import { BaseSearchConfig } from './baseSearchConfig'


export class ContactsSearchConfig extends BaseSearchConfig implements SearchConfigEntity {
  constructor() {
    const filters = [
      new StringSearch('name', MESSAGE_KEYS.SEARCH_CONFIG.CONTACT_NAME, {
        validations: [
          (value: string) => {
            if (value.length == 0) throw new Error(ERROR_KEYS.NAME_MUST_NOT_EMPTY)
            const SPECIAL_CHAR_REGEX = /[^\p{L}\p{N} ]/u;

            if (SPECIAL_CHAR_REGEX.test(value)) {
              throw new Error(ERROR_KEYS.NAME_MUST_NOT_CONTAIN_SPECIAL_CHAR);
            }
          }
        ]
      }),

      new StringSearch('email', MESSAGE_KEYS.SEARCH_CONFIG.CONTACT_EMAIL, {
        validations: [
          (value: string) => {
            if (!value.includes('@')) throw new Error(ERROR_KEYS.INVALID_EMAIL_FORMAT)
          }
        ]
      }),

      new StringSearch('phone', MESSAGE_KEYS.SEARCH_CONFIG.CONTACT_PHONE, {
        validations: [
          (value: string) => {
            if (!/^[+]?[0-9\s\-()]+$/.test(value)) throw new Error(ERROR_KEYS.INVALID_PHONE_FORMAT)
          }
        ]
      }),
      new BooleanSearch('hasPhone', MESSAGE_KEYS.SEARCH_CONFIG.CONTACT_HAS_PHONE),
      new IsGmailSearch(),

      new MultipleSelectSearch('status', MESSAGE_KEYS.SEARCH_CONFIG.CONTACT_STATUS, [
        { value: 'active', label: MESSAGE_KEYS.SEARCH_CONFIG.STATUS_ACTIVE },
        { value: 'inactive', label: MESSAGE_KEYS.SEARCH_CONFIG.STATUS_INACTIVE },
        { value: 'archived', label: MESSAGE_KEYS.SEARCH_CONFIG.STATUS_ARCHIVED }
      ])
    ]

    const dateFilters = [
      DateRangeSearchToday,
      DateRangeSearchYesterday,
      DateRangeSearchThisWeek,
      DateRangeSearchLastWeek,
      DateRangeSearchThisMonth,
      DateRangeSearchLastMonth,
      DateRangeSearchThisYear,
      DateRangeSearchLastYear
    ]

    const searchableFields = [
      'name',
      'email',
      'phone',
      'tags'
    ]

    const sorts = [
      new StringSort('name', MESSAGE_KEYS.SEARCH_CONFIG.SORT_NAME),
      new StringSort('email', MESSAGE_KEYS.SEARCH_CONFIG.SORT_EMAIL),
    ]
    super(filters, sorts, searchableFields, dateFilters)
  }
}

export const contactsSearchConfig = new ContactsSearchConfig()
