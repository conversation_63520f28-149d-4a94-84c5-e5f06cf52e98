import Link from "next/link"
import type { LucideIcon } from "lucide-react"

import { cn } from "@/lib/utils"

interface Project {
  name: string
  url: string
  icon?: LucideIcon
}

interface NavProjectsProps {
  projects: Project[]
  className?: string
}

export function NavProjects({ projects, className }: NavProjectsProps) {
  return (
    <div className={cn("grid gap-1 px-2", className)}>
      <div className="flex items-center justify-between px-2 py-1.5">
        <div className="text-xs font-medium text-muted-foreground">Projects</div>
        <Link href="#" className="text-xs text-muted-foreground hover:text-foreground">
          View All
        </Link>
      </div>
      {projects.map((project, index) => (
        <Link
          key={index}
          href={project.url}
          className="flex h-9 items-center gap-2 rounded-md px-2 py-2 text-sm hover:bg-muted/50"
        >
          {project.icon && <project.icon className="h-4 w-4 text-muted-foreground" />}
          <span className="truncate">{project.name}</span>
        </Link>
      ))}
    </div>
  )
}
