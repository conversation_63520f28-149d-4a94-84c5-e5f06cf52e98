import { useState } from "react"
import { Plus, Trash } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Test } from "@/src/lib/repositories/tests/TestsRepository"

interface TestBenefitsProps {
  test: Test
  isEditing: boolean
  onTestChange: (test: Test) => void
  t: (key: string) => string
}

export function TestBenefits({ test, isEditing, onTestChange, t }: TestBenefitsProps) {
  const [newBenefit, setNewBenefit] = useState("")

  const handleAddBenefit = () => {
    if (newBenefit.trim() && test) {
      onTestChange({
        ...test,
        benefits: [...test.benefits, newBenefit.trim()],
      })
      setNewBenefit("")
    }
  }

  const handleRemoveBenefit = (index: number) => {
    if (test) {
      const newBenefits = [...test.benefits]
      newBenefits.splice(index, 1)
      onTestChange({
        ...test,
        benefits: newBenefits,
      })
    }
  }

  const handleBenefitChange = (index: number, value: string) => {
    const newBenefits = [...test.benefits]
    newBenefits[index] = value
    onTestChange({ ...test, benefits: newBenefits })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("benefits")}</CardTitle>
        <CardDescription>{t("benefits_desc")}</CardDescription>
      </CardHeader>
      <CardContent>
        <ul className="space-y-2">
          {test.benefits.map((benefit, index) => (
            <li key={index} className="flex items-center">
              {isEditing ? (
                <>
                  <Input
                    value={benefit}
                    onChange={(e) => handleBenefitChange(index, e.target.value)}
                    className="flex-1"
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveBenefit(index)}
                    className="ml-2"
                  >
                    <Trash className="h-4 w-4 text-red-500" />
                  </Button>
                </>
              ) : (
                <>
                  <div className="h-2 w-2 rounded-full bg-green-500 mr-2"></div>
                  {benefit}
                </>
              )}
            </li>
          ))}
        </ul>

        {isEditing && (
          <div className="flex mt-4">
            <Input
              value={newBenefit}
              onChange={(e) => setNewBenefit(e.target.value)}
              placeholder="Add new benefit"
              className="flex-1"
            />
            <Button variant="outline" onClick={handleAddBenefit} className="ml-2">
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
