import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { TestSession } from "@/src/lib/test-types"

interface TestSessionSummaryProps {
  testSessions: TestSession[]
  t: (key: string) => string
}

export function TestSessionSummary({ testSessions, t }: TestSessionSummaryProps) {
  const needsReviewCount = testSessions.filter((session) => session.status === "needs-review").length
  const completedCount = testSessions.filter(
    (session) => session.status === "completed" || session.status === "reviewed",
  ).length
  const inProgressCount = testSessions.filter((session) => session.status === "in-progress").length

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("session_summary")}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">Total {t("sessions")}</span>
          <span className="font-semibold">{testSessions.length}</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">{t("completed")}</span>
          <span className="font-semibold text-green-600">{completedCount}</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">{t("need_review")}</span>
          <span className="font-semibold text-orange-600">{needsReviewCount}</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">{t("in_progress")}</span>
          <span className="font-semibold text-yellow-600">{inProgressCount}</span>
        </div>
      </CardContent>
    </Card>
  )
}
