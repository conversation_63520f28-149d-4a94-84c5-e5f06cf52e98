import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

interface TestActionsProps {
  testId: string
  onPreviewTest: () => void
  onManageQuestions: () => void
  onDuplicateTest: () => void
  onDeleteTest?: () => void
  t: (key: string) => string
}

export function TestActions({ 
  testId, 
  onPreviewTest, 
  onManageQuestions, 
  onDuplicateTest, 
  onDeleteTest, 
  t 
}: TestActionsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("actions")}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        <Button
          variant="outline"
          className="w-full justify-start"
          onClick={onPreviewTest}
        >
          {t("btn_preview_test")}
        </Button>
        <Button
          variant="outline"
          className="w-full justify-start"
          onClick={onManageQuestions}
        >
          {t("btn_manage_question")}
        </Button>
        <Button 
          variant="outline" 
          className="w-full justify-start"
          onClick={onDuplicateTest}
        >
          {t("btn_duplicate_test")}
        </Button>
        {onDeleteTest && <Button
          variant="outline"
          className="w-full justify-start text-red-500 hover:text-red-700"
          onClick={onDeleteTest}
        >
          {t("btn_delete")}
        </Button>}
      </CardContent>
    </Card>
  )
}
