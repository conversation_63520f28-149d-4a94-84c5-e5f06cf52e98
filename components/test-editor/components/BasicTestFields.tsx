"use client"

import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Test } from "@/src/lib/repositories/tests/interface"

interface BasicTestFieldsProps {
  test: Test
  isEditing: boolean
  onTestChange: (test: Test) => void
}

export function BasicTestFields({ test, isEditing, onTestChange }: BasicTestFieldsProps) {
  return (
    <>
      <div>
        <label className="text-sm font-medium mb-1 block">Title</label>
        {isEditing ? (
          <Input
            value={test.title}
            onChange={(e) => onTestChange({ ...test, title: e.target.value })}
            placeholder="Enter test title"
          />
        ) : (
          <p>{test.title}</p>
        )}
      </div>

      <div>
        <label className="text-sm font-medium mb-1 block">Description</label>
        {isEditing ? (
          <Textarea
            value={test.description || ""}
            onChange={(e) => onTestChange({ ...test, description: e.target.value })}
            placeholder="Enter test description"
            rows={3}
          />
        ) : (
          <p>{test.description || "No description"}</p>
        )}
      </div>

      <div>
        <label className="text-sm font-medium mb-1 block">Access</label>
        {isEditing ? (
          <Select
            value={test.access || "PRIVATE"}
            onValueChange={(value) =>
              onTestChange({ ...test, access: value as "PRIVATE" | "PUBLIC" })
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Select access" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="PUBLIC">Public</SelectItem>
              <SelectItem value="PRIVATE">Private</SelectItem>
            </SelectContent>
          </Select>
        ) : (
          <Badge
            variant="outline"
            className={`
              ${test.access === "PUBLIC" ? "bg-green-100 text-green-800" : ""}
              ${test.access === "PRIVATE" ? "bg-gray-100 text-gray-800" : ""}
            `}
          >
            {test.access || "PRIVATE"}
          </Badge>
        )}
      </div>

      <div>
        <label className="text-sm font-medium mb-1 block">Status</label>
        {isEditing ? (
          <Select
            value={test.status || "DRAFT"}
            onValueChange={(value) =>
              onTestChange({ ...test, status: value as "ACTIVE" | "INACTIVE" | "ARCHIVED" | "DRAFT" })
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="DRAFT">Draft</SelectItem>
              <SelectItem value="ACTIVE">Active</SelectItem>
              <SelectItem value="INACTIVE">Inactive</SelectItem>
              <SelectItem value="ARCHIVED">Archived</SelectItem>
            </SelectContent>
          </Select>
        ) : (
          <Badge
            variant="outline"
            className={`
              ${test.status === "ACTIVE" ? "bg-green-100 text-green-800" : ""}
              ${test.status === "INACTIVE" ? "bg-gray-100 text-gray-800" : ""}
              ${test.status === "ARCHIVED" ? "bg-red-100 text-red-800" : ""}
              ${test.status === "DRAFT" ? "bg-yellow-100 text-yellow-800" : ""}
            `}
          >
            {test.status || "DRAFT"}
          </Badge>
        )}
      </div>
    </>
  )
}
