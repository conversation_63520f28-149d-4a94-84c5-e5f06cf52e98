"use client"

import { Input } from "@/components/ui/input"
import {
  <PERSON>alog,
  DialogContent,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"

interface CategoryModalProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  newCategory: { name: string }
  setNewCategory: (category: { name: string }) => void
  onCreateCategory: () => Promise<void>
}

export function CategoryModal({
  isOpen,
  onOpenChange,
  newCategory,
  setNewCategory,
  onCreateCategory,
}: CategoryModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogTitle>Create New Category</DialogTitle>
        <DialogDescription className="mb-4">
          Fill in the details to create a new category.
        </DialogDescription>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Name</label>
            <Input
              value={newCategory.name}
              onChange={(e) => setNewCategory({ name: e.target.value })}
              placeholder="Category name"
              autoFocus
            />
          </div>
        </div>
        <DialogFooter className="mt-6 flex justify-end gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={onCreateCategory}>Create</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
