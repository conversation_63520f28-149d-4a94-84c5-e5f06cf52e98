"use client"

import { Input } from "@/components/ui/input"
import {
  <PERSON>alog,
  DialogContent,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"

interface CreatorModalProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  newCreatorName: string
  setNewCreatorName: (name: string) => void
  newCreatorAvatar: string
  setNewCreatorAvatar: (avatar: string) => void
  onCreateCreator: () => Promise<void>
}

export function CreatorModal({
  isOpen,
  onOpenChange,
  newCreatorName,
  setNewCreatorName,
  newCreatorAvatar,
  setNewCreatorAvatar,
  onCreateCreator,
}: CreatorModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogTitle>Create New Creator</DialogTitle>
        <DialogDescription className="mb-4">
          Fill in the details to create a new creator.
        </DialogDescription>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Name</label>
            <Input
              value={newCreatorName}
              onChange={(e) => setNewCreatorName(e.target.value)}
              placeholder="Creator's name"
              autoFocus
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Avatar URL</label>
            <Input
              value={newCreatorAvatar}
              onChange={(e) => setNewCreatorAvatar(e.target.value)}
              placeholder="Avatar image URL (optional)"
            />
          </div>
        </div>
        <DialogFooter className="mt-6 flex justify-end gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={onCreateCreator}>Create</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
