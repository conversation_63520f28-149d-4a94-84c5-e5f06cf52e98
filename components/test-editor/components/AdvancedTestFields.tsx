"use client"

import Image from "next/image"
import { Clock } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { SearchableDropdown, type Option } from "../../SearchableDropdown"
import { Test } from "@/src/lib/repositories/tests/interface"

interface AdvancedTestFieldsProps {
  test: Test
  isEditing: boolean
  onTestChange: (test: Test) => void
  // Category props
  categoriesOptions: Option[]
  onCategorySearch: (query: string) => void
  onCategorySelect: (selected: Option | Option[]) => void
  onCreateCategory: (text: string) => void
  // Creator props
  creatorOptions: Option[]
  onCreatorSearch: (query: string) => void
  onCreatorSelect: (selected: Option | Option[]) => void
  onCreateCreator: () => void
}

export function AdvancedTestFields({
  test,
  isEditing,
  onTestChange,
  categoriesOptions,
  onCategorySearch,
  onCategorySelect,
  onCreateCategory,
  creatorOptions,
  onCreatorSearch,
  onCreatorSelect,
  onCreateCreator,
}: AdvancedTestFieldsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label className="text-sm font-medium mb-1 block">Categories</label>
        {isEditing ? (
          <SearchableDropdown
            initialData={categoriesOptions}
            onSearch={onCategorySearch}
            onSelect={onCategorySelect}
            onCreateNew={onCreateCategory}
            placeholder="Enter categories (comma separated)"
          />
        ) : (
          <div className="flex flex-wrap gap-1">
            {test.categories?.map((category, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {category}
              </Badge>
            )) || <span className="text-gray-500">No categories</span>}
          </div>
        )}
      </div>

      <div>
        <label className="text-sm font-medium mb-1 block">Difficulties</label>
        {isEditing ? (
          <Input
            value={test.difficulties?.join(", ") || ""}
            onChange={(e) => onTestChange({
              ...test,
              difficulties: e.target.value.split(",").map(diff => diff.trim()).filter(Boolean)
            })}
            placeholder="Enter difficulties (comma separated)"
          />
        ) : (
          <div className="flex flex-wrap gap-1">
            {test.difficulties?.map((difficulty, index) => (
              <Badge
                key={index}
                variant="outline"
                className={`text-xs
                  ${difficulty.toLowerCase() === "easy" ? "bg-green-100 text-green-800" : ""}
                  ${difficulty.toLowerCase() === "medium" ? "bg-blue-100 text-blue-800" : ""}
                  ${difficulty.toLowerCase() === "hard" ? "bg-orange-100 text-orange-800" : ""}
                  ${difficulty.toLowerCase() === "expert" ? "bg-red-100 text-red-800" : ""}
                `}
              >
                {difficulty}
              </Badge>
            )) || <span className="text-gray-500">No difficulties</span>}
          </div>
        )}
      </div>

      <div>
        <label className="text-sm font-medium mb-1 block">Duration (seconds)</label>
        {isEditing ? (
          <Input
            type="number"
            value={test.durationInSeconds || ""}
            onChange={(e) => onTestChange({
              ...test,
              durationInSeconds: e.target.value ? Number(e.target.value) : undefined
            })}
            placeholder="Duration in seconds"
          />
        ) : (
          <div className="flex items-center">
            <Clock className="h-4 w-4 mr-1 text-gray-500" />
            <span>
              {test.durationInSeconds
                ? `${Math.floor(test.durationInSeconds / 60)}m ${test.durationInSeconds % 60}s`
                : "Not set"
              }
            </span>
          </div>
        )}
      </div>

      <div>
        <label className="text-sm font-medium mb-1 block">Creator</label>
        {isEditing ? (
          <SearchableDropdown
            initialData={creatorOptions}
            onSearch={onCreatorSearch}
            onSelect={onCreatorSelect}
            onCreateNew={onCreateCreator}
            placeholder="Select Creator or Create new"
          />
        ) : (
          <div className="flex items-center">
            {test.creator?.avatar && (
              <div className="relative h-6 w-6 rounded-full overflow-hidden mr-2">
                <Image
                  src={test.creator.avatar}
                  alt={test.creator.name}
                  fill
                  className="object-cover"
                />
              </div>
            )}
            <span>{test.creator?.name || "No creator"}</span>
          </div>
        )}
      </div>

      <div>
        <label className="text-sm font-medium mb-1 block">Cover Image URL</label>
        {isEditing ? (
          <Input
            value={test.cover || ""}
            onChange={(e) => onTestChange({ ...test, cover: e.target.value })}
            placeholder="Cover image URL (optional)"
          />
        ) : (
          <p>{test.cover || "No cover image"}</p>
        )}
      </div>

      <div>
        <label className="text-sm font-medium mb-1 block">Test Landing Media URL</label>
        {isEditing ? (
          <Input
            value={test.mediaUrl || ""}
            onChange={(e) => onTestChange({ ...test, mediaUrl: e.target.value })}
            placeholder="Test Landing Media URL (optional)"
          />
        ) : (
          <p>{test.mediaUrl || "No media URL"}</p>
        )}
      </div>

      <div>
        <label className="text-sm font-medium mb-1 block">Reason</label>
        {isEditing ? (
          <Input
            value={test.reason || ""}
            onChange={(e) => onTestChange({ ...test, reason: e.target.value })}
            placeholder="Reason (optional)"
          />
        ) : (
          <p>{test.reason || "No reason specified"}</p>
        )}
      </div>
    </div>
  )
}
