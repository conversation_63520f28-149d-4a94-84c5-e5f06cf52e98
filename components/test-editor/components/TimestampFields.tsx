"use client"

import { Test } from "@/src/lib/repositories/tests/interface"

interface TimestampFieldsProps {
  test: Test
}

export function TimestampFields({ test }: TimestampFieldsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
      <div>
        <label className="text-sm font-medium mb-1 block">Created At</label>
        <p className="text-sm text-gray-600">
          {new Date(test.createdAt).toLocaleString()}
        </p>
      </div>

      {test.updatedAt && (
        <div>
          <label className="text-sm font-medium mb-1 block">Updated At</label>
          <p className="text-sm text-gray-600">
            {new Date(test.updatedAt).toLocaleString()}
          </p>
        </div>
      )}
    </div>
  )
}
