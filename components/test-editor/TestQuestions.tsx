import { ChevronRight } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Test } from "@/src/lib/repositories/tests/interface"

interface TestQuestionsProps {
  test: Test
  onManageQuestions: () => void
  t: (key: string) => string
}

export function TestQuestions({ test, onManageQuestions, t }: TestQuestionsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("questions")}</CardTitle>
        <CardDescription>{t("questions_desc")}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex justify-between items-center">
          <div>
            <p className="text-2xl font-bold">{test.totalQuestions || 0}</p>
            <p className="text-sm text-gray-500">Total {t("questions")}</p>
          </div>
          <Button
            onClick={onManageQuestions}
            className="flex items-center"
          >
            {t("btn_manage_question")}
            <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
