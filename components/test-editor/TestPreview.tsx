import Image from "next/image"
import { <PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Test } from "@/src/lib/repositories/tests/TestsRepository"

interface TestPreviewProps {
  test: Test
  isEditing: boolean
  onTestChange: (test: Test) => void
  t: (key: string) => string
}

export function TestPreview({ test, isEditing, onTestChange, t }: TestPreviewProps) {
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="capitalize">{t("preview")}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="relative h-48 w-full rounded-md overflow-hidden mb-4">
          {isEditing ? (
            <Input
              value={test.image}
              onChange={(e) => onTestChange({ ...test, image: e.target.value })}
              placeholder="Image URL"
              className="absolute bottom-2 left-2 right-2 z-10 bg-white/80 backdrop-blur-sm"
            />
          ) : null}
          <Image src={test.image || "/placeholder.svg"} alt={test.title} fill className="object-cover" />
        </div>

        <div className="flex justify-between items-center mb-2">
          <div className="flex items-center">
            <Users className="h-4 w-4 mr-1 text-gray-500" />
            {isEditing ? (
              <Input
                type="number"
                value={test.usersCount}
                onChange={(e) => onTestChange({ ...test, usersCount: Number(e.target.value) })}
                className="w-24 h-8 text-sm"
              />
            ) : (
              <span className="text-sm">{test.usersCount.toLocaleString()} {t("test_takers")}</span>
            )}
          </div>
          <div className="flex items-center">
            <BarChart className="h-4 w-4 mr-1 text-gray-500" />
            {isEditing ? (
              <Input
                type="number"
                value={test.successRate}
                onChange={(e) => onTestChange({ ...test, successRate: Number(e.target.value) })}
                className="w-16 h-8 text-sm"
                min="0"
                max="100"
              />
            ) : (
              <span className="text-sm">{test.successRate}% {t("success_rate")}</span>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
