import { useState } from "react"
import { Plus, Trash, ChevronDown, ChevronRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

import { Test } from "@/src/lib/repositories/tests/interface"

interface TestTopicsProps {
  test: Test
  isEditing: boolean
  onTestChange: (test: Test) => void
  t: (key: string) => string
}

export function TestTopics({ test, isEditing, onTestChange, t }: TestTopicsProps) {
  const [expandedTopics, setExpandedTopics] = useState<number[]>([])

  const handleAddTopic = () => {
    const newTopic = {
      title: "",
      description: "",
      subtopics: []
    }
    onTestChange({
      ...test,
      topics: [...(test.topics || []), newTopic]
    })
  }

  const handleRemoveTopic = (index: number) => {
    const newTopics = [...(test.topics || [])]
    newTopics.splice(index, 1)
    onTestChange({
      ...test,
      topics: newTopics
    })
  }

  const handleTopicChange = (index: number, field: 'title' | 'description', value: string) => {
    const newTopics = [...(test.topics || [])]
    newTopics[index] = {
      ...newTopics[index],
      [field]: value
    }
    onTestChange({
      ...test,
      topics: newTopics
    })
  }

  const handleAddSubtopic = (topicIndex: number) => {
    const newTopics = [...(test.topics || [])]
    newTopics[topicIndex].subtopics = [
      ...(newTopics[topicIndex].subtopics || []),
      { title: "", description: "" }
    ]
    onTestChange({
      ...test,
      topics: newTopics
    })
  }

  const handleRemoveSubtopic = (topicIndex: number, subtopicIndex: number) => {
    const newTopics = [...(test.topics || [])]
    newTopics[topicIndex].subtopics?.splice(subtopicIndex, 1)
    onTestChange({
      ...test,
      topics: newTopics
    })
  }

  const handleSubtopicChange = (topicIndex: number, subtopicIndex: number, field: 'title' | 'description', value: string) => {
    const newTopics = [...(test.topics || [])]
    if (newTopics[topicIndex].subtopics) {
      newTopics[topicIndex].subtopics![subtopicIndex] = {
        ...newTopics[topicIndex].subtopics![subtopicIndex],
        [field]: value
      }
    }
    onTestChange({
      ...test,
      topics: newTopics
    })
  }

  const toggleTopicExpansion = (index: number) => {
    setExpandedTopics(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Topics</CardTitle>
        <CardDescription>Define the main topics and subtopics covered in this test</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {(test.topics || []).map((topic, topicIndex) => (
          <div key={topicIndex} className="border rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                {isEditing ? (
                  <Input
                    value={topic.title}
                    onChange={(e) => handleTopicChange(topicIndex, 'title', e.target.value)}
                    placeholder="Topic title"
                    className="w-64"
                  />
                ) : (
                  <h4 className="font-medium">{topic.title || "Untitled Topic"}</h4>
                )}
                {(topic.subtopics || []).length > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleTopicExpansion(topicIndex)}
                  >
                    {expandedTopics.includes(topicIndex) ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </Button>
                )}
              </div>
              {isEditing && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveTopic(topicIndex)}
                >
                  <Trash className="h-4 w-4 text-red-500" />
                </Button>
              )}
            </div>

            <div className="mb-3">
              {isEditing ? (
                <Textarea
                  value={topic.description}
                  onChange={(e) => handleTopicChange(topicIndex, 'description', e.target.value)}
                  placeholder="Topic description"
                  rows={2}
                />
              ) : (
                <p className="text-sm text-gray-600">{topic.description || "No description"}</p>
              )}
            </div>

            {/* Subtopics */}
            {(topic.subtopics || []).length > 0 && (
              <div className={`ml-4 space-y-3 border-l-2 border-gray-200 pl-4 ${!expandedTopics.includes(topicIndex) ? 'hidden' : ''}`}>
                {(topic.subtopics || []).map((subtopic, subtopicIndex) => (
                  <div key={subtopicIndex} className="space-y-2">
                    <div className="flex items-center justify-between">
                      {isEditing ? (
                        <Input
                          value={subtopic.title}
                          onChange={(e) => handleSubtopicChange(topicIndex, subtopicIndex, 'title', e.target.value)}
                          placeholder="Subtopic title"
                          className="w-48"
                        />
                      ) : (
                        <h5 className="font-medium text-sm">{subtopic.title || "Untitled Subtopic"}</h5>
                      )}
                      {isEditing && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveSubtopic(topicIndex, subtopicIndex)}
                        >
                          <Trash className="h-3 w-3 text-red-500" />
                        </Button>
                      )}
                    </div>
                    {isEditing ? (
                      <Textarea
                        value={subtopic.description}
                        onChange={(e) => handleSubtopicChange(topicIndex, subtopicIndex, 'description', e.target.value)}
                        placeholder="Subtopic description"
                        rows={1}
                        className="text-sm"
                      />
                    ) : (
                      <p className="text-xs text-gray-500">{subtopic.description || "No description"}</p>
                    )}
                  </div>
                ))}
              </div>
            )}

            {isEditing && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleAddSubtopic(topicIndex)}
                className="mt-2"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Subtopic
              </Button>
            )}
          </div>
        ))}

        {isEditing && (
          <Button variant="outline" onClick={handleAddTopic}>
            <Plus className="h-4 w-4 mr-2" />
            Add Topic
          </Button>
        )}
      </CardContent>
    </Card>
  )
} 