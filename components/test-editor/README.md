# Test Editor Components

This directory contains the refactored components from the original large `TestEditor` component. The original file was split into smaller, more manageable components for better maintainability and reusability.

## Components

### Core Components

1. **TestInformation** (`TestInformation.tsx`)
   - <PERSON>les test basic information (title, description, category, difficulty, duration, instructor)
   - Supports both view and edit modes
   - Props: `test`, `isEditing`, `onTestChange`, `t`

2. **TestBenefits** (`TestBenefits.tsx`)
   - Manages test benefits list (add, edit, remove benefits)
   - Supports both view and edit modes
   - Props: `test`, `isEditing`, `onTestChange`, `t`

3. **TestQuestions** (`TestQuestions.tsx`)
   - Displays questions summary and manage questions button
   - Props: `test`, `onManageQuestions`, `t`

4. **TestPreview** (`TestPreview.tsx`)
   - Shows test preview with image and statistics
   - Supports editing image URL and stats in edit mode
   - Props: `test`, `isEditing`, `onTestChange`, `t`

5. **TestSessionSummary** (`TestSessionSummary.tsx`)
   - Displays session statistics summary
   - Props: `testSessions`, `t`

6. **TestActions** (`TestActions.tsx`)
   - Contains action buttons (preview, manage questions, duplicate, delete)
   - Props: `testId`, `onPreviewTest`, `onManageQuestions`, `onDuplicateTest`, `onDeleteTest`, `t`

7. **TestSessions** (`TestSessions.tsx`)
   - Displays the test sessions tab content
   - Props: `testSessions`, `onReviewAnswers`, `onViewDetails`, `t`

8. **TestSessionItem** (`TestSessionItem.tsx`)
   - Individual test session item component
   - Props: `session`, `onReviewAnswers`, `onViewDetails`, `t`

### Utilities

9. **utils.tsx**
   - Contains utility functions: `getStatusBadge`, `formatDate`
   - Shared functions used across multiple components

## Usage

```tsx
import {
  TestInformation,
  TestBenefits,
  TestQuestions,
  TestPreview,
  TestSessionSummary,
  TestActions,
  TestSessions
} from "@/components/test-editor"

// Use individual components as needed
<TestInformation
  test={test}
  isEditing={isEditing}
  onTestChange={setTest}
  t={t}
/>
```

## Benefits of Refactoring

1. **Improved Maintainability**: Each component has a single responsibility
2. **Better Reusability**: Components can be used independently in other parts of the application
3. **Easier Testing**: Smaller components are easier to unit test
4. **Better Code Organization**: Related functionality is grouped together
5. **Reduced File Size**: The original 636-line file is now split into manageable chunks

## Recent Updates

### Test Model Alignment (Latest Update)

The components have been updated to align with the actual Test model from `TestsRepository`:

**Field Mappings Updated:**
- `test.category` → `test.categories` (now array of strings with comma-separated input)
- `test.difficulty` → `test.difficulties` (now array of strings with comma-separated input)
- `test.duration` → `test.durationInSeconds` (now number input with formatted display)
- `test.instructor` → `test.creator` (using Creator interface)
- `test.questions` → `test.totalQuestions`

**New Fields Added:**
- `test.status` - Status selection (draft, active, inactive, archived)
- `test.mediaUrl` - Optional media URL field
- `test.reason` - Optional reason field
- `test.createdAt` - Read-only timestamp display
- `test.updatedAt` - Read-only timestamp display (when available)

**Improved Handling:**
- Better null/undefined handling for optional fields
- Proper array handling for categories and difficulties
- Duration display in minutes and seconds format
- Status badges with appropriate colors
- Creator avatar display with fallback

## Original File

The original file was located at:
`src/app/dashboard/test-builder/[id]/client.tsx`

It has been refactored to use the extracted components while maintaining the same functionality and user interface.
