import { FileText } from "lucide-react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { TestSession } from "@/src/lib/test-types"
import { TestSessionItem } from "./TestSessionItem"

interface TestSessionsProps {
  testSessions: TestSession[]
  onReviewAnswers: (sessionId: string) => void
  onViewDetails: (sessionId: string) => void
  t: (key: string) => string
}

export function TestSessions({ testSessions, onReviewAnswers, onViewDetails, t }: TestSessionsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("test_sessions")}</CardTitle>
        <CardDescription>{t("test_sessions_desc")}</CardDescription>
      </CardHeader>
      <CardContent>
        {testSessions.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>{t("no_test_sessions_title")}</p>
            <p className="text-sm">{t("no_test_sessions_desc")}</p>
          </div>
        ) : (
          <div className="space-y-4">
            {testSessions.map((session) => (
              <TestSessionItem
                key={session.id}
                session={session}
                onReviewAnswers={onReviewAnswers}
                onViewDetails={onViewDetails}
                t={t}
              />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
