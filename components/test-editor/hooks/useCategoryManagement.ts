"use client"

import { useState } from "react"
import { Option } from "../../SearchableDropdown"
import { Test } from "@/src/lib/repositories/tests/interface"
import { TestsAPI } from "@/src/services/testsApi"

export function useCategoryManagement(test: Test, onTestChange: (test: Test) => void) {
  const [categoriesOptions, setCategoriesOptions] = useState<Option[]>([])
  const [isCategoryModalOpen, setIsCategoryModalOpen] = useState(false)
  const [newCategory, setNewCategory] = useState({ name: "" })

  function openCreateCategoryModal() {
    setIsCategoryModalOpen(true)
  }

  async function searchCategories(query: string) {
    setNewCategory({ name: query })
    setCategoriesOptions([])
    // try {
    //   const response = await TestsAPI.SearchCategories(query).request()
    //   const options = response.items.map((category: any) => ({
    //     label: category.name,
    //     value: category.id,
    //   }))
    //   setCategoriesOptions(options)
    // } catch (error) {
    //   setCategoriesOptions([])
    //   console.error("Failed to search categories:", error)
    // }
  }

  async function handleCreateNewCategory() {
    try {
      const response = await TestsAPI.CreateCategory({
        name: newCategory.name.trim(),
      }).request()

      const newOption: Option = {
        label: response.name,
        value: response.id,
      }

      setCategoriesOptions((prev) => [...prev, newOption])

      onTestChange({
        ...test,
        categories: [...test.categories || [], response.name],
      })

      setIsCategoryModalOpen(false)
    } catch (error) {
      console.error("Error creating category:", error)
      alert("Error creating category")
    }
  }

  function handleCategorySelect(selected: Option | Option[]) {
    onTestChange({
      ...test,
      categories: (selected as Option[]).map((opt) => opt.label),
    })
  }

  return {
    categoriesOptions,
    isCategoryModalOpen,
    setIsCategoryModalOpen,
    newCategory,
    setNewCategory,
    searchCategories,
    openCreateCategoryModal,
    handleCreateNewCategory,
    handleCategorySelect,
  }
}
