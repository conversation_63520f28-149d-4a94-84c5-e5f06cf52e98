"use client"

import { useState } from "react"
import { Option } from "../../SearchableDropdown"
import { Test } from "@/src/lib/repositories/tests/interface"

export function useCreatorManagement(test: Test, onTestChange: (test: Test) => void) {
  const [creatorOptions, setCreatorOptions] = useState<Option[]>([])
  const [isCreatorModalOpen, setIsCreatorModalOpen] = useState(false)
  const [newCreatorName, setNewCreatorName] = useState("")
  const [newCreatorAvatar, setNewCreatorAvatar] = useState("")

  async function searchCreator(query: string) {
    setCreatorOptions([])
    // try {
    //   const response = await CreatorsAPI.SearchCreators(query).request()
    //   const options = response.items.map((creator: any) => ({
    //     label: creator.name,
    //     value: creator.id,
    //   }))
    //   setCreatorOptions(options)
    // } catch (error) {
    //   setCreatorOptions([])
    //   console.error("Failed to search creators:", error)
    // }
  }

  function openCreateCreatorModal() {
    setNewCreatorName("")
    setNewCreatorAvatar("")
    setIsCreatorModalOpen(true)
  }

  async function handleCreateNewCreator() {
    if (!newCreatorName.trim()) {
      alert("Creator name is required")
      return
    }

    try {
      const res = await fetch("/api/creators", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          name: newCreatorName.trim(),
          avatar: newCreatorAvatar.trim(),
        }),
      })

      if (!res.ok) {
        throw new Error("Failed to create creator")
      }

      const newCreator = await res.json()

      const newOption: Option = {
        label: newCreator.name,
        value: newCreator.id,
      }

      setCreatorOptions((prev) => [...prev, newOption])

      onTestChange({
        ...test,
        creator: {
          id: newCreator.id,
          name: newCreator.name,
          avatar: newCreator.avatar,
        },
      })

      setIsCreatorModalOpen(false)
    } catch (error) {
      console.error("Error creating creator:", error)
      alert("Error creating creator")
    }
  }

  function handleCreatorSelect(selected: Option | Option[]) {
    onTestChange({
      ...test,
      creator: {
        id: (selected as Option).value,
        name: (selected as Option).label,
        avatar: "",
      },
    })
  }

  return {
    creatorOptions,
    isCreatorModalOpen,
    setIsCreatorModalOpen,
    newCreatorName,
    setNewCreatorName,
    newCreatorAvatar,
    setNewCreatorAvatar,
    searchCreator,
    openCreateCreatorModal,
    handleCreateNewCreator,
    handleCreatorSelect,
  }
}
