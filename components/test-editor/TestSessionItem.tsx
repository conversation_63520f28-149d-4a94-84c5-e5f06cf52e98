import { Eye, Calendar, Clock } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { TestSession } from "@/src/lib/test-types"
import { getStatusBadge, formatDate } from "./utils"

interface TestSessionItemProps {
  session: TestSession
  onReviewAnswers: (sessionId: string) => void
  onViewDetails: (sessionId: string) => void
  t: (key: string) => string
}

export function TestSessionItem({ session, onReviewAnswers, onViewDetails, t }: TestSessionItemProps) {
  return (
    <div className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-2">
            <h3 className="font-semibold">{session.studentName}</h3>
            {getStatusBadge(session.status)}
            {session.reviewableQuestions > 0 && session.status === "needs-review" && (
              <Badge variant="outline" className="text-xs">
                {session.reviewableQuestions} {t("badge_to_review")}
              </Badge>
            )}
          </div>
          <p className="text-sm text-gray-600 mb-1">{session.studentEmail}</p>
          <div className="flex items-center gap-4 text-sm text-gray-500">
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              {t("started")} {formatDate(session.startedAt)}
            </div>
            {session.completedAt && (
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                {t("completed")} {formatDate(session.completedAt)}
              </div>
            )}
            <div>
              {session.answeredQuestions}/{session.totalQuestions} {t("questions")}
            </div>
            {session.score !== undefined && (
              <div className="font-medium text-gray-700">{t("score")}: {session.score}%</div>
            )}
          </div>
        </div>
        <div className="flex gap-2">
          {session.status === "needs-review" && session.reviewableQuestions > 0 && (
            <Button
              size="sm"
              onClick={() => onReviewAnswers(session.id)}
            >
              {t("btn_review_answers")}
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={() => onViewDetails(session.id)}
          >
            <Eye className="h-4 w-4 mr-1" />
            {t("btn_view_details")}
          </Button>
        </div>
      </div>
    </div>
  )
}
