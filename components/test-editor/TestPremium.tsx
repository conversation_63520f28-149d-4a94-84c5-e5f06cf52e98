import { useState } from "react"
import { Plus, Trash, Crown } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Test } from "@/src/lib/repositories/tests/interface"

interface TestPremiumProps {
  test: Test
  isEditing: boolean
  onTestChange: (test: Test) => void
  t: (key: string) => string
}

export function TestPremium({ test, isEditing, onTestChange, t }: TestPremiumProps) {
  const [newBadge, setNewBadge] = useState("")

  const handleAddBadge = () => {
    if (newBadge.trim() && test.premium) {
      onTestChange({
        ...test,
        premium: {
          ...test.premium,
          badges: [...(test.premium.badges || []), newBadge.trim()],
        }
      })
      setNewBadge("")
    }
  }

  const handleRemoveBadge = (index: number) => {
    if (test.premium) {
      const newBadges = [...(test.premium.badges || [])]
      newBadges.splice(index, 1)
      onTestChange({
        ...test,
        premium: {
          ...test.premium,
          badges: newBadges,
        }
      })
    }
  }

  const handleBadgeChange = (index: number, value: string) => {
    if (test.premium) {
      const newBadges = [...(test.premium.badges || [])]
      newBadges[index] = value
      onTestChange({
        ...test,
        premium: {
          ...test.premium,
          badges: newBadges,
        }
      })
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Crown className="h-5 w-5 text-yellow-500" />
          Premium Settings
        </CardTitle>
        <CardDescription>Configure premium features and pricing</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <label className="text-sm font-medium">Premium Test</label>
          {isEditing ? (
            <Switch
              checked={test.premium?.isPremium || false}
              onCheckedChange={(checked) =>
                onTestChange({
                  ...test,
                  premium: {
                    ...test.premium,
                    isPremium: checked,
                  }
                })
              }
            />
          ) : (
            <Badge variant={test.premium?.isPremium ? "default" : "secondary"}>
              {test.premium?.isPremium ? "Premium" : "Free"}
            </Badge>
          )}
        </div>

        {test.premium?.isPremium && (
          <>
            <div>
              <label className="text-sm font-medium mb-1 block">Price</label>
              {isEditing ? (
                <Input
                  value={test.premium.price || ""}
                  onChange={(e) =>
                    onTestChange({
                      ...test,
                      premium: {
                        ...test.premium,
                        price: e.target.value,
                        isPremium: test.premium?.isPremium || false
                      }
                    })
                  }
                  placeholder="e.g., $6.99"
                />
              ) : (
                <p className="text-lg font-semibold text-green-600">
                  {test.premium.price || "No price set"}
                </p>
              )}
            </div>

            <div>
              <label className="text-sm font-medium mb-1 block">Offer Text</label>
              {isEditing ? (
                <Textarea
                  value={test.premium.offerText || ""}
                  onChange={(e) =>
                    onTestChange({
                      ...test,
                      premium: {
                        ...test.premium,
                        offerText: e.target.value,
                        isPremium: test.premium?.isPremium || false
                      }
                    })
                  }
                  placeholder="e.g., Includes bonus cheatsheet and 1-on-1 code review"
                  rows={2}
                />
              ) : (
                <p>{test.premium.offerText || "No offer text"}</p>
              )}
            </div>

            <div>
              <label className="text-sm font-medium mb-1 block">Badges</label>
              <div className="space-y-2">
                {(test.premium.badges || []).map((badge, index) => (
                  <div key={index} className="flex items-center">
                    {isEditing ? (
                      <>
                        <Input
                          value={badge}
                          onChange={(e) => handleBadgeChange(index, e.target.value)}
                          className="flex-1"
                        />
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveBadge(index)}
                          className="ml-2"
                        >
                          <Trash className="h-4 w-4 text-red-500" />
                        </Button>
                      </>
                    ) : (
                      <Badge variant="outline" className="text-xs">
                        {badge}
                      </Badge>
                    )}
                  </div>
                ))}
              </div>

              {isEditing && (
                <div className="flex mt-2">
                  <Input
                    value={newBadge}
                    onChange={(e) => setNewBadge(e.target.value)}
                    placeholder="Add new badge"
                    className="flex-1"
                  />
                  <Button variant="outline" onClick={handleAddBadge} className="ml-2">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
} 