"use client"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Test } from "@/src/lib/repositories/tests/interface"
import { useCreatorManagement } from "./hooks/useCreatorManagement"
import { useCategoryManagement } from "./hooks/useCategoryManagement"
import { CreatorModal } from "./components/CreatorModal"
import { CategoryModal } from "./components/CategoryModal"
import { BasicTestFields } from "./components/BasicTestFields"
import { AdvancedTestFields } from "./components/AdvancedTestFields"
import { TimestampFields } from "./components/TimestampFields"

interface TestInformationProps {
  test: Test
  isEditing: boolean
  onTestChange: (test: Test) => void
  t: (key: string) => string
}

export function TestInformation({
  test,
  isEditing,
  onTestChange,
  t,
}: TestInformationProps) {
  const creatorManagement = useCreatorManagement(test, onTestChange)
  const categoryManagement = useCategoryManagement(test, onTestChange)

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>{t("test_information")}</CardTitle>
          <CardDescription>{t("test_information_desc")}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <BasicTestFields
            test={test}
            isEditing={isEditing}
            onTestChange={onTestChange}
          />

          <AdvancedTestFields
            test={test}
            isEditing={isEditing}
            onTestChange={onTestChange}
            categoriesOptions={categoryManagement.categoriesOptions}
            onCategorySearch={categoryManagement.searchCategories}
            onCategorySelect={categoryManagement.handleCategorySelect}
            onCreateCategory={categoryManagement.openCreateCategoryModal}
            creatorOptions={creatorManagement.creatorOptions}
            onCreatorSearch={creatorManagement.searchCreator}
            onCreatorSelect={creatorManagement.handleCreatorSelect}
            onCreateCreator={creatorManagement.openCreateCreatorModal}
          />

          <TimestampFields test={test} />
        </CardContent>
      </Card>

      <CreatorModal
        isOpen={creatorManagement.isCreatorModalOpen}
        onOpenChange={creatorManagement.setIsCreatorModalOpen}
        newCreatorName={creatorManagement.newCreatorName}
        setNewCreatorName={creatorManagement.setNewCreatorName}
        newCreatorAvatar={creatorManagement.newCreatorAvatar}
        setNewCreatorAvatar={creatorManagement.setNewCreatorAvatar}
        onCreateCreator={creatorManagement.handleCreateNewCreator}
      />

      <CategoryModal
        isOpen={categoryManagement.isCategoryModalOpen}
        onOpenChange={categoryManagement.setIsCategoryModalOpen}
        newCategory={categoryManagement.newCategory}
        setNewCategory={categoryManagement.setNewCategory}
        onCreateCategory={categoryManagement.handleCreateNewCategory}
      />
    </>
  )
}
