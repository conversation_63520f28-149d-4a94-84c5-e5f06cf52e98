import Image from "next/image"
import { Clock } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Test } from "@/src/lib/repositories/tests/TestsRepository"

interface TestInformationProps {
  test: Test
  isEditing: boolean
  onTestChange: (test: Test) => void
  t: (key: string) => string
}

export function TestInformation({ test, isEditing, onTestChange, t }: TestInformationProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("test_information")}</CardTitle>
        <CardDescription>{t("test_information_desc")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <label className="text-sm font-medium mb-1 block">Title</label>
          {isEditing ? (
            <Input
              value={test.title}
              onChange={(e) => onTestChange({ ...test, title: e.target.value })}
              placeholder="Enter test title"
            />
          ) : (
            <p>{test.title}</p>
          )}
        </div>

        <div>
          <label className="text-sm font-medium mb-1 block">Description</label>
          {isEditing ? (
            <Textarea
              value={test.description || ""}
              onChange={(e) => onTestChange({ ...test, description: e.target.value })}
              placeholder="Enter test description"
              rows={3}
            />
          ) : (
            <p>{test.description || "No description"}</p>
          )}
        </div>

        <div>
          <label className="text-sm font-medium mb-1 block">Status</label>
          {isEditing ? (
            <Select
              value={test.status || "draft"}
              onValueChange={(value) =>
                onTestChange({ ...test, status: value as "active" | "inactive" | "archived" | "draft" })
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="archived">Archived</SelectItem>
              </SelectContent>
            </Select>
          ) : (
            <Badge
              variant="outline"
              className={`
                ${test.status === "active" ? "bg-green-100 text-green-800" : ""}
                ${test.status === "inactive" ? "bg-gray-100 text-gray-800" : ""}
                ${test.status === "archived" ? "bg-red-100 text-red-800" : ""}
                ${test.status === "draft" ? "bg-yellow-100 text-yellow-800" : ""}
              `}
            >
              {test.status || "draft"}
            </Badge>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium mb-1 block">Categories</label>
            {isEditing ? (
              <Input
                value={test.categories?.join(", ") || ""}
                onChange={(e) => onTestChange({
                  ...test,
                  categories: e.target.value.split(",").map(cat => cat.trim()).filter(Boolean)
                })}
                placeholder="Enter categories (comma separated)"
              />
            ) : (
              <div className="flex flex-wrap gap-1">
                {test.categories?.map((category, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {category}
                  </Badge>
                )) || <span className="text-gray-500">No categories</span>}
              </div>
            )}
          </div>

          <div>
            <label className="text-sm font-medium mb-1 block">Difficulties</label>
            {isEditing ? (
              <Input
                value={test.difficulties?.join(", ") || ""}
                onChange={(e) => onTestChange({
                  ...test,
                  difficulties: e.target.value.split(",").map(diff => diff.trim()).filter(Boolean)
                })}
                placeholder="Enter difficulties (comma separated)"
              />
            ) : (
              <div className="flex flex-wrap gap-1">
                {test.difficulties?.map((difficulty, index) => (
                  <Badge
                    key={index}
                    variant="outline"
                    className={`text-xs
                      ${difficulty.toLowerCase() === "easy" ? "bg-green-100 text-green-800" : ""}
                      ${difficulty.toLowerCase() === "medium" ? "bg-blue-100 text-blue-800" : ""}
                      ${difficulty.toLowerCase() === "hard" ? "bg-orange-100 text-orange-800" : ""}
                      ${difficulty.toLowerCase() === "expert" ? "bg-red-100 text-red-800" : ""}
                    `}
                  >
                    {difficulty}
                  </Badge>
                )) || <span className="text-gray-500">No difficulties</span>}
              </div>
            )}
          </div>

          <div>
            <label className="text-sm font-medium mb-1 block">Duration (seconds)</label>
            {isEditing ? (
              <Input
                type="number"
                value={test.durationInSeconds || ""}
                onChange={(e) => onTestChange({
                  ...test,
                  durationInSeconds: e.target.value ? Number(e.target.value) : undefined
                })}
                placeholder="Duration in seconds"
              />
            ) : (
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-1 text-gray-500" />
                <span>
                  {test.durationInSeconds
                    ? `${Math.floor(test.durationInSeconds / 60)}m ${test.durationInSeconds % 60}s`
                    : "Not set"
                  }
                </span>
              </div>
            )}
          </div>

          <div>
            <label className="text-sm font-medium mb-1 block">Creator</label>
            {isEditing ? (
              <Input
                value={test.creator?.name || ""}
                onChange={(e) =>
                  onTestChange({
                    ...test,
                    creator: {
                      ...test.creator,
                      id: test.creator?.id || "",
                      name: e.target.value
                    },
                  })
                }
                placeholder="Creator name"
              />
            ) : (
              <div className="flex items-center">
                {test.creator?.avatar && (
                  <div className="relative h-6 w-6 rounded-full overflow-hidden mr-2">
                    <Image
                      src={test.creator.avatar}
                      alt={test.creator.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                )}
                <span>{test.creator?.name || "No creator"}</span>
              </div>
            )}
          </div>

          <div>
            <label className="text-sm font-medium mb-1 block">Media URL</label>
            {isEditing ? (
              <Input
                value={test.mediaUrl || ""}
                onChange={(e) => onTestChange({ ...test, mediaUrl: e.target.value })}
                placeholder="Media URL (optional)"
              />
            ) : (
              <p>{test.mediaUrl || "No media URL"}</p>
            )}
          </div>

          <div>
            <label className="text-sm font-medium mb-1 block">Reason</label>
            {isEditing ? (
              <Input
                value={test.reason || ""}
                onChange={(e) => onTestChange({ ...test, reason: e.target.value })}
                placeholder="Reason (optional)"
              />
            ) : (
              <p>{test.reason || "No reason specified"}</p>
            )}
          </div>
        </div>

        {/* Timestamps section */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
          <div>
            <label className="text-sm font-medium mb-1 block">Created At</label>
            <p className="text-sm text-gray-600">
              {new Date(test.createdAt).toLocaleString()}
            </p>
          </div>

          {test.updatedAt && (
            <div>
              <label className="text-sm font-medium mb-1 block">Updated At</label>
              <p className="text-sm text-gray-600">
                {new Date(test.updatedAt).toLocaleString()}
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
