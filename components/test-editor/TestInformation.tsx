"use client"

import { useState } from "react"
import Image from "next/image"
import { Clock } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button" // For buttons in modal
import { SearchableDropdown, type Option } from "../SearchableDropdown"
import { Test } from "@/src/lib/repositories/tests/interface"
import { CreatorsAPI } from "@/src/services/creatorsApi"
import { TestsAPI } from "@/src/services/testsApi"

interface TestInformationProps {
  test: Test
  isEditing: boolean
  onTestChange: (test: Test) => void
  t: (key: string) => string
}

export function TestInformation({
  test,
  isEditing,
  onTestChange,
  t,
}: TestInformationProps) {
  const [creatorOptions, setCreatorOptions] = useState<Option[]>([])
  const [isCreatorModalOpen, setIsCreatorModalOpen] = useState(false)
  const [newCreatorName, setNewCreatorName] = useState("")
  const [newCreatorAvatar, setNewCreatorAvatar] = useState("")

  async function searchCreator(query: string) {
    setCreatorOptions([])
    // try {
    //   const response = await CreatorsAPI.SearchCreators(query).request()
    //   const options = response.items.map((creator: any) => ({
    //     label: creator.name,
    //     value: creator.id,
    //   }))
    //   setCreatorOptions(options)
    // } catch (error) {
    //   setCreatorOptions([])
    //   console.error("Failed to search creators:", error)
    // }
  }

  function openCreateCreatorModal() {
    setNewCreatorName("")
    setNewCreatorAvatar("")
    setIsCreatorModalOpen(true)
  }

  async function handleCreateNewCreator() {
    if (!newCreatorName.trim()) {
      alert("Creator name is required")
      return
    }

    try {
      const res = await fetch("/api/creators", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          name: newCreatorName.trim(),
          avatar: newCreatorAvatar.trim(),
        }),
      })

      if (!res.ok) {
        throw new Error("Failed to create creator")
      }

      const newCreator = await res.json()

      const newOption: Option = {
        label: newCreator.name,
        value: newCreator.id,
      }

      setCreatorOptions((prev) => [...prev, newOption])

      onTestChange({
        ...test,
        creator: {
          id: newCreator.id,
          name: newCreator.name,
          avatar: newCreator.avatar,
        },
      })

      setIsCreatorModalOpen(false)
    } catch (error) {
      console.error("Error creating creator:", error)
      alert("Error creating creator")
    }
  }

  const [categoriesOptions, setCategoriesOptions] = useState<Option[]>([])
  const [isCategoryModalOpen, setIsCategoryModalOpen] = useState(false)
  const [newCategory, setNewCategory] = useState({ name: "" })

  function openCreateCategoryModal() {
    setIsCategoryModalOpen(true)
  }

  async function searchCategories(query: string) {
    setNewCategory({ name: query })
    setCategoriesOptions([])
    // try {
    //   const response = await TestsAPI.SearchCategories(query).request()
    //   const options = response.items.map((category: any) => ({
    //     label: category.name,
    //     value: category.id,
    //   }))
    //   setCategoriesOptions(options)
    // } catch (error) {
    //   setCategoriesOptions([])
    //   console.error("Failed to search categories:", error)
    // }
  }

  async function handleCreateNewCategory() {
    try {
      const response = await TestsAPI.CreateCategory({
        name: newCategory.name.trim(),
      }).request()

      const newOption: Option = {
        label: newCategory.name,
        value: newCategory.id,
      }

      setCategoriesOptions((prev) => [...prev, newOption])

      onTestChange({
        ...test,
        categories: [...test.categories || [], newCategory.name],
      })

      setIsCategoryModalOpen(false)
    } catch (error) {
      console.error("Error creating category:", error)
      alert("Error creating category")
    }
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>{t("test_information")}</CardTitle>
          <CardDescription>{t("test_information_desc")}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium mb-1 block">Title</label>
            {isEditing ? (
              <Input
                value={test.title}
                onChange={(e) => onTestChange({ ...test, title: e.target.value })}
                placeholder="Enter test title"
              />
            ) : (
              <p>{test.title}</p>
            )}
          </div>

          <div>
            <label className="text-sm font-medium mb-1 block">Description</label>
            {isEditing ? (
              <Textarea
                value={test.description || ""}
                onChange={(e) => onTestChange({ ...test, description: e.target.value })}
                placeholder="Enter test description"
                rows={3}
              />
            ) : (
              <p>{test.description || "No description"}</p>
            )}
          </div>

          <div>
            <label className="text-sm font-medium mb-1 block">Access</label>
            {isEditing ? (
              <Select
                value={test.access || "PRIVATE"}
                onValueChange={(value) =>
                  onTestChange({ ...test, access: value as "PRIVATE" | "PUBLIC" })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select access" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PUBLIC">Public</SelectItem>
                  <SelectItem value="PRIVATE">Private</SelectItem>
                </SelectContent>
              </Select>
            ) : (
              <Badge
                variant="outline"
                className={`
                ${test.access === "PUBLIC" ? "bg-green-100 text-green-800" : ""}
                ${test.access === "PRIVATE" ? "bg-gray-100 text-gray-800" : ""}
              `}
              >
                {test.access || "PRIVATE"}
              </Badge>
            )}
          </div>

          <div>
            <label className="text-sm font-medium mb-1 block">Status</label>
            {isEditing ? (
              <Select
                value={test.status || "DRAFT"}
                onValueChange={(value) =>
                  onTestChange({ ...test, status: value as "ACTIVE" | "INACTIVE" | "ARCHIVED" | "DRAFT" })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="DRAFT">Draft</SelectItem>
                  <SelectItem value="ACTIVE">Active</SelectItem>
                  <SelectItem value="INACTIVE">Inactive</SelectItem>
                  <SelectItem value="ARCHIVED">Archived</SelectItem>
                </SelectContent>
              </Select>
            ) : (
              <Badge
                variant="outline"
                className={`
                ${test.status === "ACTIVE" ? "bg-green-100 text-green-800" : ""}
                ${test.status === "INACTIVE" ? "bg-gray-100 text-gray-800" : ""}
                ${test.status === "ARCHIVED" ? "bg-red-100 text-red-800" : ""}
                ${test.status === "DRAFT" ? "bg-yellow-100 text-yellow-800" : ""}
              `}
              >
                {test.status || "DRAFT"}
              </Badge>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium mb-1 block">Categories</label>
              {isEditing ? (
                // <Input
                //   value={test.categories?.join(", ") || ""}
                //   onChange={(e) => onTestChange({
                //     ...test,
                //     categories: e.target.value.split(",").map(cat => cat.trim()).filter(Boolean)
                //   })}
                //   placeholder="Enter categories (comma separated)"
                // />

                <SearchableDropdown
                  initialData={categoriesOptions}
                  onSearch={searchCategories}
                  onSelect={(selected: Option | Option[]) =>
                    onTestChange({
                      ...test,
                      categories: (selected as Option[]).map((opt) => opt.label),
                    })
                  }
                  onCreateNew={(text: string) => openCreateCategoryModal()}
                  placeholder="Enter categories (comma separated)"
                />
              ) : (
                <div className="flex flex-wrap gap-1">
                  {test.categories?.map((category, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {category}
                    </Badge>
                  )) || <span className="text-gray-500">No categories</span>}
                </div>
              )}
            </div>

            <div>
              <label className="text-sm font-medium mb-1 block">Difficulties</label>
              {isEditing ? (
                <Input
                  value={test.difficulties?.join(", ") || ""}
                  onChange={(e) => onTestChange({
                    ...test,
                    difficulties: e.target.value.split(",").map(diff => diff.trim()).filter(Boolean)
                  })}
                  placeholder="Enter difficulties (comma separated)"
                />
              ) : (
                <div className="flex flex-wrap gap-1">
                  {test.difficulties?.map((difficulty, index) => (
                    <Badge
                      key={index}
                      variant="outline"
                      className={`text-xs
                      ${difficulty.toLowerCase() === "easy" ? "bg-green-100 text-green-800" : ""}
                      ${difficulty.toLowerCase() === "medium" ? "bg-blue-100 text-blue-800" : ""}
                      ${difficulty.toLowerCase() === "hard" ? "bg-orange-100 text-orange-800" : ""}
                      ${difficulty.toLowerCase() === "expert" ? "bg-red-100 text-red-800" : ""}
                    `}
                    >
                      {difficulty}
                    </Badge>
                  )) || <span className="text-gray-500">No difficulties</span>}
                </div>
              )}
            </div>

            <div>
              <label className="text-sm font-medium mb-1 block">Duration (seconds)</label>
              {isEditing ? (
                <Input
                  type="number"
                  value={test.durationInSeconds || ""}
                  onChange={(e) => onTestChange({
                    ...test,
                    durationInSeconds: e.target.value ? Number(e.target.value) : undefined
                  })}
                  placeholder="Duration in seconds"
                />
              ) : (
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-1 text-gray-500" />
                  <span>
                    {test.durationInSeconds
                      ? `${Math.floor(test.durationInSeconds / 60)}m ${test.durationInSeconds % 60}s`
                      : "Not set"
                    }
                  </span>
                </div>
              )}
            </div>

            <div>
              <label className="text-sm font-medium mb-1 block">Creator</label>
              {isEditing ? (
                <SearchableDropdown
                  initialData={creatorOptions}
                  onSearch={searchCreator}
                  onSelect={(selected: Option | Option[]) =>
                    onTestChange({
                      ...test,
                      creator: {
                        id: (selected as Option).value,
                        name: (selected as Option).label,
                        avatar: "",
                      },
                    })
                  }
                  onCreateNew={openCreateCreatorModal}
                  placeholder="Select Creator or Create new"
                />
              ) : (
                <div className="flex items-center">
                  {test.creator?.avatar && (
                    <div className="relative h-6 w-6 rounded-full overflow-hidden mr-2">
                      <Image
                        src={test.creator.avatar}
                        alt={test.creator.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                  )}
                  <span>{test.creator?.name || "No creator"}</span>
                </div>
              )}
            </div>

            <div>
              <label className="text-sm font-medium mb-1 block">Cover Image URL</label>
              {isEditing ? (
                <Input
                  value={test.cover || ""}
                  onChange={(e) => onTestChange({ ...test, cover: e.target.value })}
                  placeholder="Cover image URL (optional)"
                />
              ) : (
                <p>{test.cover || "No cover image"}</p>
              )}
            </div>

            <div>
              <label className="text-sm font-medium mb-1 block">Media URL</label>
              {isEditing ? (
                <Input
                  value={test.mediaUrl || ""}
                  onChange={(e) => onTestChange({ ...test, mediaUrl: e.target.value })}
                  placeholder="Media URL (optional)"
                />
              ) : (
                <p>{test.mediaUrl || "No media URL"}</p>
              )}
            </div>

            <div>
              <label className="text-sm font-medium mb-1 block">Reason</label>
              {isEditing ? (
                <Input
                  value={test.reason || ""}
                  onChange={(e) => onTestChange({ ...test, reason: e.target.value })}
                  placeholder="Reason (optional)"
                />
              ) : (
                <p>{test.reason || "No reason specified"}</p>
              )}
            </div>
          </div>

          {/* Timestamps section */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
            <div>
              <label className="text-sm font-medium mb-1 block">Created At</label>
              <p className="text-sm text-gray-600">
                {new Date(test.createdAt).toLocaleString()}
              </p>
            </div>

            {test.updatedAt && (
              <div>
                <label className="text-sm font-medium mb-1 block">Updated At</label>
                <p className="text-sm text-gray-600">
                  {new Date(test.updatedAt).toLocaleString()}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Modal for new creator */}
      <Dialog open={isCreatorModalOpen} onOpenChange={setIsCreatorModalOpen}>
        <DialogContent>
          <DialogTitle>Create New Creator</DialogTitle>
          <DialogDescription className="mb-4">
            Fill in the details to create a new creator.
          </DialogDescription>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Name</label>
              <Input
                value={newCreatorName}
                onChange={(e) => setNewCreatorName(e.target.value)}
                placeholder="Creator's name"
                autoFocus
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Avatar URL</label>
              <Input
                value={newCreatorAvatar}
                onChange={(e) => setNewCreatorAvatar(e.target.value)}
                placeholder="Avatar image URL (optional)"
              />
            </div>
          </div>
          <DialogFooter className="mt-6 flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsCreatorModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateNewCreator}>Create</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal for new category */}
      <Dialog open={isCategoryModalOpen} onOpenChange={setIsCategoryModalOpen}>
        <DialogContent>
          <DialogTitle>Create New Category</DialogTitle>
          <DialogDescription className="mb-4">
            Fill in the details to create a new category.
          </DialogDescription>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Name</label>
              <Input
                value={newCategory.name}
                onChange={(e) => setNewCategory({ name: e.target.value })}
                placeholder="Category name"
                autoFocus
              />
            </div>
          </div>
          <DialogFooter className="mt-6 flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsCategoryModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateNewCategory}>Create</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
