import { Badge } from "@/components/ui/badge"
import { TestSession } from "@/src/lib/test-types"

export const getStatusBadge = (status: TestSession["status"]) => {
  switch (status) {
    case "completed":
      return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Completed</Badge>
    case "needs-review":
      return <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-100">Needs Review</Badge>
    case "reviewed":
      return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Reviewed</Badge>
    case "in-progress":
      return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">In Progress</Badge>
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}

export const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  })
}
