"use client";

import MarkdownContent from "@/components/MarkdownContent";
import { TestSessionQuestion } from "@/src/app/test-session/[id]/model";
import React from "react";

interface Metadata {
  data?: Record<string, any>;
}

interface QuestionContentProps {
  children: TestSessionQuestion;
}

const renderTemplate = (
  template: string,
  data: Record<string, any> = {}
): string => {
  return template.replace(/{{(.*?)}}/g, (_, key) => {
    const value = data[key.trim()];
    if (!value) return "";

    try {
      return JSON.stringify(value, null, 2); // Pretty JSON formatting
    } catch {
      return "";
    }
  });
};

const QuestionContent: React.FC<QuestionContentProps> = ({
  children: { question, data },
}) => {
  const processedQuestion = renderTemplate(question, data ?? {});

  return (
    <div className="prose max-w-none dark:prose-invert prose-sm">
      <MarkdownContent>{processedQuestion}</MarkdownContent>
    </div>
  );
};

export default React.memo(QuestionContent);
