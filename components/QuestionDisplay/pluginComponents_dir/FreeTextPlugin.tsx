"use client";

import React, { useRef, useEffect } from "react";
import { PluginProps } from "@/src/app/test-session/[id]/model"
import { Textarea } from "@/components/ui/textarea";
import { DisplayQuestion } from "./DisplayQuestion";

export const FreeTextPlugin: React.FC<PluginProps> = ({
  question,
  answer,
  onAnswer,
}) => {
  const textareaRef = React.useRef<HTMLTextAreaElement>(null);

  const adjustHeight = () => {
    if (!textareaRef.current) return;
    textareaRef.current.style.height = "auto"; // reset height
    textareaRef.current.style.height = textareaRef.current.scrollHeight + "px"; // set height to scrollHeight
  };

  // Adjust height on mount and when answer changes
  useEffect(() => {
    adjustHeight();
  }, [answer]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onAnswer(e.target.value);
    adjustHeight();
  };

  return (
    <div className="flex flex-col space-y-4">
      <DisplayQuestion question={question} />
      <Textarea
        ref={textareaRef}
        placeholder="Tulis jawabanmu di sini..."
        value={answer || ""}
        onChange={handleChange}
        className="min-h-[120px] resize-none overflow-hidden"
      />
    </div>
  );
};
