/* eslint-disable @next/next/no-img-element */
import { TestSessionQuestion } from "@/src/app/test-session/[id]/model";
import QuestionContent from "../QuestionContent";

export function DisplayQuestion({ question }: { question: TestSessionQuestion }) {
  return (
    <div className="mb-4 md:mb-6 md:text-lg text-gray-700 w-full">
      {question.image && (
        <div className="mb-6 md:mb-8">
          <img
            src={question.image}
            alt="Question visual"
            width={800}
            height={400}
            className="rounded-lg w-full h-auto object-cover md:max-h-80 md:object-contain md:mx-auto"
          />
        </div>
      )}
      {question.audio && (
        <div className="mb-4">
          <audio controls>
            <source src={question.audio} type="audio/mpeg" />
            Your browser does not support the audio element.
          </audio>
        </div>
      )}
      <QuestionContent>{question}</QuestionContent>
    </div>
  );
}