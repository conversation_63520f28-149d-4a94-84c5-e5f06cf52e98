"use client";

import React, { useState, useRef, useEffect } from "react";
import { AudioAnswer, PluginProps } from "@/src/app/test-session/[id]/model"
import QuestionContent from "../QuestionContent";

// import or define AudioAnswer class here
// import { AudioAnswer } from '...';

export const VoiceInputPlugin: React.FC<PluginProps> = ({
  question,
  answer,
  onAnswer,
}) => {
  const [recording, setRecording] = useState(false);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  useEffect(() => {
    return () => {
      if (audioUrl) URL.revokeObjectURL(audioUrl);
    };
  }, [audioUrl]);

  useEffect(() => {
    if (!answer) return;
    // If answer is AudioAnswer instance, use its localUrl for audio element
    if (answer instanceof AudioAnswer) {
      setAudioUrl(answer.localUrl);
    } else {
      setAudioUrl(null);
    }
  }, [answer]);

  const startRecording = async () => {
    if (!navigator.mediaDevices?.getUserMedia) {
      alert("Your browser does not support audio recording.");
      return;
    }

    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    mediaRecorderRef.current = new MediaRecorder(stream);
    audioChunksRef.current = [];

    mediaRecorderRef.current.ondataavailable = (event) => {
      audioChunksRef.current.push(event.data);
    };

    mediaRecorderRef.current.onstop = () => {
      const audioBlob = new Blob(audioChunksRef.current, { type: "audio/webm" });
      const url = URL.createObjectURL(audioBlob);
      setAudioUrl(url);
      
      // Create AudioAnswer instance and pass it to onAnswer
      const audioAnswer = new AudioAnswer(url, audioBlob);
      onAnswer(audioAnswer);
    };

    mediaRecorderRef.current.start();
    setRecording(true);
  };

  const stopRecording = () => {
    mediaRecorderRef.current?.stop();
    setRecording(false);
  };

  const RecordingVisualizer = () => (
    <div className="flex space-x-1 items-end h-12">
      {[1, 2, 3, 4, 5].map((_, i) => (
        <span
          key={i}
          className={`w-2 bg-red-600 rounded animate-pulse-delay-${i}`}
          style={{
            animationDuration: "1.2s",
            animationTimingFunction: "ease-in-out",
            animationIterationCount: "infinite",
            animationDirection: "alternate",
            height: `${4 + i * 4}px`,
          }}
        />
      ))}
      <style jsx>{`
        @keyframes pulseDelay0 {
          0%, 100% { transform: scaleY(0.3); }
          50% { transform: scaleY(1); }
        }
        @keyframes pulseDelay1 {
          0%, 100% { transform: scaleY(0.4); }
          50% { transform: scaleY(1.1); }
        }
        @keyframes pulseDelay2 {
          0%, 100% { transform: scaleY(0.5); }
          50% { transform: scaleY(1.2); }
        }
        @keyframes pulseDelay3 {
          0%, 100% { transform: scaleY(0.4); }
          50% { transform: scaleY(1.1); }
        }
        @keyframes pulseDelay4 {
          0%, 100% { transform: scaleY(0.3); }
          50% { transform: scaleY(1); }
        }
        .animate-pulse-delay-0 { animation-name: pulseDelay0; }
        .animate-pulse-delay-1 { animation-name: pulseDelay1; }
        .animate-pulse-delay-2 { animation-name: pulseDelay2; }
        .animate-pulse-delay-3 { animation-name: pulseDelay3; }
        .animate-pulse-delay-4 { animation-name: pulseDelay4; }
      `}</style>
    </div>
  );

  return (
    <div className="space-y-4 max-w-xl mx-auto p-4 border rounded-md shadow-sm">
      <div className="mb-4 md:mb-6 md:text-lg text-gray-700 w-full">
        <QuestionContent>{question}</QuestionContent>
      </div>

      <div className="flex items-center gap-4">
        {!recording ? (
          <button
            onClick={startRecording}
            className="px-5 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
          >
            Start Recording
          </button>
        ) : (
          <>
            <button
              onClick={stopRecording}
              className="px-5 py-2 bg-gray-700 text-white rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-600"
            >
              Stop Recording
            </button>
            <RecordingVisualizer />
          </>
        )}
      </div>

      {audioUrl && (
        <audio controls className="w-full mt-4 rounded-md">
          <source src={audioUrl} type="audio/webm" />
          Your browser does not support the audio element.
        </audio>
      )}
    </div>
  );
};
