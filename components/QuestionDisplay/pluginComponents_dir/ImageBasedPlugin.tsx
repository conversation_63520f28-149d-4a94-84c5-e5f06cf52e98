/* eslint-disable @next/next/no-img-element */
"use client"

import React from "react"
import { PluginProps } from "@/src/app/test-session/[id]/model"
import QuestionContent from "../QuestionContent";
import OptionsComponent from "@/components/OptionsComponent";

export const ImageBasedPlugin: React.FC<PluginProps> = ({
  question,
  answer,
  onAnswer,
}) => {
  return (
    <div className="space-y-3 md:max-w-2xl md:mx-auto">
      <div className="mb-4 md:mb-6 md:text-lg text-gray-700 w-full">
        {question.image && (
          <div className="mb-6 md:mb-8">
            <img
              src={question.image}
              alt="Question visual"
              width={800}
              height={400}
              className="rounded-lg w-full h-auto object-cover md:max-h-80 md:object-contain md:mx-auto"
            />
          </div>
        )}
        <QuestionContent>{question}</QuestionContent>
      </div>

      <OptionsComponent
        options={question.options || []}
        selectedId={answer}
        randomize={question.randomizeOptions}
        onSelect={onAnswer}
      />
    </div>
  )
}
