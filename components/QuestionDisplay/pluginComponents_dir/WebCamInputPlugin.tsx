"use client";

import React, { useState, useRef, useEffect } from "react";
import { PluginProps, WebCamInput } from "@/src/app/test-session/[id]/model"
import QuestionContent from "../QuestionContent";

export const WebCamInputPlugin: React.FC<PluginProps> = ({
  question,
  answer,
  onAnswer,
}) => {
  const [recording, setRecording] = useState(false);
  const [videoUrl, setVideoUrl] = useState<string | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const chunksRef = useRef<Blob[]>([]);
  const streamRef = useRef<MediaStream | null>(null);
  const liveVideoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    return () => {
      if (videoUrl) URL.revokeObjectURL(videoUrl);
      if (streamRef.current) {
        streamRef.current.getTracks().forEach((t) => t.stop());
      }
    };
  }, [videoUrl]);

  useEffect(() => {
    if (!answer) return;
    if ("blob" in answer && "localUrl" in answer) {
      setVideoUrl(answer.localUrl);
    }
  }, [answer]);

  const startRecording = async () => {
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      alert("Your browser does not support webcam recording.");
      return;
    }

    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true,
      });
      streamRef.current = stream;

      if (liveVideoRef.current) {
        liveVideoRef.current.srcObject = stream;
        liveVideoRef.current.play();
      }

      mediaRecorderRef.current = new MediaRecorder(stream);
      chunksRef.current = [];

      mediaRecorderRef.current.ondataavailable = (e) => {
        chunksRef.current.push(e.data);
      };

      mediaRecorderRef.current.onstop = () => {
        const blob = new Blob(chunksRef.current, { type: "video/webm" });
        const url = URL.createObjectURL(blob);
        setVideoUrl(url);
        onAnswer(new WebCamInput(url, blob));
        stream.getTracks().forEach((track) => track.stop());

        // Clear live preview srcObject
        if (liveVideoRef.current) {
          liveVideoRef.current.srcObject = null;
        }
      };

      mediaRecorderRef.current.start();
      setRecording(true);
    } catch (error) {
      alert("Failed to start webcam recording: " + error);
    }
  };

  const stopRecording = () => {
    mediaRecorderRef.current?.stop();
    setRecording(false);
  };

  return (
    <div className="space-y-4 max-w-xl mx-auto p-4 border rounded-md shadow-sm">
      <div className="mb-4 md:mb-6 md:text-lg text-gray-700 w-full">
        <QuestionContent>{question}</QuestionContent>
      </div>

      <div className="flex items-center gap-4">
        {!recording ? (
          <button
            onClick={startRecording}
            className="px-5 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
          >
            Start Webcam Recording
          </button>
        ) : (
          <button
            onClick={stopRecording}
            className="px-5 py-2 bg-gray-700 text-white rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-600"
          >
            Stop Recording
          </button>
        )}
      </div>

      {recording && (
        <video
          ref={liveVideoRef}
          className="w-full mt-4 rounded-md"
          playsInline
          muted
          autoPlay
        />
      )}

      {!recording && videoUrl && (
        <video
          controls
          src={videoUrl}
          className="w-full mt-4 rounded-md"
          playsInline
        />
      )}
    </div>
  );
};
