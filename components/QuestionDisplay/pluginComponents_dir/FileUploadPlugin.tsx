/* eslint-disable @next/next/no-img-element */
"use client"

import type React from "react"
import { useState } from "react"
import { PluginProps } from "@/src/app/test-session/[id]/model"
import QuestionContent from "../QuestionContent";

export const FileUploadPlugin: React.FC<PluginProps> = ({ question, answer, onAnswer }) => {
  const [fileName, setFileName] = useState<string | null>(answer instanceof File ? answer.name : null)

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0]
      setFileName(file.name)
      onAnswer(file)
    }
  }

  return (
    <div className="space-y-4">
      <div className="mb-4 md:mb-6 md:text-lg text-gray-700 w-full">
        {question.image && (
          <div className="mb-6 md:mb-8">
            <img
              src={question.image}
              alt="Question visual"
              width={800}
              height={400}
              className="rounded-lg w-full h-auto object-cover md:max-h-80 md:object-contain md:mx-auto"
            />
          </div>
        )}
        {question.audio && (
          <div className="mb-4">
            <audio controls>
              <source src={question.audio} type="audio/mpeg" />
              Your browser does not support the audio element.
            </audio>
          </div>
        )}
        <QuestionContent>{question}</QuestionContent>
      </div>
      <div className="w-full max-w-md mx-auto">
        <label
          htmlFor="file-upload"
          className="relative flex flex-col items-center justify-center w-full px-4 py-6 text-center transition border border-dashed rounded-md cursor-pointer hover:border-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2"
        >
          <input
            id="file-upload"
            type="file"
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            onChange={handleFileChange}
            aria-label="Upload file"
          />
          <svg
            className="w-10 h-10 mb-3 text-gray-400"
            fill="none"
            stroke="currentColor"
            strokeWidth={1.5}
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path strokeLinecap="round" strokeLinejoin="round" d="M12 4v16m8-8H4" />
          </svg>
          <p className="text-sm font-semibold text-gray-600">
            {fileName || "Klik untuk memilih file atau seret file ke sini"}
          </p>
          <p className="mt-1 text-xs text-gray-400">PNG, JPG, GIF up to 10MB</p>
        </label>
      </div>
    </div>
  )
}