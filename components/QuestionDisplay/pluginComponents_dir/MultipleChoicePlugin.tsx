"use client"

import React from "react"
import { PluginProps } from "@/src/app/test-session/[id]/model"
import OptionsComponent from "@/components/OptionsComponent"
import { DisplayQuestion } from "./DisplayQuestion"

export const MultipleChoicePlugin: React.FC<PluginProps> = ({
  question,
  answer,
  onAnswer,
}) => {
  return (
    <div className="space-y-3 md:max-w-2xl md:mx-auto">
      <DisplayQuestion question={question} />
      <OptionsComponent
        options={question.options || []}
        selectedId={answer}
        // randomize={question.randomizeOptions}
        randomize={false}
        onSelect={onAnswer}
      />
    </div>
  )
}
