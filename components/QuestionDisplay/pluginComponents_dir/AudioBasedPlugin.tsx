"use client";

import React from "react";
import { PluginProps } from "@/src/app/test-session/[id]/model"
import QuestionContent from "../QuestionContent";
import OptionsComponent from "@/components/OptionsComponent";

export const AudioBasedPlugin: React.FC<PluginProps> = ({
  question,
  answer,
  onAnswer,
}) => {
  return (
    <div className="space-y-3 md:max-w-2xl md:mx-auto">
      <div className="mb-4 md:mb-6 md:text-lg text-gray-700 w-full">
        {question.audio && (
          <div className="mb-4">
            <audio controls className="mb-4 w-full">
              <source src={question.audio} type="audio/mpeg" />
              Your browser does not support the audio element.
            </audio>
          </div>
        )}
        <QuestionContent>{question}</QuestionContent>
      </div>

      <OptionsComponent
        options={question.options || []}
        selectedId={answer}
        randomize={question.randomizeOptions}
        onSelect={onAnswer}
      />
    </div>
  );
};
