/* eslint-disable @next/next/no-img-element */
"use client"

import type React from "react"
import { PluginProps } from "@/src/app/test-session/[id]/model"
import QuestionContent from "../QuestionContent";

export const CodeInputPlugin: React.FC<PluginProps> = ({ question, answer, onAnswer }) => {
  return (
    <div className="space-y-4">
      <div className="mb-4 md:mb-6 md:text-lg text-gray-700 w-full">
        {question.image && (
          <div className="mb-6 md:mb-8">
            <img
              src={question.image}
              alt="Question visual"
              width={800}
              height={400}
              className="rounded-lg w-full h-auto object-cover md:max-h-80 md:object-contain md:mx-auto"
            />
          </div>
        )}
        {question.audio && (
          <div className="mb-4">
            <audio controls>
              <source src={question.audio} type="audio/mpeg" />
              Your browser does not support the audio element.
            </audio>
          </div>
        )}
        <QuestionContent>{question}</QuestionContent>
      </div>
      <textarea
        className="w-full border border-gray-300 p-3 rounded-lg font-mono bg-gray-50"
        placeholder="Write your code here..."
        spellCheck={false}
        value={answer || ""}
        onChange={(e) => onAnswer(e.target.value)}
        rows={10}
      />
    </div>
  )
}