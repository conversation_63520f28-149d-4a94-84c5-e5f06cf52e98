"use client"

import { PluginProps } from '@/src/app/test-session/[id]/model';
import { MultipleChoicePlugin, FreeTextPlugin, CodeInputPlugin, ImageBasedPlugin, AudioBasedPlugin, AudioAnswerPlugin, VoiceInputPlugin, FileUploadPlugin } from './pluginComponents_dir';
import { ScreenRecordInputPlugin } from './pluginComponents_dir/ScreenRecordInputPlugin';
import { WebCamInputPlugin } from './pluginComponents_dir/WebCamInputPlugin';

export * from './pluginComponents_dir'

export const pluginRegistry: Record<string, React.FC<PluginProps>> = {
  multipleChoice: MultipleChoicePlugin,
  singleChoice: MultipleChoicePlugin,
  textInput: FreeTextPlugin,
  freeText: FreeTextPlugin,
  codeInput: CodeInputPlugin,
  imageBased: ImageBasedPlugin,
  audioBased: AudioBasedPlugin,
  audioAnswer: AudioAnswerPlugin,
  voiceInput: VoiceInputPlugin,
  fileUpload: FileUploadPlugin,
  screenRecordInput: ScreenRecordInputPlugin,
  webcamInput: WebCamInputPlugin,
};