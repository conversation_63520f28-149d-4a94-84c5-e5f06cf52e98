"use client"

import { useState, useEffect } from "react"
import { Card } from "@/components/ui/card"
import ConstructionMessage from "@/components/construction-message"
import TestCarousel from "@/components/test-carousel"
import type { TestCardProps } from "@/components/test-card" // Import TestCardProps

export interface UnderConstructionProps {
  tests: TestCardProps[]
}

export default function UnderConstruction({ tests }: UnderConstructionProps) {
  const [mounted, setMounted] = useState(false)

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) return null

  return (
    <div className="relative min-h-screen w-full overflow-hidden bg-gradient-to-br from-slate-50 to-slate-100">
      {/* Background blur elements */}
      <div className="absolute -left-20 -top-20 h-96 w-96 rounded-full bg-blue-200 opacity-20 blur-3xl" />
      <div className="absolute bottom-20 right-20 h-96 w-96 rounded-full bg-purple-200 opacity-20 blur-3xl" />
      <div className="absolute left-1/3 top-1/3 h-64 w-64 rounded-full bg-green-200 opacity-20 blur-3xl" />

      <div className="container relative z-10 mx-auto px-4 py-16">
        <div className="mx-auto max-w-6xl">
          <Card className="overflow-hidden bg-white/80 backdrop-blur-sm">
            <div className="grid gap-8 p-8 md:grid-cols-2">
              {/* Left Pane */}
              <ConstructionMessage />

              {/* Right Pane */}
              <div className="flex items-center justify-center">
                <TestCarousel tests={tests} />
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  )
}
