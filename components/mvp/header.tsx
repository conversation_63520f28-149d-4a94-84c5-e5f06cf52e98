"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { useAuth } from "@/src/lib/auth-utils"
import { useState, useEffect } from "react"

export function Header() {
  const pathname = usePathname()
  const { getCurrentUser, logout } = useAuth()
  const [user, setUser] = useState(null)

  // Get the user once when the component mounts
  useEffect(() => {
    const currentUser = getCurrentUser()
    setUser(currentUser)
  }, []) // Empty dependency array - only run once on mount

  if (!user) return null

  return (
    <header className="border-b">
      <div className="container flex items-center justify-between h-16 px-4 mx-auto">
        <Link href="/mvp/tests" className="text-xl font-bold">
          MCQ Manager
        </Link>
        <nav className="flex items-center space-x-4">
          <Link
            href="/mvp/tests"
            className={`text-sm ${pathname === "/mvp/tests" ? "text-primary font-medium" : "text-muted-foreground"}`}
          >
            Tests
          </Link>
          <Link
            href="/mvp/files"
            className={`text-sm ${pathname === "/mvp/files" ? "text-primary font-medium" : "text-muted-foreground"}`}
          >
            Media Files
          </Link>
          <Button variant="ghost" onClick={logout} className="text-sm">
            Logout
          </Button>
        </nav>
      </div>
    </header>
  )
}
