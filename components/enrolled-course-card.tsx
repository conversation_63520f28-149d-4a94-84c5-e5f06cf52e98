import Link from "next/link"
import Image from "next/image"
import { <PERSON>, BookO<PERSON> } from "lucide-react"

import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"

interface EnrolledCourseCardProps {
  course: {
    id: string
    title: string
    instructor: string
    instructorAvatar: string
    completionPercentage: number
    lastAccessed: string
    summary: string
    coverImage: string
    totalLessons: number
    completedLessons: number
    estimatedHours: number
    category: string
  }
}

export function EnrolledCourseCard({ course }: EnrolledCourseCardProps) {
  return (
    <Card className="overflow-hidden flex flex-col h-full">
      <div className="relative h-48 w-full">
        <Image src={course.coverImage || "/placeholder.svg"} alt={course.title} fill className="object-cover" />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
        <Badge className="absolute top-3 right-3">{course.category}</Badge>
      </div>
      <CardContent className="flex-grow pt-4">
        <div className="mb-3 flex items-center gap-2">
          <Avatar className="h-6 w-6">
            <AvatarImage src={course.instructorAvatar || "/placeholder.svg"} alt={course.instructor} />
            <AvatarFallback>
              {course.instructor
                .split(" ")
                .map((n) => n[0])
                .join("")}
            </AvatarFallback>
          </Avatar>
          <span className="text-sm text-muted-foreground">{course.instructor}</span>
        </div>
        <h3 className="font-semibold line-clamp-2 mb-2">{course.title}</h3>
        <p className="text-sm text-muted-foreground line-clamp-2 mb-3">{course.summary}</p>
        <div className="space-y-3">
          <div className="flex items-center justify-between text-sm">
            <span>Progress</span>
            <span className="font-medium">{course.completionPercentage}%</span>
          </div>
          <Progress value={course.completionPercentage} className="h-2" />
          <div className="flex flex-wrap gap-3 text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <BookOpen className="h-3.5 w-3.5" />
              <span>
                {course.completedLessons}/{course.totalLessons} lessons
              </span>
            </div>
            <div className="flex items-center gap-1">
              <Clock className="h-3.5 w-3.5" />
              <span>{course.estimatedHours} hours</span>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="border-t bg-muted/50 px-6 py-3">
        <div className="flex w-full items-center justify-between">
          <span className="text-xs text-muted-foreground">Last accessed {course.lastAccessed}</span>
          <Button asChild size="sm">
            <Link href={`/dashboard/my-courses/${course.id}`}>Continue</Link>
          </Button>
        </div>
      </CardFooter>
    </Card>
  )
}
