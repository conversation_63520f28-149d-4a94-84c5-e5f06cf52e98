"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";

type NotFoundLayoutProps = {
  title: string;
  description: string;
  redirectHref: string;
  redirectLabel: string;
};

export default function NotFoundLayout({
  title,
  description,
  redirectHref,
  redirectLabel,
}: NotFoundLayoutProps) {
  return (
    <div className="min-h-screen flex items-center justify-center px-4">
      <div className="w-full max-w-md text-center space-y-4">
        <h1 className="text-2xl font-bold text-destructive">{title}</h1>
        <p className="text-muted-foreground">{description}</p>
        <Link href={redirectHref} passHref>
          <Button className="mt-2">{redirectLabel}</Button>
        </Link>
      </div>
    </div>
  );
}
