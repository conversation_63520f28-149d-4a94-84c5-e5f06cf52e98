"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  ScrollArea
} from "@/components/ui/scroll-area"

import { useLocalization } from "@/src/localization/functions/client"
import en from "@/src/app/dashboard/question-editor/locales/en.json"
import { MultipleChoiceQuestion } from "@/src/lib/repositories/questions/interface"

interface QuestionListProps {
  questions: any[]
  selectedQuestion: number
  onSelectQuestion: (index: number) => void
  onDeleteQuestion: (index: number) => void
}

export function QuestionList({
  questions,
  selectedQuestion,
  onSelectQuestion,
  onDeleteQuestion,
}: QuestionListProps) {
  const [viewMode, setViewMode] = useState<"compact" | "detailed">("detailed")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedTags, setSelectedTags] = useState<string>("basics")
  const { t } = useLocalization("public-test", { en })

  const filteredQuestions = questions.filter((question) => {
    const searchFilter = question.question
      .toLowerCase()
      .includes(searchQuery.toLowerCase())
    const tagFilter = question?.tag?.every((tag: string) =>
      selectedTags.includes(tag)
    )

    if (selectedTags?.length > 0) return searchFilter && tagFilter
    return searchFilter
  })

  return (
    <div className="border rounded-md p-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">
          {t("questionManagement.allQuestions")} ({questions.length})
        </h2>
        <Button
          variant="outline"
          size="sm"
          onClick={() =>
            setViewMode(viewMode === "compact" ? "detailed" : "compact")
          }
        >
          {viewMode === "compact"
            ? t("questionManagement.detailedView")
            : t("questionManagement.compactView")}
        </Button>
      </div>

      <div className="space-y-4 mb-4">
        <Input
          placeholder={t("questionManagement.searchQuestions")}
          className="w-full"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
        <div className="relative">
          <Select value={selectedTags} onValueChange={setSelectedTags}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder={t("questionManagement.filterByTag")} />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem value="competency-1">Competency 1</SelectItem>
                <SelectItem value="competency-2">Competency 2</SelectItem>
                <SelectItem value="competency-3">Competency 3</SelectItem>
                <SelectItem value="basics">Basics</SelectItem>
                <SelectItem value="advanced">Advanced</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
      </div>

      <ScrollArea className="h-[400px] border-t pt-4">
        <div className="space-y-6">
          {filteredQuestions.map((question, index) => (
            <div
              key={question.id}
              className={`border-b pb-4 ${index === filteredQuestions.length - 1 ? "border-b-0" : ""
                }`}
            >
              <p className="font-medium mb-2">{question.question}</p>

              {viewMode === "detailed" &&
                (question as MultipleChoiceQuestion).options && (
                  <div className="mb-2">
                    {(question as MultipleChoiceQuestion).options.map(
                      (option) => (
                        <div
                          key={option.id}
                          className={`ml-4 ${(question as MultipleChoiceQuestion).correctAnswers?.includes(
                            option.id
                          )
                            ? "text-green-600 font-medium"
                            : ""
                            }`}
                        >
                          {option.id}. {option.text}
                        </div>
                      )
                    )}
                  </div>
                )}

              {viewMode === "compact" && (
                <div className="mb-2 text-sm text-gray-500">
                  {t(`questionTypes.${question.type}`)}
                </div>
              )}

              <div className="mt-2 flex items-center justify-between">
                <span className="text-sm text-gray-600">
                  {question["competency"]}
                </span>
                <div className="space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onSelectQuestion(index)}
                  >
                    {t("common.edit")}
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => onDeleteQuestion(index)}
                  >
                    {t("common.delete")}
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>
    </div >
  )
}
