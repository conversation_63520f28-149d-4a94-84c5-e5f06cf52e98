"use client";

import type React from "react";
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { X, Upload, Play, Pause } from "lucide-react";
import { BaseOption, PluginProps } from "@/src/lib/question-types";

function t(key: string) {
  return key;
}

// Multiple Choice Plugin (also used for Single Choice)
export const MultipleChoicePlugin: React.FC<PluginProps> = ({
  question,
  onChange,
}) => {
  const isSingleChoice = question.type === "singleChoice";

  const handleOptionChange = (optionIndex: number) => {
    const updatedOptions = [...(question.options || [])];
    const optionId = updatedOptions[optionIndex].id;

    // Update correctAnswers array
    let correctAnswers: string[] = [];
    if (isSingleChoice) {
      correctAnswers = [optionId];
    } else {
      correctAnswers = [...(question.correctAnswers || [])];
      if (correctAnswers.includes(optionId)) {
        correctAnswers = correctAnswers.filter((id) => id !== optionId);
      } else {
        correctAnswers.push(optionId);
      }
    }

    onChange({ correctAnswers });
  };

  const handleOptionTextChange = (optionIndex: number, text: string) => {
    const updatedOptions = [...(question.options || [])];
    updatedOptions[optionIndex] = {
      ...updatedOptions[optionIndex],
      text,
    };
    onChange({ options: updatedOptions });
  };

  const addOption = () => {
    const options = question.options || [];
    const newOption: BaseOption = {
      id: String.fromCharCode(65 + options.length), // A, B, C, etc.
      text: "",
    };
    onChange({ options: [...options, newOption] });
  };

  const removeOption = (optionIndex: number) => {
    const options = question.options || [];
    const updatedOptions = options.filter((_, index) => index !== optionIndex);

    // Update correctAnswers to remove any that were in the deleted option
    const removedOptionId = options[optionIndex].id;
    const correctAnswers = (question.correctAnswers || []).filter(
      (id) => id !== removedOptionId
    );

    onChange({ options: updatedOptions, correctAnswers });
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-sm font-medium">
          {isSingleChoice
            ? t("plugins.singleChoice.title")
            : t("plugins.multipleChoice.title")}
        </h3>
        <Button variant="outline" size="sm" onClick={addOption}>
          {isSingleChoice
            ? t("plugins.singleChoice.addOption")
            : t("plugins.multipleChoice.addOption")}
        </Button>
      </div>

      <div className="space-y-3 max-h-[300px] overflow-y-auto">
        {question.options?.map((option, index) => (
          <div
            key={index}
            className="flex items-start space-x-3 border-b pb-3 last:border-b-0"
          >
            <div className="flex-shrink-0 pt-1">
              <Checkbox
                id={`option-${index}`}
                checked={question.correctAnswers?.includes(option.id) ?? false}
                onCheckedChange={() => handleOptionChange(index)}
              />
            </div>
            <div className="grid gap-1.5 w-full">
              <div className="flex items-center">
                <span className="bg-gray-100 px-2 py-1 rounded-md text-sm font-medium mr-2">
                  {option.id}
                </span>
                <Input
                  value={option.text || ""}
                  onChange={(e) =>
                    handleOptionTextChange(index, e.target.value)
                  }
                  className="flex-1"
                  placeholder={t("plugins.multipleChoice.enterOptionText")}
                />
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 text-gray-500"
              onClick={() => removeOption(index)}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        ))}
      </div>

      <div className="bg-gray-50 p-3 rounded-md text-sm">
        <p>
          {isSingleChoice
            ? t("plugins.singleChoice.singleSelectionInfo")
            : t("plugins.multipleChoice.multipleSelectionInfo")}
        </p>
        <p className="mt-2 text-xs text-gray-500">
          {isSingleChoice
            ? t("plugins.singleChoice.correctAnswer")
            : t("plugins.multipleChoice.correctAnswers")}
          {question.correctAnswers?.join(", ") || "None selected"}
        </p>
      </div>
    </div>
  );
};

// Single Choice Plugin (redirects to MultipleChoice with different behavior)
export const SingleChoicePlugin: React.FC<PluginProps> = (props) => {
  return <MultipleChoicePlugin {...props} />;
};

// Free Text Plugin (for text input and free text)
export const FreeTextPlugin: React.FC<PluginProps> = ({
  question,
  onChange,
}) => {
  return (
    <div className="space-y-4">
      <h3 className="text-sm font-medium">{t("plugins.textInput.title")}</h3>

      <div className="border rounded-md p-3 bg-gray-50">
        <p className="text-sm text-gray-500 mb-2">
          {t("plugins.textInput.testTakerWillEnterText")}
        </p>
        <Textarea
          placeholder={t("plugins.textInput.exampleAnswerPlaceholder")}
          className="min-h-[100px] bg-white"
          value={question.sampleSolution || ""}
          onChange={(e) => onChange({ sampleSolution: e.target.value })}
        />
      </div>

      <div>
        <label className="text-sm font-medium mb-2 block">
          {t("plugins.textInput.assessmentCriteria")}
        </label>
        <Textarea
          placeholder={t("plugins.textInput.assessmentCriteriaPlaceholder")}
          className="min-h-[100px]"
          value={question.assessmentCriteria || ""}
          onChange={(e) => onChange({ assessmentCriteria: e.target.value })}
        />
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="word-limit"
          checked={!!question.wordLimit}
          onCheckedChange={(checked) => {
            if (checked) {
              onChange({ wordLimit: 250 }); // Default word limit
            } else {
              onChange({ wordLimit: undefined });
            }
          }}
        />
        <label htmlFor="word-limit" className="text-sm">
          {t("plugins.textInput.setWordLimit")}
        </label>
        {question.wordLimit !== undefined && (
          <Input
            type="number"
            className="w-24"
            placeholder="Limit"
            value={question.wordLimit}
            onChange={(e) =>
              onChange({ wordLimit: Number.parseInt(e.target.value) || 0 })
            }
          />
        )}
      </div>
    </div>
  );
};

// Code Input Plugin
export const CodeInputPlugin: React.FC<PluginProps> = ({
  question,
  onChange,
}) => {
  return (
    <div className="space-y-4">
      <h3 className="text-sm font-medium">{t("plugins.codeInput.title")}</h3>

      <div className="border rounded-md p-3 bg-gray-50">
        <div className="flex justify-between items-center mb-2">
          <p className="text-sm text-gray-500">
            {t("plugins.codeInput.testTakerWillEnterCode")}
          </p>
          <Select
            value={question.language || "javascript"}
            onValueChange={(value) => onChange({ language: value })}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue
                placeholder={t("plugins.codeInput.selectLanguage")}
              />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="javascript">JavaScript</SelectItem>
              <SelectItem value="python">Python</SelectItem>
              <SelectItem value="java">Java</SelectItem>
              <SelectItem value="csharp">C#</SelectItem>
              <SelectItem value="cpp">C++</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Textarea
          placeholder={t("plugins.codeInput.sampleSolutionPlaceholder")}
          className="min-h-[150px] bg-white font-mono text-sm"
          value={question.sampleSolution || ""}
          onChange={(e) => onChange({ sampleSolution: e.target.value })}
        />
      </div>

      <div>
        <label className="text-sm font-medium mb-2 block">
          {t("plugins.codeInput.assessmentCriteria")}
        </label>
        <Textarea
          placeholder={t("plugins.codeInput.assessmentCriteriaPlaceholder")}
          className="min-h-[100px]"
          value={question.assessmentCriteria || ""}
          onChange={(e) => onChange({ assessmentCriteria: e.target.value })}
        />
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="auto-grade"
          checked={question.autoGrade || false}
          onCheckedChange={(checked) => onChange({ autoGrade: !!checked })}
        />
        <label htmlFor="auto-grade" className="text-sm">
          {t("plugins.codeInput.enableAutoGrading")}
        </label>
      </div>
    </div>
  );
};

// Image Based Plugin
export const ImageBasedPlugin: React.FC<PluginProps> = ({
  question,
  onChange,
}) => {
  return (
    <div className="space-y-4">
      <h3 className="text-sm font-medium">{t("plugins.imageBased.title")}</h3>

      <div className="border border-dashed border-gray-300 rounded-md p-4">
        <div className="text-center">
          {question.image ? (
            <div className="relative">
              <img
                src={question.image || "/placeholder.svg"}
                alt="Question image"
                className="max-h-[200px] mx-auto object-contain"
              />
              <Button
                variant="outline"
                size="sm"
                className="absolute top-2 right-2"
                onClick={() => onChange({ image: undefined })}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ) : (
            <div className="mt-2">
              <Button variant="outline" size="sm" className="mr-2">
                <Upload className="h-4 w-4 mr-2" />
                {t("plugins.imageBased.uploadImage")}
              </Button>
              <p className="text-xs text-gray-500 mt-2">
                {t("plugins.imageBased.imageSupport")}
              </p>
            </div>
          )}
        </div>
      </div>

      <MultipleChoicePlugin question={question} onChange={onChange} />
    </div>
  );
};

// Audio Based Plugin
export const AudioBasedPlugin: React.FC<PluginProps> = ({
  question,
  onChange,
}) => {
  const [isPlaying, setIsPlaying] = useState(false);

  return (
    <div className="space-y-4">
      <h3 className="text-sm font-medium">{t("plugins.audioBased.title")}</h3>

      <div className="border border-dashed border-gray-300 rounded-md p-4">
        <div className="text-center">
          {question.audio ? (
            <div className="flex items-center space-x-3 p-2 bg-gray-50 rounded-md">
              <Button
                variant="outline"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={() => setIsPlaying(!isPlaying)}
              >
                {isPlaying ? (
                  <Pause className="h-4 w-4" />
                ) : (
                  <Play className="h-4 w-4" />
                )}
              </Button>
              <div className="h-2 bg-gray-200 rounded-full flex-1">
                <div className="h-2 bg-gray-500 rounded-full w-1/3"></div>
              </div>
              <span className="text-xs text-gray-500">0:12</span>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={() => onChange({ audio: undefined })}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ) : (
            <div className="mt-2">
              <Button variant="outline" size="sm" className="mr-2">
                <Upload className="h-4 w-4 mr-2" />
                {t("plugins.audioBased.uploadAudio")}
              </Button>
              <p className="text-xs text-gray-500 mt-2">
                {t("plugins.audioBased.audioSupport")}
              </p>
            </div>
          )}
        </div>
      </div>

      <MultipleChoicePlugin question={question} onChange={onChange} />
    </div>
  );
};

// Audio Answer Plugin
export const AudioAnswerPlugin: React.FC<PluginProps> = ({
  question,
  onChange,
}) => {
  return (
    <div className="space-y-4">
      <h3 className="text-sm font-medium">{t("plugins.audioAnswer.title")}</h3>

      <div className="space-y-3 max-h-[300px] overflow-y-auto">
        {question.options?.map((option, index) => (
          <div
            key={index}
            className="flex items-start space-x-3 border-b pb-3 last:border-b-0"
          >
            <div className="flex-shrink-0 pt-1">
              <Checkbox
                id={`option-${index}`}
                checked={question.correctAnswers?.includes(option.id) ?? false}
                onCheckedChange={() => {
                  const optionId = option.id;
                  const correctAnswers = [...(question.correctAnswers || [])];
                  const isCorrect = correctAnswers.includes(optionId);

                  // Toggle the correct answer
                  if (isCorrect) {
                    onChange({
                      correctAnswers: correctAnswers.filter(
                        (id) => id !== optionId
                      ),
                    });
                  } else {
                    onChange({
                      correctAnswers: [...correctAnswers, optionId],
                    });
                  }
                }}
              />
            </div>
            <div className="grid gap-1.5 w-full">
              <div className="flex items-center">
                <span className="bg-gray-100 px-2 py-1 rounded-md text-sm font-medium mr-2">
                  {option.id}
                </span>
                <div className="flex items-center space-x-3 p-2 bg-gray-50 rounded-md flex-1">
                  <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                    <Play className="h-4 w-4" />
                  </Button>
                  <div className="h-2 bg-gray-200 rounded-full flex-1">
                    <div className="h-2 bg-gray-500 rounded-full w-1/3"></div>
                  </div>
                  <span className="text-xs text-gray-500">0:08</span>
                </div>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 text-gray-500"
              onClick={() => {
                const options = question.options || [];
                const updatedOptions = options.filter((_, i) => i !== index);

                // Update correctAnswers to remove any that were in the deleted option
                const removedOptionId = options[index].id;
                const correctAnswers = (question.correctAnswers || []).filter(
                  (id) => id !== removedOptionId
                );

                onChange({ options: updatedOptions, correctAnswers });
              }}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        ))}
      </div>

      <Button
        variant="outline"
        size="sm"
        onClick={() => {
          const options = question.options || [];
          const newOption: BaseOption = {
            id: String.fromCharCode(65 + options.length),
            audio: "",
          };
          onChange({ options: [...options, newOption] });
        }}
      >
        {t("plugins.audioAnswer.addAudioOption")}
      </Button>
    </div>
  );
};

// Voice Input Plugin
export const VoiceInputPlugin: React.FC<PluginProps> = ({
  question,
  onChange,
}) => {
  return (
    <div className="space-y-4">
      <h3 className="text-sm font-medium">{t("plugins.voiceInput.title")}</h3>

      <div className="border rounded-md p-3 bg-gray-50">
        <p className="text-sm text-gray-500 mb-2">
          {t("plugins.voiceInput.testTakerWillRecord")}
        </p>
        <div className="flex items-center justify-center p-4 border border-dashed border-gray-300 rounded-md bg-white">
          <span className="text-sm text-gray-500">
            {t("plugins.voiceInput.recordingWillAppear")}
          </span>
        </div>
      </div>

      <div>
        <label className="text-sm font-medium mb-2 block">
          {t("plugins.voiceInput.assessmentCriteria")}
        </label>
        <Textarea
          placeholder={t("plugins.voiceInput.assessmentCriteriaPlaceholder")}
          className="min-h-[100px]"
          value={question.assessmentCriteria || ""}
          onChange={(e) => onChange({ assessmentCriteria: e.target.value })}
        />
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="time-limit"
          checked={!!question.timeLimit}
          onCheckedChange={(checked) => {
            if (checked) {
              onChange({ timeLimit: 60 }); // Default time limit in seconds
            } else {
              onChange({ timeLimit: undefined });
            }
          }}
        />
        <label htmlFor="time-limit" className="text-sm">
          {t("plugins.voiceInput.setTimeLimit")}
        </label>
        {question.timeLimit !== undefined && (
          <Input
            type="number"
            className="w-24"
            placeholder="Seconds"
            value={question.timeLimit}
            onChange={(e) =>
              onChange({ timeLimit: Number.parseInt(e.target.value) || 0 })
            }
          />
        )}
      </div>
    </div>
  );
};

// File Upload Plugin
export const FileUploadPlugin: React.FC<PluginProps> = ({
  question,
  onChange,
}) => {
  return (
    <div className="space-y-4">
      <h3 className="text-sm font-medium">{t("plugins.fileUpload.title")}</h3>

      <div className="border rounded-md p-3 bg-gray-50">
        <p className="text-sm text-gray-500 mb-2">
          {t("plugins.fileUpload.testTakerWillUpload")}
        </p>
        <div className="flex items-center justify-center p-4 border border-dashed border-gray-300 rounded-md bg-white">
          <span className="text-sm text-gray-500">
            {t("plugins.fileUpload.uploadedFilesWillAppear")}
          </span>
        </div>
      </div>

      <div>
        <label className="text-sm font-medium mb-2 block">
          {t("plugins.fileUpload.assessmentCriteria")}
        </label>
        <Textarea
          placeholder={t("plugins.fileUpload.assessmentCriteriaPlaceholder")}
          className="min-h-[100px]"
          value={question.assessmentCriteria || ""}
          onChange={(e) => onChange({ assessmentCriteria: e.target.value })}
        />
      </div>

      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <Checkbox
            id="file-type-limit"
            checked={
              !!(
                question.allowedFileTypes &&
                question.allowedFileTypes.length > 0
              )
            }
            onCheckedChange={(checked) => {
              if (checked) {
                onChange({ allowedFileTypes: ["PDF", "DOC", "DOCX"] }); // Default allowed types
              } else {
                onChange({ allowedFileTypes: undefined });
              }
            }}
          />
          <label htmlFor="file-type-limit" className="text-sm">
            {t("plugins.fileUpload.limitFileTypes")}
          </label>
        </div>

        {question.allowedFileTypes && question.allowedFileTypes.length > 0 && (
          <div className="flex flex-wrap gap-2 pl-6">
            {["PDF", "DOC", "DOCX", "JPG", "PNG"].map((type) => (
              <div key={type} className="flex items-center space-x-1">
                <Checkbox
                  id={`file-type-${type}`}
                  checked={question.allowedFileTypes?.includes(type) || false}
                  onCheckedChange={(checked) => {
                    const currentTypes = question.allowedFileTypes || [];
                    const updatedTypes = checked
                      ? [...currentTypes, type]
                      : currentTypes.filter((t) => t !== type);
                    onChange({ allowedFileTypes: updatedTypes });
                  }}
                />
                <label htmlFor={`file-type-${type}`} className="text-xs">
                  {type}
                </label>
              </div>
            ))}
          </div>
        )}

        <div className="flex items-center space-x-2">
          <Checkbox
            id="file-size-limit"
            checked={!!question.fileSizeLimit}
            onCheckedChange={(checked) => {
              if (checked) {
                onChange({ fileSizeLimit: 10 }); // Default 10MB
              } else {
                onChange({ fileSizeLimit: undefined });
              }
            }}
          />
          <label htmlFor="file-size-limit" className="text-sm">
            {t("plugins.fileUpload.limitFileSize")}
          </label>
          {question.fileSizeLimit !== undefined && (
            <Input
              type="number"
              className="w-24"
              placeholder="MB"
              value={question.fileSizeLimit}
              onChange={(e) =>
                onChange({
                  fileSizeLimit: Number.parseInt(e.target.value) || 0,
                })
              }
            />
          )}
        </div>
      </div>
    </div>
  );
};
