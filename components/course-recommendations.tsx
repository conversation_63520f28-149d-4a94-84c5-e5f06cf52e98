import Image from "next/image"
import Link from "next/link"
import { ArrowR<PERSON> } from "lucide-react"

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

interface CourseRecommendationsProps {
  courseId: string
}

export function CourseRecommendations({ courseId }: CourseRecommendationsProps) {
  // Mock data for recommended courses
  const recommendations = [
    {
      id: "rec-1",
      title: "Redux Fundamentals",
      instructor: "<PERSON>",
      image: "/placeholder.svg?height=100&width=200&query=redux+course",
      reason: "Based on your interest in React",
    },
    {
      id: "rec-2",
      title: "TypeScript for React Developers",
      instructor: "<PERSON>",
      image: "/placeholder.svg?height=100&width=200&query=typescript+react+course",
      reason: "Complements your current course",
    },
    {
      id: "rec-3",
      title: "Next.js Masterclass",
      instructor: "<PERSON>",
      image: "/placeholder.svg?height=100&width=200&query=nextjs+course",
      reason: "Popular with students of this course",
    },
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recommended Courses</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {recommendations.map((course) => (
            <div key={course.id} className="flex gap-3">
              <div className="relative h-16 w-28 flex-shrink-0 overflow-hidden rounded-md">
                <Image src={course.image || "/placeholder.svg"} alt={course.title} fill className="object-cover" />
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="font-medium line-clamp-1">{course.title}</h4>
                <p className="text-sm text-muted-foreground line-clamp-1">By {course.instructor}</p>
                <p className="text-xs text-muted-foreground mt-1">{course.reason}</p>
              </div>
            </div>
          ))}
          <Button variant="outline" className="w-full" asChild>
            <Link href="/dashboard/explore">
              Explore More Courses
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
