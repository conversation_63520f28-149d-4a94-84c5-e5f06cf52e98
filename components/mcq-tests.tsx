"use client"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>R<PERSON> } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"

interface MCQTestsProps {
  courseId: string
}

export function MCQTests({ courseId }: MCQTestsProps) {
  // Mock data for MCQ tests
  const tests = [
    {
      id: "test-1",
      title: "React Hooks Fundamentals",
      description: "Test your knowledge of React hooks basics",
      questions: 10,
      timeLimit: "15 minutes",
      completed: true,
      score: 90,
      section: "Introduction",
    },
    {
      id: "test-2",
      title: "Advanced Hooks Quiz",
      description: "Challenge yourself with advanced hooks concepts",
      questions: 15,
      timeLimit: "20 minutes",
      completed: true,
      score: 75,
      section: "Advanced Hooks",
    },
    {
      id: "test-3",
      title: "Performance Optimization Test",
      description: "Test your understanding of React performance optimization",
      questions: 12,
      timeLimit: "18 minutes",
      completed: false,
      score: null,
      section: "Performance Optimization",
    },
    {
      id: "test-4",
      title: "Component Patterns Assessment",
      description: "Evaluate your knowledge of advanced component patterns",
      questions: 20,
      timeLimit: "30 minutes",
      completed: false,
      score: null,
      section: "Advanced Component Patterns",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold">Course Tests</h2>
          <p className="text-sm text-muted-foreground">Complete these tests to assess your understanding</p>
        </div>
        <Badge variant="outline" className="px-3">
          {tests.filter((test) => test.completed).length}/{tests.length} Completed
        </Badge>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        {tests.map((test) => (
          <Card key={test.id} className={test.completed ? "border-primary/20 bg-primary/5" : ""}>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle>{test.title}</CardTitle>
                  <CardDescription>{test.description}</CardDescription>
                </div>
                {test.completed && <Badge className="bg-primary">{test.score}%</Badge>}
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex flex-wrap gap-4 text-sm">
                  <div className="flex items-center gap-1">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <span>{test.questions} questions</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span>{test.timeLimit}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Badge variant="outline" className="font-normal">
                      {test.section}
                    </Badge>
                  </div>
                </div>

                {test.completed && (
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span>Your score</span>
                      <span className="font-medium">{test.score}%</span>
                    </div>
                    <Progress
                      value={test.score || 0}
                      className="h-2"
                      // indicatorClassName={
                      //   test.score && test.score >= 80
                      //     ? "bg-green-500"
                      //     : test.score && test.score >= 60
                      //       ? "bg-yellow-500"
                      //       : "bg-red-500"
                      // }
                    />
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter>
              <Button className="w-full" variant={test.completed ? "outline" : "default"}>
                {test.completed ? (
                  <>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    View Results
                  </>
                ) : (
                  <>
                    Start Test
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  )
}
