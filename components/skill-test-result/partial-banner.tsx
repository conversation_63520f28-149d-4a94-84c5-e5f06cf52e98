import { <PERSON>ert } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";

export function PartialBanner() {
  return (
    <Alert
      variant="default"
      className="bg-gradient-to-r from-amber-400 to-orange-500 text-white rounded-lg mb-6 overflow-hidden"
    >
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 px-6 py-5">
        <div>
          <div className="flex items-center gap-2 mb-1">
            <div className="relative">
              <div className="h-3 w-3 bg-white rounded-full animate-ping absolute"></div>
              <div className="h-3 w-3 bg-white rounded-full relative"></div>
            </div>
            <h3 className="text-xl font-bold">Partial Results Available</h3>
          </div>
          <p className="text-white/90 max-w-2xl">
            Some of your answers require human review. We'll update your results
            soon! In the meantime, you can explore your current performance in
            the tabs below.
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-3 mt-4 md:mt-0">
          <Button
            variant="default"
            className="bg-white text-orange-600 hover:bg-white/90 h-9 px-4 py-2"
          >
            Create Account to Get Notified
          </Button>
        </div>
      </div>
    </Alert>
  );
}
