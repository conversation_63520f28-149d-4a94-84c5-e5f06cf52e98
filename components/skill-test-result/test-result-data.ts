// export const testResultData = {
//   testName: "HTML & CSS Fundamentals",
//   score: 85,
//   totalQuestions: 20,
//   correctAnswers: 17,
//   incorrectAnswers: 3,
//   timeTaken: "12:45",
//   completedOn: "May 15, 2025",
//   badge: "Advanced",
//   percentile: 92,
// }

// // Chart data
// export const chartData = [
//   { name: "Correct", value: testResultData.correctAnswers, color: "#4ade80" },
//   { name: "Incorrect", value: testResultData.incorrectAnswers, color: "#f87171" },
// ]

// // Question categories performance
// export const categoryPerformance = [
//   { name: "HTML Structure", score: 100, questions: 5 },
//   { name: "CSS Styling", score: 80, questions: 5 },
//   { name: "Responsive Design", score: 90, questions: 5 },
//   { name: "CSS Flexbox", score: 60, questions: 5 },
// ]

// // Recommendations based on performance
// export const recommendations = [
//   {
//     title: "CSS Flexbox Mastery",
//     description: "Improve your flexbox skills with this advanced course",
//     icon: "📚",
//     link: "#",
//   },
//   {
//     title: "Responsive Design Workshop",
//     description: "Take your responsive design skills to the next level",
//     icon: "🖥️",
//     link: "#",
//   },
//   {
//     title: "Frontend Developer Path",
//     description: "Continue your journey to become a frontend expert",
//     icon: "🚀",
//     link: "#",
//   },
// ]

// // Sample question data
// export const sampleQuestions = [
//   {
//     id: 1,
//     question: "Which CSS property is used to control the space between elements?",
//     isCorrect: true,
//     options: [
//       {
//         text: "margin",
//         isSelected: true,
//         isCorrect: true,
//         explanation: "Margin controls the space outside elements, creating separation between elements.",
//       },
//       {
//         text: "padding",
//         isSelected: false,
//         isCorrect: false,
//         explanation: "Padding controls the space inside elements, between the content and its border.",
//       },
//       {
//         text: "border",
//         isSelected: false,
//         isCorrect: false,
//       },
//       {
//         text: "spacing",
//         isSelected: false,
//         isCorrect: false,
//       },
//     ],
//     tip: "Remember that margin is for external spacing (outside the element) while padding is for internal spacing (inside the element).",
//   },
//   {
//     id: 7,
//     question: "Which HTML element is used to create a responsive image?",
//     isCorrect: false,
//     options: [
//       {
//         text: "<responsive-img>",
//         isSelected: true,
//         isCorrect: false,
//         explanation: "This is not a valid HTML element.",
//       },
//       {
//         text: "<picture>",
//         isSelected: false,
//         isCorrect: false,
//       },
//       {
//         text: "<img>",
//         isSelected: false,
//         isCorrect: true,
//         explanation:
//           "The <img> element with appropriate attributes like 'srcset' and 'sizes' can create responsive images. You can also use CSS with max-width: 100% to make images responsive.",
//       },
//       {
//         text: "<fluid-image>",
//         isSelected: false,
//         isCorrect: false,
//       },
//     ],
//     tip: "To make images responsive, use the <img> tag with 'srcset' and 'sizes' attributes, or apply CSS with max-width: 100% and height: auto.",
//   },
// ]
