import { Badge } from "@/components/ui/badge";
import { TestQuestionInsight } from "@/src/lib/repositories/test-result/types";
import { CheckCircle, XCircle } from "lucide-react";

interface QuestionItemProps {
  question: TestQuestionInsight;
}

export default function QuestionItem({ question }: QuestionItemProps) {
  return (
    <div className="border rounded-lg mb-6 overflow-hidden">
      <div className="bg-gray-50 p-4 border-b">
        <div className="flex justify-between items-start">
          <div>
            <span className="text-sm text-gray-500">
              Question {question.id}
            </span>
            <h4 className="font-medium">{question.questionText}</h4>
          </div>
          {question.isCorrect ? (
            <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
              Correct
            </Badge>
          ) : (
            <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
              Incorrect
            </Badge>
          )}
        </div>
      </div>

      <div className="p-4">
        <div className="space-y-4">
          {question.answerStats &&
            question.answerStats.map((option, index) => (
              <div
                key={index}
                className={`p-3 rounded-md flex items-start gap-3 ${
                  option.isCorrect
                    ? "bg-green-50 border border-green-200"
                    : option.isSelected && !option.isCorrect
                    ? "bg-red-50 border border-red-200"
                    : "bg-gray-50 border border-gray-200"
                }`}
              >
                <div className="mt-0.5">
                  {option.isCorrect ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : option.isSelected && !option.isCorrect ? (
                    <XCircle className="h-5 w-5 text-red-600" />
                  ) : (
                    <div className="h-5 w-5 rounded-full border border-gray-300" />
                  )}
                </div>
                <div className="flex-1">
                  <div className="flex justify-between">
                    <p
                      className={`font-medium ${
                        option.isCorrect
                          ? "text-green-800"
                          : option.isSelected && !option.isCorrect
                          ? "text-red-800"
                          : ""
                      }`}
                    >
                      {option.answer}
                    </p>
                    <span className="text-sm text-gray-500 ml-2">
                      {index === 0
                        ? "42%"
                        : index === 1
                        ? "35%"
                        : index === 2
                        ? "18%"
                        : "5%"}{" "}
                      of users
                    </span>
                  </div>
                  {(option.isCorrect ||
                    (option.isSelected && !option.isCorrect)) && (
                    <p className="text-sm mt-1 text-gray-600">
                      {option.explanation}
                    </p>
                  )}
                  {!option.isCorrect && option.explanation && (
                    <p className="text-sm mt-1 text-orange-700 bg-orange-50 p-2 rounded border border-orange-100">
                      {option.explanation}
                    </p>
                  )}
                </div>
              </div>
            ))}
        </div>

        {question.explanation && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex gap-2">
              <div className="text-blue-500 mt-0.5">💡</div>
              <div>
                <p className="font-medium text-blue-800">Learning Tip</p>
                <p className="text-sm text-blue-700">{question.explanation}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
