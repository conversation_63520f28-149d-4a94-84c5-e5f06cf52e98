import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { TestResult } from "@/src/lib/repositories/test-result/types";
import { Download, Share2 } from "lucide-react";

interface CertificateTabProps {
  testResult: TestResult;
}

export default function CertificateTab({ testResult }: CertificateTabProps) {
  const isPartial = testResult.status === "partial";

  return (
    <Card>
      <CardHeader>
        <CardTitle>Your Certificate</CardTitle>
        <CardDescription>Download or share your achievement</CardDescription>
      </CardHeader>
      <CardContent className="flex flex-col items-center py-8">
        <div className="relative max-w-3xl w-full mx-auto bg-gradient-to-tr from-white via-gray-50 to-gray-100 rounded-lg shadow-lg border-4 border-yellow-400 p-10 overflow-hidden">
          {isPartial && (
            <div className="absolute top-0 left-0 right-0 bg-yellow-100 text-yellow-800 text-sm font-semibold text-center py-2 px-4 rounded-t-lg z-10">
              ⚠️ This certificate is not valid yet. The test result is
              incomplete. Do not screenshot or share.
            </div>
          )}

          {/* Decorative Top Line */}
          <div className="border-t-4 border-yellow-400 w-24 mx-auto mb-6 rounded" />

          <div className="text-center pt-4">
            <h2 className="text-4xl font-serif font-bold mb-4 tracking-wide text-yellow-700 drop-shadow-md">
              Certificate of Achievement
            </h2>

            <div className="flex justify-center mb-8">
              <svg
                width="200"
                height="30"
                viewBox="0 0 200 30"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="select-none"
              >
                <defs>
                  <linearGradient
                    id="greyGradient"
                    x1="0"
                    y1="0"
                    x2="200"
                    y2="0"
                  >
                    <stop offset="0%" stopColor="#d1d5db" />
                    <stop offset="50%" stopColor="#6b7280" />
                    <stop offset="100%" stopColor="#d1d5db" />
                  </linearGradient>
                </defs>
                <path
                  d="M0 20 Q50 0 100 20 T200 20"
                  stroke="url(#greyGradient)"
                  strokeWidth="4"
                  fill="none"
                />
              </svg>
            </div>

            <p className="text-gray-600 mb-6 italic">This certifies that</p>

            <p className="text-3xl font-semibold mb-6 text-gray-900">
              {"John Doe"}
            </p>

            <p className="text-gray-600 mb-2 italic">
              has successfully completed
            </p>

            <p className="text-2xl font-bold mb-6 text-yellow-700 tracking-wide">
              {testResult.testTitle}
            </p>

            <p className="text-gray-600 mb-2 italic">with a score of</p>

            <p className="text-5xl font-extrabold text-green-600 mb-8">
              {testResult.score}%
            </p>

            <p className="text-gray-600 mb-8">
              on {testResult.completedOn.toLocaleString()}
            </p>

            {/* Decorative Bottom Line */}
            <div className="border-b-4 border-yellow-400 w-20 mx-auto mb-8 rounded" />

            <div className="border-t border-yellow-300 pt-4">
              <p className="text-sm text-gray-500">
                Verify this certificate at{" "}
                <a
                  href={`https://SkillPintar.com/verify/${testResult.id}`}
                  className="text-yellow-600 underline"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  SkillPintar.com/verify/{testResult.id}
                </a>
              </p>
            </div>

            {/* Signature area */}
            <div className="mt-12 flex justify-center items-center gap-8">
              <div className="border-t border-yellow-600 w-40 text-center text-yellow-700 text-sm font-serif italic">
                Authorized Signature
              </div>
              <div className="border-t border-yellow-600 w-40 text-center text-yellow-700 text-sm font-serif italic">
                Date
              </div>
            </div>
          </div>
        </div>

        <div className="flex gap-4 mt-8">
          <Button
            disabled={isPartial}
            variant="outline"
            className="flex items-center"
          >
            <Download className="mr-2 h-4 w-4" />
            Download
          </Button>
          <Button
            disabled={isPartial}
            variant="outline"
            className="flex items-center"
          >
            <Share2 className="mr-2 h-4 w-4" />
            Share
          </Button>
        </div>

        {isPartial && (
          <p className="mt-6 text-sm text-yellow-600 text-center max-w-md">
            This certificate is not final. Please wait for the test review to
            complete to unlock it fully.
          </p>
        )}
      </CardContent>
    </Card>
  );
}
