import ScoreCard from "../cards/score-card";
import CategoryPerformanceCard from "../cards/category-performance-card";
import PercentileRankCard from "../cards/percentile-rank-card";
import RecommendationsCard from "../cards/recommendations-card";
import ShareAchievementCard from "../cards/share-achievement-card";
import {
  RecommendationStep,
  TestResult,
} from "@/src/lib/repositories/test-result/types";

interface OverviewTabProps {
  testResult: TestResult;
  recommendations: RecommendationStep[];
}

export default function OverviewTab({
  testResult,
  recommendations,
}: OverviewTabProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {/* Score Card */}
      <ScoreCard
        score={testResult.score}
        correctAnswers={testResult.correctAnswers}
        incorrectAnswers={testResult.incorrectAnswers}
        timeTaken={testResult.timeTaken.toString()}
        badge={testResult.badge}
        chartData={[
          {
            name: "Correct",
            value: testResult.correctAnswers,
            color: "#4ade80",
          },
          {
            name: "Incorrect",
            value: testResult.incorrectAnswers,
            color: "#f87171",
          },
        ]}
      />

      {/* Performance by Category */}
      <CategoryPerformanceCard categories={testResult.performanceByCategory} />

      {/* Percentile Rank */}
      <PercentileRankCard
        percentile={85}
        otherTestTakers={[
          { id: "foo", name: "Junaid Akbar", score: 98 },
          {
            id: "bar",
            name: "Rina Febriyanti",
            score: 92,
            avatarUrl: "/avatars/rina.png",
          },
          { id: "yes", name: "Yusuf M.", score: 69 },
        ]}
        currentUserId="bar"
        currentUserName="You"
      />

      {/* Recommendations */}
      <RecommendationsCard recommendations={recommendations} />

      {/* Share Your Achievement */}
      <ShareAchievementCard />
    </div>
  );
}
