import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import QuestionItem from "../question-item";
import { TestQuestionInsight } from "@/src/lib/repositories/test-result/types";
// import { sampleQuestions } from "../test-result-data"

export default function QuestionsTab({
  questionAnalysis,
}: {
  questionAnalysis: TestQuestionInsight[];
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Question Analysis</CardTitle>
        <CardDescription>
          Review your answers and see the correct solutions
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-8">
          <div className="text-center mb-8">
            <div className="inline-flex items-center mb-4">
              <Badge className="bg-gradient-to-r from-amber-500 to-orange-500 text-white mr-2">
                PRO
              </Badge>
              <span className="font-medium">Premium Feature</span>
            </div>
            <h3 className="text-xl font-bold mb-2">
              Unlock Complete Question Analysis
            </h3>
            <p className="text-gray-500 max-w-md mx-auto mb-4">
              Create an account and subscribe to our Pro plan to access detailed
              analysis for all 20 questions.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-3">
              <Button className="bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600">
                Create Account
              </Button>
              <Button variant="outline">Learn About Pro</Button>
            </div>
          </div>

          <div className="border-t border-dashed border-gray-200 pt-8">
            <div className="text-center mb-6">
              <h3 className="text-lg font-medium mb-2">
                You will get analysis like this
              </h3>
              <p className="text-gray-500 max-w-lg mx-auto">
                Pro subscribers receive detailed analysis for all questions,
                including common mistakes and statistics from other test takers.
              </p>
            </div>

            {/* Sample question analyses */}
            {questionAnalysis.map((question) => (
              <QuestionItem key={question.id} question={question} />
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
