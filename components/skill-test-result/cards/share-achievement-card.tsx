"use client"

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>le,
} from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Award,
  Gift,
  Copy,
  Linkedin,
  Instagram,
  TicketPercent,
} from "lucide-react"
import { useState } from "react"

export default function ShareAchievementCard() {
  const [copiedType, setCopiedType] = useState<null | "instagram" | "generic">(null)

  const shareText = {
    generic: `🎯 I just completed a challenge on DevQuizz and scored among the top performers! Check it out 👉 https://devquizz.com`,
    linkedin: `🚀 I just completed a skills assessment on DevQuizz and ranked in the top tier! Proud to share this milestone. #DevQuizz #CareerGrowth #TechSkills\n\n👉 Take your challenge: https://devquizz.com`,
    instagram: `🔥 Just aced a challenge on DevQuizz!\nRanked top 15% 🚀\n\nTry it now 👉 devquizz.com\n\n#DevQuizz #SkillUp #TechChallenge`,
  }

  const copyToClipboard = (text: string, type: "instagram" | "generic") => {
    navigator.clipboard.writeText(text).then(() => {
      setCopiedType(type)
      setTimeout(() => setCopiedType(null), 2000)
    })
  }

  return (
    <Card className="bg-gradient-to-br from-green-50 via-white to-green-100 border border-green-200 shadow-md overflow-hidden">
      <CardHeader className="bg-gradient-to-r from-green-100 to-green-200 rounded-t-md px-6 py-4 border-b border-green-300">
        <CardTitle className="text-lg text-green-800">Share Your Achievement</CardTitle>
      </CardHeader>

      <CardContent className="flex flex-col items-center py-6 px-6 text-center">
        <div className="flex items-center justify-center w-16 h-16 rounded-full bg-green-200 mb-4 shadow-inner">
          <Award className="h-8 w-8 text-green-700" />
        </div>

        <p className="mb-1 text-sm text-gray-800 font-medium">
          Show off your skills and inspire others!
        </p>
        <p className="text-sm text-gray-600 mb-4">
          Share your results to earn <strong>rewards</strong> like points or vouchers.
        </p>

        {/* Voucher Visualization */}
        <div className="w-full bg-white border border-dashed border-green-400 rounded-md p-4 mb-5 shadow-sm relative">
          <div className="absolute -top-3 -left-3 bg-green-500 text-white px-2 py-1 text-xs rounded shadow-sm">
            Voucher
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <TicketPercent className="text-green-500 h-6 w-6" />
              <div className="text-left">
                <p className="text-sm font-semibold text-green-700">10% OFF</p>
                <p className="text-xs text-gray-600">For your next tech course</p>
              </div>
            </div>
            <span className="bg-green-100 text-green-700 text-xs font-mono px-3 py-1 rounded-md">
              SHARE10
            </span>
          </div>
        </div>

        <div className="flex flex-col gap-2 w-full">
          <Button
            variant="outline"
            className="flex justify-between w-full text-sm"
            onClick={() =>
              window.open(
                `https://www.linkedin.com/sharing/share-offsite/?url=https://devquizz.com`,
                "_blank"
              )
            }
          >
            <div className="flex items-center gap-2">
              <Linkedin className="h-4 w-4 text-blue-700" />
              Share on LinkedIn
            </div>
          </Button>

          <Button
            variant="outline"
            className="flex justify-between w-full text-sm"
            onClick={() => copyToClipboard(shareText.instagram, "instagram")}
          >
            <div className="flex items-center gap-2">
              <Instagram className="h-4 w-4 text-pink-500" />
              Copy Instagram Text
            </div>
            {copiedType === "instagram" && (
              <span className="text-xs text-green-600">Copied!</span>
            )}
          </Button>

          <Button
            variant="outline"
            className="flex justify-between w-full text-sm"
            onClick={() => copyToClipboard(shareText.generic, "generic")}
          >
            <div className="flex items-center gap-2">
              <Copy className="h-4 w-4 text-gray-500" />
              Copy Generic Text
            </div>
            {copiedType === "generic" && (
              <span className="text-xs text-green-600">Copied!</span>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
