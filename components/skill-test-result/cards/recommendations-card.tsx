import Link from "next/link"
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { ChevronRight, Clock } from "lucide-react"
import { RecommendationStep } from "@/src/lib/repositories/test-result/types"

interface RecommendationsCardProps {
  recommendations: Array<RecommendationStep>
}

export default function RecommendationsCard({
  recommendations,
}: RecommendationsCardProps) {
  return (
    <Card className="md:col-span-2 overflow-hidden">
      <div className="bg-gradient-to-r from-yellow-200 via-yellow-100 to-yellow-50 text-yellow-900 text-sm font-medium px-6 py-3 rounded-md m-4 flex items-center gap-2 shadow-sm border border-yellow-200">
        <Clock className="h-4 w-4" />
        This feature is <span className="underline underline-offset-2">coming soon</span> — we're actively working on it!
      </div>

      <CardHeader>
        <CardTitle className="text-lg">Recommended Next Steps</CardTitle>
        <CardDescription>Based on your performance</CardDescription>
      </CardHeader>

      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {recommendations.map((recommendation, index) => (
            <div
              key={index}
              className="p-4 border rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors"
            >
              <div className="flex items-start gap-3">
                <div className="text-2xl">{recommendation.icon}</div>
                <div>
                  <h3 className="font-medium mb-1">{recommendation.title}</h3>
                  <p className="text-sm text-gray-600 mb-2">{recommendation.description}</p>
                  <Link
                    href={recommendation.link}
                    className="text-sm text-green-600 font-medium flex items-center gap-1 hover:underline"
                  >
                    Learn more <ChevronRight className="h-3 w-3" />
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
