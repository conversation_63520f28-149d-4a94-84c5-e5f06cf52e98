"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, Clock, XCircle } from "lucide-react";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, <PERSON> } from "recharts";

interface ScoreCardProps {
  score: number;
  correctAnswers: number;
  incorrectAnswers: number;
  timeTaken: string;
  badge: string;
  chartData: Array<{
    name: string;
    value: number;
    color: string;
  }>;
}

export default function ScoreCard({
  score,
  correctAnswers,
  incorrectAnswers,
  timeTaken,
  badge,
  chartData,
}: ScoreCardProps) {
  return (
    <Card className="md:col-span-1">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Your Score</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col items-center justify-center py-6">
        <div className="relative w-48 h-48 mb-4">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={80}
                paddingAngle={2}
                dataKey="value"
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              {/* <Legend verticalAlign="bottom" height={36} /> */}
            </PieChart>
          </ResponsiveContainer>
          <div className="absolute inset-0 flex items-center justify-center flex-col">
            <span className="text-4xl font-bold">{score}%</span>
            <span className="text-sm text-gray-500">Score</span>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 w-full">
          <div className="flex flex-col items-center p-3 bg-green-50 rounded-lg">
            <div className="flex items-center gap-1 text-green-600 mb-1">
              <CheckCircle className="h-4 w-4" />
              <span className="font-medium">Correct</span>
            </div>
            <span className="text-xl font-bold">{correctAnswers}</span>
          </div>
          <div className="flex flex-col items-center p-3 bg-red-50 rounded-lg">
            <div className="flex items-center gap-1 text-red-600 mb-1">
              <XCircle className="h-4 w-4" />
              <span className="font-medium">Incorrect</span>
            </div>
            <span className="text-xl font-bold">{incorrectAnswers}</span>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-4">
        <div className="flex items-center gap-2">
          <Clock className="h-4 w-4 text-gray-500" />
          <span className="text-sm text-gray-500">Time taken: {timeTaken}</span>
        </div>
        <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
          {badge}
        </Badge>
      </CardFooter>
    </Card>
  );
}
