import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { CategoryPerformance } from "@/src/lib/repositories/test-result/types";

interface CategoryPerformanceCardProps {
  categories: Array<CategoryPerformance>;
}

export default function CategoryPerformanceCard({ categories }: CategoryPerformanceCardProps) {
  return (
    <Card className="md:col-span-1">
      <CardHeader>
        <CardTitle className="text-lg">Performance by Category</CardTitle>
        <CardDescription>See how you performed in different areas</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {categories.map((category, index) => (
            <div key={index}>
              <div className="flex justify-between mb-1">
                <span className="text-sm font-medium">{category.category}</span>
                <span className="text-sm font-medium">{category.successRate}%</span>
              </div>
              <Progress value={category.successRate} className="h-2" />
              <div className="mt-1">
                <span className="text-xs text-gray-500">
                  {category.correct} of {category.total} questions correct
                </span>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}