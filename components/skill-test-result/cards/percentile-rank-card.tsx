/* eslint-disable @next/next/no-img-element */
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

interface TestTaker {
  id?: string
  name: string
  score: number
  avatarUrl?: string
}

interface PercentileRankCardProps {
  percentile: number
  otherTestTakers: TestTaker[]
  currentUserId?: string // optional, but preferred
  currentUserName?: string
}

export default function PercentileRankCard({
  percentile,
  otherTestTakers,
  currentUserId,
  currentUserName,
}: PercentileRankCardProps) {
  const sortedTakers = [...otherTestTakers].sort((a, b) => b.score - a.score)

  const isCurrentUser = (taker: TestTaker) => {
    return (
      (currentUserId && taker.id === currentUserId) ||
      (currentUserName && taker.name === currentUserName)
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Your Ranking</CardTitle>
        <CardDescription>How you compare to others</CardDescription>
      </CardHeader>

      <CardContent className="py-4">
        {/* Bar */}
        <div className="w-full h-6 bg-gray-200 rounded-full mb-6 relative">
          <div
            className="h-6 bg-green-500 rounded-full"
            style={{ width: `${percentile}%` }}
          />
          <div
            className="absolute top-0 w-4 h-6 bg-blue-600 rounded-full border-2 border-white"
            style={{
              left: `${percentile}%`,
              transform: "translateX(-50%)",
            }}
            title="You"
          />
        </div>

        <p className="text-center mb-6">
          You scored better than{" "}
          <span className="font-bold">{percentile}%</span> of test takers
        </p>

        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-2">Other Test Takers</h3>
          <ul className="space-y-2 max-h-56 overflow-y-auto">
            {sortedTakers.map((taker, index) => {
              const isYou = isCurrentUser(taker)
              return (
                <li
                  key={index}
                  className={`flex items-center gap-3 p-2 rounded-md ${
                    isYou ? "bg-blue-50 border border-blue-200" : ""
                  }`}
                >
                  {/* Avatar */}
                  {taker.avatarUrl ? (
                    <img
                      src={taker.avatarUrl}
                      alt={taker.name}
                      className="w-8 h-8 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center text-xs text-gray-600 font-medium">
                      {taker.name.charAt(0)}
                    </div>
                  )}

                  {/* Name and score */}
                  <div className="flex justify-between items-center w-full">
                    <span className="text-sm text-gray-800 font-medium">
                      {isYou ? "You" : taker.name}
                    </span>
                    <span className="text-sm font-semibold text-green-600">{taker.score}</span>
                  </div>
                </li>
              )
            })}
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
