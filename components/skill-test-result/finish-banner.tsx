import { Alert } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";

interface FinishBannerProps {
  title: string;
}

export function FinishBanner({ title }: FinishBannerProps) {
  return (
    <Alert
      variant="default"
      className="bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-lg mb-4 overflow-hidden"
    >
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 px-6 py-5">
        <div>
          <h3 className="text-xl font-bold mb-1">
            🎉 Congratulations on completing the test!
          </h3>
          <p className="text-white/90 max-w-2xl">
            You've successfully completed the {title} assessment. Your detailed
            results and performance breakdown are available in the tabs below.
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-3 mt-4 md:mt-0">
          <Button variant="default" className="bg-white text-emerald-600 hover:bg-white/90 h-9 px-4 py-2">
            Take Another Test
          </Button>
          <Button variant="default" className="bg-white text-emerald-600 hover:bg-white/90 h-9 px-4 py-2">
            Upgrade to Pro
          </Button>
        </div>
      </div>
    </Alert>
  );
}
