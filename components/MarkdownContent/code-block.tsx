"use client"

import type React from "react"
import { useState } from "react"
import { <PERSON>rism as Syntax<PERSON>ighlighter } from "react-syntax-highlighter"
import { atomDark } from "react-syntax-highlighter/dist/esm/styles/prism"
import { Check, Copy } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { twMerge } from "tailwind-merge"

interface CodeBlockProps {
  language: string
  code: string
  showLineNumbers?: boolean;
  className?: string;
}

const CodeBlock: React.FC<CodeBlockProps> = ({ language, code, showLineNumbers = true, className }) => {
  const [copied, setCopied] = useState(false)

  const handleCopy = async () => {
    await navigator.clipboard.writeText(code)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  return (
    <div className={twMerge("relative my-6 rounded-lg overflow-hidden",className)}>
      <div className="absolute right-2 top-2 z-10 flex gap-2">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="secondary"
                size="icon"
                className="h-8 w-8 bg-[var(--bg-custom-gray-800)]/70 hover:bg-[var(--bg-custom-gray-700)] text-white border-none"
                onClick={handleCopy}
              >
                {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>{copied ? "Copied!" : "Copy code"}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <div className="bg-[var(--bg-custom-gray-800)] text-[var(--text-custom-gray-200)] px-4 py-2 text-xs font-mono flex items-center">
        <span>{language}</span>
      </div>
      <SyntaxHighlighter
        language={language}
        style={atomDark}
        showLineNumbers={showLineNumbers}
        customStyle={{ margin: 0, borderRadius: 0 }}
      >
        {code}
      </SyntaxHighlighter>
    </div>
  )
}

export default CodeBlock

