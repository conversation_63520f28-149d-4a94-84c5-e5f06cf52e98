"use client"

import { useRef } from "react"

import type React from "react"
import { useState } from "react"
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  AreaChart,
  Area,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts"
import { Copy, Download, Bar<PERSON>hart2, LineChartIcon, PieChartIcon } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Tooltip as UITooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

// Define the chart data structure
interface ChartData {
  type: "line" | "bar" | "area" | "pie"
  data: any[]
  options?: {
    xKey?: string
    yKeys?: string[]
    colors?: string[]
    title?: string
    showGrid?: boolean
    showLegend?: boolean
    aspectRatio?: number
    stacked?: boolean
  }
}

interface ChartRendererProps {
  chartData: string
  className?: string
}

const defaultColors = ["#8884d8", "#82ca9d", "#ffc658", "#ff8042", "#0088fe", "#00c49f", "#ffbb28", "#ff8042"]

const ChartRenderer: React.FC<ChartRendererProps> = ({ chartData, className = "" }) => {
  let parsedData: ChartData
  const [currentType, setCurrentType] = useState<string | null>(null)
  const [copied, setCopied] = useState(false)
  const chartRef = useRef<HTMLDivElement>(null)

  try {
    parsedData = JSON.parse(chartData)
    // Set the initial chart type if not already set
    if (currentType === null) {
      setCurrentType(parsedData.type)
    }
  } catch (error) {
    return (
      <div className="p-4 border border-[var(--border-custom-red-300)] bg-red-50 dark:bg-[var(--bg-custom-red-900)]/20 dark:border-[var(--border-custom-red-800)] rounded-md text-[var(--text-custom-red-600)] dark:text-[var(--text-custom-red-400)]">
        <p className="font-medium">Chart Error</p>
        <p className="text-sm">Invalid chart data format. Please provide valid JSON.</p>
        <pre className="mt-2 p-2 bg-[var(--bg-custom-gray-100)] dark:bg-[var(--bg-custom-gray-800)] rounded text-xs overflow-x-auto">{chartData}</pre>
      </div>
    )
  }

  const { type, data, options = {} } = parsedData

  if (!data || !Array.isArray(data) || data.length === 0) {
    return (
      <div className="p-4 border border-[var(--border-custom-yellow-300)] bg-yellow-50 dark:bg-[var(--bg-custom-yellow-900)]/20 dark:border-[var(--border-custom-yellow-800)] rounded-md text-[var(--text-custom-yellow-600)] dark:text-[var(--text-custom-yellow-400)]">
        <p className="font-medium">Chart Warning</p>
        <p className="text-sm">No data provided for the chart.</p>
      </div>
    )
  }

  const {
    xKey = "name",
    yKeys = Object.keys(data[0]).filter((key) => key !== xKey && typeof data[0][key] === "number"),
    colors = defaultColors,
    showGrid = true,
    showLegend = true,
    aspectRatio = 2,
    stacked = false,
  } = options

  // Ensure we have colors for all data series
  const chartColors = yKeys.map((_, index) => colors[index % colors.length])

  // Title component if provided
  const ChartTitle = options.title ? (
    <h3 className="text-center font-medium mb-2 text-[var(--text-custom-gray-700)] dark:text-[var(--text-custom-gray-300)]">{options.title}</h3>
  ) : null

  const handleCopy = async () => {
    await navigator.clipboard.writeText(chartData)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  const handleExportImage = () => {
    if (!chartRef.current) return

    // Use html2canvas to capture the chart as an image
    import("html2canvas").then((html2canvas) => {
      html2canvas.default(chartRef.current!).then((canvas) => {
        // Convert to PNG and download
        const image = canvas.toDataURL("image/png")
        const a = document.createElement("a")
        a.href = image
        a.download = `${options.title || "chart"}.png`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
      })
    })
  }

  const handleDownloadData = () => {
    // Convert data to CSV
    const headers = [xKey, ...yKeys]
    const csvContent = [headers.join(","), ...data.map((item) => headers.map((header) => item[header]).join(","))].join(
      "\n",
    )

    // Create a blob and download
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `${options.title || "chart-data"}.csv`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const changeChartType = (newType: string) => {
    setCurrentType(newType)
  }

  // Check if the data structure supports multiple chart types
  const supportsMultipleTypes = yKeys.length > 0 && data.length > 0

  // Render the appropriate chart based on type
  const renderChart = () => {
    const activeType = currentType || type

    switch (activeType) {
      case "line":
        return (
          <ResponsiveContainer width="100%" aspect={aspectRatio}>
            <LineChart data={data}>
              {showGrid && <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />}
              <XAxis dataKey={xKey} tick={{ fill: "var(--foreground)" }} axisLine={{ stroke: "#e0e0e0" }} />
              <YAxis tick={{ fill: "var(--foreground)" }} axisLine={{ stroke: "#e0e0e0" }} />
              <Tooltip
                contentStyle={{
                  backgroundColor: "white",
                  borderColor: "var(--border)",
                  color: "var(--foreground)",
                }}
              />
              {showLegend && <Legend />}
              {yKeys.map((key, index) => (
                <Line
                  key={key}
                  type="monotone"
                  dataKey={key}
                  stroke={chartColors[index]}
                  activeDot={{ r: 8 }}
                  strokeWidth={2}
                />
              ))}
            </LineChart>
          </ResponsiveContainer>
        )

      case "bar":
        return (
          <ResponsiveContainer width="100%" aspect={aspectRatio}>
            <BarChart data={data}>
              {showGrid && <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />}
              <XAxis dataKey={xKey} tick={{ fill: "var(--foreground)" }} axisLine={{ stroke: "#e0e0e0" }} />
              <YAxis tick={{ fill: "var(--foreground)" }} axisLine={{ stroke: "#e0e0e0" }} />
              <Tooltip
                contentStyle={{
                  backgroundColor: "white",
                  borderColor: "var(--border)",
                  color: "var(--foreground)",
                }}
              />
              {showLegend && <Legend />}
              {yKeys.map((key, index) => (
                <Bar key={key} dataKey={key} fill={chartColors[index]} stackId={stacked ? "stack" : undefined} />
              ))}
            </BarChart>
          </ResponsiveContainer>
        )

      case "area":
        return (
          <ResponsiveContainer width="100%" aspect={aspectRatio}>
            <AreaChart data={data}>
              {showGrid && <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />}
              <XAxis dataKey={xKey} tick={{ fill: "var(--foreground)" }} axisLine={{ stroke: "#e0e0e0" }} />
              <YAxis tick={{ fill: "var(--foreground)" }} axisLine={{ stroke: "#e0e0e0" }} />
              <Tooltip
                contentStyle={{
                  backgroundColor: "white",
                  borderColor: "var(--border)",
                  color: "var(--foreground)",
                }}
              />
              {showLegend && <Legend />}
              {yKeys.map((key, index) => (
                <Area
                  key={key}
                  type="monotone"
                  dataKey={key}
                  fill={chartColors[index]}
                  stroke={chartColors[index]}
                  fillOpacity={0.6}
                  stackId={stacked ? "stack" : undefined}
                />
              ))}
            </AreaChart>
          </ResponsiveContainer>
        )

      case "pie":
        // For pie charts, we need to transform the data
        const pieData =
          yKeys.length === 1
            ? data.map((item) => ({ name: item[xKey], value: item[yKeys[0]] }))
            : yKeys.map((key) => ({
                name: key,
                value: data.reduce((sum, item) => sum + (item[key] || 0), 0),
              }))

        return (
          <ResponsiveContainer width="100%" aspect={aspectRatio}>
            <PieChart>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                labelLine={true}
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {pieData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={chartColors[index % chartColors.length]} />
                ))}
              </Pie>
              <Tooltip
                contentStyle={{
                  backgroundColor: "white",
                  borderColor: "var(--border)",
                  color: "var(--foreground)",
                }}
              />
              {showLegend && <Legend />}
            </PieChart>
          </ResponsiveContainer>
        )

      default:
        return (
          <div className="p-4 border border-[var(--border-custom-red-300)] bg-red-50 dark:bg-[var(--bg-custom-red-900)]/20 dark:border-[var(--border-custom-red-800)] rounded-md text-[var(--text-custom-red-600)] dark:text-[var(--text-custom-red-400)]">
            <p className="font-medium">Chart Error</p>
            <p className="text-sm">Unsupported chart type: {activeType}</p>
            <p className="text-sm">Supported types: line, bar, area, pie</p>
          </div>
        )
    }
  }

  return (
    <div
      className={`chart-container relative p-4 bg-white dark:bg-[var(--bg-custom-gray-800)] rounded-lg border border-[var(--border-custom-gray-200)] dark:border-[var(--border-custom-gray-700)] ${className}`}
    >
      <div className="hidden absolute right-2 top-2 z-10 gap-2">
        {supportsMultipleTypes && (
          <>
            <TooltipProvider>
              <UITooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="default"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => changeChartType("line")}
                  >
                    <LineChartIcon className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Line Chart</p>
                </TooltipContent>
              </UITooltip>
            </TooltipProvider>

            <TooltipProvider>
              <UITooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="default"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => changeChartType("bar")}
                  >
                    <BarChart2 className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Bar Chart</p>
                </TooltipContent>
              </UITooltip>
            </TooltipProvider>

            <TooltipProvider>
              <UITooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="default"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => changeChartType("pie")}
                  >
                    <PieChartIcon className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Pie Chart</p>
                </TooltipContent>
              </UITooltip>
            </TooltipProvider>
          </>
        )}

        <TooltipProvider>
          <UITooltip>
            <TooltipTrigger asChild>
              <Button variant="secondary" size="icon" className="h-8 w-8" onClick={handleCopy}>
                <Copy className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Copy chart data</p>
            </TooltipContent>
          </UITooltip>
        </TooltipProvider>

        <TooltipProvider>
          <UITooltip>
            <TooltipTrigger asChild>
              <Button variant="secondary" size="icon" className="h-8 w-8" onClick={handleExportImage}>
                <Download className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Export as PNG</p>
            </TooltipContent>
          </UITooltip>
        </TooltipProvider>

        <TooltipProvider>
          <UITooltip>
            <TooltipTrigger asChild>
              <Button variant="secondary" size="icon" className="h-8 w-8" onClick={handleDownloadData}>
                <Download className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Download CSV data</p>
            </TooltipContent>
          </UITooltip>
        </TooltipProvider>
      </div>

      {ChartTitle}
      <div ref={chartRef}>{renderChart()}</div>
    </div>
  )
}

export default ChartRenderer

