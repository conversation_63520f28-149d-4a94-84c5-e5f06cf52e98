"use client"

import type React from "react"
import { useState } from "react"
import "katex/dist/katex.min.css"
import { Copy, Download } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>lt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface MathBlockProps {
  formula: string
  isInline?: boolean
}

const MathBlock: React.FC<MathBlockProps> = ({ formula, isInline = false }) => {
  const [copied, setCopied] = useState(false)

  const handleCopy = async () => {
    await navigator.clipboard.writeText(formula)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  const handleDownload = () => {
    // Create a blob with the LaTeX formula
    const blob = new Blob([formula], { type: "text/plain" })
    const url = URL.createObjectURL(blob)

    // Create a temporary link and trigger download
    const a = document.createElement("a")
    a.href = url
    a.download = "formula.tex"
    document.body.appendChild(a)
    a.click()

    // Clean up
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // For inline math, we don't show buttons
  if (isInline) {
    return null
  }

  return (
    <div className="absolute right-2 top-2 z-10 flex gap-2">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="secondary"
              size="icon"
              className="h-8 w-8 bg-white/90 dark:bg-[var(--bg-custom-gray-800)]/90 hover:bg-[var(--bg-custom-gray-100)] dark:hover:bg-[var(--bg-custom-gray-700)] border-none"
              onClick={handleCopy}
            >
              <Copy className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Copy LaTeX</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="secondary"
              size="icon"
              className="h-8 w-8 bg-white/90 dark:bg-[var(--bg-custom-gray-800)]/90 hover:bg-[var(--bg-custom-gray-100)] dark:hover:bg-[var(--bg-custom-gray-700)] border-none"
              onClick={handleDownload}
            >
              <Download className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Download LaTeX</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  )
}

export default MathBlock

