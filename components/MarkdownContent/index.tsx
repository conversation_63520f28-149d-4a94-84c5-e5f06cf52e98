"use client";

import React from "react";
import MarkdownRenderer from "./markdown-renderer";

interface MarkdownContentProps {
  children: string;
}

const MarkdownContent: React.FC<MarkdownContentProps> = ({ children }) => {
  return (
    <div className="prose max-w-none dark:prose-invert prose-sm">
      <MarkdownRenderer content={children} isChatMessage />
    </div>
  );
};

export default React.memo(MarkdownContent)
