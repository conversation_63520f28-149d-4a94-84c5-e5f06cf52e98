/* eslint-disable @next/next/no-img-element */
"use client";

import type React from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import remarkMath from "remark-math";
import remarkBreaks from "remark-breaks";
import rehypeKatex from "rehype-katex";
import "katex/dist/katex.min.css";
import Link from "next/link";
import MermaidDiagram from "./mermaid-diagram";
import ChartRenderer from "./chart-renderer";
import CodeBlock from "./code-block";
import MathBlock from "./math-block";
import { convertLatexDelimiters } from "@/src/utils/math-utils";

interface MarkdownRendererProps {
  content: string;
  isChatMessage?: boolean;
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  isChatMessage = false,
}) => {
  const processedContent = convertLatexDelimiters(content);
  return (
    <ReactMarkdown
      remarkPlugins={[remarkGfm, remarkMath, remarkBreaks]}
      rehypePlugins={[rehypeKatex]}
      components={{
        h1: ({ ...props }) => (
          <h1 className="text-3xl font-bold mt-8 mb-4 pb-2 border-b border-gray-200 dark:border-gray-700" {...props} />
        ),
        h2: ({ ...props }) => (
          <h2 className="text-2xl font-bold mt-6 mb-3" {...props} />
        ),
        h3: ({ ...props }) => (
          <h3 className="text-xl font-bold mt-5 mb-2" {...props} />
        ),
        h4: ({ ...props }) => (
          <h4 className="text-lg font-bold mt-4 mb-2" {...props} />
        ),
        p: ({ ...props }) => (
          <p className="my-2 leading-relaxed" {...props} />
        ),
        a: ({ href, ...props }) => (
          <Link
            href={href || "#"}
            className="text-blue-600 hover:text-blue-800 hover:underline transition-colors"
            {...props}
          />
        ),
        ul: ({ ...props }) => (
          <ul className="list-disc pl-6 my-4 space-y-2" {...props} />
        ),
        ol: ({ ...props }) => (
          <ol className="list-decimal pl-6 my-4 space-y-2" {...props} />
        ),
        li: ({ ...props }) => <li className="mb-1" {...props} />,
        blockquote: ({ ...props }) => (
          <blockquote className="border-l-4 border-gray-300 pl-4 italic bg-gray-50 dark:bg-gray-800 rounded" {...props} />
        ),
        table: ({ ...props }) => (
          <div className="overflow-x-auto my-6">
            <table className="min-w-full divide-y divide-gray-200 border border-gray-200 rounded" {...props} />
          </div>
        ),
        thead: ({ ...props }) => (
          <thead className="bg-gray-50 dark:bg-gray-700" {...props} />
        ),
        th: ({ ...props }) => (
          <th className="px-4 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-b" {...props} />
        ),
        tr: ({ ...props }) => (
          <tr className="hover:bg-gray-50 dark:hover:bg-gray-800" {...props} />
        ),
        td: ({ ...props }) => (
          <td className="px-4 py-3 text-sm border-b border-gray-200 dark:border-gray-700" {...props} />
        ),
        img: ({ src, alt, ...props }) => {
          if (!src) return null;
          const lowerSrc = src.toLowerCase();

          if (/\.(mp3|wav|ogg)$/.test(lowerSrc)) {
            return (
              <div className="my-4">
                <audio controls className="mx-auto rounded shadow-md w-full">
                  <source src={src} />
                  Your browser does not support the audio element.
                </audio>
              </div>
            );
          }

          if (/\.(mp4|webm|ogg)$/.test(lowerSrc)) {
            return (
              <div className="my-4">
                <video controls className="mx-auto rounded shadow-md w-full h-auto">
                  <source src={src} />
                  Your browser does not support the video element.
                </video>
              </div>
            );
          }

          return (
            <div className="my-4">
              <img
                src={src}
                alt={alt || "Image"}
                width={800}
                height={400}
                className="rounded-lg mx-auto w-full h-auto"
                {...props}
              />
            </div>
          );
        },
        // pre: ({ ...props }) => <div {...props} />,
        // @ts-ignore: inline does not exist
        code: ({ inline, className, children, ...props }) => {
          const match = /language-(\w+)/.exec(className || "");
          const content = String(children).replace(/\n$/, "");

          if (!inline && match) {
            const lang = match[1];
            if (lang === "mermaid") return <MermaidDiagram chart={content} />;
            if (lang === "chart") return <ChartRenderer chartData={content} />;
            if (lang === "math") {
              return (
                <div className="relative my-6">
                  <MathBlock formula={content} />
                  <div className="katex-display">
                    <span className="katex-display-math">{content}</span>
                  </div>
                </div>
              );
            }
            return <CodeBlock language={lang} code={content} />;
          }

          if (inline) {
            return (
              <code className="px-1.5 py-0.5 rounded bg-gray-100 dark:bg-gray-800 font-mono text-sm" {...props}>
                {children}
              </code>
            );
          }

          return <CodeBlock language="text" code={content} />;
        },
        hr: ({ ...props }) => (
          <hr className="my-6 border-t border-gray-200 dark:border-gray-700" {...props} />
        ),
        strong: ({ ...props }) => (
          <strong className="font-bold" {...props} />
        ),
        em: ({ ...props }) => <em className="italic" {...props} />,
        // @ts-ignore: math does not exist
        math: ({ value }) => (
          <span className="inline-math">
            <span className="katex-inline">{value}</span>
          </span>
        ),
        inlineMath: ({ value }) => (
          <span className="inline-math">
            <span className="katex-inline">{value}</span>
            <MathBlock formula={value} isInline={true} />
          </span>
        ),
      }}
    >
      {processedContent}
    </ReactMarkdown>
  );
};

export default MarkdownRenderer;
