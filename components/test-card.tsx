import Image from "next/image"
import Link from "next/link"
import { Clock, Users } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

export interface TestCardProps {
  id: string
  title: string
  description: string
  image: string
  difficulty: "EASY" | "MEDIUM" | "HARD"
  benefits: string[]
  userCount: string
  successRate: string
  duration: string
  instructorName: string
  instructorImage?: string
  ctaText: string
  ctaLink: string
}

export default function TestCard({
  id,
  title,
  description,
  image,
  difficulty,
  benefits,
  userCount,
  successRate,
  duration,
  instructorName,
  instructorImage,
  ctaText,
  ctaLink,
}: TestCardProps) {
  // Map difficulty to color
  const difficultyColor = {
    EASY: "bg-green-100 text-green-800",
    MEDIUM: "bg-yellow-100 text-yellow-800",
    HARD: "bg-red-100 text-red-800",
  }[difficulty]

  return (
    <div className="bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100 hover:shadow-md transition-shadow flex flex-col w-full">
      <div className="relative h-48">
        <Image
          src={image || "/placeholder.svg?height=192&width=384&query=course"}
          alt={title}
          fill
          className="object-cover"
        />
        <div className="absolute top-4 right-4 flex flex-wrap gap-1">
          <div
            className={cn(
              "inline-flex items-center border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent shadow hover:bg-primary/80 py-1 px-2 rounded-full text-xs",
              difficultyColor,
            )}
          >
            {difficulty}
          </div>
        </div>
      </div>
      <div className="p-6 flex flex-col flex-1">
        <h3 className="text-xl font-bold mb-2">{title}</h3>
        <p className="text-gray-600 mb-4">{description}</p>

        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Benefits:</h4>
          <div className="flex flex-wrap gap-2">
            {benefits.map((benefit, index) => (
              <div
                key={index}
                className="inline-flex items-center border text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-green-50 text-green-700 border-green-200 py-1 px-2 rounded-full"
              >
                {benefit}
              </div>
            ))}
          </div>
        </div>

        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-1 text-gray-600">
            <Users className="h-4 w-4" />
            <span className="text-sm">{userCount} users</span>
          </div>
          <div className="flex items-center gap-1">
            <span className="text-sm font-medium">Success rate:</span>
            <span className="text-sm font-bold text-blue-600">{successRate}</span>
          </div>
        </div>

        <div className="flex items-center gap-2 mb-4 text-gray-600">
          <Clock className="h-4 w-4" />
          <span className="text-sm">{duration}</span>
        </div>

        <div className="flex justify-between items-center mt-auto">
          <div className="flex items-center gap-2">
            <span className="relative flex shrink-0 overflow-hidden rounded-full h-8 w-8">
              {instructorImage ? (
                <Image src={instructorImage || "/placeholder.svg"} alt={instructorName} fill className="object-cover" />
              ) : (
                <span className="flex h-full w-full items-center justify-center rounded-full bg-muted">
                  {instructorName.charAt(0)}
                </span>
              )}
            </span>
            <span className="text-sm">{instructorName}</span>
          </div>
          <Link href={ctaLink}>
            <Button className="bg-green-600 hover:bg-green-700 text-white">{ctaText}</Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
