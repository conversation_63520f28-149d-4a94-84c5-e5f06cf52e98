"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON>, ThumbsUp, Thum<PERSON>Down, Clock, MoreHorizontal, Flag, Trash2 } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

interface CourseCommentsProps {
  courseId: string
}

export function CourseComments({ courseId }: CourseCommentsProps) {
  const [activeTab, setActiveTab] = useState("all")

  // Mock data for comments
  const comments = [
    {
      id: "comment-1",
      user: {
        name: "<PERSON>",
        avatar: "/abstract-letter-aj.png",
      },
      content: "The section on useReducer was incredibly helpful. I finally understand how to use it properly!",
      timestamp: "2 days ago",
      videoTitle: "useReducer Deep Dive",
      videoTimestamp: "12:45",
      likes: 24,
      dislikes: 2,
      isLiked: true,
      isDisliked: false,
    },
    {
      id: "comment-2",
      user: {
        name: "Sarah Miller",
        avatar: "/stylized-sm-logo.png",
      },
      content:
        "Could you explain the difference between useMemo and useCallback in more detail? I'm still confused about when to use each one.",
      timestamp: "1 day ago",
      videoTitle: "React.memo and useMemo",
      videoTimestamp: "08:20",
      likes: 8,
      dislikes: 0,
      isLiked: false,
      isDisliked: false,
    },
    {
      id: "comment-3",
      user: {
        name: "David Chen",
        avatar: "/dc-skyline-night.png",
      },
      content: "The code splitting strategies you shared have made my app so much faster. Thanks for the great tips!",
      timestamp: "5 hours ago",
      videoTitle: "Code Splitting Strategies",
      videoTimestamp: "15:30",
      likes: 16,
      dislikes: 1,
      isLiked: false,
      isDisliked: false,
    },
    {
      id: "comment-4",
      user: {
        name: "Emily Rodriguez",
        avatar: "/emergency-room-scene.png",
      },
      content:
        "I found a small error in the custom hooks example. The dependency array is missing the userId variable.",
      timestamp: "3 hours ago",
      videoTitle: "Custom Hooks Patterns",
      videoTimestamp: "19:15",
      likes: 12,
      dislikes: 0,
      isLiked: true,
      isDisliked: false,
    },
    {
      id: "comment-5",
      user: {
        name: "Michael Wong",
        avatar: "/intertwined-letters.png",
      },
      content:
        "This course is exactly what I needed to level up my React skills. The explanations are clear and the examples are practical.",
      timestamp: "1 day ago",
      videoTitle: "Course Overview",
      videoTimestamp: "02:10",
      likes: 32,
      dislikes: 0,
      isLiked: false,
      isDisliked: false,
    },
  ]

  const [commentsList, setCommentsList] = useState(comments)

  const toggleLike = (commentId: string) => {
    setCommentsList(
      commentsList.map((comment) => {
        if (comment.id === commentId) {
          if (comment.isLiked) {
            return { ...comment, isLiked: false, likes: comment.likes - 1 }
          } else {
            // If it was disliked, remove the dislike
            const newDislikes = comment.isDisliked ? comment.dislikes - 1 : comment.dislikes
            return {
              ...comment,
              isLiked: true,
              isDisliked: false,
              likes: comment.likes + 1,
              dislikes: newDislikes,
            }
          }
        }
        return comment
      }),
    )
  }

  const toggleDislike = (commentId: string) => {
    setCommentsList(
      commentsList.map((comment) => {
        if (comment.id === commentId) {
          if (comment.isDisliked) {
            return { ...comment, isDisliked: false, dislikes: comment.dislikes - 1 }
          } else {
            // If it was liked, remove the like
            const newLikes = comment.isLiked ? comment.likes - 1 : comment.likes
            return {
              ...comment,
              isDisliked: true,
              isLiked: false,
              dislikes: comment.dislikes + 1,
              likes: newLikes,
            }
          }
        }
        return comment
      }),
    )
  }

  const deleteComment = (commentId: string) => {
    setCommentsList(commentsList.filter((comment) => comment.id !== commentId))
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <MessageSquare className="mr-2 h-5 w-5" />
          Course Comments and Feedback
        </CardTitle>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList>
            <TabsTrigger value="all">All Comments</TabsTrigger>
            <TabsTrigger value="unread">Unread</TabsTrigger>
            <TabsTrigger value="flagged">Flagged</TabsTrigger>
          </TabsList>
        </Tabs>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {commentsList.length === 0 ? (
            <div className="text-center py-12 text-muted-foreground">
              <p>No comments yet.</p>
            </div>
          ) : (
            commentsList.map((comment) => (
              <div key={comment.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex justify-between items-start">
                  <div className="flex items-start gap-3">
                    <Avatar>
                      <AvatarImage src={comment.user.avatar || "/placeholder.svg"} alt={comment.user.name} />
                      <AvatarFallback>
                        {comment.user.name
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">{comment.user.name}</div>
                      <div className="text-sm text-muted-foreground flex items-center gap-2">
                        <span>{comment.timestamp}</span>
                        <span>•</span>
                        <Badge variant="outline" className="text-xs font-normal">
                          {comment.videoTitle}
                        </Badge>
                        <span className="flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          {comment.videoTimestamp}
                        </span>
                      </div>
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Flag className="h-4 w-4 mr-2" />
                        Flag comment
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => deleteComment(comment.id)}>
                        <Trash2 className="h-4 w-4 mr-2 text-destructive" />
                        Delete comment
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <p className="text-sm">{comment.content}</p>
                <div className="flex items-center gap-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`flex items-center gap-1 ${comment.isLiked ? "text-primary" : ""}`}
                    onClick={() => toggleLike(comment.id)}
                  >
                    <ThumbsUp className="h-4 w-4" />
                    <span>{comment.likes}</span>
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`flex items-center gap-1 ${comment.isDisliked ? "text-destructive" : ""}`}
                    onClick={() => toggleDislike(comment.id)}
                  >
                    <ThumbsDown className="h-4 w-4" />
                    <span>{comment.dislikes}</span>
                  </Button>
                  <Button variant="ghost" size="sm" className="ml-auto">
                    Reply
                  </Button>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  )
}
