import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"

import { questionSchema } from "./questionSchema"
import { Question } from "@/src/lib/repositories/questions/interface"
import { ZodError } from "zod"
import { useLocalization } from "@/src/localization/functions/client"
import en from "@/src/app/dashboard/question-editor/locales/en.json"

export function JsonEditor({
  question,
  onChange,
}: {
  question: Question
  onChange: (updatedQuestion: Partial<Question>) => void
}) {
  const { t } = useLocalization("json-editor", { en })


  // Omit 'id' from question when initializing text
  const [text, setText] = useState(() =>
    JSON.stringify(
      // Omit 'id' field
      (({ id, ...rest }) => rest)(question),
      null,
      2
    )
  )
  const [error, setError] = useState<string | null>(null)
  const [detailedErrors, setDetailedErrors] = useState<string[] | null>(null)

  // If question changes externally, update editor text (also omitting id)
  useEffect(() => {
    setText(
      JSON.stringify(
        (({ id, ...rest }) => rest)(question),
        null,
        2
      )
    )
  }, [question])

  const handleJsonChange = (newText: string) => {
    setError(null)
    setDetailedErrors(null)
    setText(newText)

    try {
      const parsed = JSON.parse(newText)

      // Merge back original id before validating & calling onChange
      const merged = { ...parsed, id: question.id }

      const validation = questionSchema.safeParse(merged)

      if (validation.success) {
        setError(null)
        setDetailedErrors(null)
        onChange(validation.data as Partial<Question>)
      } else {
        const formattedErrors = formatZodErrors(validation.error)
        setError("JSON does not match required format.")
        setDetailedErrors(formattedErrors)
        console.error(validation.error.format())
      }
    } catch (e) {
      setError(`Invalid JSON syntax. ${(e as Error).message}`)
      setDetailedErrors(null)
    }
  }

  // Helper to convert ZodError to a list of strings
  function formatZodErrors(error: ZodError): string[] {
    return error.errors.map((err) => {
      const path = err.path.length ? err.path.join(".") : "root"
      return `${path}: ${err.message}`
    })
  }

  return (
    <div className="border rounded-md">
      <div className="bg-gray-50 px-4 py-2 border-b flex justify-between items-center">
        <span className="text-sm font-medium">{t("questionManagement.jsonEditor")}</span>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              try {
                navigator.clipboard.writeText(text)
              } catch (error) {
                console.error("Failed to copy JSON:", error)
              }
            }}
          >
            {t("questionManagement.copyJSON")}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              try {
                navigator.clipboard.readText().then((clipboardText) => {
                  handleJsonChange(clipboardText)
                })
              } catch (error) {
                console.error("Failed to paste JSON:", error)
              }
            }}
          >
            {t("questionManagement.pasteJSON")}
          </Button>
        </div>
      </div>
      <Textarea
        className="min-h-[300px] font-mono text-sm p-4 border-0 rounded-none focus-visible:ring-0"
        value={text}
        onChange={(e) => handleJsonChange(e.target.value)}
      />
      {error && (
        <div className="text-red-600 text-sm mt-2 px-4 space-y-1">
          <p>{error}</p>
          {detailedErrors && (
            <ul className="list-disc list-inside">
              {detailedErrors.map((errMsg, i) => (
                <li key={i}>{errMsg}</li>
              ))}
            </ul>
          )}
        </div>
      )}
    </div>
  )
}
