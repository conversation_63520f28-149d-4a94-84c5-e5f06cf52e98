"use client"

import React from "react"
import { Question } from "@/src/lib/repositories/questions/types";
import { Textarea } from "@/components/ui/textarea"
import { OptionsEditor } from "../OptionsComponent";

type Props = {
  question: Question
  onChange: (updated: Partial<Question>) => void
}

/**
 * Dynamically renders specific UI components depending on question type.
 */
export const DynamicAnswerEditor: React.FC<Props> = ({ question, onChange }) => {
  const handleOptionsChange = (newOptions: any[]) => {
    onChange({
      options: newOptions,
    })
  }

  const handleReviewGuideChange = (value: string) => {
    onChange({
      reviewGuide: value,
    })
  }

  switch (question.type) {
    case "multipleChoice":
    case "singleChoice":
    case "imageBased":
    case "audioBased":
      return (
        <div className="space-y-4">
          <OptionsEditor
            options={question.options}
            onChange={handleOptionsChange}
            multipleCorrect={true}
          />
        </div>
      )

    case "audioAnswer":
    case "voiceInput":
    case "fileUpload":
    case "textInput":
    case "codeInput":
      return (
        <div className="space-y-4">
          <label className="text-sm font-medium block">Review Guide</label>
          <Textarea
            value={
              typeof question.reviewGuide === "string"
                ? question.reviewGuide
                : ""
            }
            onChange={(e) => handleReviewGuideChange(e.target.value)}
            placeholder="Enter review instructions..."
          />
        </div>
      )

    default:
      return <p className="text-gray-500">Unsupported question type.</p>
  }
}
