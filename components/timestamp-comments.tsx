"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON>, ThumbsUp, ThumbsDown, Clock, Send, MoreHorizontal, Flag, Trash2 } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

interface TimestampCommentsProps {
  videoId: string
}

export function TimestampComments({ videoId }: TimestampCommentsProps) {
  const [activeTab, setActiveTab] = useState("all")
  const [newComment, setNewComment] = useState("")

  // Mock data for comments
  const comments = [
    {
      id: "comment-1",
      user: {
        name: "<PERSON>",
        avatar: "/abstract-letter-aj.png",
      },
      content: "The explanation of useReducer here is incredibly helpful. I finally understand how to use it properly!",
      timestamp: "2 days ago",
      videoTimestamp: "12:45",
      likes: 24,
      dislikes: 2,
      isLiked: true,
      isDisliked: false,
    },
    {
      id: "comment-2",
      user: {
        name: "Sarah Miller",
        avatar: "/stylized-sm-logo.png",
      },
      content:
        "Could you explain the difference between useMemo and useCallback in more detail? I'm still confused about when to use each one.",
      timestamp: "1 day ago",
      videoTimestamp: "08:20",
      likes: 8,
      dislikes: 0,
      isLiked: false,
      isDisliked: false,
    },
    {
      id: "comment-3",
      user: {
        name: "David Chen",
        avatar: "/dc-skyline-night.png",
      },
      content: "The code splitting strategies you shared have made my app so much faster. Thanks for the great tips!",
      timestamp: "5 hours ago",
      videoTimestamp: "15:30",
      likes: 16,
      dislikes: 1,
      isLiked: false,
      isDisliked: false,
    },
    {
      id: "comment-4",
      user: {
        name: "Emily Rodriguez",
        avatar: "/emergency-room-scene.png",
      },
      content:
        "I found a small error in the custom hooks example. The dependency array is missing the userId variable.",
      timestamp: "3 hours ago",
      videoTimestamp: "19:15",
      likes: 12,
      dislikes: 0,
      isLiked: true,
      isDisliked: false,
    },
  ]

  const [commentsList, setCommentsList] = useState(comments)

  const toggleLike = (commentId: string) => {
    setCommentsList(
      commentsList.map((comment) => {
        if (comment.id === commentId) {
          if (comment.isLiked) {
            return { ...comment, isLiked: false, likes: comment.likes - 1 }
          } else {
            // If it was disliked, remove the dislike
            const newDislikes = comment.isDisliked ? comment.dislikes - 1 : comment.dislikes
            return {
              ...comment,
              isLiked: true,
              isDisliked: false,
              likes: comment.likes + 1,
              dislikes: newDislikes,
            }
          }
        }
        return comment
      }),
    )
  }

  const toggleDislike = (commentId: string) => {
    setCommentsList(
      commentsList.map((comment) => {
        if (comment.id === commentId) {
          if (comment.isDisliked) {
            return { ...comment, isDisliked: false, dislikes: comment.dislikes - 1 }
          } else {
            // If it was liked, remove the like
            const newLikes = comment.isLiked ? comment.likes - 1 : comment.likes
            return {
              ...comment,
              isDisliked: true,
              isLiked: false,
              dislikes: comment.dislikes + 1,
              likes: newLikes,
            }
          }
        }
        return comment
      }),
    )
  }

  const deleteComment = (commentId: string) => {
    setCommentsList(commentsList.filter((comment) => comment.id !== commentId))
  }

  const addComment = () => {
    if (!newComment.trim()) return

    const newCommentObj = {
      id: `comment-${Date.now()}`,
      user: {
        name: "You",
        avatar: "/placeholder.svg?height=40&width=40&query=user+avatar",
      },
      content: newComment,
      timestamp: "Just now",
      videoTimestamp: "10:30", // This would be the current video timestamp
      likes: 0,
      dislikes: 0,
      isLiked: false,
      isDisliked: false,
    }

    setCommentsList([newCommentObj, ...commentsList])
    setNewComment("")
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <MessageSquare className="mr-2 h-5 w-5" />
          Video Comments
        </CardTitle>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList>
            <TabsTrigger value="all">All Comments</TabsTrigger>
            <TabsTrigger value="my">My Comments</TabsTrigger>
            <TabsTrigger value="timestamps">By Timestamp</TabsTrigger>
          </TabsList>
        </Tabs>
      </CardHeader>
      <CardContent>
        <div className="mb-4 flex items-center gap-2">
          <Avatar>
            <AvatarImage src="/placeholder.svg?height=40&width=40&query=user+avatar" alt="Your Avatar" />
            <AvatarFallback>YA</AvatarFallback>
          </Avatar>
          <div className="relative flex-1">
            <Input
              placeholder="Add a comment at the current timestamp..."
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              className="pr-20"
            />
            <Button
              size="sm"
              className="absolute right-1 top-1/2 -translate-y-1/2"
              onClick={addComment}
              disabled={!newComment.trim()}
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="space-y-4">
          {commentsList.length === 0 ? (
            <div className="text-center py-12 text-muted-foreground">
              <p>No comments yet. Be the first to comment!</p>
            </div>
          ) : (
            commentsList.map((comment) => (
              <div key={comment.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex justify-between items-start">
                  <div className="flex items-start gap-3">
                    <Avatar>
                      <AvatarImage src={comment.user.avatar || "/placeholder.svg"} alt={comment.user.name} />
                      <AvatarFallback>
                        {comment.user.name
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">{comment.user.name}</div>
                      <div className="text-sm text-muted-foreground flex items-center gap-2">
                        <span>{comment.timestamp}</span>
                        <span>•</span>
                        <span className="flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          {comment.videoTimestamp}
                        </span>
                      </div>
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => {}}>
                        <Clock className="h-4 w-4 mr-2" />
                        Jump to timestamp
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Flag className="h-4 w-4 mr-2" />
                        Report comment
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => deleteComment(comment.id)}>
                        <Trash2 className="h-4 w-4 mr-2 text-destructive" />
                        Delete comment
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <p className="text-sm">{comment.content}</p>
                <div className="flex items-center gap-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`flex items-center gap-1 ${comment.isLiked ? "text-primary" : ""}`}
                    onClick={() => toggleLike(comment.id)}
                  >
                    <ThumbsUp className="h-4 w-4" />
                    <span>{comment.likes}</span>
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`flex items-center gap-1 ${comment.isDisliked ? "text-destructive" : ""}`}
                    onClick={() => toggleDislike(comment.id)}
                  >
                    <ThumbsDown className="h-4 w-4" />
                    <span>{comment.dislikes}</span>
                  </Button>
                  <Button variant="ghost" size="sm" className="ml-auto">
                    Reply
                  </Button>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  )
}
