"use client"

import * as React from "react"
import { Plus, X } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"

export interface Option {
  label: string
  value: string
}

interface SearchableDropdownProps {
  initialData: Option[]
  placeholder?: string
  onSearch: (query: string) => void
  onSelect: (selected: Option | Option[]) => void
  onCreateNew?: (text: string) => void
  multiple?: boolean
  className?: string
}

export const SearchableDropdown: React.FC<SearchableDropdownProps> = ({
  initialData,
  onSearch,
  onSelect,
  onCreateNew,
  multiple = false,
  placeholder = "Search...",
  className,
}) => {
  const [open, setOpen] = React.useState(false)
  const [searchValue, setSearchValue] = React.useState("")
  // selected is Option or Option[] depending on multiple
  const [selected, setSelected] = React.useState<Option | Option[] | null>(
    multiple ? [] : null
  )

  const containerRef = React.useRef<HTMLDivElement>(null)

  React.useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setOpen(false)
      }
    }
    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  const handleSelect = (item: Option) => {
    if (multiple) {
      // If already selected, remove it, else add it
      const selectedArray = selected as Option[]
      const exists = selectedArray.find((opt) => opt.value === item.value)
      let newSelected: Option[]
      if (exists) {
        newSelected = selectedArray.filter((opt) => opt.value !== item.value)
      } else {
        newSelected = [...selectedArray, item]
      }
      setSelected(newSelected)
      onSelect(newSelected)
      setSearchValue("")
      setOpen(true)
    } else {
      setSelected(item)
      onSelect(item)
      setSearchValue(item.label)
      setOpen(false)
    }
  }

  const handleRemove = (item: Option) => {
    if (!multiple) return
    const selectedArray = selected as Option[]
    const newSelected = selectedArray.filter((opt) => opt.value !== item.value)
    setSelected(newSelected)
    onSelect(newSelected)
  }

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const val = e.target.value
    setSearchValue(val)
    setOpen(true)
    onSearch(val)
  }

  const handleCreate = () => {
    if (onCreateNew) {
      onCreateNew(searchValue)
      setOpen(false)
      setSearchValue("")
    }
  }

  const hasResults = initialData.length > 0
  const trimmedSearch = searchValue.trim()

  // Helper to check if an option is selected (for multiple)
  const isSelected = (item: Option) => {
    if (multiple) {
      return (selected as Option[]).some((opt) => opt.value === item.value)
    } else {
      return (selected as Option | null)?.value === item.value
    }
  }

  return (
    <div ref={containerRef} className={cn("relative w-full", className)}>
      {/* Show chips when multiple */}
      {multiple && (selected as Option[]).length > 0 && (
        <div className="mb-1 flex flex-wrap gap-1">
          {(selected as Option[]).map((opt) => (
            <div
              key={opt.value}
              className="flex items-center gap-1 rounded bg-blue-100 px-2 py-1 text-sm text-blue-700"
            >
              {opt.label}
              <button
                type="button"
                onClick={() => handleRemove(opt)}
                className="inline-flex items-center justify-center rounded hover:bg-blue-200"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          ))}
        </div>
      )}

      <Input
        type="text"
        placeholder={placeholder}
        value={searchValue}
        onChange={handleSearch}
        onFocus={() => setOpen(true)}
        autoComplete="off"
      />

      {open && (
        <div className="absolute z-50 mt-1 w-full max-h-60 overflow-auto rounded-md border border-gray-200 bg-white shadow-lg">
          {hasResults ? (
            <ul>
              {initialData.map((item) => (
                <li
                  key={item.value}
                  onClick={() => handleSelect(item)}
                  className={cn(
                    "cursor-pointer px-4 py-2 hover:bg-blue-100",
                    isSelected(item) && "bg-blue-200 font-semibold"
                  )}
                >
                  {item.label}
                  {isSelected(item) && multiple && (
                    <span className="text-blue-600 ml-2 font-bold">✓</span>
                  )}
                </li>
              ))}
            </ul>
          ) : (
            <div className="p-3 text-sm text-gray-500 flex flex-col gap-2">
              <span>No results found.</span>
              {onCreateNew && trimmedSearch !== "" && (
                <Button
                  variant="ghost"
                  className="text-left justify-start px-2 py-1 text-sm"
                  onClick={handleCreate}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Create “{searchValue}”
                </Button>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  )
}
