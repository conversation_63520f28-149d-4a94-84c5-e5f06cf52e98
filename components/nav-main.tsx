import * as React from "react"
import Link from "next/link"
import type { LucideIcon } from "lucide-react"

import { cn } from "@/lib/utils"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"

interface NavItem {
  title: string
  url: string
  icon?: LucideIcon
  isActive?: boolean
  items?: {
    title: string
    url: string
    isActive?: boolean
  }[]
}

interface NavMainProps {
  items: NavItem[]
  className?: string
}

export function NavMain({ items, className }: NavMainProps) {
  return (
    <div className={cn("grid gap-1 px-2", className)}>
      {items.map((item, index) => (
        <React.Fragment key={index}>
          {item.items ? (
            <Accordion type="single" collapsible>
              <AccordionItem value={item.title} className="border-none">
                <AccordionTrigger
                  className={cn(
                    "group flex h-9 items-center gap-2 rounded-md px-2 py-2 text-sm hover:bg-muted/50",
                    item.isActive && "bg-muted font-medium",
                  )}
                >
                  {item.icon && <item.icon className="h-4 w-4 text-muted-foreground" />}
                  <span className="truncate">{item.title}</span>
                </AccordionTrigger>
                <AccordionContent className="ml-6 mt-1 space-y-1 pb-1 pl-2 pt-0">
                  {item.items.map((subItem, subIndex) => (
                    <Link
                      key={subIndex}
                      href={subItem.url}
                      className={cn(
                        "flex h-8 items-center rounded-md px-2 py-2 text-sm hover:bg-muted/50",
                        subItem.isActive && "bg-muted font-medium",
                      )}
                    >
                      {subItem.title}
                    </Link>
                  ))}
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          ) : (
            <Link
              href={item.url}
              className={cn(
                "flex h-9 items-center gap-2 rounded-md px-2 py-2 text-sm hover:bg-muted/50",
                item.isActive && "bg-muted font-medium",
              )}
            >
              {item.icon && <item.icon className="h-4 w-4 text-muted-foreground" />}
              <span className="truncate">{item.title}</span>
            </Link>
          )}
        </React.Fragment>
      ))}
    </div>
  )
}
