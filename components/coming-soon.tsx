"use client"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { ChevronDown } from "lucide-react"

import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

// Localization dictionaries
const locales = {
  en: {
    underConstruction: "Under Construction",
    comingSoon: "Coming Soon",
    workInProgress: "We're working hard to bring you this feature.",
    stayTuned: "Stay tuned for updates!",
    backToHome: "Back to Home",
    getNotified: "Get Notified",
    changeLanguage: "Change Language",
  },
  es: {
    underConstruction: "En Construcción",
    comingSoon: "Próximamente",
    workInProgress: "Estamos trabajando duro para traerte esta función.",
    stayTuned: "¡Mantente atento para actualizaciones!",
    backToHome: "Volver al Inicio",
    getNotified: "Recibir Notificaciones",
    changeLanguage: "Cambiar Idioma",
  },
  fr: {
    underConstruction: "En Construction",
    comingSoon: "Bientôt Disponible",
    workInProgress: "Nous travaillons dur pour vous apporter cette fonctionnalité.",
    stayTuned: "Restez à l'écoute pour les mises à jour!",
    backToHome: "Retour à l'Accueil",
    getNotified: "Être Notifié",
    changeLanguage: "Changer de Langue",
  },
}

type Locale = keyof typeof locales
type LocaleText = (typeof locales)[Locale]

export interface ComingSoonProps {
  /**
   * The illustration to display
   * @default "/coming-soon-illustration.png"
   */
  illustration?: string

  /**
   * The title to display
   * @default undefined - will use localized "Coming Soon" text
   */
  title?: string

  /**
   * The description to display
   * @default undefined - will use localized default description
   */
  description?: string

  /**
   * The text for the CTA button
   * @default undefined - will use localized "Back to Home" text
   */
  ctaText?: string

  /**
   * The target route for the CTA button
   * @default "/"
   */
  ctaHref?: string

  /**
   * The initial locale
   * @default "en"
   */
  initialLocale?: Locale

  /**
   * Whether to show the language switcher
   * @default true
   */
  showLanguageSwitcher?: boolean

  /**
   * Additional CSS classes for the container
   */
  className?: string
}

export default function ComingSoon({
  illustration = "/coming-soon-rocket.png",
  title,
  description,
  ctaText,
  ctaHref = "/",
  initialLocale = "en",
  showLanguageSwitcher = true,
  className = "",
}: ComingSoonProps) {
  const [locale, setLocale] = useState<Locale>(initialLocale)
  const t = locales[locale]

  // Use provided text or fallback to localized text
  const displayTitle = title || t.comingSoon
  const displayDescription = description || `${t.workInProgress} ${t.stayTuned}`
  const displayCtaText = ctaText || t.backToHome

  return (
    <div
      className={`flex min-h-[50vh] w-full flex-col items-center justify-center px-4 py-12 text-center ${className}`}
    >
      <div className="mx-auto max-w-3xl">
        <div className="relative mx-auto mb-8 h-64 w-64 sm:h-80 sm:w-80">
          <Image src={illustration || "/placeholder.svg"} alt="Coming Soon" fill className="object-contain" priority />
        </div>

        <h1 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl">{displayTitle}</h1>

        <p className="mb-8 text-lg text-muted-foreground">{displayDescription}</p>

        <div className="flex flex-col items-center justify-center gap-4 sm:flex-row">
          <Button asChild size="lg">
            <Link href={ctaHref}>{displayCtaText}</Link>
          </Button>

          {showLanguageSwitcher && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="lg">
                  {t.changeLanguage}
                  <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => setLocale("en")}>English</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setLocale("es")}>Español</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setLocale("fr")}>Français</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>
    </div>
  )
}
