"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Copy } from "lucide-react"

export function SchemaExample() {
  const [copied, setCopied] = useState<string | null>(null)

  const examples = {
    multipleChoice: {
      questions: [
        {
          type: "multiple_choice",
          question: "What is the capital of France?",
          options: ["London", "Berlin", "Paris", "Madrid"],
          correctAnswer: 2,
          difficulty: "easy",
          tags: ["geography", "europe"],
          points: 1,
        },
      ],
    },
    freeText: {
      questions: [
        {
          type: "free_text",
          question: "Explain the concept of object-oriented programming.",
          sampleAnswer: "Object-oriented programming is a programming paradigm based on the concept of objects...",
          difficulty: "medium",
          tags: ["programming", "computer science"],
          points: 2,
        },
      ],
    },
    codeInput: {
      questions: [
        {
          type: "code_input",
          question: "Write a function that returns the sum of two numbers.",
          language: "javascript",
          sampleSolution: "function sum(a, b) {\n  return a + b;\n}",
          difficulty: "medium",
          tags: ["programming", "javascript"],
          points: 3,
        },
      ],
    },
    mixed: {
      questions: [
        {
          type: "multiple_choice",
          question: "Which of the following is NOT a JavaScript data type?",
          options: ["String", "Boolean", "Float", "Object"],
          correctAnswer: 2,
          difficulty: "easy",
          tags: ["programming", "javascript"],
        },
        {
          type: "free_text",
          question: "What is the difference between let and const in JavaScript?",
          sampleAnswer: "let allows reassignment of values while const does not...",
          difficulty: "medium",
          tags: ["programming", "javascript"],
        },
      ],
    },
  }

  const copyToClipboard = (key: string) => {
    navigator.clipboard.writeText(JSON.stringify(examples[key as keyof typeof examples], null, 2))
    setCopied(key)
    setTimeout(() => setCopied(null), 2000)
  }

  return (
    <div className="rounded-lg border p-4">
      <h2 className="mb-4 text-lg font-medium">JSON Schema Examples</h2>

      <Tabs defaultValue="multipleChoice">
        <TabsList className="mb-4">
          <TabsTrigger value="multipleChoice">Multiple Choice</TabsTrigger>
          <TabsTrigger value="freeText">Free Text</TabsTrigger>
          <TabsTrigger value="codeInput">Code Input</TabsTrigger>
          <TabsTrigger value="mixed">Mixed Types</TabsTrigger>
        </TabsList>

        {Object.entries(examples).map(([key, value]) => (
          <TabsContent key={key} value={key} className="relative">
            <div className="absolute right-2 top-2">
              <Button variant="ghost" size="icon" onClick={() => copyToClipboard(key)} className="h-8 w-8">
                <Copy className="h-4 w-4" />
                <span className="sr-only">Copy to clipboard</span>
              </Button>
            </div>
            {copied === key && (
              <div className="absolute right-12 top-2 rounded-md bg-muted px-2 py-1 text-xs">Copied!</div>
            )}
            <pre className="max-h-[300px] overflow-auto rounded bg-muted p-4 text-xs">
              {JSON.stringify(value, null, 2)}
            </pre>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  )
}
