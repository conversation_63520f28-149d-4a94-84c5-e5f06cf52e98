"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, CheckCircle, Upload } from "lucide-react"
import { QuestionAPI } from "@/src/services"

export function QuestionImport() {
  const [jsonInput, setJsonInput] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<{
    success: boolean
    message: string
    details?: any
  } | null>(null)

  const handleImport = async () => {
    setIsLoading(true)
    setResult(null)

    try {
      // Validate JSON
      const data = JSON.parse(jsonInput)

      // Send to API
      const responseData = await QuestionAPI.BulkCreateQuestions(data).request()

      setResult({
        success: true,
        message: `Successfully imported ${responseData.length} questions.`,
      })
    } catch (error) {
      setResult({
        success: false,
        message: error instanceof Error ? error.message : "Invalid JSON format.",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (event) => {
      setJsonInput(event.target?.result as string)
    }
    reader.readAsText(file)
  }

  return (
    <div className="space-y-4 rounded-lg border p-4">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-medium">Import Questions</h2>
        <div>
          <input type="file" id="file-upload" className="hidden" accept=".json" onChange={handleFileUpload} />
          <label htmlFor="file-upload">
            <Button variant="outline" size="sm" className="cursor-pointer" asChild>
              <span>
                <Upload className="mr-2 h-4 w-4" />
                Upload JSON
              </span>
            </Button>
          </label>
        </div>
      </div>

      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Note</AlertTitle>
        <AlertDescription>
          Using mock implementation. Data will be stored in memory and will be lost on server restart.
        </AlertDescription>
      </Alert>

      <Textarea
        placeholder="Paste your JSON here..."
        className="min-h-[200px]"
        value={jsonInput}
        onChange={(e) => setJsonInput(e.target.value)}
      />

      <Button onClick={handleImport} disabled={isLoading || !jsonInput.trim()}>
        {isLoading ? "Importing..." : "Import Questions"}
      </Button>

      {result && (
        <Alert variant={result.success ? "default" : "destructive"}>
          {result.success ? <CheckCircle className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
          <AlertTitle>{result.success ? "Success" : "Error"}</AlertTitle>
          <AlertDescription>
            {result.message}
            {result.details && (
              <pre className="mt-2 max-h-[200px] overflow-auto rounded bg-muted p-2 text-xs">
                {JSON.stringify(result.details, null, 2)}
              </pre>
            )}
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
