"use client"

import * as React from "react"
import { useRouter } from "next/navigation"
import {
  Calendar,
  CreditCard,
  Settings,
  User,
  LayoutDashboard,
  ClipboardList,
  LogOut,
  Search,
  Trophy,
  BookOpen,
  HelpCircle,
  Presentation,
  GraduationCap,
} from "lucide-react"

import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
  CommandShortcut,
} from "@/components/ui/command"
import { secureStorage, StorageKeys } from "@/src/utils/SecureStorage"
import { AuthAPI } from "@/src/services/authApi"

export function CommandPalette() {
  const [open, setOpen] = React.useState(false)
  const router = useRouter()

  React.useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
        e.preventDefault()
        setOpen((open) => !open)
      }
    }

    document.addEventListener("keydown", down)
    return () => document.removeEventListener("keydown", down)
  }, [])

  const runCommand = React.useCallback((command: () => void) => {
    setOpen(false)
    command()
  }, [])

  const performLogout = async () => {
    try {
      await AuthAPI.Logout().request()
    } catch (error) {
      console.error("Failed to logout:", error)
    } finally {
      secureStorage.removeItem(StorageKeys.UserToken)
      secureStorage.removeItem(StorageKeys.RefreshToken)
      secureStorage.removeItem(StorageKeys.CookieToken)
      router.push("/tests")
    }
  }

  return (
    <CommandDialog open={open} onOpenChange={setOpen}>
      <CommandInput placeholder="Type a command or search..." />
      <CommandList>
        <CommandEmpty>No results found.</CommandEmpty>
        <CommandGroup heading="Navigation">
          <CommandItem onSelect={() => runCommand(() => router.push("/dashboard"))}>
            <LayoutDashboard className="mr-2 h-4 w-4" />
            <span>Dashboard</span>
          </CommandItem>
          <CommandItem onSelect={() => runCommand(() => router.push("/dashboard/my-tests"))}>
            <ClipboardList className="mr-2 h-4 w-4" />
            <span>My Tests</span>
          </CommandItem>
          <CommandItem onSelect={() => runCommand(() => router.push("/dashboard/my-courses"))}>
            <GraduationCap className="mr-2 h-4 w-4" />
            <span>My Courses</span>
          </CommandItem>
          <CommandItem onSelect={() => runCommand(() => router.push("/dashboard/created-courses"))}>
            <Presentation className="mr-2 h-4 w-4" />
            <span>Created Courses</span>
          </CommandItem>
          <CommandItem onSelect={() => runCommand(() => router.push("/dashboard/settings"))}>
            <Settings className="mr-2 h-4 w-4" />
            <span>Settings</span>
            <CommandShortcut>⌘S</CommandShortcut>
          </CommandItem>
        </CommandGroup>
        <CommandSeparator />
        <CommandGroup heading="Tests">
          <CommandItem onSelect={() => runCommand(() => router.push("/dashboard/my-tests"))}>
            <Search className="mr-2 h-4 w-4" />
            <span>Search Tests</span>
          </CommandItem>
          <CommandItem onSelect={() => runCommand(() => router.push("/dashboard/my-tests?filter=recent"))}>
            <Calendar className="mr-2 h-4 w-4" />
            <span>Recent Tests</span>
          </CommandItem>
          <CommandItem onSelect={() => runCommand(() => router.push("/dashboard/my-tests?tab=achievements"))}>
            <Trophy className="mr-2 h-4 w-4" />
            <span>Achievements</span>
          </CommandItem>
        </CommandGroup>
        <CommandSeparator />
        <CommandGroup heading="Learning">
          <CommandItem onSelect={() => runCommand(() => router.push("/dashboard?section=courses"))}>
            <BookOpen className="mr-2 h-4 w-4" />
            <span>Recommended Courses</span>
          </CommandItem>
          <CommandItem onSelect={() => runCommand(() => router.push("/dashboard?section=skills"))}>
            <CreditCard className="mr-2 h-4 w-4" />
            <span>Skills Progress</span>
          </CommandItem>
          <CommandItem onSelect={() => runCommand(() => router.push("/dashboard/my-courses"))}>
            <GraduationCap className="mr-2 h-4 w-4" />
            <span>My Courses</span>
          </CommandItem>
          <CommandItem onSelect={() => runCommand(() => router.push("/dashboard/created-courses"))}>
            <Presentation className="mr-2 h-4 w-4" />
            <span>Created Courses</span>
          </CommandItem>
        </CommandGroup>
        <CommandSeparator />
        <CommandGroup heading="Account">
          <CommandItem onSelect={() => runCommand(() => router.push("/dashboard/settings?tab=profile"))}>
            <User className="mr-2 h-4 w-4" />
            <span>Profile</span>
          </CommandItem>
          <CommandItem onSelect={() => runCommand(() => router.push("/dashboard/settings?tab=password"))}>
            <CreditCard className="mr-2 h-4 w-4" />
            <span>Change Password</span>
          </CommandItem>
          <CommandItem onSelect={() => runCommand(() => router.push("/dashboard/settings?tab=notifications"))}>
            <Settings className="mr-2 h-4 w-4" />
            <span>Notification Settings</span>
          </CommandItem>
          <CommandItem onSelect={() => runCommand(() =>
            performLogout()
          )}>
            <LogOut className="mr-2 h-4 w-4" />
            <span>Logout</span>
            <CommandShortcut>⌘L</CommandShortcut>
          </CommandItem>
        </CommandGroup>
        <CommandSeparator />
        <CommandGroup heading="Help">
          <CommandItem onSelect={() => runCommand(() => window.open("https://support.example.com", "_blank"))}>
            <HelpCircle className="mr-2 h-4 w-4" />
            <span>Support</span>
          </CommandItem>
          <CommandItem onSelect={() => runCommand(() => window.open("https://docs.example.com", "_blank"))}>
            <BookOpen className="mr-2 h-4 w-4" />
            <span>Documentation</span>
          </CommandItem>
        </CommandGroup>
      </CommandList>
    </CommandDialog>
  )
}
