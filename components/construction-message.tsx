"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { useLocale } from "@/src/lib/locale";

export default function ConstructionMessage() {
  const { t } = useLocale();

  return (
    <div className="flex flex-col justify-center">
      <div className="mb-6 inline-block rounded-full bg-primary/10 px-4 py-2 text-sm font-medium text-primary">
        {t.notice}
      </div>
      <h1 className="mb-4 text-4xl font-bold tracking-tight">
        {t.underConstruction}
      </h1>
      <p className="mb-4 text-xl text-muted-foreground">{t.pageNotReady}</p>
      <p className="mb-6">{t.checkOtherServices}</p>

      <div className="flex flex-col space-y-3 sm:flex-row sm:space-x-4 sm:space-y-0">
        <Link href="/" passHref>
          <Button className="bg-green-600 hover:bg-green-700 text-white h-12 px-6 text-base flex items-center gap-2">
            Back to Home
          </Button>
        </Link>
        <Link href="/tests" passHref>
          <Button className="bg-green-600 hover:bg-green-700 text-white h-12 px-6 text-base flex items-center gap-2">
            Explore Public Tests
          </Button>
        </Link>
      </div>
    </div>
  );
}
