"use client"

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Chart, ChartContainer } from "@/components/ui/chart"
import {
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts"

interface CourseAnalyticsProps {
  courseId: string
}

export function CourseAnalytics({ courseId }: CourseAnalyticsProps) {
  // Mock data for analytics
  const viewsData = [
    { name: "Jan", views: 400 },
    { name: "Feb", views: 300 },
    { name: "Mar", views: 500 },
    { name: "Apr", views: 280 },
    { name: "May", views: 590 },
    { name: "<PERSON>", views: 800 },
    { name: "<PERSON>", views: 810 },
  ]

  const completionData = [
    { name: "Week 1", completed: 65, started: 100 },
    { name: "Week 2", completed: 59, started: 80 },
    { name: "Week 3", completed: 45, started: 60 },
    { name: "Week 4", completed: 30, started: 40 },
    { name: "Week 5", completed: 18, started: 30 },
  ]

  const engagementData = [
    { name: "Video Views", value: 65 },
    { name: "Comments", value: 15 },
    { name: "Downloads", value: 10 },
    { name: "Quizzes", value: 10 },
  ]

  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042"]

  const sectionCompletionData = [
    { name: "Introduction", completion: 92 },
    { name: "Advanced Hooks", completion: 78 },
    { name: "Performance Optimization", completion: 65 },
  ]

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">128</div>
            <p className="text-xs text-muted-foreground">+18% from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">67%</div>
            <p className="text-xs text-muted-foreground">+5% from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Watch Time</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">42 min</div>
            <p className="text-xs text-muted-foreground">+12 min from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Comments</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">87</div>
            <p className="text-xs text-muted-foreground">+24 from last month</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="views" className="space-y-4">
        <TabsList>
          <TabsTrigger value="views" className="flex items-center gap-2">
            <LineChart className="h-4 w-4" />
            Views
          </TabsTrigger>
          <TabsTrigger value="completion" className="flex items-center gap-2">
            <BarChart className="h-4 w-4" />
            Completion
          </TabsTrigger>
          <TabsTrigger value="engagement" className="flex items-center gap-2">
            <PieChart className="h-4 w-4" />
            Engagement
          </TabsTrigger>
        </TabsList>

        <TabsContent value="views" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Course Views Over Time</CardTitle>
              <CardDescription>Track how many students are viewing your course each month.</CardDescription>
            </CardHeader>
            <CardContent className="h-[300px]">
              <Chart>
                <ChartContainer>
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsLineChart
                      data={viewsData}
                      margin={{
                        top: 5,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line type="monotone" dataKey="views" stroke="#8884d8" activeDot={{ r: 8 }} />
                    </RechartsLineChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </Chart>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="completion" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Course Completion Rate</CardTitle>
              <CardDescription>Track how many students complete each section of your course.</CardDescription>
            </CardHeader>
            <CardContent className="h-[300px]">
              <Chart>
                <ChartContainer>
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsBarChart
                      data={completionData}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="started" stackId="a" fill="#8884d8" />
                      <Bar dataKey="completed" stackId="a" fill="#82ca9d" />
                    </RechartsBarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </Chart>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Section Completion Rates</CardTitle>
              <CardDescription>Percentage of students who completed each section.</CardDescription>
            </CardHeader>
            <CardContent className="h-[300px]">
              <Chart>
                <ChartContainer>
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsBarChart
                      data={sectionCompletionData}
                      layout="vertical"
                      margin={{
                        top: 5,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" domain={[0, 100]} />
                      <YAxis dataKey="name" type="category" />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="completion" fill="#82ca9d" />
                    </RechartsBarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </Chart>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="engagement" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Student Engagement</CardTitle>
              <CardDescription>How students are engaging with your course content.</CardDescription>
            </CardHeader>
            <CardContent className="h-[300px]">
              <Chart>
                <ChartContainer>
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsPieChart>
                      <Pie
                        data={engagementData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {engagementData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                      <Legend />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </Chart>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
