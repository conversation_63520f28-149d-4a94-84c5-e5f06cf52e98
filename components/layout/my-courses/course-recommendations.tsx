'use client'

import Image from "next/image"
import Link from "next/link"
import { ArrowRight } from "lucide-react"

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useEffect, useState } from "react"
import { RecommendationsCourse } from "@/src/lib/repositories/dashboard/recommendations-courses/RecommendationsCoursesRepository"
import { DashboardAPI } from "@/src/services/dashboardApi"

import { useLocalization } from "@/src/localization/functions/client";
import en from "./locales/en.json"

interface CourseRecommendationsProps {
  courseId: string
}

export function CourseRecommendations({ courseId }: CourseRecommendationsProps) {
  const [recommendedCourses, setRecommendedCourses] = useState<RecommendationsCourse[]>([])
  const [loading, setLoading] = useState(true);
  const { t } = useLocalization("public-test", { en });

  useEffect(() => {
    const fetchData = async () => {
      try {
        const data = await DashboardAPI.DashboardRecommendations({page: 1}).request();
        setRecommendedCourses(data);
      } catch (err) {
        console.error(err);
      } finally {
        setLoading(false)
      }
    };
    fetchData();
  }, []);

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("recommended_courses")}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {recommendedCourses.map((course) => (
            <div key={course.id} className="flex gap-3">
              <div className="relative h-16 w-28 flex-shrink-0 overflow-hidden rounded-md">
                <Image src={course.image || "https://ui.shadcn.com/placeholder.svg"} alt={course.title} fill className="object-cover" />
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="font-medium line-clamp-1">{course.title}</h4>
                <p className="text-sm text-muted-foreground line-clamp-1">By {course.instructor}</p>
                <p className="text-xs text-muted-foreground mt-1">{course.reason}</p>
              </div>
            </div>
          ))}
          <Button variant="outline" className="w-full" asChild>
            <Link href="/dashboard/explore">
              {t("explore_more_courses")}
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
