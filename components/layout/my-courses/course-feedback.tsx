"use client"

import { useState } from "react"
import { Star, Send } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"

import { MyCoursesAPI } from "@/src/services/myCoursesApi";
import { useLocalization } from "@/src/localization/functions/client";
import en from "./locales/en.json"

interface CourseFeedbackProps {
  courseId: string
}

export function CourseFeedback({ courseId }: CourseFeedbackProps) {
  const [rating, setRating] = useState(0)
  const [hoveredRating, setHoveredRating] = useState(0)
  const [feedback, setFeedback] = useState("")
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [loading, setLoading] = useState(true);
  const { t } = useLocalization("public-test", { en });

  const handleSubmit = async () => {
    try {
      setLoading(true)
      if (rating === 0 || !feedback.trim()) return

      const newFeedback = {
        courseId, rating, feedback
      }

      await MyCoursesAPI.CreateCourseFeedback(newFeedback).request();
      setIsSubmitted(true)
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false)
    }
  }

  if (isSubmitted) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t("your_feedback")}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <div className="flex justify-center mb-2">
              {[1, 2, 3, 4, 5].map((star) => (
                <Star
                  key={star}
                  className={`h-6 w-6 ${star <= rating ? "fill-primary text-primary" : "text-muted-foreground"}`}
                />
              ))}
            </div>
            <p className="text-muted-foreground mb-4">
              {t("thank_you_feedback")}
            </p>
            <Button variant="outline" onClick={() => setIsSubmitted(false)}>
              {t("edit_feedback")}
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("rate_this_course")}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <div className="flex justify-center mb-2">
              {[1, 2, 3, 4, 5].map((star) => (
                <Star
                  key={star}
                  className={`h-6 w-6 cursor-pointer ${star <= (hoveredRating || rating) ? "fill-primary text-primary" : "text-muted-foreground"
                    }`}
                  onClick={() => setRating(star)}
                  onMouseEnter={() => setHoveredRating(star)}
                  onMouseLeave={() => setHoveredRating(0)}
                />
              ))}
            </div>
            <p className="text-center text-sm text-muted-foreground">
              {rating === 0
                ? t("click_to_rate")
                : `${t("you_rated_course")} ${rating} ${rating === 1 ? t("star") : t("stars")}`}
            </p>
          </div>
          <div className="space-y-2">
            <label htmlFor="feedback" className="text-sm font-medium">
              {t("your_feedback")}
            </label>
            <Textarea
              id="feedback"
              placeholder={t("feedback_placeholder")}
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              className="min-h-[100px]"
            />
          </div>
          <Button className="w-full" onClick={handleSubmit} disabled={rating === 0 || !feedback.trim()}>
            <Send className="mr-2 h-4 w-4" />
            {t("submit_feedback")}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
