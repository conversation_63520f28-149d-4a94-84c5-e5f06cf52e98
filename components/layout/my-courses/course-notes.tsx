"use client"

import { useEffect, useState } from "react"
import { StickyNote, Clock, Edit, Trash2, Save, X, Plus } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { MyCourseNote } from "@/src/lib/repositories/my-courses/MyCoursesRepository"
import { MyCoursesAPI } from "@/src/services/myCoursesApi";

import { useLocalization } from "@/src/hooks/useLocalization/client";
import en from "./locales/en.json"

interface CourseNotesProps {
  videoId: string
}

export function CourseNotes({ videoId }: CourseNotesProps) {
  const [notes, setNotes] = useState<MyCourseNote[]>([])
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("all")
  const [newNoteContent, setNewNoteContent] = useState("")
  const [newNoteTitle, setNewNoteTitle] = useState("")
  const [isAddingNote, setIsAddingNote] = useState(false)
  const [editingNoteId, setEditingNoteId] = useState<string | null>(null)
  const { t } = useLocalization("public-test", { en });

  const fetchData = async () => {
    try {
      const filter = activeTab == 'this-video' ? 'this-video' : activeTab == 'this-section' ? 'this-section' : 'all';
      const id = activeTab == 'this-video' ? videoId : activeTab == 'this-section' ? `section-${videoId}` : undefined;

      const data = await MyCoursesAPI.CourseNotes({
        filters: {
          filter,
          id
        },
        page: 1
      }).request();
      setNotes(data || []);
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false)
    }
  };

  useEffect(() => {
    fetchData();
  }, [videoId, activeTab]);

  const [editedNoteContent, setEditedNoteContent] = useState("")
  const [editedNoteTitle, setEditedNoteTitle] = useState("")

  const addNote = async () => {
    try {
      setLoading(true)
      if (!newNoteTitle.trim() || !newNoteContent.trim()) return

      const newNote = {
        id: `note-${Date.now()}`,
        video_id: videoId,
        section_id: `section-${videoId}`,
        title: newNoteTitle,
        content: newNoteContent,
        videoTimestamp: "10:30", 
      }

      const createdNotes = await MyCoursesAPI.CreateCourseNote(newNote).request();
      setNotes((prev) => [...prev, createdNotes]);
      setNewNoteTitle("")
      setNewNoteContent("")
      setIsAddingNote(false)
      fetchData();
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false)
    }
  }

  const startEditingNote = (note: (typeof notes)[0]) => {
    setEditingNoteId(note.id)
    setEditedNoteTitle(note.title)
    setEditedNoteContent(note.content)
  }

  const saveEditedNote = async (noteId: string) => {
    try {
      setLoading(true)
      if (!editedNoteTitle.trim() || !editedNoteContent.trim()) return
      const updateData = {
        ...notes.filter(a => a.id === noteId)[0],
        title: editedNoteTitle,
        content: editedNoteContent,
      };
      const updatedNotes = await MyCoursesAPI.UpdateCourseNote(noteId, updateData).request();
      setNotes(prev => ([...prev, updatedNotes]))
      setEditingNoteId(null)
      fetchData()
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false)
    }
  }

  const deleteNote = async (noteId: string) => {
    try {
      setLoading(true)
      if (!editedNoteTitle.trim() || !editedNoteContent.trim()) return
      await MyCoursesAPI.DeleteCourseNote(noteId).request();
      setNotes(notes.filter((note) => note.id !== noteId))
      fetchData()
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <StickyNote className="mr-2 h-5 w-5" />
            {t("my_notes")}
          </CardTitle>
          <Button onClick={() => setIsAddingNote(true)} disabled={isAddingNote}>
            <Plus className="mr-2 h-4 w-4" />
            {t("add_note")}
          </Button>
        </div>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList>
            <TabsTrigger value="all">{t("all_notes")}</TabsTrigger>
            <TabsTrigger value="this-video">{t("this_video")}</TabsTrigger>
            <TabsTrigger value="this-section">{t("this_section")}</TabsTrigger>
          </TabsList>
        </Tabs>
      </CardHeader>
      <CardContent>
        {isAddingNote && (
          <div className="mb-6 border rounded-lg p-4 space-y-3">
            <Input
              placeholder={t("note_title")}
              value={newNoteTitle}
              onChange={(e) => setNewNoteTitle(e.target.value)}
              className="font-medium"
            />
            <div className="flex items-center text-xs text-muted-foreground mb-2">
              <Clock className="h-3 w-3 mr-1" />
              <span>{t("at_timestamp")}: 10:30</span>
            </div>
            <Textarea
              placeholder={t("note_placeholder")}
              value={newNoteContent}
              onChange={(e) => setNewNoteContent(e.target.value)}
              className="min-h-[100px]"
            />
            <div className="flex justify-end gap-2">
              <Button variant="outline" size="sm" disabled={loading} onClick={() => setIsAddingNote(false)}>
                {t("cancel")}
              </Button>
              <Button size="sm" onClick={addNote} disabled={loading || !newNoteTitle.trim() || !newNoteContent.trim()}>
                {loading ? "loading..." : t("save_note")}
              </Button>
            </div>
          </div>
        )}

        <div className="space-y-4">
          {notes.length === 0 ? (
            <div className="text-center py-12 text-muted-foreground">
              <p>{t("no_notes_yet")}</p>
            </div>
          ) : (
            notes.map((note) => (
              <div key={note.id} className="border rounded-lg p-4 space-y-3">
                {editingNoteId === note.id ? (
                  <>
                    <Input
                      value={editedNoteTitle}
                      onChange={(e) => setEditedNoteTitle(e.target.value)}
                      className="font-medium"
                    />
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Clock className="h-3 w-3 mr-1" />
                      <span>{t("at_timestamp")}: {note.videoTimestamp}</span>
                    </div>
                    <Textarea
                      value={editedNoteContent}
                      onChange={(e) => setEditedNoteContent(e.target.value)}
                      className="min-h-[100px]"
                    />
                    <div className="flex justify-end gap-2">
                      <Button variant="outline" size="sm" onClick={() => setEditingNoteId(null)}>
                        <X className="h-4 w-4 mr-1" />
                        {t("cancel")}
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => saveEditedNote(note.id)}
                        disabled={!editedNoteTitle.trim() || !editedNoteContent.trim()}
                      >
                        <Save className="h-4 w-4 mr-1" />
                        {t("save")}
                      </Button>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="flex justify-between items-start">
                      <h3 className="font-medium">{note.title}</h3>
                      <div className="flex gap-1">
                        <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => startEditingNote(note)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => deleteNote(note.id)}>
                          <Trash2 className="h-4 w-4 text-destructive" />
                        </Button>
                      </div>
                    </div>
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Clock className="h-3 w-3 mr-1" />
                      <span>{t("at_timestamp")}: {note.videoTimestamp}</span>
                      <span className="mx-2">•</span>
                      <span>{t("updated")} {note.updatedAt?.toLocaleDateString()}</span>
                    </div>
                    <div className="text-sm whitespace-pre-line">{note.content}</div>
                    <Button variant="outline" size="sm" className="w-full">
                      {t("jump_to_timestamp")}
                    </Button>
                  </>
                )}
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  )
}
