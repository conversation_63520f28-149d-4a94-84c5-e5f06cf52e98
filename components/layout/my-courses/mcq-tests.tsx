"use client"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, ArrowRight } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { useEffect, useState } from "react"
import { MyCoursesMCQTest } from "@/src/lib/repositories/my-courses/MyCoursesRepository"
import { MyCoursesAPI } from "@/src/services/myCoursesApi"

import { useLocalization } from "@/src/localization/functions/client";
import en from "./locales/en.json"

interface MCQTestsProps {
  courseId: string
}

export function MCQTests({ courseId }: MCQTestsProps) {
  const [tests, setTests] = useState<MyCoursesMCQTest[]>([])
  const [loading, setLoading] = useState(true);
  const { t } = useLocalization("public-test", { en });

  const fetchMyCourses = async () => {
    try {
      const data = await MyCoursesAPI.CourseMCQTests({
        page: 1,
        filters: {
          course_id: courseId
        }
      }).request();
      setTests(data);
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false)
    }
  };

  useEffect(() => {
    fetchMyCourses();
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold">{t("course_tests")}</h2>
          <p className="text-sm text-muted-foreground">{t("course_tests_description")}</p>
        </div>
        <Badge variant="outline" className="px-3">
          {tests.filter((test) => test.completed).length}/{tests.length} {t("completed")}
        </Badge>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        {tests.map((test) => (
          <Card key={test.id} className={test.completed ? "border-primary/20 bg-primary/5" : ""}>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle>{test.title}</CardTitle>
                  <CardDescription>{test.description}</CardDescription>
                </div>
                {test.completed && <Badge className="bg-primary">{test.score}%</Badge>}
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex flex-wrap gap-4 text-sm">
                  <div className="flex items-center gap-1">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <span>{test.questions} {t("questions")}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span>{test.timeLimit}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Badge variant="outline" className="font-normal">
                      {test.section}
                    </Badge>
                  </div>
                </div>

                {test.completed && (
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span>{t("your_score")}</span>
                      <span className="font-medium">{test.score}%</span>
                    </div>
                    <Progress
                      value={test.score || 0}
                      className="h-2"
                      indicatorClassName={
                        test.score && test.score >= 80
                          ? "bg-green-500"
                          : test.score && test.score >= 60
                            ? "bg-yellow-500"
                            : "bg-red-500"
                      }
                    />
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter>
              <Button className="w-full" variant={test.completed ? "outline" : "default"}>
                {test.completed ? (
                  <>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    {t("view_results")}
                  </>
                ) : (
                  <>
                    {t("start_test")}
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  )
}
