"use client"

import { useEffect, useState } from "react"
import { MessageSquare, ThumbsUp, ThumbsDown, Clock, Send, MoreH<PERSON>zontal, Flag, Trash2 } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { VideoComment } from "@/src/lib/repositories/my-courses/MyCoursesRepository"
import { MyCoursesAPI } from "@/src/services/myCoursesApi"

import { useLocalization } from "@/src/hooks/useLocalization/client";
import en from "./locales/en.json"

interface TimestampCommentsProps {
  videoId: string
}

export function TimestampComments({ videoId }: TimestampCommentsProps) {
  const [commentsList, setCommentsList] = useState<VideoComment[]>([])
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<"all" | "my" | "timestamps" | string>("all")
  const [userName, _] = useState("You")
  const [newComment, setNewComment] = useState("")
  const { t } = useLocalization("public-test", { en });

  const fetchData = async () => {
    try {
      const filter = activeTab == 'timestamps' ? 'timestamps' : activeTab == 'my' ? 'my' : 'all';
      const currentUserName = activeTab == 'my' ? userName : undefined;

      const data = await MyCoursesAPI.VideoComments({ filters: { id: videoId, filter: "my", currentUserName }, page: 1 }).request();
      setCommentsList(data);
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false)
    }
  };

  useEffect(() => {
    fetchData();
  }, [videoId, activeTab]);

  const toggleLike = (commentId: string) => {
    setCommentsList(
      commentsList.map((comment) => {
        if (comment.id === commentId) {
          if (comment.isLiked) {
            return { ...comment, isLiked: false, likes: comment.likes - 1 }
          } else {
            const newDislikes = comment.isDisliked ? comment.dislikes - 1 : comment.dislikes
            return {
              ...comment,
              isLiked: true,
              isDisliked: false,
              likes: comment.likes + 1,
              dislikes: newDislikes,
            }
          }
        }
        return comment
      }),
    )
  }

  const toggleDislike = (commentId: string) => {
    setCommentsList(
      commentsList.map((comment) => {
        if (comment.id === commentId) {
          if (comment.isDisliked) {
            return { ...comment, isDisliked: false, dislikes: comment.dislikes - 1 }
          } else {
            const newLikes = comment.isLiked ? comment.likes - 1 : comment.likes
            return {
              ...comment,
              isDisliked: true,
              isLiked: false,
              dislikes: comment.dislikes + 1,
              likes: newLikes,
            }
          }
        }
        return comment
      }),
    )
  }

  const deleteComment = (commentId: string) => {
    setCommentsList(commentsList.filter((comment) => comment.id !== commentId))
  }

  const addComment = async () => {
    try {
      setLoading(true)
      if (!newComment.trim()) return

      const newCommentObj = {
        id: `comment-${Date.now()}`,
        video_id: videoId,
        user: {
          name: userName,
          avatar: "https://ui.shadcn.com/placeholder.svg",
        },
        content: newComment,
        timestamp: "Just now",
        videoTimestamp: "10:30", // This would be the current video timestamp
        likes: 0,
        dislikes: 0,
        isLiked: false,
        isDisliked: false,
      }
      const createdVideoComments = await MyCoursesAPI.CreateVideoComment(newCommentObj).request();
      setCommentsList((prev) => [...prev, createdVideoComments]);
      setNewComment("")
      fetchData();
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <MessageSquare className="mr-2 h-5 w-5" />
          {t("video_comments")}
        </CardTitle>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList>
            <TabsTrigger value="all">{t("all_comments")}</TabsTrigger>
            <TabsTrigger value="my">{t("my_comments")}</TabsTrigger>
            <TabsTrigger value="timestamps">{t("by_timestamp")}</TabsTrigger>
          </TabsList>
        </Tabs>
      </CardHeader>
      <CardContent>
        <div className="mb-4 flex items-center gap-2">
          <Avatar>
            <AvatarImage src="https://ui.shadcn.com/placeholder.svg" alt="Your Avatar" />
            <AvatarFallback>YA</AvatarFallback>
          </Avatar>
          <div className="relative flex-1">
            <Input
              placeholder={t("add_comment_placeholder")}
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              className="pr-20"
            />
            <div
              className="absolute right-2 top-1/2 -translate-y-1/2"
            >
              <button
                onClick={addComment}
                disabled={!newComment.trim()}
                className="p-2 bg-sp-primary text-sp-neutral-100 shadow hover:bg-sp-primary/90 rounded-full disabled:pointer-events-none disabled:opacity-50">
                <Send className="h-3 w-3" />
              </button>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          {commentsList.length === 0 ? (
            <div className="text-center py-12 text-muted-foreground">
              <p>{t("no_comments_yet")}</p>
            </div>
          ) : (
            commentsList.map((comment) => (
              <div key={comment.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex justify-between items-start">
                  <div className="flex items-start gap-3">
                    <Avatar>
                      <AvatarImage src={comment.user.avatar || "https://ui.shadcn.com/placeholder.svg"} alt={comment.user.name} />
                      <AvatarFallback>
                        {comment.user.name
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">{comment.user.name}</div>
                      <div className="text-sm text-muted-foreground flex items-center gap-2">
                        <span>{comment.timestamp}</span>
                        <span>•</span>
                        <span className="flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          {comment.videoTimestamp}
                        </span>
                      </div>
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => { }}>
                        <Clock className="h-4 w-4 mr-2" />
                        {t("jump_to_timestamp")}
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Flag className="h-4 w-4 mr-2" />
                        {t("report_comment")}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => deleteComment(comment.id)}>
                        <Trash2 className="h-4 w-4 mr-2 text-destructive" />
                        {t("delete_comment")}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <p className="text-sm">{comment.content}</p>
                <div className="flex items-center gap-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`flex items-center gap-1 ${comment.isLiked ? "text-primary" : ""}`}
                    onClick={() => toggleLike(comment.id)}
                  >
                    <ThumbsUp className="h-4 w-4" />
                    <span>{comment.likes}</span>
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`flex items-center gap-1 ${comment.isDisliked ? "text-destructive" : ""}`}
                    onClick={() => toggleDislike(comment.id)}
                  >
                    <ThumbsDown className="h-4 w-4" />
                    <span>{comment.dislikes}</span>
                  </Button>
                  <Button variant="ghost" size="sm" className="ml-auto">
                    {t("reply")}
                  </Button>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  )
}
