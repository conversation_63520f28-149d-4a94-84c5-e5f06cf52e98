'use client'

import React, { useEffect, useMemo } from 'react'
import { useState } from "react"
import { Calendar, Filter, Search, TrendingUp } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useLocalization } from '@/src/hooks/useLocalization/client'
import en from "./locales/en.json"
import { EnrolledCourseCard } from './enrolled-course-card'
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { MyCourse, MyCourseResponse } from '@/src/lib/repositories/my-courses/MyCoursesRepository'
import { MyCoursesAPI } from "@/src/services/myCoursesApi";

export default function MyCourseBody() {
  const [myCourses, setMyCourses] = useState<MyCourse[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [sortBy, setSortBy] = useState("progress")
  const [loading, setLoading] = useState(true);

  const [page, setPage] = useState(1);
  const pageSize = 6;
  const [totalPages, setTotalPages] = useState(1);

  const { t } = useLocalization("public-test", { en });

  useEffect(() => {
    const fetchMyCourses = async () => {
      try {
        const response = await MyCoursesAPI.MyCourses({ page, per_page: pageSize }).request();
        setMyCourses(response.items);
        setTotalPages(response.total_pages);
      } catch (err) {
        console.error(err);
      } finally {
        setLoading(false)
      }
    };
    fetchMyCourses();
  }, [page]);

  const filteredData = useMemo(() =>
    myCourses
      .filter((val) => {
        const matchesSearch = val?.title.toLowerCase().includes(searchQuery.toLowerCase())
        const matchesCategory = categoryFilter === "all" || val?.category.includes(categoryFilter)
        return matchesSearch && matchesCategory
      })
      .sort((a, b) => {
        if (sortBy === "recent") {
          return b.updatedAt.getTime() - a.updatedAt.getTime();
        } else if (sortBy === "progress") {
          return b.completionPercentage - a.completionPercentage;
        } else if (sortBy === "a-z") {
          return a.title.localeCompare(b.title);
        } else if (sortBy === "z-a") {
          return b.title.localeCompare(a.title);
        }
        return 0;
      })
    ,
    [myCourses, categoryFilter, searchQuery, sortBy]
  )

  const categories = useMemo(() => ["all", ...Array.from(new Set(myCourses.map((val) => val?.category)))].flatMap(e => e), [myCourses])

  return (
    <Card className="shadow-none bg-sp-neutral-100 border-none rounded-3xl p-4 flex flex-col gap-4">
      {/* Filters */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type={t("search")}
            placeholder={t("search_courses") + '...'}
            className="pl-8 rounded-full pr-3 py-2"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger className="w-full md:w-[180px] rounded-full">
            <Filter className="mr-2 h-4 w-4" />
            <SelectValue placeholder="Category" />
          </SelectTrigger>
          <SelectContent>
            {categories.map((category) => (
              <SelectItem key={category} value={category}>
                {category === "all" ? "All Categories" : category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select value={sortBy} onValueChange={setSortBy}>
          <SelectTrigger className="w-full sm:w-[180px] rounded-full">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="progress">{t("progress")}</SelectItem>
            <SelectItem value="recent">{t("recently_accessed")}</SelectItem>
            <SelectItem value="a-z">A-Z</SelectItem>
            <SelectItem value="z-a">Z-A</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* filteredData cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredData.map((course) => (
          <EnrolledCourseCard key={course.id} course={course} />
        ))}
      </div>

      {filteredData.length === 0 && (
        <div className="text-center py-12 border rounded-lg">
          <p className="text-muted-foreground">{t("no_my_courses_found")}</p>
        </div>
      )}

      <div className="mt-6">
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                aria-disabled={page <= 1}
                tabIndex={page <= 1 ? -1 : undefined}
                className={
                  page <= 1 ? "pointer-events-none opacity-50" : undefined
                }
                href="#"
                onClick={() => setPage(prev => Math.max(prev - 1, 1))} />
            </PaginationItem>

            {Array.from({ length: totalPages }, (_, i) => (
              <PaginationItem key={i}>
                <PaginationLink
                  href="#"
                  isActive={page === i + 1}
                  onClick={() => setPage(i + 1)}
                >
                  {i + 1}
                </PaginationLink>
              </PaginationItem>
            ))}

            <PaginationItem>
              <PaginationNext
                aria-disabled={page >= totalPages}
                tabIndex={page >= totalPages ? -1 : undefined}
                className={
                  page >= totalPages ? "pointer-events-none opacity-50" : undefined
                }
                href="#"
                onClick={() => setPage(prev => Math.min(prev + 1, totalPages))} />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </Card>
  )
}
