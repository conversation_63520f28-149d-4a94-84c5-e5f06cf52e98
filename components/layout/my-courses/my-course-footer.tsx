'use client'

import { useLocalization } from "@/src/hooks/useLocalization/client";
import { Book<PERSON><PERSON>, Clock, Award } from "lucide-react"
import en from "./locales/en.json"
import { useEffect, useState } from "react";
import { MyCoursesTotalLearningStats } from "@/src/lib/repositories/my-courses/MyCoursesRepository";
import { MyCoursesAPI } from "@/src/services/myCoursesApi";

export default function MyCourseFooter() {
  const [stats, setStats] = useState<MyCoursesTotalLearningStats>({ totalCourses: 0, totalHours: 0, certificatesEarned: 0 });
  const [loading, setLoading] = useState(true);
  const { t } = useLocalization("public-test", { en });

  useEffect(() => {
    const runFetch = async () => {
      try {
        const data = await MyCoursesAPI.TotalLearningStats().request();
        setStats(data)
      } catch (err) {
        console.error(err);
      } finally {
        setLoading(false)
      }
    };
    runFetch();
  }, []);

  return (
    <div className="mt-4 rounded-lg border bg-card p-6">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="space-y-1">
          <h2 className="text-xl font-semibold">{t("your_learning_stats")}</h2>
          <p className="text-sm text-muted-foreground">{t("track_progress")}</p>
        </div>
        <div className="flex flex-wrap gap-4">
          <div className="flex items-center gap-2">
            <div className="rounded-full bg-primary/10 p-2">
              <BookOpen className="h-5 w-5 text-primary" />
            </div>
            <div>
              <p className="text-sm font-medium">{stats.totalCourses} {t("courses")}</p>
              <p className="text-xs text-muted-foreground">{t("enrolled")}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="rounded-full bg-primary/10 p-2">
              <Clock className="h-5 w-5 text-primary" />
            </div>
            <div>
              <p className="text-sm font-medium">{stats.totalHours} {t("hours")}</p>
              <p className="text-xs text-muted-foreground">{t("total_learning")}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="rounded-full bg-primary/10 p-2">
              <Award className="h-5 w-5 text-primary" />
            </div>
            <div>
              <p className="text-sm font-medium">{stats.certificatesEarned} {t("certificates")}</p>
              <p className="text-xs text-muted-foreground">{t("earned")}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
