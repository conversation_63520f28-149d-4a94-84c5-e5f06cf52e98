"use client"

import { <PERSON><PERSON>, Plus, Refresh<PERSON><PERSON>, Trash2, } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, Tooltip<PERSON>ontent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

import { useLocalization } from "@/src/localization/functions/client"
import en from "./locales/en.json"
import { Invitation } from "@/src/lib/repositories/collaborators/CollaboratorsRepository"

export default function TabsInvitations({
  invitations,
  getRoleBadge,
  resendInvitation,
  cancelInvitation,
  handleNewInvitation,
}: {
  invitations: Invitation[]
  getRoleBadge: (role: string) => JSX.Element | null
  resendInvitation: (collaborator: Invitation) => void
  cancelInvitation: (id: string) => void,
  handleNewInvitation: () => void,
}) {
  const { t } = useLocalization("public-test", { en });

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("invitations.title")}</CardTitle>
        <CardDescription>{t("invitations.description")}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {invitations.length === 0 ? (
            <div className="text-center py-12 text-muted-foreground">
              <p>{t("invitations.empty")}</p>
            </div>
          ) : (
            invitations.map((invitation) => (
              <div key={invitation.id} className="flex items-center justify-between rounded-lg border p-4">
                <div>
                  <div className="font-medium">{invitation.email}</div>
                  <div className="flex items-center gap-2 mt-1">
                    {getRoleBadge(invitation.role)}
                    <span className="text-xs text-muted-foreground">
                      {t("invitations.sentAt", { date: invitation.sentAt.toLocaleDateString() })}
                    </span>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="hidden md:block text-sm text-muted-foreground">
                    {t("invitations.expiresAt", { date: invitation.expiresAt.toLocaleDateString() })}
                  </div>

                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button size="icon" variant="ghost" onClick={() => resendInvitation(invitation)}>
                          <RefreshCw className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>{t("invitations.resend")}</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>

                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          size="icon"
                          variant="ghost"
                          onClick={() => {
                            const link = `${window.location.origin}/invitation?code=${invitation.code}`;
                            navigator.clipboard.writeText(link);
                          }}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>{t("invitations.copyLink")}</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>

                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button size="icon" variant="ghost" onClick={() => cancelInvitation(invitation.id)}>
                          <Trash2 className="h-4 w-4 text-destructive" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>{t("invitations.cancel")}</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
      <CardFooter>
        <Button
          variant="outline"
          className="w-full"
          onClick={() => handleNewInvitation()}
        >
          <Plus className="mr-2 h-4 w-4" />
          {t("invitations.new")}
        </Button>
      </CardFooter>
    </Card>
  )
}
