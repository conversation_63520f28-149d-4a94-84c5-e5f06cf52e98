"use client"

import { MoreH<PERSON><PERSON><PERSON>, } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

import { useLocalization } from "@/src/localization/functions/client"
import en from "./locales/en.json"
import { CollaboratorUser } from "@/src/lib/repositories/collaborators/CollaboratorsRepository"

export default function TabsCollaborators({
  collaborators,
  getRoleBadge,
  changeCollaboratorRole,
  removeCollaborator
}: {
  collaborators: CollaboratorUser[]
  getRoleBadge: (role: string) => JSX.Element | null
  changeCollaboratorRole: (collaborator: CollaboratorUser) => void
  removeCollaborator: (id: string) => void
}) {
  const { t } = useLocalization("public-test", { en });

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("collaborators.title")}</CardTitle>
        <CardDescription>
          {t("collaborators.description")}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {collaborators.length === 0 ? (
            <div className="text-center py-12 text-muted-foreground">
              <p>{t("collaborators.empty")}</p>
            </div>
          ) : (
            collaborators.map((collaborator) => (
              <div key={collaborator.id} className="flex items-center justify-between rounded-lg border p-4">
                <div className="flex items-center gap-4">
                  <Avatar>
                    <AvatarImage src={collaborator.avatar || "/placeholder.svg"} alt={collaborator.name} />
                    <AvatarFallback>
                      {collaborator.name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">{collaborator.name}</div>
                    <div className="text-sm text-muted-foreground">{collaborator.email}</div>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <div className="hidden md:block">
                    <div className="text-sm text-right">{getRoleBadge(collaborator.role)}</div>
                    <div className="text-xs text-muted-foreground">
                      {t("collaborators.active", { date: collaborator.lastActive })}
                    </div>
                  </div>
                  <div className="hidden md:block text-sm text-muted-foreground">
                    {t("collaborators.courses", { count: collaborator.courses })}
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => { /* View profile action */ }}>
                        {t("collaborators.viewProfile")}
                      </DropdownMenuItem>
                      {collaborator.role !== "admin" && (
                        <>
                          <DropdownMenuItem
                            onClick={() => changeCollaboratorRole(collaborator)}
                          >
                            {t("collaborators.changeRole", {
                              newRole:
                                collaborator.role === "team"
                                  ? t("collaborators.external")
                                  : t("collaborators.team")
                            })}
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-destructive"
                            onClick={() => removeCollaborator(collaborator.id)}
                          >
                            {t("collaborators.remove")}
                          </DropdownMenuItem>
                        </>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  )
}
