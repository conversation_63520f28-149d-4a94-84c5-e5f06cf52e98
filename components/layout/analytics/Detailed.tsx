import React from 'react'

import { useState, useEffect } from "react"
import {
  BarC<PERSON>,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { DetailedType } from '@/src/lib/repositories/analytics/detailed/DetailedRepository'
import { AnalyticsAPI } from "@/src/services/analyticsApi";
import en from "./locales/en.json";
import { useLocalization } from '@/src/hooks/useLocalization/client'

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"]

export default function Detailed() {
  const [detailed, setDetailed] = useState<DetailedType>({
    courseEnrollmentData: [],
    landingPageData: [],
    testCompletionData: [],
    testScoreDistribution: [],
    testTakerData: []
  })
  const [loading, setLoading] = useState(true);
  const { t } = useLocalization("public-test", { en });

  const fetchTests = async () => {
    try {
      const data = await AnalyticsAPI.AnalyticsDetailed({page: 1}).request();
      setDetailed(data);
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false)
    }
  };

  useEffect(() => {
    fetchTests();
  }, []);

  return (
    <Tabs defaultValue="test-takers" className="space-y-2">
      <TabsList>
        <TabsTrigger value="test-takers">{t("test_takers")}</TabsTrigger>
        <TabsTrigger value="tests">{t("tests")}</TabsTrigger>
        <TabsTrigger value="courses">{t("courses")}</TabsTrigger>
        <TabsTrigger value="landing-pages">{t("landing_pages")}</TabsTrigger>
      </TabsList>

      <TabsContent value="test-takers" className="space-y-4">
        <div className="grid gap-4 md:grid-cols-2">
          <Card className='shadow-none bg-sp-neutral-100 border-none rounded-3xl'>
            <CardHeader>
              <CardTitle>{t("test_takers_over_time")}</CardTitle>
              <CardDescription>{t("monthly_test_taker_count")}</CardDescription>
            </CardHeader>
            <CardContent className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={detailed.testTakerData}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="count" fill="#8884d8" name="Test Takers" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
          <Card className='shadow-none bg-sp-neutral-100 border-none rounded-3xl'>
            <CardHeader>
              <CardTitle>{t("test_taker_demographics")}</CardTitle>
              <CardDescription>{t("breakdown_by_role_experience")}</CardDescription>
            </CardHeader>
            <CardContent className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={[
                      { name: "Students", value: 45 },
                      { name: "Professionals", value: 30 },
                      { name: "Educators", value: 15 },
                      { name: "Others", value: 10 },
                    ]}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {detailed.testScoreDistribution?.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>
      </TabsContent>

      <TabsContent value="tests" className="space-y-4">
        <div className="grid gap-4 md:grid-cols-2">
          <Card className='shadow-none bg-sp-neutral-100 border-none rounded-3xl'>
            <CardHeader>
              <CardTitle>{t("test_completion_rate")}</CardTitle>
              <CardDescription>{t("tests_started_completed")}</CardDescription>
            </CardHeader>
            <CardContent className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={detailed.testCompletionData}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="started" fill="#8884d8" name="Tests Started" />
                  <Bar dataKey="completed" fill="#82ca9d" name="Tests Completed" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
          <Card className='shadow-none bg-sp-neutral-100 border-none rounded-3xl'>
            <CardHeader>
              <CardTitle>{t("test_score_distribution")}</CardTitle>
              <CardDescription>{t("score_ranges")}</CardDescription>
            </CardHeader>
            <CardContent className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={detailed.testScoreDistribution}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {detailed.testScoreDistribution?.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>
      </TabsContent>

      <TabsContent value="courses" className="space-y-4">
        <div className="grid gap-4 md:grid-cols-2">
          <Card className='shadow-none bg-sp-neutral-100 border-none rounded-3xl'>
            <CardHeader>
              <CardTitle>{t("course_enrollments")}</CardTitle>
              <CardDescription>{t("top_courses")}</CardDescription>
            </CardHeader>
            <CardContent className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  layout="vertical"
                  data={detailed.courseEnrollmentData}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 100,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis type="category" dataKey="name" />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="students" fill="#8884d8" name="Students" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
          <Card className='shadow-none bg-sp-neutral-100 border-none rounded-3xl'>
            <CardHeader>
              <CardTitle>{t("course_completion_rate")}</CardTitle>
              <CardDescription>{t("course_completion_percent")}</CardDescription>
            </CardHeader>
            <CardContent className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  layout="vertical"
                  data={[
                    { name: "JavaScript Basics", rate: 78 },
                    { name: "React Fundamentals", rate: 65 },
                    { name: "Node.js Essentials", rate: 72 },
                    { name: "CSS Mastery", rate: 85 },
                    { name: "Python for Beginners", rate: 80 },
                  ]}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 100,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" domain={[0, 100]} />
                  <YAxis type="category" dataKey="name" />
                  <Tooltip formatter={(value) => [`${value}%`, "Completion Rate"]} />
                  <Legend />
                  <Bar dataKey="rate" fill="#82ca9d" name="Completion Rate (%)" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>
      </TabsContent>

      <TabsContent value="landing-pages" className="space-y-4">
        <div className="grid gap-4 md:grid-cols-2">
          <Card className='shadow-none bg-sp-neutral-100 border-none rounded-3xl'>
            <CardHeader>
              <CardTitle>{t("landing_page_visitors")}</CardTitle>
              <CardDescription>{t("visitor_count_per_page")}</CardDescription>
            </CardHeader>
            <CardContent className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={detailed.landingPageData}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="visitors" fill="#8884d8" name="Visitors" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
          <Card className='shadow-none bg-sp-neutral-100 border-none rounded-3xl'>
            <CardHeader>
              <CardTitle>{t("conversion_rates")}</CardTitle>
              <CardDescription>{t("conversion_percent")}</CardDescription>
            </CardHeader>
            <CardContent className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={detailed.landingPageData.map((item) => ({
                    name: item.name,
                    rate: ((item.conversions / item.visitors) * 100).toFixed(1),
                  }))}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis domain={[0, 20]} />
                  <Tooltip formatter={(value) => [`${value}%`, "Conversion Rate"]} />
                  <Legend />
                  <Line type="monotone" dataKey="rate" stroke="#82ca9d" name="Conversion Rate (%)" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>
      </TabsContent>
    </Tabs>
  )
}
