import React from 'react'

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Clock } from "lucide-react"
import en from "./locales/en.json";
import { useLocalization } from '@/src/hooks/useLocalization/client';

export default function HeaderAnalytics({ timeRange, setTimeRange }) {
  const { t } = useLocalization("public-test", { en });

  return (
    <div className="flex justify-between items-center mb-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">{t("title")}</h1>
        <p className="text-muted-foreground">{t("description")}</p>
      </div>
      <div className="flex items-center gap-4">
        <Select value={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select time range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7days">{t("range_last_7_days")}</SelectItem>
            <SelectItem value="30days">{t("range_last_30_days")}</SelectItem>
            <SelectItem value="90days">{t("range_last_90_days")}</SelectItem>
            <SelectItem value="year">{t("range_last_year")}</SelectItem>
            <SelectItem value="all">{t("range_all_time")}</SelectItem>
          </SelectContent>
        </Select>
        <Button>
          <Clock className="mr-2 h-4 w-4" />
          {t("action_update_now")}
        </Button>
      </div>
    </div>
  )
}
