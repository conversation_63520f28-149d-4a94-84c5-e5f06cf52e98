import { Card, CardContent } from "@/components/ui/card";
import { STAT_KEYS, StatsItem } from "@/src/lib/repositories/analytics/summary/AnalyticsSummaryRepository";
import { Users, FileCheck, GraduationCap, Eye, ArrowUpRight, LucideIcon, PencilIcon } from "lucide-react";
import en from "./locales/en.json";
import { useLocalization } from "@/src/localization/functions/client";

export interface StatCardData extends StatsItem {
  title?: string
  index?: number,
  icon?: LucideIcon;
  timeRange?: string
}

interface StatsCardProps extends StatCardData { }

interface SummaryCardsProps {
  data?: StatCardData[];
  timeRange?: string
}

const StatsCard: React.FC<StatsCardProps> = ({ timeRange, uniq_id, title, value, change }) => {
  const { t } = useLocalization("public-test", { en });
  let note = '';
  switch (timeRange) {
    case "7days":
      note = t("range_last_7_days");
      break;
    case "30days":
      note = t("range_last_30_days");
      break;
    case "90days":
      note = t("range_last_90_days");
      break;
    case "year":
      note = t("range_last_year");
      break;
  }


  const Icon =
    uniq_id == STAT_KEYS.TOTAL_TEST_TAKERS ? Users :
      uniq_id == STAT_KEYS.TESTS_COMPLETED ? FileCheck :
        uniq_id == STAT_KEYS.COURSE_ENROLLMENTS ? GraduationCap :
          uniq_id == STAT_KEYS.LANDING_PAGE_VISITORS ? Eye : PencilIcon
  return (
    <Card className="shadow-none bg-sp-neutral-100 border-none rounded-3xl">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <h3 className="text-2xl font-bold mt-1">{value}</h3>
          </div>
          <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
            {!Icon ? null : (
              <Icon className="h-6 w-6 text-primary" />
            )}
          </div>
        </div>
        <div className="flex items-center mt-4 text-sm text-green-600">
          <ArrowUpRight className="h-4 w-4 mr-1" />
          <span>{change} {note}</span>
        </div>
      </CardContent>
    </Card>
  );
}

const SORT_ORDER = [
  STAT_KEYS.TOTAL_TEST_TAKERS,
  STAT_KEYS.TESTS_COMPLETED,
  STAT_KEYS.COURSE_ENROLLMENTS,
  STAT_KEYS.LANDING_PAGE_VISITORS,
];

export const SummaryCards: React.FC<SummaryCardsProps> = ({ data = [], timeRange }) => {
  const { t } = useLocalization("public-test", { en });

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
      {data.sort(
        (a, b) => SORT_ORDER.indexOf(a.uniq_id) - SORT_ORDER.indexOf(b.uniq_id)
      ).map((stat, index) => {
        const Icon =
          stat.uniq_id == STAT_KEYS.TOTAL_TEST_TAKERS ? Users :
            stat.uniq_id == STAT_KEYS.TESTS_COMPLETED ? FileCheck :
              stat.uniq_id == STAT_KEYS.COURSE_ENROLLMENTS ? GraduationCap :
                stat.uniq_id == STAT_KEYS.LANDING_PAGE_VISITORS ? Eye : PencilIcon

        const title =
          stat.uniq_id == STAT_KEYS.TOTAL_TEST_TAKERS ? t("total_test_takers") :
            stat.uniq_id == STAT_KEYS.TESTS_COMPLETED ? t("test_completed") :
              stat.uniq_id == STAT_KEYS.COURSE_ENROLLMENTS ? t("course_enrollments") :
                stat.uniq_id == STAT_KEYS.LANDING_PAGE_VISITORS ? t("landing_page_visitors") : ''
        return (
          <StatsCard timeRange={timeRange} key={index} index={index + 1} title={title} {...stat} />
        )
      })}
    </div>
  );
}
