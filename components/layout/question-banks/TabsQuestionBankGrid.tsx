"use client";

import { Question, QuestionBank } from "@/src/lib/repositories/questions/types";
import QuestionGroupCard from "./Grid/QuestionGroupCard";
import QuestionCard from "./Grid/QuestionCard";

interface TabsQuestionBankGridProps {
  questions: QuestionBank[];
  handleDelete: (item: QuestionBank) => void;
  handleClone: (item: QuestionBank) => void;
}

export default function TabsQuestionBankGrid({ questions, handleDelete, handleClone }: TabsQuestionBankGridProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {questions.map((item) =>
        item.type === "QUESTION_GROUP" ? (
          <QuestionGroupCard key={item.id} item={item} handleDelete={handleDelete} />
        ) : (
          <QuestionCard key={item.id} item={item} handleDelete={handleDelete} handleClone={handleClone} />
        )
      )}
    </div>
  );
}
