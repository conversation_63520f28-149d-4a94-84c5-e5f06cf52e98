"use client";

import Question<PERSON><PERSON><PERSON><PERSON> from "./Grid/QuestionGroupCard";
import QuestionCard from "./Grid/QuestionCard";
import { Question, QuestionBank, QuestionGroup } from "@/src/lib/repositories/questions/interface";

interface TabsQuestionBankGridProps {
  questions: Question[];
  handleDelete: (item: Question) => void;
  handleClone: (item: Question) => void;
}

export default function TabsQuestionBankGrid({ questions, handleDelete, handleClone }: TabsQuestionBankGridProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {questions.map((item) =>
        !item.type ? null : (
          <QuestionCard key={item.id} item={item as Question} handleDelete={handleDelete} handleClone={handleClone} />
        )
      )}
    </div>
  );
}
