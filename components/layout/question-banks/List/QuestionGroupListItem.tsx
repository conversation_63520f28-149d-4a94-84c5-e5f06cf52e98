"use client";

import {
  MoreH<PERSON>zon<PERSON>,
  Folder,
  Tag,
  Clock,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { useLocalization } from "@/src/localization/functions/client";
import { QuestionGroup } from "@/src/lib/repositories/questions/interface";
import { locales } from "../locales";

interface QuestionGroupListItemProps {
  item: QuestionGroup;
  handleDelete: (item: QuestionGroup) => void;
}

export default function QuestionGroupListItem({ item, handleDelete }: QuestionGroupListItemProps) {
  const { t } = useLocalization("public-test", locales);

  return (
    <div className="flex items-start justify-between border rounded-lg p-4 hover:bg-muted/50 bg-white shadow-sm">
      <div className="flex-1 pr-4">
        <div className="flex items-center gap-2 mb-1">
          <Folder className="h-5 w-5 text-primary" />
          <h3 className="font-medium text-base line-clamp-1">{item.name}</h3>
        </div>
        <p className="text-sm text-muted-foreground mb-2">{item.description}</p>
        <div className="flex items-center text-sm text-gray-500 gap-2 mb-2">
          <span>
            {item.questionCount} {t("card.questions")}
          </span>
          <span>•</span>
          <span className="flex items-center">
            <Clock className="h-3 w-3 mr-1" />
            {new Date(item.updatedAt).toLocaleDateString()}
          </span>
        </div>
        <div className="flex flex-wrap gap-2">
          {item.tags.map((tag) => (
            <Badge key={tag} variant="outline" className="text-xs">
              <Tag className="h-3 w-3 mr-1" />
              {tag}
            </Badge>
          ))}
        </div>
      </div>

      <div className="flex flex-col items-end justify-between h-full gap-2">
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            {t("card.viewQuestionGroups")}
          </Button>
          <Button variant="outline" size="sm">
            {t("card.addToTest")}
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>{t("card.actions")}</DropdownMenuLabel>
              <DropdownMenuItem>{t("card.edit")}</DropdownMenuItem>
              <DropdownMenuItem>{t("card.duplicate")}</DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>{t("card.share")}</DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handleDelete(item)}
                className="text-red-600"
              >
                {t("card.delete")}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
}
