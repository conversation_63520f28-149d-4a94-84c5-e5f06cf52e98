"use client";

import { Question } from "@/src/lib/repositories/questions/interface";
import React from "react";
import QuestionGroupListItem from "./List/QuestionGroupListItem";
import QuestionListItem from "./List/QuestionListItem";

interface TabsQuestionBankListProps {
  questions: Question[];
  handleDelete: (item: Question) => void;
  handleClone: (item: Question) => void;
}

export default function TabsQuestionBankList({ questions, handleDelete, handleClone }: TabsQuestionBankListProps) {
  return (
    <div className="flex flex-col gap-3">
      {questions.map((item) =>
        !item.type ? null: (
          <QuestionListItem key={item.id} item={item} handleDelete={handleDelete} handleClone={handleClone} />
        )
      )}
    </div>
  );
}
