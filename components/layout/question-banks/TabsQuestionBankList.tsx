"use client";

import { Question, QuestionBank } from "@/src/lib/repositories/questions/types";
import React from "react";
import QuestionGroupListItem from "./List/QuestionGroupListItem";
import QuestionListItem from "./List/QuestionListItem";

interface TabsQuestionBankListProps {
  questions: QuestionBank[];
  handleDelete: (item: QuestionBank) => void;
  handleClone: (item: QuestionBank) => void;
}

export default function TabsQuestionBankList({ questions, handleDelete, handleClone }: TabsQuestionBankListProps) {
  return (
    <div className="flex flex-col gap-3">
      {questions.map((item) =>
        item.type === "QUESTION_GROUP" ? (
          <QuestionGroupListItem key={item.id} item={item} handleDelete={handleDelete} />
        ) : (
          <QuestionListItem key={item.id} item={item} handleDelete={handleDelete} handleClone={handleClone} />
        )
      )}
    </div>
  );
}
