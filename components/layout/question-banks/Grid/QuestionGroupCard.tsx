"use client";

import {
  More<PERSON><PERSON><PERSON><PERSON>,
  Folder,
  Tag,
  Clock,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { useLocalization } from "@/src/localization/functions/client";
import { Question, QuestionGroup } from "@/src/lib/repositories/questions/interface";
import { locales } from "../locales";

interface QuestionGroupCardProps {
  item: QuestionGroup;
  handleDelete: (item: QuestionGroup) => void;
}

export default function QuestionGroupCard({ item, handleDelete }: QuestionGroupCardProps) {
  const { t } = useLocalization("public-test", locales);

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-2">
            <Folder className="h-5 w-5 text-primary" />
            <CardTitle className="text-lg line-clamp-1">{item.name}</CardTitle>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>{t("card.actions")}</DropdownMenuLabel>
              <DropdownMenuItem>{t("card.edit")}</DropdownMenuItem>
              <DropdownMenuItem>{t("card.duplicate")}</DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>{t("card.share")}</DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handleDelete(item)}
                className="text-red-600"
              >
                {t("card.delete")}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent>
        <p className="text-sm text-muted-foreground mb-2">{item.description}</p>
        <div className="flex items-center text-sm text-muted-foreground gap-2 mb-2">
          <span>
            {item.questionCount} {t("card.questions")}
          </span>
          <span>•</span>
          <span className="flex items-center">
            <Clock className="h-3 w-3 mr-1" />
            {new Date(item.updatedAt).toLocaleDateString()}
          </span>
        </div>
        <div className="flex flex-wrap gap-2">
          {item.tags.map((tag) => (
            <Badge key={tag} variant="outline" className="text-xs">
              <Tag className="h-3 w-3 mr-1" />
              {tag}
            </Badge>
          ))}
        </div>
      </CardContent>

      <CardFooter className="pt-0 flex justify-between">
        <Button variant="outline" size="sm">
          {t("card.viewQuestionGroups")}
        </Button>
        <Button variant="outline" size="sm">
          {t("card.addToTest")}
        </Button>
      </CardFooter>
    </Card>
  );
}
