type LinkItem = {
  label: string;
  url: string;
};

export const FooterLoginPage: React.FC = () => {
  const links: LinkItem[] = [
    { label: 'creative tim', url: '/' },
    { label: 'about us', url: '/' },
    { label: 'blog', url: '/' },
    { label: 'licence', url: '/' },
  ];

  return (
    <div className="flex md:flex-row flex-col items-center justify-between pt-3 pb-6 gap-2">
      <p className="text-body-5 font-inter font-normal text-sp-neutral-700">
        © 2025, made by{' '}
        <span className="text-sp-primary font-semibold">Skill Pintar</span> learning is sharing.
      </p>
      <div className="flex flex-row  items-center md:gap-6 gap-2">
        {links.map((link, index) => (
          <a key={index} href={link.url}>
            <p className="text-body-5 text-sp-neutral-700 capitalize font-inter font-normal">
              {link.label}
            </p>
          </a>
        ))}
      </div>
    </div>
  );
};