'use client'

import React, { useState } from 'react';
import Image from 'next/image';
import { ArrowLeftIcon, ArrowRightIcon } from 'lucide-react'
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';

interface loginType {
  workspaceName: string;
}

export const BodyLoginPage: React.FC = () => {
  const [setupState, setSetupState] = useState<loginType>({
    workspaceName: "Achmad’s Workspace",
  });

  const handleContinue = () => {
    console.log("Workspace Name:", setupState.workspaceName);
  };

  return (
    <div className='order-first bg-sp-neutral-100 p-3 h-full flex flex-1 rounded-3xl justify-between'>
      <div className='order-first flex flex-col justify-center items-start p-12 gap-20 xl:w-auto w-full'>
        <div className='flex flex-col gap-10 justify-center items-start'>
          <Button variant="outline" className='py-5 px-6 bg-sp-background text-sp-neutral-800'>
            <ArrowLeftIcon className="h-4 w-4" />
            <span className='font-inter text-body-3 font-normal ml-2'>Back</span>
          </Button>
          <div className='flex flex-col gap-2'>
            <p className='font-clash text-heading-3'>Setup Dashboard</p>
            <p className='font-clash text-body-3 text-sp-neutral-600 font-normal'>
              Configure and personalize your dashboard to display information.
            </p>
          </div>
        </div>

        <div className='flex flex-col w-full gap-6'>
          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="name">Workspace Name</Label>
            <Input
              type="text"
              id="name"
              placeholder="Name"
              value={setupState.workspaceName}
              onChange={(e) =>
                setSetupState({ ...setupState, workspaceName: e.target.value })
              }
            />
          </div>
          <Button onClick={handleContinue}>
            Continue
            <ArrowRightIcon className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </div>

      <div className='bg-sp-neutral-400 w-1/2 rounded-2xl xl:flex hidden flex-col justify-center items-center relative'>
        <div className='flex w-full h-full relative'>
          <Image
            src="/images/login-banner-pattern.png"
            alt="banner-login"
            fill
            className="object-cover object-top"
          />
        </div>
        <div className='absolute flex flex-col justify-center items-center gap-5'>
          <Image
            src="/images/login-banner-ilustration.png"
            alt="banner-illustration"
            width={420}
            height={420}
          />
          <div className='flex flex-col gap-5 w-[80%]'>
            <p className='text-body-1 font-inter text-center text-sp-neutral-800'>
              "The only thing that interferes with my learning is my education."
            </p>
            <p className='text-body-3 font-inter text-center text-sp-neutral-600'>
              ~ Albert Einstein ~
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

