
'use client'

import type React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>, Clock, Star, TrendingUp } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { MyTests } from "@/src/lib/repositories/my-tests/MyTestsRepository"

import { useLocalization } from '@/src/hooks/useLocalization/client'
import en from "./locales/en.json"

export default function TabsAchievements({ completedTests }: { completedTests: MyTests[] }) {
  const { t } = useLocalization("public-test", { en });

  const totalTests = completedTests.length
  const averageScore = Math.round(completedTests.reduce((sum, test) => sum + test.score, 0) / totalTests)
  const totalBadges = completedTests.reduce((sum, test) => sum + test.badges.length, 0)
  const highestScore = Math.max(...completedTests.map((test) => test.score))

  return (
    <>
      {/* Achievement stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="shadow-none bg-sp-neutral-100 border-none rounded-3xl">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t("tests_completed")}</p>
                <p className="text-2xl font-bold">{totalTests}</p>
              </div>
              <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                <ClipboardIcon className="h-6 w-6 text-primary" />
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="shadow-none bg-sp-neutral-100 border-none rounded-3xl">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t("average_score")}</p>
                <p className="text-2xl font-bold">{averageScore}%</p>
              </div>
              <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                <BarChart className="h-6 w-6 text-primary" />
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="shadow-none bg-sp-neutral-100 border-none rounded-3xl">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t("badges_earned")}</p>
                <p className="text-2xl font-bold">{totalBadges}</p>
              </div>
              <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                <Award className="h-6 w-6 text-primary" />
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="shadow-none bg-sp-neutral-100 border-none rounded-3xl">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t("highest_score")}</p>
                <p className="text-2xl font-bold">{highestScore}%</p>
              </div>
              <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                <Star className="h-6 w-6 text-primary" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Badges */}
      <Card className="shadow-none bg-sp-neutral-100 border-none rounded-3xl">
        <CardContent className="pt-6">
          <h3 className="text-lg font-semibold mb-4">{t("earned_badges")}</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {[
              { name: "Perfect Score", count: 2, icon: Star, color: "bg-yellow-100 text-yellow-800" },
              { name: "Fast Learner", count: 1, icon: Clock, color: "bg-blue-100 text-blue-800" },
              { name: "Top 10%", count: 1, icon: TrendingUp, color: "bg-green-100 text-green-800" },
              { name: "Persistent", count: 1, icon: Award, color: "bg-purple-100 text-purple-800" },
              { name: "Quick Thinker", count: 1, icon: Clock, color: "bg-blue-100 text-blue-800" },
              { name: "Creative Thinker", count: 1, icon: Award, color: "bg-pink-100 text-pink-800" },
              { name: "Analytical Thinker", count: 1, icon: BarChart, color: "bg-indigo-100 text-indigo-800" },
              { name: "Expert", count: 1, icon: Star, color: "bg-yellow-100 text-yellow-800" },
            ].map((badge, index) => {
              const Icon = badge.icon
              return (
                <div key={index} className="border rounded-lg p-4 flex flex-col items-center text-center">
                  <div className={`h-12 w-12 rounded-full ${badge.color} flex items-center justify-center mb-2`}>
                    <Icon className="h-6 w-6" />
                  </div>
                  <p className="font-medium text-sm">{badge.name}</p>
                  <p className="text-xs text-muted-foreground">{t("earned")} {badge.count} {t("times")}</p>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Skills */}
      <Card className="shadow-none bg-sp-neutral-100 border-none rounded-3xl">
        <CardContent className="pt-6">
          <h3 className="text-lg font-semibold mb-4">Skills Progress</h3>
          <div className="space-y-4">
            {[
              { skill: "HTML", progress: 90 },
              { skill: "CSS", progress: 75 },
              { skill: "JavaScript", progress: 65 },
              { skill: "React", progress: 50 },
              { skill: "Node.js", progress: 40 },
              { skill: "UI/UX Design", progress: 85 },
              { skill: "Git & GitHub", progress: 95 },
              { skill: "Database Design", progress: 80 },
            ].map((skill) => (
              <div key={skill.skill} className="space-y-1">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium">{skill.skill}</p>
                  <p className="text-sm text-muted-foreground">{skill.progress}%</p>
                </div>
                <Progress value={skill.progress} className="h-2" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </>
  )
}

function ClipboardIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <rect width="8" height="4" x="8" y="2" rx="1" ry="1" />
      <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" />
    </svg>
  )
}

