import React from 'react'
import Image from "next/image"
import { <PERSON>, <PERSON>, Star, Timer } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"

import { useLocalization } from '@/src/localization/functions/client'
import en from "./locales/en.json"
import { MyTestDetail } from '@/src/lib/repositories/my-tests/MyTestsRepository'

export default function DetailTestAnalytics({ test, setShowProModal }: { test: MyTestDetail, setShowProModal: (props: boolean) => void }) {
  const { t } = useLocalization("public-test", { en });

  return (
    <Tabs defaultValue="questions" className='space-y-4'>
      <TabsList>
        <TabsTrigger value="questions">{t("dashboard_question_analytics")}</TabsTrigger>
        <TabsTrigger value="recommendations">{t("recommendations")}</TabsTrigger>
      </TabsList>

      <TabsContent value="questions" className="space-y-4">
        <Card className='shadow-none bg-sp-neutral-100 border-none rounded-3xl'>
          <CardHeader>
            <CardTitle>{t("dashboard_question_analytics")}</CardTitle>
            <CardDescription>{t("review_answers_detail")}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Sample questions - first 5 visible */}
              {test.questionBreakdown.map((question, index) => (
                <div key={index} className="border-b pb-4 last:border-0">
                  <div className="flex items-start gap-3">
                    <div
                      className={`flex-shrink-0 h-6 w-6 rounded-full flex items-center justify-center text-white ${question.isCorrect ? "bg-green-500" : "bg-red-500"}`}
                    >
                      {question.isCorrect ? "✓" : "✗"}
                    </div>
                    <div className="flex-1">
                      <p className="font-medium mb-2">
                        {index + 1}. {question.question}
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-muted-foreground">{t("your_answer")}:</p>
                          <p className={question.isCorrect ? "text-green-600" : "text-red-600"}>
                            {question.yourAnswer}
                          </p>
                        </div>
                        {!question.isCorrect && (
                          <div>
                            <p className="text-muted-foreground">{t("correct_answer")}:</p>
                            <p className="text-green-600">{question.correctAnswer}</p>
                          </div>
                        )}
                      </div>
                      <div className="mt-2">
                        <Badge variant="outline" className="text-xs">
                          {question.category}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              {/* Blurred pro section */}
              <div className="relative">
                <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex flex-col items-center justify-center z-10">
                  <Lock className="h-8 w-8 text-primary mb-2" />
                  <h3 className="text-lg font-semibold mb-1">{t("detailed_analytics")}</h3>
                  <p className="text-center text-muted-foreground mb-4 max-w-md">
                    {t("upgrade_to_pro_analytics")}
                  </p>
                  <Button onClick={() => setShowProModal(true)}>{t("upgrade_to_pro_short")}</Button>
                </div>

                {/* Blurred content */}
                <div className="opacity-20 pointer-events-none">
                  {Array.from({ length: 5 }).map((_, index) => (
                    <div key={index} className="border-b pb-4 last:border-0">
                      <div className="flex items-start gap-3">
                        <div className="flex-shrink-0 h-6 w-6 rounded-full bg-gray-300"></div>
                        <div className="flex-1">
                          <p className="font-medium mb-2 bg-gray-200 h-5 rounded w-3/4"></p>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <p className="bg-gray-200 h-4 rounded w-1/4 mb-1"></p>
                              <p className="bg-gray-200 h-4 rounded w-1/2"></p>
                            </div>
                            <div>
                              <p className="bg-gray-200 h-4 rounded w-1/4 mb-1"></p>
                              <p className="bg-gray-200 h-4 rounded w-1/2"></p>
                            </div>
                          </div>
                          <div className="mt-2">
                            <div className="bg-gray-200 h-4 rounded w-16"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className='shadow-none bg-sp-neutral-100 border-none rounded-3xl'>
          <CardHeader>
            <CardTitle>{t("performance_insights")}</CardTitle>
            <CardDescription>{t("ai_powered_analytics")}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="relative">
              <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex flex-col items-center justify-center z-10">
                <Lock className="h-8 w-8 text-primary mb-2" />
                <h3 className="text-lg font-semibold mb-1">{t("pro_feature")}</h3>
                <p className="text-center text-muted-foreground mb-4 max-w-md">
                  {t("upgrade_to_pro_ai")}
                </p>
                <Button onClick={() => setShowProModal(true)}>{t("upgrade_to_pro_short")}</Button>
              </div>

              {/* Blurred content */}
              <div className="opacity-20 pointer-events-none">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-semibold mb-3">{t("strengths")}</h3>
                    <ul className="space-y-2">
                      {Array.from({ length: 3 }).map((_, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <div className="h-5 w-5 rounded-full bg-gray-200 flex-shrink-0 mt-0.5"></div>
                          <div className="bg-gray-200 h-5 rounded w-full"></div>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-3">{t("areas_to_improve")}</h3>
                    <ul className="space-y-2">
                      {Array.from({ length: 3 }).map((_, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <div className="h-5 w-5 rounded-full bg-gray-200 flex-shrink-0 mt-0.5"></div>
                          <div className="bg-gray-200 h-5 rounded w-full"></div>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
                <div className="mt-6">
                  <h3 className="font-semibold mb-3">{t("personalized_study_plan")}</h3>
                  <div className="space-y-3">
                    {Array.from({ length: 3 }).map((_, index) => (
                      <div key={index} className="bg-gray-200 h-5 rounded w-full"></div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="recommendations" className="space-y-4">
        <Card className='shadow-none bg-sp-neutral-100 border-none rounded-3xl'>
          <CardHeader>
            <CardTitle>{t("recommended_courses")}</CardTitle>
            <CardDescription>{t("based_on_results")}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {test.recommendedCourses.map((course, index) => (
                <Card key={index} className="overflow-hidden border-0 shadow-md flex flex-col">
                  <div className="relative aspect-[3/2] w-full">
                    <Image
                      src={course.image || "/placeholder.svg"}
                      alt={course.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <CardContent className="p-4 flex flex-1 flex-col">
                    <div className="flex flex-1 flex-col">
                      <h3 className="font-semibold">{course.title}</h3>
                      <p className="text-sm text-muted-foreground line-clamp-2 mt-1">{course.description}</p>
                      <div className="flex items-center mt-3 text-xs text-muted-foreground">
                        <Clock className="h-3 w-3 mr-1" />
                        <span className="mr-3">{course.duration}</span>
                        <Star className="h-3 w-3 mr-1" />
                        <span className="mr-3">{course.instructor}</span>
                        <Timer className="h-3 w-3 mr-1" />
                        <span>{course.level}</span>
                      </div>
                    </div>
                    <Button className="w-full mt-3" variant="outline" size="sm">
                      {t("view_course")}
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className='shadow-none bg-sp-neutral-100 border-none rounded-3xl'>
          <CardHeader>
            <CardTitle>{t("skill_improvement_plan")}</CardTitle>
            <CardDescription>{t("skill_plan_description")}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="relative">
              <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex flex-col items-center justify-center z-10">
                <Lock className="h-8 w-8 text-primary mb-2" />
                <h3 className="text-lg font-semibold mb-1">{t("pro_feature")}</h3>
                <p className="text-center text-muted-foreground mb-4 max-w-md">
                  {t("upgrade_to_pro_skills")}
                </p>
                <Button onClick={() => setShowProModal(true)}>{t("upgrade_to_pro_short")}</Button>
              </div>

              {/* Blurred content */}
              <div className="opacity-20 pointer-events-none">
                <div className="space-y-6">
                  <div>
                    <h3 className="font-semibold mb-3">{t("short_term_goals")}</h3>
                    <ul className="space-y-2">
                      {Array.from({ length: 3 }).map((_, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <div className="h-5 w-5 rounded-full bg-gray-200 flex-shrink-0 mt-0.5"></div>
                          <div className="bg-gray-200 h-5 rounded w-full"></div>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-3">{t("medium_term_goals")}</h3>
                    <ul className="space-y-2">
                      {Array.from({ length: 3 }).map((_, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <div className="h-5 w-5 rounded-full bg-gray-200 flex-shrink-0 mt-0.5"></div>
                          <div className="bg-gray-200 h-5 rounded w-full"></div>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-3">{t("recommended_resources")}</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {Array.from({ length: 4 }).map((_, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <div className="h-8 w-8 rounded bg-gray-200 flex-shrink-0"></div>
                          <div className="flex-1">
                            <div className="bg-gray-200 h-4 rounded w-3/4 mb-1"></div>
                            <div className="bg-gray-200 h-3 rounded w-1/2"></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  )
}
