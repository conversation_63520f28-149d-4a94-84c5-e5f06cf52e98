'use client'

import React from 'react'
import Image from "next/image"
import { Award, ExternalLink, } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"

import { useLocalization } from '@/src/localization/functions/client'
import en from "./locales/en.json"
import { MyTestDetail } from '@/src/lib/repositories/my-tests/MyTestsRepository'

export default function DetailTestOverview({ test }: { test: MyTestDetail }) {
  const { t } = useLocalization("public-test", { en });

  return (
    <div className="grid gap-4 md:grid-cols-3">
      <Card className="md:col-span-2 shadow-none bg-sp-neutral-100 border-none rounded-3xl">
        <CardHeader className="pb-2">
          <CardTitle>{t("test_overview")}</CardTitle>
          <CardDescription>{test.description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-6">
            <div className="flex-1">
              <div className="flex justify-between items-center mb-2">
                <h3 className="font-semibold">{t("your_score")}</h3>
                <span className="text-2xl font-bold">{test.score}%</span>
              </div>
              <Progress value={test.score} className="h-2 mb-4" />

              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="flex flex-col">
                  <span className="text-sm text-muted-foreground">{t("correct_answers_summary")}</span>
                  <span className="font-medium">
                    {test.correctAnswers}/{test.totalQuestions}
                  </span>
                </div>
                <div className="flex flex-col">
                  <span className="text-sm text-muted-foreground">{t("time_spent")}</span>
                  <span className="font-medium">{test.timeSpent}</span>
                </div>
                <div className="flex flex-col">
                  <span className="text-sm text-muted-foreground">{t("date_taken")}</span>
                  <span className="font-medium">{test.date.toLocaleDateString()}</span>
                </div>
                <div className="flex flex-col">
                  <span className="text-sm text-muted-foreground">{t("category")}</span>
                  <span className="font-medium">{test.category}</span>
                </div>
              </div>

              <div className="flex flex-wrap gap-2">
                {test.badges.map((badge, index) => (
                  <Badge key={index} variant="secondary" className="bg-yellow-100 text-yellow-800 border-0">
                    <Award className="h-3 w-3 mr-1" />
                    {badge}
                  </Badge>
                ))}
              </div>
            </div>

            <div className="flex-1">
              <h3 className="font-semibold mb-3">{t("category_breakdown")}</h3>
              <div className="space-y-3">
                {test.categoryBreakdown.map((category, index) => (
                  <div key={index} className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span>{category.category}</span>
                      <span>
                        {category.correct}/{category.total} ({category.percentage}%)
                      </span>
                    </div>
                    <Progress value={category.percentage} className="h-2" />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className='shadow-none bg-sp-neutral-100 border-none rounded-3xl'>
        <CardHeader className="pb-2">
          <CardTitle>{t("instructor")}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-3 mb-4">
            <div className="relative h-12 w-12 rounded-full overflow-hidden">
              <Image
                src={test.instructor.avatar || "/placeholder.svg"}
                alt={test.instructor.name}
                fill
                className="object-cover"
              />
            </div>
            <div>
              <p className="font-medium">{test.instructor.name}</p>
              <p className="text-sm text-muted-foreground">{t("test_creator")}</p>
            </div>
          </div>
          <Separator className="my-4" />
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">{t("test_duration")}</span>
              <span className="text-sm font-medium">{test.duration}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">{t("questions")}</span>
              <span className="text-sm font-medium">{test.totalQuestions}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">{t("category")}</span>
              <span className="text-sm font-medium">{test.category}</span>
            </div>
          </div>
          <Separator className="my-4" />
          <div className="flex justify-between">
            <Button variant="outline" size="sm" className="w-full">
              <ExternalLink className="h-4 w-4 mr-2" />
              {t("view_certificate")}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
