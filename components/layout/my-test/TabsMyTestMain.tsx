'use client'

import React from 'react'
import { useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import TabsTests from './TabsTests'
import TabsAchievements from './TabsAchievements'
import { MyTests } from '@/src/lib/repositories/my-tests/MyTestsRepository'
import { MyTestsAPI } from "@/src/services/myTestsApi"
import { useLocalization } from '@/src/localization/functions/client'
import en from "./locales/en.json"

export default function TabsMyTestMain() {
  const searchParams = useSearchParams()
  const [activeTab, setActiveTab] = useState("tests")
  const [completedTests, setCompletedTests] = useState<MyTests[]>([])
  const [loading, setLoading] = useState(true);
  const { t } = useLocalization("dashboard.my-test", { en });

  useEffect(() => {
    const fetchTests = async () => {
      try {
        const data = await MyTestsAPI.MyTests({page: 1}).request();
        setCompletedTests(data);
      } catch (err) {
        console.error(err);
      } finally {
        setLoading(false)
      }
    };
    fetchTests();
  }, []);

  useEffect(() => {
    const tab = searchParams.get("tab")
    if (tab === "achievements") {
      setActiveTab("achievements")
    }
  }, [searchParams])

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
      <TabsList>
        <TabsTrigger value="tests">{t("completed_tests")}</TabsTrigger>
        <TabsTrigger value="achievements">{t("achievements")}</TabsTrigger>
      </TabsList>

      <TabsContent value="tests" className="space-y-4">
        <TabsTests completedTests={completedTests}/>
      </TabsContent>

      <TabsContent value="achievements" className="space-y-6">
        <TabsAchievements completedTests={completedTests}/>
      </TabsContent>
    </Tabs>
  )
}
