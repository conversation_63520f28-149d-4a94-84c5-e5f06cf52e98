'use client'

import React, { useEffect, useMemo } from 'react'
import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { Calendar, Filter, Search, TrendingUp } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { MyTests } from '@/src/lib/repositories/my-tests/MyTestsRepository'
import { useLocalization } from '@/src/hooks/useLocalization/client'
import en from "./locales/en.json"

export default function TabsTests({ completedTests }: { completedTests: MyTests[] }) {
  const [searchQuery, setSearchQuery] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [sortBy, setSortBy] = useState("recent")
  const { t } = useLocalization("public-test", { en });

  const filteredTests = useMemo(() =>
    completedTests
      .filter((test) => {
        const matchesSearch = test?.title.toLowerCase().includes(searchQuery.toLowerCase())
        const matchesCategory = categoryFilter === "all" || test?.category === categoryFilter
        return matchesSearch && matchesCategory
      })
      .sort((a, b) => {
        if (sortBy === "recent") {
          return new Date(b.date).getTime() - new Date(a.date).getTime()
        } else if (sortBy === "score-high") {
          return b.score - a.score
        } else if (sortBy === "score-low") {
          return a.score - b.score
        }
        return 0
      })
    ,
    [completedTests, categoryFilter, searchQuery]
  )

  const categories = useMemo(() => ["all", ...Array.from(new Set(completedTests.map((test) => test?.category)))], [completedTests])

  return (
    <Card className="shadow-none bg-sp-neutral-100 border-none rounded-3xl p-4 flex flex-col gap-4">
      {/* Filters */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder={t("search_tests") + '...'}
            className="pl-8 rounded-full pr-3 py-2"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger className="w-full md:w-[180px] rounded-full">
            <Filter className="mr-2 h-4 w-4" />
            <SelectValue placeholder="Category" />
          </SelectTrigger>
          <SelectContent>
            {categories.map((category) => (
              <SelectItem key={category} value={category}>
                {category === "all" ? "All Categories" : category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select value={sortBy} onValueChange={setSortBy}>
          <SelectTrigger className="w-full md:w-[180px] rounded-full">
            <TrendingUp className="mr-2 h-4 w-4" />
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="recent">{t("most_recent")}</SelectItem>
            <SelectItem value="score-high">{t("highest_score")}</SelectItem>
            <SelectItem value="score-low">{t("lowest_score")}</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Test cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredTests.map((test) => (
          <Card key={test?.id} className="overflow-hidden">
            <div className="relative aspect-[3/2] w-full">
              <Image src={test?.image || "/placeholder.svg"} alt={test?.title} fill className="object-cover" />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
              <div className="absolute bottom-3 left-3 right-3">
                <div className="flex justify-between items-center">
                  <Badge
                    variant="outline"
                    className={`
                          ${test?.score >= 90 ? "bg-green-100 text-green-800" : ""}
                          ${test?.score >= 70 && test?.score < 90 ? "bg-blue-100 text-blue-800" : ""}
                          ${test?.score >= 50 && test?.score < 70 ? "bg-orange-100 text-orange-800" : ""}
                          ${test?.score < 50 ? "bg-red-100 text-red-800" : ""}
                          border-0
                        `}
                  >
                    {t("score")}: {test?.score}%
                  </Badge>
                  <div className="flex gap-1">
                    {test?.badges?.slice(0, 2).map((badge, index) => (
                      <Badge key={index} variant="secondary" className="bg-yellow-100 text-yellow-800 border-0">
                        {badge}
                      </Badge>
                    ))}
                    {test?.badges?.length > 2 && (
                      <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 border-0">
                        +{test?.badges?.length - 2}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </div>
            <CardContent className="p-4">
              <h3 className="font-semibold line-clamp-1">{test?.title}</h3>
              <div className="flex items-center mt-1 text-xs text-muted-foreground">
                <Calendar className="h-3 w-3 mr-1" />
                <span>{test?.date.toLocaleDateString()}</span>
              </div>
              <div className="mt-3 space-y-2">
                <div className="flex justify-between text-sm">
                  <span>{t("correct_answers")}</span>
                  <span className="font-medium">
                    {test?.correctAnswers}/{test?.totalQuestions}
                  </span>
                </div>
                <Progress value={(test?.correctAnswers / test?.totalQuestions) * 100} className="h-2" />
              </div>
              <div className="flex items-center justify-between mt-4">
                <div className="flex items-center">
                  <div className="relative h-6 w-6 rounded-full overflow-hidden">
                    <Image
                      src={test?.instructor.avatar || "/placeholder.svg"}
                      alt={test?.instructor.name || ''}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <span className="text-xs ml-2">{test?.instructor.name}</span>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" asChild>
                    <Link href={`/dashboard/my-tests/${test?.id}`}>{t("details")}</Link>
                  </Button>
                  <Button size="sm">{t("retake")}</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredTests.length === 0 && (
        <div className="text-center py-12 border rounded-lg">
          <p className="text-muted-foreground">{t("no_tests_found")}</p>
        </div>
      )}
    </Card>
  )
}
