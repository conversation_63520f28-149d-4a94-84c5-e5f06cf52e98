import Link from "next/link"
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Course } from "@/src/lib/repositories/courses/CoursesRepository"

import { useLocalization } from "@/src/localization/functions/client"
import en from "./locales/en.json"

export function CourseCard({ course }: { course: Course }) {
  const { t } = useLocalization("public-test", { en });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", { year: "numeric", month: "short", day: "numeric" })
  }

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold line-clamp-2">{course.title}</h3>
        </div>
        <div className="flex items-center text-sm text-muted-foreground">
          <Calendar className="mr-1 h-4 w-4" />
          {t("created_on")} {course.createdAt?.toLocaleDateString()}
        </div>
      </CardHeader>
      <CardContent className="flex-grow">
        <p className="text-sm text-muted-foreground line-clamp-3 mb-4">{course.summary}</p>

        <div className="space-y-3">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center">
              <Users className="mr-1 h-4 w-4" />
              <span>{course.students} {t("students")}</span>
            </div>
            <span className="font-medium">{course.completionRate}% {t("completion")}</span>
          </div>
          <Progress value={course.completionRate} className="h-2" />

          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center">
              <BarChart className="mr-1 h-4 w-4" />
              <span>{t("engagement")}</span>
            </div>
            <span className="font-medium">{course.engagement}%</span>
          </div>
          <Progress value={course.engagement} className="h-2" />
        </div>
      </CardContent>
      <CardFooter>
        <Button asChild className="w-full">
          <Link href={`/dashboard/created-courses-2/${course.id}`}>{t("edit_course")}</Link>
        </Button>
      </CardFooter>
    </Card>
  )
}
