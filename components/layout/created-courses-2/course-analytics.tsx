"use client"

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  <PERSON><PERSON><PERSON> as RechartsBar<PERSON>hart,
  <PERSON>,
  LineChart as RechartsLineChart,
  Line,
  <PERSON><PERSON>hart as RechartsPieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts"
import { useEffect, useState } from "react"
import { CourseAnalyticsResponse, CourseAnalyticsTotal, CourseCompletionEntry, CourseEngagementEntry, CourseSectionCompletionEntry, CourseViewData } from "@/src/lib/repositories/courses/CoursesRepository"
import { CoursesAPI } from "@/src/services/coursesApi"

import { useLocalization } from "@/src/hooks/useLocalization/client"
import en from "./locales/en.json"

interface CourseAnalyticsProps {
  courseId: string
}

export function CourseAnalytics({ courseId }: CourseAnalyticsProps) {
  const [loading, setLoading] = useState(true);
  const [viewsData, setViewsData] = useState<CourseViewData[]>([])
  const [completionData, setCompletionData] = useState<CourseCompletionEntry[]>([])
  const [engagementData, setEngagementData] = useState<CourseEngagementEntry[]>([])
  const [colors, setColors] = useState<string[]>([])
  const [sectionCompletionData, setSectionCompletionData] = useState<CourseSectionCompletionEntry[]>([])
  const [total, setTotal] = useState<CourseAnalyticsTotal>({
    totalStudents: 0,
    completionRate: 0,
    averageWatchTime: 0,
    totalComments: 0,
  })
  const COLORS = colors
  const { t } = useLocalization("public-test", { en });

  const fetchData = async () => {
    try {
      const data = await CoursesAPI.CourseAnalytics({page: 1}).request();
      setViewsData(data.items.viewsData);
      setCompletionData(data.items.completionData)
      setEngagementData(data.items.engagementData)
      setColors(data.items.colors)
      setSectionCompletionData(data.items.sectionCompletionData)
      setTotal(data.dashboard)
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false)
    }
  };

  useEffect(() => {
    if (courseId.length > 0) fetchData();
  }, [courseId]);

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("total_students")}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{total.totalStudents}</div>
            <p className="text-xs text-muted-foreground">+18% {t("total_students_change")}</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("completion_rate")}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{total.completionRate}%</div>
            <p className="text-xs text-muted-foreground">+5% {t("completion_rate_change")}</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("avg_watch_time")}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{total.averageWatchTime} min</div>
            <p className="text-xs text-muted-foreground">+12 min {t("avg_watch_time_change")}</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("total_comments")}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{total.totalComments}</div>
            <p className="text-xs text-muted-foreground">+24 {t("total_comments_change")}</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="views" className="space-y-4">
        <TabsList>
          <TabsTrigger value="views" className="flex items-center gap-2">
            <LineChart className="h-4 w-4" />
            {t("views")}
          </TabsTrigger>
          <TabsTrigger value="completion" className="flex items-center gap-2">
            <BarChart className="h-4 w-4" />
            {t("completion")}
          </TabsTrigger>
          <TabsTrigger value="engagement" className="flex items-center gap-2">
            <PieChart className="h-4 w-4" />
            {t("engagement")}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="views" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("course_views_over_time")}</CardTitle>
              <CardDescription>
                {t("course_views_description")}
              </CardDescription>
            </CardHeader>
            <CardContent className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsLineChart
                  data={viewsData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="views" stroke="#8884d8" activeDot={{ r: 8 }} />
                </RechartsLineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="completion" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("course_completion_rate")}</CardTitle>
              <CardDescription>{t("course_completion_description")}</CardDescription>
            </CardHeader>
            <CardContent className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsBarChart
                  data={completionData}
                  margin={{
                    top: 20,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="started" stackId="a" fill="#8884d8" />
                  <Bar dataKey="completed" stackId="a" fill="#82ca9d" />
                </RechartsBarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t("section_completion_rates")}</CardTitle>
              <CardDescription>{t("section_completion_description")}</CardDescription>
            </CardHeader>
            <CardContent className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsBarChart
                  data={sectionCompletionData}
                  layout="vertical"
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" domain={[0, 100]} />
                  <YAxis dataKey="name" type="category" />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="completion" fill="#82ca9d" />
                </RechartsBarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="engagement" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("student_engagement")}</CardTitle>
              <CardDescription>{t("student_engagement_description")}</CardDescription>
            </CardHeader>
            <CardContent className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsPieChart>
                  <Pie
                    data={engagementData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {engagementData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </RechartsPieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
