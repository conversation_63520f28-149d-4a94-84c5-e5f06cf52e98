"use client"

import { useEffect, useState } from "react"
import { VideoIcon, Plus, Trash2, GripVertical, Edit, Grid, List } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { CourseDetail, Section, Video } from "@/src/lib/repositories/courses/CoursesRepository"

import { useLocalization } from "@/src/localization/functions/client"
import en from "./locales/en.json"

export function CourseContentEditor({
  sections: initialSections,
  setCoursesDetails,
  setChangeSection
}: {
  sections: Section[],
  setCoursesDetails: React.Dispatch<React.SetStateAction<CourseDetail>>;
  setChangeSection: (props: boolean) => void
}
) {
  const [sections, setSections] = useState<Section[]>(initialSections)
  const [editingVideo, setEditingVideo] = useState<Video | null>(null)
  const [editingSection, setEditingSection] = useState<string | null>(null)
  const [videoDialogOpen, setVideoDialogOpen] = useState(false)
  const [sectionDialogOpen, setSectionDialogOpen] = useState(false)
  const [newSectionTitle, setNewSectionTitle] = useState("")
  const [newSectionDescription, setNewSectionDescription] = useState("")
  const [viewMode, setViewMode] = useState<"list" | "grid">("list") 
  const [newVideoData, setNewVideoData] = useState({
    title: "",
    url: "",
    duration: "",
    description: "",
  })
  const { t } = useLocalization("public-test", { en });

  const addSection = async () => {
    if (!newSectionTitle.trim()) return

    const newSection: Section = {
      id: `section-${Date.now()}`,
      title: newSectionTitle,
      description: newSectionDescription, 
      videos: [],
    }

    setCoursesDetails((old: CourseDetail) => ({ ...old, sections: [...sections, newSection] }))
    setSections([...sections, newSection])
    setNewSectionTitle("")
    setNewSectionDescription("") 
    setChangeSection(true)
    setSectionDialogOpen(false)
  }

  const updateSection = (sectionId: string, title: string, description?: string) => {
    const newSection = sections.map((section) => {
      if (section.id === sectionId) {
        return {
          ...section,
          title,
          description: description !== undefined ? description : section.description,
        }
      }
      return section
    })

    setSections(newSection)
    setCoursesDetails((old: CourseDetail) => ({ ...old, sections: newSection }))
  }

  const deleteSection = (sectionId: string) => {
    const newSection = sections.filter((section) => section.id !== sectionId)
    setSections(newSection)
    setCoursesDetails((old: CourseDetail) => ({ ...old, sections: newSection }))
    setChangeSection(true)
  }

  const addVideo = (sectionId: string) => {
    if (!newVideoData.title.trim() || !newVideoData.url.trim()) return

    const newVideo: Video = {
      id: `video-${Date.now()}`,
      title: newVideoData.title,
      url: newVideoData.url,
      duration: newVideoData.duration || "0:00",
      description: newVideoData.description,
    }

    const newSection = sections.map((section) =>
      section.id === sectionId ? { ...section, videos: [...section.videos, newVideo] } : section,
    )
    setSections(newSection)
    setCoursesDetails((old: CourseDetail) => ({ ...old, sections: newSection }))
    setChangeSection(true)
    setNewVideoData({ title: "", url: "", duration: "", description: "" })
    setVideoDialogOpen(false)
  }

  const updateVideo = (sectionId: string, videoId: string, videoData: Partial<Video>) => {
    const newSection = sections.map((section) =>
      section.id === sectionId
        ? {
          ...section,
          videos: section.videos.map((video) => (video.id === videoId ? { ...video, ...videoData } : video)),
        }
        : section,
    )

    setSections(newSection)
    setCoursesDetails((old: CourseDetail) => ({ ...old, sections: newSection }))
    setChangeSection(true)
    setEditingVideo(null)
    setVideoDialogOpen(false)
  }

  const deleteVideo = (sectionId: string, videoId: string) => {
    const newSection = sections.map((section) =>
      section.id === sectionId
        ? { ...section, videos: section.videos.filter((video) => video.id !== videoId) }
        : section,
    )

    setSections(newSection)
    setCoursesDetails((old: CourseDetail) => ({ ...old, sections: newSection }))
    setChangeSection(true)
  }

  function getYoutubeId(url: string) {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/
    const match = url.match(regExp)
    return match && match[2].length === 11 ? match[2] : null
  }

  useEffect(() => {
    if (!initialSections) return;
    setSections(initialSections)
  }, [initialSections])

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>{t("course_content")}</CardTitle>
          <div className="flex items-center gap-2">
            <div className="flex items-center border rounded-md overflow-hidden">
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                className="rounded-none px-3"
                onClick={() => setViewMode("list")}
              >
                <List className="h-4 w-4 mr-1" />
                {t("list")}
              </Button>
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="sm"
                className="rounded-none px-3"
                onClick={() => setViewMode("grid")}
              >
                <Grid className="h-4 w-4 mr-1" />
                {t("grid")}
              </Button>
            </div>
            <Dialog open={sectionDialogOpen} onOpenChange={setSectionDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  {t("add_section")}
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>{t("add_new_section")}</DialogTitle>
                  <DialogDescription>{t("create_new_section_description")}</DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="section-title">{t("section_title")}</Label>
                    <Input
                      id="section-title"
                      placeholder={t("section_title_placeholder")}
                      value={newSectionTitle}
                      onChange={(e) => setNewSectionTitle(e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="section-description">{t("section_description")}</Label>
                    <Textarea
                      id="section-description"
                      placeholder={t("section_description_placeholder")}
                      value={newSectionDescription}
                      onChange={(e) => setNewSectionDescription(e.target.value)}
                      className="min-h-[80px]"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setSectionDialogOpen(false)}>
                    {t("cancel")}
                  </Button>
                  <Button onClick={addSection}>{t("add_section")}</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          {sections.length === 0 ? (
            <div className="text-center py-12 text-muted-foreground">
              <p>{t("no_sections_added")}</p>
            </div>
          ) : (
            <Accordion type="multiple" className="w-full">
              {sections.map((section) => (
                <AccordionItem key={section.id} value={section.id} className="border rounded-md mb-4 px-2">
                  <div className="flex items-center justify-between py-4">
                    <div className="flex items-center flex-1">
                      <GripVertical className="h-5 w-5 text-muted-foreground mr-2 cursor-move" />
                      {editingSection === section.id ? (
                        <div className="flex-1 flex flex-col gap-2">
                          <div className="flex items-center gap-2">
                            <Input
                              value={section.title}
                              onChange={(e) => {
                                updateSection(section.id, e.target.value)
                              }}
                              className="h-8"
                            />
                            <Button size="sm" onClick={() => {
                              setChangeSection(true)
                              setEditingSection(null)
                            }}>
                              {t('save')}
                            </Button>
                          </div>
                          <Textarea
                            value={section.description || ""}
                            onChange={(e) => updateSection(section.id, section.title, e.target.value)}
                            placeholder={t("section_description")}
                            className="text-sm min-h-[60px]"
                          />
                        </div>
                      ) : (
                        <AccordionTrigger className="hover:no-underline flex-1">
                          <div className="text-left">
                            <span className="font-medium">{section.title}</span>
                            {section.description && (
                              <p className="text-xs text-muted-foreground mt-1 line-clamp-2">{section.description}</p>
                            )}
                          </div>
                        </AccordionTrigger>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={(e) => {
                          e.stopPropagation()
                          setEditingSection(section.id)
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={(e) => {
                          e.stopPropagation()
                          deleteSection(section.id)
                        }}
                      >
                        <Trash2 className="h-4 w-4 text-destructive" />
                      </Button>
                    </div>
                  </div>
                  <AccordionContent>
                    <div className="space-y-4 pt-2 pb-4">
                      {section.videos.length > 0 ? (
                        viewMode === "list" ? (
                          
                          section.videos.map((video, index) => (
                            <div key={video.id} className="flex items-center gap-3 p-3 rounded-md border bg-background">
                              <div className="flex items-center gap-2 flex-1">
                                <GripVertical className="h-5 w-5 text-muted-foreground cursor-move" />
                                <VideoIcon className="h-4 w-4 text-muted-foreground" />
                                <div className="flex-1">
                                  <p className="font-medium">{video.title}</p>
                                  <p className="text-xs text-muted-foreground">{video.url}</p>
                                </div>
                                <div className="flex items-center text-xs text-muted-foreground">{video.duration}</div>
                              </div>
                              <div className="flex items-center gap-1">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => {
                                    setEditingVideo(video)
                                    setNewVideoData({
                                      title: video.title,
                                      url: video.url,
                                      duration: video.duration,
                                      description: video.description || "",
                                    })
                                    setVideoDialogOpen(true)
                                  }}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button variant="ghost" size="icon" onClick={() => deleteVideo(section.id, video.id)}>
                                  <Trash2 className="h-4 w-4 text-destructive" />
                                </Button>
                              </div>
                            </div>
                          ))
                        ) : (
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {section.videos.map((video) => (
                              <div key={video.id} className="border rounded-md overflow-hidden bg-background">
                                <div className="aspect-video bg-muted relative flex items-center justify-center">
                                  {video.url.includes("youtube") ? (
                                    <div className="w-full h-full bg-black">
                                      <iframe
                                        src={`https://www.youtube.com/embed/${getYoutubeId(video.url)}`}
                                        className="w-full h-full"
                                        allowFullScreen
                                      ></iframe>
                                    </div>
                                  ) : (
                                    <div className="flex flex-col items-center justify-center">
                                      <VideoIcon className="h-10 w-10 text-muted-foreground" />
                                      <span className="text-xs text-muted-foreground mt-2">{t("video_preview")}</span>
                                    </div>
                                  )}
                                </div>
                                <div className="p-3">
                                  <h4 className="font-medium text-sm">{video.title}</h4>
                                  {video.description && (
                                    <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                                      {video.description}
                                    </p>
                                  )}
                                  <p className="text-xs text-muted-foreground mt-1">{video.duration}</p>
                                  <div className="flex items-center justify-end gap-1 mt-2">
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="h-7 w-7"
                                      onClick={() => {
                                        setEditingVideo(video)
                                        setNewVideoData({
                                          title: video.title,
                                          url: video.url,
                                          duration: video.duration,
                                          description: video.description || "",
                                        })
                                        setVideoDialogOpen(true)
                                      }}
                                    >
                                      <Edit className="h-3 w-3" />
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="h-7 w-7"
                                      onClick={() => deleteVideo(section.id, video.id)}
                                    >
                                      <Trash2 className="h-3 w-3 text-destructive" />
                                    </Button>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        )
                      ) : (
                        <p className="text-sm text-muted-foreground text-center py-4">
                          {t("no_videos_in_section")}
                        </p>
                      )}

                      <Dialog open={videoDialogOpen} onOpenChange={setVideoDialogOpen}>
                        <DialogTrigger asChild>
                          <Button variant="outline" className="w-full">
                            <Plus className="mr-2 h-4 w-4" />
                            {t("add_video")}
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>{editingVideo ? t("edit_video") : t("add_new_video")}</DialogTitle>
                            <DialogDescription>
                              {editingVideo ? t("update_video_details") : t("add_video_description")}
                            </DialogDescription>
                          </DialogHeader>
                          <div className="space-y-4 py-4">
                            <div className="space-y-2">
                              <Label htmlFor="video-title">{t("video_title")}</Label>
                              <Input
                                id="video-title"
                                placeholder={t("enter_video_title")}
                                value={newVideoData.title}
                                onChange={(e) => setNewVideoData({ ...newVideoData, title: e.target.value })}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="video-description">{t("video_description")}</Label>
                              <Textarea
                                id="video-description"
                                placeholder={t("video_description_placeholder")}
                                value={newVideoData.description || ""}
                                onChange={(e) => setNewVideoData({ ...newVideoData, description: e.target.value })}
                                className="min-h-[100px]"
                              />
                              <p className="text-xs text-muted-foreground">
                                {t("video_description_hint")}
                              </p>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="video-url">{t("video_url")}</Label>
                              <Input
                                id="video-url"
                                placeholder={t("video_url_example")}
                                value={newVideoData.url}
                                onChange={(e) => setNewVideoData({ ...newVideoData, url: e.target.value })}
                              />
                              <p className="text-xs text-muted-foreground">{t("video_url_placeholder")}</p>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="video-duration">{t("duration")} (optional)</Label>
                              <Input
                                id="video-duration"
                                placeholder={t("duration_example")}
                                value={newVideoData.duration}
                                onChange={(e) => setNewVideoData({ ...newVideoData, duration: e.target.value })}
                              />
                            </div>
                          </div>
                          <DialogFooter>
                            <Button
                              variant="outline"
                              onClick={() => {
                                setVideoDialogOpen(false)
                                setEditingVideo(null)
                                setNewVideoData({ title: "", url: "", duration: "", description: "" })
                              }}
                            >
                              {t("cancel")}
                            </Button>
                            <Button
                              onClick={() => {
                                if (editingVideo) {
                                  updateVideo(section.id, editingVideo.id, newVideoData)
                                } else {
                                  addVideo(section.id)
                                }
                              }}
                            >
                              {editingVideo ?  t("update_video") : t("add_video")}
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
