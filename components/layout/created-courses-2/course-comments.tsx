"use client"

import { useEffect, useState } from "react"
import { Message<PERSON>quare, ThumbsUp, ThumbsDown, Clock, MoreH<PERSON>zon<PERSON>, Flag, Trash2, FlagOff } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { CourseComment } from "@/src/lib/repositories/courses/CoursesRepository"
import { CoursesAPI } from "@/src/services/coursesApi"

import { useLocalization } from "@/src/localization/functions/client"
import en from "./locales/en.json"

interface CourseCommentsProps {
  courseId: string
}

export function CourseComments({ courseId }: CourseCommentsProps) {
  const [activeTab, setActiveTab] = useState("all")
  const [loading, setLoading] = useState(true);
  const [commentsList, setCommentsList] = useState<CourseComment[]>([])
  const { t } = useLocalization("public-test", { en });

  const fetchData = async () => {
    try {
      const filter = activeTab == 'unread' ? 'unread' : activeTab == 'flagged' ? 'flagged' : 'all'
      const data = await CoursesAPI.CourseComments({
        filters: {
          filter
        },
        page: 1
      }).request();
      setCommentsList(data);
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false)
    }
  };

  useEffect(() => {
    if (courseId.length > 0) fetchData();
  }, [courseId, activeTab]);

  const saveEditedComment = async (newComment: CourseComment) => {
    try {
      setLoading(true)
      const newCommentData = {
        ...newComment
      }

      const newDataCourses = await CoursesAPI.UpdateCourseComment(newComment.id, newCommentData).request();
      console.log(newDataCourses);

      setCommentsList(prev => ([...prev, newDataCourses]));
      fetchData()
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false)
    }
  }

  const toggleLike = (commentId: string) => {
    const newComment = commentsList.map((comment) => {
      if (comment.id === commentId) {
        if (comment.isLiked) {
          return { ...comment, isLiked: false, likes: comment.likes - 1 }
        } else {
          const newDislikes = comment.isDisliked ? comment.dislikes - 1 : comment.dislikes
          return {
            ...comment,
            isLiked: true,
            isDisliked: false,
            likes: comment.likes + 1,
            dislikes: newDislikes,
          }
        }
      }
      return comment
    })
    setCommentsList(newComment)
    saveEditedComment(newComment.filter(a => a.id == commentId)[0])
  }

  const toggleDislike = (commentId: string) => {
    const newComment = commentsList.map((comment) => {
      if (comment.id === commentId) {
        if (comment.isDisliked) {
          return { ...comment, isDisliked: false, dislikes: comment.dislikes - 1 }
        } else {
          const newLikes = comment.isLiked ? comment.likes - 1 : comment.likes
          return {
            ...comment,
            isDisliked: true,
            isLiked: false,
            dislikes: comment.dislikes + 1,
            likes: newLikes,
          }
        }
      }
      return comment
    })
    setCommentsList(newComment)
    saveEditedComment(newComment.filter(a => a.id == commentId)[0])

  }

  const handleFlagComment = (commentId: string, status: boolean = false) => {
    const newComment = commentsList.map((comment) => {
      if (comment.id === commentId) return {
        ...comment,
        isFlagged: status,
      }
      return comment
    })
    setCommentsList(newComment)
    saveEditedComment(newComment.filter(a => a.id == commentId)[0])
  }

  const deleteComment = async (commentId: string) => {
    try {
      setLoading(true)
      if (!commentId) return
      await CoursesAPI.DeleteCourseComment(commentId).request();
      setCommentsList(commentsList.filter((comment) => comment.id !== commentId))
      fetchData()
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <MessageSquare className="mr-2 h-5 w-5" />
          {t("course_comments_feedback")}
        </CardTitle>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList>
            <TabsTrigger value="all">{t("all_comments")}</TabsTrigger>
            <TabsTrigger value="unread">{t("unread")}</TabsTrigger>
            <TabsTrigger value="flagged">{t("flagged")}</TabsTrigger>
          </TabsList>
        </Tabs>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {commentsList.length === 0 ? (
            <div className="text-center py-12 text-muted-foreground">
              <p>{t("no_comments_yet")}</p>
            </div>
          ) : (
            commentsList.map((comment) => (
              <div key={comment.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex justify-between items-start">
                  <div className="flex items-start gap-3">
                    <Avatar>
                      <AvatarImage src={comment.user.avatar || "/placeholder.svg"} alt={comment.user.name} />
                      <AvatarFallback>
                        {comment.user.name
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">{comment.user.name}</div>
                      <div className="text-sm text-muted-foreground flex items-center gap-2">
                        <span>{comment.timestamp}</span>
                        <span>•</span>
                        <Badge variant="outline" className="text-xs font-normal">
                          {comment.videoTitle}
                        </Badge>
                        <span className="flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          {comment.videoTimestamp}
                        </span>
                      </div>
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleFlagComment(comment.id, !comment.isFlagged)}>
                        {comment.isFlagged ? (
                          <>
                            <FlagOff className="h-4 w-4 mr-2" />
                            {t("unflag_comment")}
                          </>
                        ) : (
                          <>
                            <Flag className="h-4 w-4 mr-2" />
                            {t("flag_comment")}
                          </>
                        )}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => deleteComment(comment.id)}>
                        <Trash2 className="h-4 w-4 mr-2 text-destructive" />
                        {t("delete_comment")}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <p className="text-sm">{comment.content}</p>
                <div className="flex items-center gap-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`flex items-center gap-1 ${comment.isLiked ? "text-sp-success" : ""}`}
                    onClick={() => toggleLike(comment.id)}
                  >
                    <ThumbsUp className="h-4 w-4" />
                    <span>{comment.likes}</span>
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`flex items-center gap-1 ${comment.isDisliked ? "text-destructive" : ""}`}
                    onClick={() => toggleDislike(comment.id)}
                  >
                    <ThumbsDown className="h-4 w-4" />
                    <span>{comment.dislikes}</span>
                  </Button>
                  <Button variant="ghost" size="sm" className="ml-auto">
                    {t("reply")}
                  </Button>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  )
}
