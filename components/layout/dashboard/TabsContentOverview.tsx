'use client';

import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  ArrowRight,
  Award,
  CheckCircle2,
  ChevronRight,
  Lock,
  Sparkles,
  Star,
} from 'lucide-react';
import { ActivityItem } from '@/src/lib/repositories/dashboard/overview/OverviewRepository';
import { DashboardAPI } from "@/src/services/dashboardApi";
import { useLocalization } from '@/src/localization/functions/client';
import { locales } from './locales';

const TabsContentOverview: React.FC = () => {
  const [activities, setActivities] = useState<ActivityItem[]>([])
  const [loading, setLoading] = useState(true);
  const { t } = useLocalization("components.TabsContentOverview", locales);

  useEffect(() => {
    const fetchTests = async () => {
      try {
        const data = await DashboardAPI.DashboardOverview({page: 1}).request();
        setActivities(data);
      } catch (err) {
        console.error(err);
      } finally {
        setLoading(false)
      }
    };
    fetchTests();
  }, []);

  return (
    <>
      {/* Recent Activity Card */}
      <Card className="shadow-none bg-sp-neutral-100 border-none rounded-3xl">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle>{t("recent_activity")}</CardTitle>
            <Button variant="ghost" size="sm" className="gap-1" asChild>
              <Link href="/dashboard/my-tests">
                {t("btn_view_all")} <ChevronRight className="h-4 w-4" />
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-5">
            {activities.map((activity, index) => (
              <div
                key={index}
                className="flex items-start gap-4 pb-4 border-b last:border-0 last:pb-0"
              >
                <div className="relative h-12 w-12 rounded-md overflow-hidden flex-shrink-0">
                  <Image
                    src={activity.image || '/placeholder.svg'}
                    alt={activity.title}
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    {activity.type === 'test_completed' ? (
                      <Badge variant="outline" className="bg-blue-100 text-blue-800 border-0">
                        <CheckCircle2 className="h-3 w-3 mr-1" />
                        {t("badge_test_completed")}
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-0">
                        <Award className="h-3 w-3 mr-1" />
                        {t("badge_badge_earned")}
                      </Badge>
                    )}
                    <span className="text-xs text-muted-foreground">{activity.date.toLocaleDateString()}</span>
                  </div>
                  <p className="font-medium mt-1">{activity.title}</p>
                  {activity.type === 'test_completed' ? (
                    <div className="flex items-center mt-1">
                      <span className="text-sm">{t("score")}: {activity.score}%</span>
                      <Progress value={activity.score} className="h-1.5 w-24 ml-2" />
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground mt-1">{activity.description}</p>
                  )}
                </div>
                {activity.type == 'test_completed' && (
                  <Button variant="ghost" size="sm" className="flex-shrink-0 w-8 h-8" asChild >
                    <Link
                      href={
                        activity.type === 'test_completed'
                          ? `/dashboard/my-tests/${index + 1}`
                          : '#'
                      }
                    >
                      <ArrowRight className="h-4 w-4" />
                    </Link>
                  </Button>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Learning Goals Card */}
      <Card className="shadow-none bg-sp-neutral-100 border-none rounded-3xl relative overflow-hidden">
        <div className="absolute top-2 right-2">
          <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20">
            <Sparkles className="h-3 w-3 mr-1" />
            {t("badge_pro_feature")}
          </Badge>
        </div>
        <CardHeader>
          <CardTitle>{t("learning_goals")}</CardTitle>
          <CardDescription>
            {t("learning_goals_desc")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center text-center py-6">
            <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
              <Lock className="h-6 w-6 text-primary" />
            </div>
            <h3 className="text-lg font-semibold mb-2">{t("coming_soon")}</h3>
            <p className="text-muted-foreground mb-6 max-w-md">
              {t("coming_soon_desc")}
            </p>
            <Button size="lg" className="gap-2">
              <Star className="h-4 w-4" />
              {t("btn_coming_soon")}
            </Button>
          </div>
        </CardContent>
      </Card>
    </>
  );
};

export default TabsContentOverview;
