'use client'

import React, { useEffect, useMemo, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { LucideIcon, Star, Zap, TrendingUp, Award, CheckCircle2, PenIcon } from "lucide-react"
import { ProgressStats } from "@/src/lib/repositories/dashboard/progress/ProgressRepository"
import { DashboardAPI } from "@/src/services/dashboardApi"
import { useLocalization } from "@/src/localization/functions/client"
import { locales } from "./locales"

interface newBadges {
  name: string
  id: number
  color?: string
  icon?: LucideIcon
}

export const TabsContentProgress: React.FC = () => {
  const [userProgress, setUserProgress] = useState<ProgressStats>({
    userId: "",
    badges: [],
    designSkills: [],
    performanceData: [],
    technicalSkills: []
  })
  const [loading, setLoading] = useState(true);
  const { t } = useLocalization("components.tabsContentProgress", locales);

  useEffect(() => {
    const fetchTests = async () => {
      try {
        const exampleID = "alpha789xyz"
        const data = await DashboardAPI.DashboardProgress(exampleID).request();
        setUserProgress(data);
      } catch (err) {
        console.error(err);
      } finally {
        setLoading(false)
      }
    };
    fetchTests();
  }, []);

  const badges = useMemo(() => {
    const newBadges: newBadges[] = userProgress.badges
    return newBadges.map(a => {
      a.icon = a.id == 1 ? Star : a.id == 2 ? Zap : a.id == 3 ? TrendingUp : a.id == 4 ? Award : a.id == 5 ? CheckCircle2 : PenIcon
      a.color = a.id == 1 ? 'bg-yellow-100 text-yellow-800' : a.id == 2 ? 'bg-blue-100 text-blue-800' : a.id == 3 ? 'bg-green-100 text-green-800' : a.id == 4 ? 'bg-purple-100 text-purple-800' : a.id == 5 ? 'bg-indigo-100 text-indigo-800' : ''

      return a
    })
  }, [userProgress.badges])
  
  return (
    <>
      {/* Skills Progress */}
      <Card id="skills" className="shadow-none bg-sp-neutral-100 border-none rounded-3xl">
        <CardHeader>
          <CardTitle>{t("skills_progress")}</CardTitle>
          <CardDescription>{t("skills_progress_desc")}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-6">
            {/* Technical Skills */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-muted-foreground">{t("technical_skills")}</h3>
              {userProgress.technicalSkills.map((skill) => (
                <div key={skill.skill} className="space-y-1.5">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium">{skill.skill}</p>
                    <p className="text-sm text-muted-foreground">{skill.progress}%</p>
                  </div>
                  <Progress value={skill.progress} className="h-2" />
                </div>
              ))}
            </div>

            {/* Design Skills */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-muted-foreground">{t("design_skills")}</h3>
              {userProgress.designSkills.map((skill) => (
                <div key={skill.skill} className="space-y-1.5">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium">{skill.skill}</p>
                    <p className="text-sm text-muted-foreground">{skill.progress}%</p>
                  </div>
                  <Progress value={skill.progress} className="h-2" />
                </div>
              ))}
            </div>
          </div>

          {/* Badges */}
          <div className="mt-8">
            <h3 className="text-sm font-medium text-muted-foreground mb-4">{t("earned_badges")}</h3>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4">
              {badges.map((badge, index) => {
                if (!badge.icon) badge.icon = PenIcon
                const Icon = badge.icon
                return (
                  <div key={index} className="border rounded-lg p-3 flex flex-col items-center text-center">
                    <div className={`h-10 w-10 rounded-full ${badge.color} flex items-center justify-center mb-2`}>
                      <Icon className="h-5 w-5" />
                    </div>
                    <p className="text-xs font-medium">{badge.name}</p>
                  </div>
                )
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Performance History */}
      <Card className="shadow-none bg-sp-neutral-100 border-none rounded-3xl">
        <CardHeader>
          <CardTitle>{t("performance_history")}</CardTitle>
          <CardDescription>{t("performance_history_desc")}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] flex items-end justify-between gap-2 pt-10 relative">
            <div className="absolute inset-0 grid grid-cols-1 grid-rows-5 border-b">
              {[0, 1, 2, 3, 4].map((i) => (
                <div key={i} className="border-t flex items-center">
                  <span className="text-xs text-muted-foreground pl-2">{100 - i * 20}%</span>
                </div>
              ))}
            </div>

            {userProgress.performanceData.map((item, i) => (
              <div key={i} className="flex flex-col items-center flex-1 z-10">
                <div
                  className="w-full max-w-[50px] bg-primary rounded-t-sm"
                  style={{ height: `${item.score * 2.5}px` }}
                />
                <span className="text-xs mt-2">{item.month}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </>
  )
}
