'use client';

import { TrendingUp, TrendingDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { useEffect, useMemo, useState } from "react";
import { UserStats } from "@/src/lib/repositories/dashboard/summary/DashboardSummaryRepository";
import { DashboardAPI } from "@/src/services/dashboardApi";
import { useLocalization } from "@/src/localization/functions/client";
import { locales } from './locales'

type DashboardSummaryProps = {
  user?: UserStats;
};

const DashboardSummary: React.FC<DashboardSummaryProps> = ({ user }) => {
  const [userStats, setUserStats] = useState<UserStats>(user || {
    id: "",
    name: "",
    testsCompleted: 0,
    testsChange: 0,
    averageScore: 0,
    scoreChange: -0,
    badges: 0,
    badgesChange: 0,
    studyTime: "",
    studyTimeChange: "",
  })
  const [loading, setLoading] = useState(true);
  const { t } = useLocalization("dashboard-summary", locales);

  useEffect(() => {
    const fetchTests = async () => {
      try {
        const exampleID = "alpha789xyz"
        const data = await DashboardAPI.DashboardSummary(exampleID).request();
        setUserStats(data);
      } catch (err) {
        console.error(err);
      } finally {
        setLoading(false)
      }
    };
    fetchTests();
  }, []);

  const stats = useMemo(() => [
    {
      label: "Tests Completed",
      value: userStats.testsCompleted,
      change: userStats.testsChange,
    },
    {
      label: "Average Score",
      value: `${userStats.averageScore}%`,
      change: userStats.scoreChange,
    },
    {
      label: "Badges Earned",
      value: userStats.badges,
      change: userStats.badgesChange,
    },
    {
      label: "Study Time",
      value: userStats.studyTime,
      change: userStats.studyTimeChange,
    },
  ],
    [userStats])

  return (
    <div className="relative overflow-hidden rounded-xl bg-gradient-to-r from-primary/90 to-primary p-8 text-primary-foreground shadow-lg">
      <div className="absolute inset-0 bg-grid-white/10 [mask-image:linear-gradient(0deg,#fff_1px,transparent_1px),linear-gradient(90deg,#fff_1px,transparent_1px)] [mask-size:24px_24px]" />
      <div className="relative">
        <div className="flex flex-col md:flex-row gap-4 md:items-center md:justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{t("head_title")}, {userStats.name}!</h2>
            <p className="text-primary-foreground/80 mt-2">
              {t("head_desc_1")} {userStats.testsCompleted} {t("head_desc_2")} {userStats.badges} {t("head_desc_3")}
            </p>
          </div>
          <div className="flex gap-3">
            <Button variant="secondary" asChild>
              <Link href="/dashboard/my-tests">{t("btn_my_tests")}</Link>
            </Button>
            <Button variant="secondary" asChild>
              <Link href="/dashboard/my-courses">{t("btn_my_coures")}</Link>
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
          {stats.map((stat, index) => {
            const changeValue = typeof stat.change === "string"
              ? parseFloat(stat.change)
              : stat.change;

            const isNegative = changeValue < 0;
            const Icon = isNegative ? TrendingDown : TrendingUp;
            const colorClass = isNegative ? "bg-red-500/20" : "bg-green-500/20";
            const prefix = isNegative ? "" : "+";

            return (
              <div
                key={index}
                className="bg-white/10 backdrop-blur-sm rounded-lg p-4 flex flex-col"
              >
                <span className="text-sm font-medium text-primary-foreground/70">{stat.label}</span>
                <div className="flex items-center mt-2">
                  <span className="text-2xl font-bold">{stat.value}</span>
                  <Badge variant="outline" className={`ml-2 ${colorClass} text-white border-0`}>
                    <Icon className="h-3 w-3 mr-1" />
                    {prefix}
                    {stat.change}
                  </Badge>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default DashboardSummary;
