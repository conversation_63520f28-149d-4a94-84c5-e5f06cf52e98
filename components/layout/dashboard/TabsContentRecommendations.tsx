'use client'

import React, { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import {
  <PERSON>R<PERSON>,
  BookOpen,
  Clock,
  Lock,
  Star,
  Users,
} from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { RecommendationsCourse } from "@/src/lib/repositories/dashboard/recommendations-courses/RecommendationsCoursesRepository";
import { DashboardAPI } from "@/src/services/dashboardApi";

import { useLocalization } from "@/src/hooks/useLocalization/client";
import { locales } from "./locales";

export const TabsContentRecommendations: React.FC = () => {
  const [recommendedCourses, setRecommendedCourses] = useState<RecommendationsCourse[]>([])
  const [loading, setLoading] = useState(true);
  const { t } = useLocalization("components.TabsContentRecommendations", locales);

  useEffect(() => {
    const fetchTests = async () => {
      try {
        const data = await DashboardAPI.DashboardRecommendations({page: 1}).request();
        setRecommendedCourses(data);
      } catch (err) {
        console.error(err);
      } finally {
        setLoading(false)
      }
    };
    fetchTests();
  }, []);

  return (
    <>
      {/* Recommended Courses */}
      <Card id="courses" className="shadow-none bg-sp-neutral-100 border-none rounded-3xl">
        <CardHeader>
          <CardTitle>{t("recommended_courses")}</CardTitle>
          <CardDescription>{t("recommended_courses_desc")}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {recommendedCourses.map((course, index) => (
              <Card key={index} className="overflow-hidden border-0 shadow-md flex flex-col">
                <div className="relative aspect-[3/2] w-full">
                  <Badge className="absolute top-2 right-2 z-10 bg-primary text-primary-foreground border-0">
                    {course.match}% {t("badge_match")}
                  </Badge>
                  <Image
                    src={course.image || "/placeholder.svg"}
                    alt={course.title}
                    fill
                    className="object-cover"
                  />
                </div>
                <CardContent className="p-4 flex flex-1 flex-col">
                  <div className="flex flex-1 flex-col">
                    <h3 className="font-semibold">{course.title}</h3>
                    <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                      {course.description}
                    </p>
                    <div className="flex items-center mt-3 text-xs text-muted-foreground">
                      <Users className="h-3 w-3 mr-1" />
                      <span className="mr-3">{course.instructor}</span>
                      <Clock className="h-3 w-3 mr-1" />
                      <span className="mr-3">{course.duration}</span>
                      <BookOpen className="h-3 w-3 mr-1" />
                      <span>{course.level}</span>
                    </div>
                  </div>
                  <Button className="w-full mt-3" variant="outline" size="sm" asChild>
                    <Link href="#">
                      {t("btn_view_course")}
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="mt-6 text-center flex flex-col items-center">
            <p className="text-sm text-muted-foreground mb-2">
              {t("recommended_courses_desc_footer")}
            </p>
            <Button asChild className="gap-2">
              <Link href="/dashboard/my-courses">
                <Star className="h-4 w-4" />
                {t("recommended_courses_desc_footer_btn")}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Learning Paths */}
      <Card className="shadow-none bg-sp-neutral-100 border-none rounded-3xl relative overflow-hidden">
        <div className="absolute top-2 right-2">
          <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">
            <Star className="h-3 w-3 mr-1" />
            {t("basic_plan")}
          </Badge>
        </div>
        <CardHeader>
          <CardTitle>{t("learning_paths")}</CardTitle>
          <CardDescription>{t("learning_paths_desc")}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center text-center py-6">
            <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
              <Lock className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-lg font-semibold mb-2">{t("unlock_learning_paths")}</h3>
            <p className="text-muted-foreground mb-6 max-w-md">
              {t("unlock_learning_paths_desc")}
            </p>
            <Button size="lg" className="gap-2 bg-blue-600 hover:bg-blue-700">
              <ArrowRight className="h-4 w-4" />
              {t("unlock_learning_paths_btn")}
            </Button>
          </div>
        </CardContent>
      </Card>
    </>
  );
};
