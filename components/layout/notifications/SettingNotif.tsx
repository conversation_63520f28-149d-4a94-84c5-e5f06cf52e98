"use client"

import {
  Settings,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"

import { useLocalization } from "@/src/hooks/useLocalization/client"
import en from "./locales/en.json"
import { TabConfig } from "@/src/app/dashboard/notifications/client"

interface SettingNotifProps {
  tabConfig: TabConfig[];
  switches: Record<string, boolean>;
  toggleSwitch: (id: string) => void;
}

const SettingNotif: React.FC<SettingNotifProps> = ({ tabConfig, switches, toggleSwitch }) => {
  const { t } = useLocalization("public-test", { en });

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm">
          <Settings className="h-4 w-4 mr-2" />
          {t("Settings")}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <div className="p-4 w-80">
          <h4 className="font-medium mb-3">{t("Notification_Settings")}</h4>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="email-notifications">{t("Email_Notifications")}</Label>
                <p className="text-xs text-muted-foreground">{t("Email_Notifications_Description")}</p>
              </div>
              <Switch id="email-notifications" defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="push-notifications">{t("Push_Notifications")}</Label>
                <p className="text-xs text-muted-foreground">{t("Push_Notifications_Description")}</p>
              </div>
              <Switch id="push-notifications" defaultChecked />
            </div>
            <Separator />
            <h4 className="font-medium">{t("Notification_Types")}</h4>
            <div className="grid grid-cols-2 gap-3">
              {tabConfig.map(({ id, label }) => (
                <div key={id} className="flex items-center space-x-2">
                  <Switch
                    id={`${id}-notifications`}
                    defaultChecked
                    checked={switches[id]}
                    onCheckedChange={() => toggleSwitch(id)}
                  />
                  <Label htmlFor={`${id}-notifications`}>{t(label)}</Label>
                </div>
              ))}
              <div className="flex items-center space-x-2">
                <Switch id="system-notifications" defaultChecked />
                <Label htmlFor="system-notifications">{t("System")}</Label>
              </div>
            </div>
          </div>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export default SettingNotif
