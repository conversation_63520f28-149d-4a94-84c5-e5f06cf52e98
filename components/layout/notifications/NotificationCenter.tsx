import React, { useEffect, useState } from "react";
import {
  Bell,
  CheckCircle2,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from "lucide-react"; // Assuming these are icons from lucide-react or similar
import { Button } from "@/components/ui/button";

import { useLocalization } from "@/src/localization/functions/client"
import en from "./locales/en.json"

interface NotificationCenterProps {
  onUpgrade: () => void;
}

const features = [
  {
    title: "Real-time notifications",
    description: "Get instant updates about your tests, courses, and achievements",
  },
  {
    title: "Customizable filters",
    description: "Organize notifications by type, date, and priority",
  },
  {
    title: "Multi-channel delivery",
    description: "Receive notifications via email, push, and in-app alerts",
  },
];

export const NotificationCenter: React.FC<NotificationCenterProps> = ({
  onUpgrade,
}) => {
  const [showOverlay, setShowOverlay] = useState(false);
  const { t } = useLocalization("public-test", { en });

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowOverlay(true);
    }, 300);

  }, []);

  if (!showOverlay) return null;

  return (
    <div className="fixed inset-0 top-0 flex flex-col items-center justify-center z-50">
      {/* Overlay */}
      <div
        onClick={() => setShowOverlay(false)}
        className="bg-white/80 backdrop-blur-sm flex w-full h-full absolute"
      />

      {/* Modal */}
      <div className="max-w-md w-full p-6 bg-white rounded-xl shadow-xl border border-primary/20 relative overflow-hidden">
        <div className="absolute top-0 right-0">
          <div className="bg-primary text-primary-foreground text-xs py-1 px-3 rounded-bl-lg flex items-center">
            <Sparkles className="h-3 w-3 inline-block mr-1" />
            {t("Coming_Soon")}
          </div>
        </div>

        <div className="flex flex-col items-center text-center mb-6">
          <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
            <Bell className="h-8 w-8 text-primary" />
          </div>
          <h2 className="text-2xl font-bold mb-2">{t("Notification_Center")}</h2>
          <p className="text-muted-foreground px-4">
            {t("Notification_Center_Description")}
          </p>
        </div>

        <div className="space-y-4">
          {features.map(({ title, description }) => (
            <div key={title} className="flex items-start gap-3">
              <CheckCircle2 className="h-5 w-5 text-green-600 flex-shrink-0 mt-0.5" />
              <div>
                <p className="font-medium">{t(title)}</p>
                <p className="text-sm text-muted-foreground">{t(description)}</p>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-8 space-y-4">
          <div className="bg-primary/5 p-4 rounded-lg border border-primary/10">
            <p className="font-medium text-center mb-2">{t("Pre_Launch_Special_Offer")}</p>
            <p className="text-sm text-center text-muted-foreground mb-2">
              {t("Special_Offer_Description")}
            </p>
            <p className="text-center font-bold text-primary text-xl">
              {t("Special_Offer_Price")}
            </p>
            <p className="text-center text-xs text-muted-foreground">
              {t("Special_Offer_Regular_Price")}
            </p>
          </div>

          <Button size="lg" className="w-full" onClick={onUpgrade}>
            <Star className="h-4 w-4 mr-2" />
            {t("Upgrade_to_Pro")}
          </Button>

          <p className="text-xs text-center text-muted-foreground">
            {t("Early_Adopter_Note")}
          </p>
        </div>
      </div>
    </div>
  );
};
