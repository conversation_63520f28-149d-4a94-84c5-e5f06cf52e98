import React from "react";
import { Lock, CheckCircle2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

import { useLocalization } from "@/src/localization/functions/client"
import en from "./locales/en.json"

interface UpgradeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpgrade?: () => void;
}

const ProUpgradeModal: React.FC<UpgradeModalProps> = ({ isOpen, onClose, onUpgrade }) => {
  const { t } = useLocalization("public-test", { en });

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-background rounded-lg max-w-md w-full p-6 shadow-xl">
        <div className="flex flex-col items-center text-center">
          <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
            <Lock className="h-8 w-8 text-primary" />
          </div>
          <h2 className="text-xl font-bold mb-2">{t("Unlock_Pro_Features")}</h2>
          <p className="text-muted-foreground mb-6">{t("Unlock_Pro_Description")}</p>

          <div className="w-full space-y-4">
            <div className="border rounded-lg p-4 bg-primary/5">
              <div className="flex justify-between items-center mb-2">
                <span className="font-medium">{t("Pro_Plan")}</span>
                <div className="text-right">
                  <span className="text-muted-foreground line-through text-sm">$19.99</span>
                  <span className="text-xl font-bold ml-2">$9.99</span>
                  <span className="text-xs text-muted-foreground">/{t("month")}</span>
                </div>
              </div>
              <ul className="space-y-2 text-sm">
                {[
                  "Smart_Notification_Center",
                  "Real_time_alerts",
                  "Custom_notification_preferences",
                  "Early_access_to_all_new_features",
                ].map((feature, idx) => (
                  <li className="flex items-center" key={idx}>
                    <CheckCircle2 className="h-4 w-4 text-green-500 mr-2" />
                    <span>{t(feature)}</span>
                  </li>
                ))}
              </ul>
            </div>

            <Button className="w-full" size="lg" onClick={onUpgrade}>
              {t("Upgrade_Now")}
            </Button>
            <Button variant="outline" className="w-full" onClick={onClose}>
              {t("Maybe_Later")}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProUpgradeModal;
