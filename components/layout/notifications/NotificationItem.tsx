"use client"

import Link from "next/link"
import {
  <PERSON>,
  <PERSON><PERSON>pen,
  CheckCircle2,
  Clock,
  MoreHorizontal,
  Settings,
  Star,
  Users,
  Zap,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

import { useLocalization } from "@/src/hooks/useLocalization/client"
import en from "./locales/en.json"

const iconMap = {
  CheckCircle2: <CheckCircle2 className="h-5 w-5 text-blue-600" />,
  Award: <Award className="h-5 w-5 text-yellow-600" />,
  BookOpen: <BookOpen className="h-5 w-5 text-green-600" />,
  Settings: <Settings className="h-5 w-5 text-gray-600" />,
  Star: <Star className="h-5 w-5 text-purple-600" />,
  Clock: <Clock className="h-5 w-5 text-orange-600" />,
  Zap: <Zap className="h-5 w-5 text-indigo-600" />,
  Users: <Users className="h-5 w-5 text-pink-600" />,
};

interface NotificationItemProps {
  notification: any
  onMarkAsRead: (id: number) => void
  onDelete: (id: number) => void
}

export function NotificationItem({ notification, onMarkAsRead, onDelete }: NotificationItemProps) {
  const { t } = useLocalization("public-test", { en });

  return (
    <div className={`flex items-start gap-4 p-4 ${!notification.read ? "bg-muted/30" : ""}`}>
      <div className={`h-10 w-10 rounded-full ${notification.iconBg} flex items-center justify-center flex-shrink-0`}>
        {iconMap[notification.icon]}
      </div>
      <div className="flex-1">
        <div className="flex items-center gap-2">
          {!notification.read && (
            <Badge variant="outline" className="bg-primary text-primary-foreground text-xs">
              {t("New")}
            </Badge>
          )}
          <Link href={notification.link} className="font-medium hover:underline">
            {notification.title}
          </Link>
        </div>
        <p className="text-sm text-muted-foreground mt-1">
          {notification.description}
        </p>
        <div className="flex items-center gap-4 mt-2">
          <span className="text-xs text-muted-foreground">
            {notification.date}
          </span>
          {!notification.read && (
            <Button
              variant="ghost"
              size="sm"
              className="h-auto p-0 text-xs text-muted-foreground hover:text-foreground"
              onClick={() => onMarkAsRead(notification.id)}
            >
              {t("Mark_as_read")}
            </Button>
          )}
        </div>
      </div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem asChild>
            <Link href={notification.link}>{t("View_details")}</Link>
          </DropdownMenuItem>
          {!notification.read ? (
            <DropdownMenuItem onClick={() => onMarkAsRead(notification.id)}>
              {t("Mark_as_read")}
            </DropdownMenuItem>
          ) : (
            <DropdownMenuItem>
              {t("Mark_as_unread")}
            </DropdownMenuItem>
          )}
          <DropdownMenuSeparator />
          <DropdownMenuItem
            className="text-red-500 focus:text-red-500"
            onClick={() => onDelete(notification.id)}
          >
            {t("Delete")}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>

  )
}