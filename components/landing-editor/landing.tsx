"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Zap, Shield, Heart, Star, ArrowRight, CheckCircle } from "lucide-react"

interface LandingProps {
  data: {
    id: string
    name: string
    components: any[]
    styles: {
      primaryColor: string
      secondaryColor: string
      accentColor: string
      fontFamily: string
      spacing: string
    }
  }
  previewMode?: "desktop" | "tablet" | "mobile"
  isPreview?: boolean
  onUpdateData?: (data: any) => void
}

const iconMap = {
  zap: Zap,
  shield: Shield,
  heart: Heart,
  star: Star,
  check: CheckCircle,
}

export function Landing({ data, previewMode = "desktop", isPreview = false, onUpdateData }: LandingProps) {
  const [selectedComponent, setSelectedComponent] = useState<string | null>(null)

  const renderComponent = (component: any) => {
    const { id, type, props } = component

    switch (type) {
      case "hero":
        return (
          <div
            key={id}
            className={`relative min-h-[500px] flex items-center justify-center text-white ${
              !isPreview && selectedComponent === id ? "ring-2 ring-blue-500" : ""
            }`}
            style={{
              backgroundImage: props.backgroundImage
                ? `url(${props.backgroundImage})`
                : "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              backgroundSize: "cover",
              backgroundPosition: "center",
            }}
            onClick={() => !isPreview && setSelectedComponent(id)}
          >
            <div className="absolute inset-0 bg-black/40" />
            <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
              <h1 className="text-4xl md:text-6xl font-bold mb-6">{props.title || "Your Amazing Headline"}</h1>
              <p className="text-xl md:text-2xl mb-8 opacity-90">
                {props.subtitle || "A compelling subtitle that explains your value proposition"}
              </p>
              <Button
                size="lg"
                className="bg-white text-gray-900 hover:bg-gray-100"
                style={{ backgroundColor: data.styles.accentColor, color: "white" }}
              >
                {props.buttonText || "Get Started"}
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </div>
        )

      case "features":
        return (
          <div
            key={id}
            className={`py-16 px-4 ${!isPreview && selectedComponent === id ? "ring-2 ring-blue-500" : ""}`}
            onClick={() => !isPreview && setSelectedComponent(id)}
          >
            <div className="max-w-6xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl md:text-4xl font-bold mb-4">{props.title || "Amazing Features"}</h2>
                {props.subtitle && <p className="text-xl text-gray-600 max-w-2xl mx-auto">{props.subtitle}</p>}
              </div>
              <div className="grid md:grid-cols-3 gap-8">
                {(props.features || []).map((feature: any, index: number) => {
                  const IconComponent = iconMap[feature.icon as keyof typeof iconMap] || Star
                  return (
                    <Card key={index} className="text-center p-6">
                      <CardContent className="pt-6">
                        <div
                          className="w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4"
                          style={{ backgroundColor: data.styles.primaryColor }}
                        >
                          <IconComponent className="h-6 w-6 text-white" />
                        </div>
                        <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                        <p className="text-gray-600">{feature.description}</p>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </div>
          </div>
        )

      case "testimonials":
        return (
          <div
            key={id}
            className={`py-16 px-4 bg-gray-50 ${!isPreview && selectedComponent === id ? "ring-2 ring-blue-500" : ""}`}
            onClick={() => !isPreview && setSelectedComponent(id)}
          >
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-3xl font-bold mb-12">What Our Customers Say</h2>
              <div className="grid md:grid-cols-2 gap-8">
                {(props.testimonials || []).map((testimonial: any, index: number) => (
                  <Card key={index} className="p-6">
                    <CardContent className="pt-6">
                      <div className="flex mb-4">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                        ))}
                      </div>
                      <p className="text-gray-600 mb-4">"{testimonial.content}"</p>
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-gray-300 rounded-full mr-3" />
                        <div>
                          <p className="font-semibold">{testimonial.name}</p>
                          <p className="text-sm text-gray-500">{testimonial.role}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        )

      case "cta":
        return (
          <div
            key={id}
            className={`py-16 px-4 text-center ${!isPreview && selectedComponent === id ? "ring-2 ring-blue-500" : ""}`}
            style={{ backgroundColor: data.styles.primaryColor }}
            onClick={() => !isPreview && setSelectedComponent(id)}
          >
            <div className="max-w-2xl mx-auto text-white">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">{props.title || "Ready to Get Started?"}</h2>
              <p className="text-xl mb-8 opacity-90">
                {props.subtitle || "Join thousands of satisfied customers today"}
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" className="bg-white text-gray-900 hover:bg-gray-100">
                  {props.buttonText || "Get Started"}
                </Button>
                {props.secondaryButtonText && (
                  <Button
                    size="lg"
                    variant="outline"
                    className="border-white text-white hover:bg-white hover:text-gray-900"
                  >
                    {props.secondaryButtonText}
                  </Button>
                )}
              </div>
            </div>
          </div>
        )

      default:
        return (
          <div
            key={id}
            className={`p-8 border-2 border-dashed border-gray-300 text-center ${
              !isPreview && selectedComponent === id ? "ring-2 ring-blue-500" : ""
            }`}
            onClick={() => !isPreview && setSelectedComponent(id)}
          >
            <p className="text-gray-500">Unknown component type: {type}</p>
          </div>
        )
    }
  }

  return (
    <div className="min-h-screen" style={{ fontFamily: data.styles.fontFamily }}>
      {data.components.map(renderComponent)}

      {data.components.length === 0 && !isPreview && (
        <div className="min-h-[400px] flex items-center justify-center text-center p-8">
          <div>
            <h3 className="text-lg font-semibold mb-2">Start Building Your Landing Page</h3>
            <p className="text-gray-600 mb-4">Drag components from the sidebar to get started</p>
            <Badge variant="outline">Tip: Click on components to select and edit them</Badge>
          </div>
        </div>
      )}
    </div>
  )
}
