"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { Palette, Type, BracketsIcon as Spacing, Save, RotateCcw, Eye } from "lucide-react"

interface BrandPaletteProps {
  styles: {
    primaryColor: string
    secondaryColor: string
    accentColor: string
    fontFamily: string
    spacing: string
  }
  onUpdateStyles: (styles: any) => void
}

const colorPresets = [
  {
    name: "Ocean Blue",
    primary: "#3b82f6",
    secondary: "#64748b",
    accent: "#06b6d4",
  },
  {
    name: "<PERSON> Green",
    primary: "#059669",
    secondary: "#6b7280",
    accent: "#f59e0b",
  },
  {
    name: "Sunset Orange",
    primary: "#ea580c",
    secondary: "#64748b",
    accent: "#8b5cf6",
  },
  {
    name: "Royal Purple",
    primary: "#7c3aed",
    secondary: "#64748b",
    accent: "#f59e0b",
  },
]

const fontOptions = [
  { value: "Inter", label: "Inter (Modern)" },
  { value: "Roboto", label: "Roboto (Clean)" },
  { value: "Poppins", label: "Poppins (Friendly)" },
  { value: "Playfair Display", label: "Playfair (Elegant)" },
  { value: "Montserrat", label: "Montserrat (Bold)" },
]

const spacingOptions = [
  { value: "compact", label: "Compact" },
  { value: "normal", label: "Normal" },
  { value: "relaxed", label: "Relaxed" },
]

export function BrandPalette({ styles, onUpdateStyles }: BrandPaletteProps) {
  const [savedPalettes, setSavedPalettes] = useState<any[]>([])

  const updateStyle = (key: string, value: string) => {
    onUpdateStyles({
      ...styles,
      [key]: value,
    })
  }

  const applyPreset = (preset: any) => {
    onUpdateStyles({
      ...styles,
      primaryColor: preset.primary,
      secondaryColor: preset.secondary,
      accentColor: preset.accent,
    })
  }

  const savePalette = () => {
    const newPalette = {
      id: Date.now(),
      name: `Custom Palette ${savedPalettes.length + 1}`,
      ...styles,
    }
    setSavedPalettes([...savedPalettes, newPalette])
  }

  const resetToDefaults = () => {
    onUpdateStyles({
      primaryColor: "#3b82f6",
      secondaryColor: "#64748b",
      accentColor: "#f59e0b",
      fontFamily: "Inter",
      spacing: "normal",
    })
  }

  return (
    <ScrollArea className="h-full">
      <div className="p-4 space-y-6">
        {/* Color Palette */}
        <div>
          <div className="flex items-center space-x-2 mb-4">
            <Palette className="h-4 w-4" />
            <h3 className="font-semibold">Color Palette</h3>
          </div>

          <div className="space-y-4">
            <div>
              <Label htmlFor="primary-color">Primary Color</Label>
              <div className="flex items-center space-x-2 mt-1">
                <Input
                  id="primary-color"
                  type="color"
                  value={styles.primaryColor}
                  onChange={(e) => updateStyle("primaryColor", e.target.value)}
                  className="w-12 h-10 p-1 border rounded"
                />
                <Input
                  value={styles.primaryColor}
                  onChange={(e) => updateStyle("primaryColor", e.target.value)}
                  className="flex-1"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="secondary-color">Secondary Color</Label>
              <div className="flex items-center space-x-2 mt-1">
                <Input
                  id="secondary-color"
                  type="color"
                  value={styles.secondaryColor}
                  onChange={(e) => updateStyle("secondaryColor", e.target.value)}
                  className="w-12 h-10 p-1 border rounded"
                />
                <Input
                  value={styles.secondaryColor}
                  onChange={(e) => updateStyle("secondaryColor", e.target.value)}
                  className="flex-1"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="accent-color">Accent Color</Label>
              <div className="flex items-center space-x-2 mt-1">
                <Input
                  id="accent-color"
                  type="color"
                  value={styles.accentColor}
                  onChange={(e) => updateStyle("accentColor", e.target.value)}
                  className="w-12 h-10 p-1 border rounded"
                />
                <Input
                  value={styles.accentColor}
                  onChange={(e) => updateStyle("accentColor", e.target.value)}
                  className="flex-1"
                />
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* Color Presets */}
        <div>
          <h4 className="font-medium mb-3">Color Presets</h4>
          <div className="grid grid-cols-2 gap-2">
            {colorPresets.map((preset) => (
              <Card
                key={preset.name}
                className="cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => applyPreset(preset)}
              >
                <CardContent className="p-3">
                  <div className="flex space-x-1 mb-2">
                    <div className="w-4 h-4 rounded" style={{ backgroundColor: preset.primary }} />
                    <div className="w-4 h-4 rounded" style={{ backgroundColor: preset.secondary }} />
                    <div className="w-4 h-4 rounded" style={{ backgroundColor: preset.accent }} />
                  </div>
                  <p className="text-xs font-medium">{preset.name}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        <Separator />

        {/* Typography */}
        <div>
          <div className="flex items-center space-x-2 mb-4">
            <Type className="h-4 w-4" />
            <h3 className="font-semibold">Typography</h3>
          </div>

          <div className="space-y-4">
            <div>
              <Label htmlFor="font-family">Font Family</Label>
              <Select value={styles.fontFamily} onValueChange={(value) => updateStyle("fontFamily", value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {fontOptions.map((font) => (
                    <SelectItem key={font.value} value={font.value}>
                      {font.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="p-4 border rounded-lg" style={{ fontFamily: styles.fontFamily }}>
              <h4 className="font-bold text-lg mb-2">Preview Text</h4>
              <p className="text-sm text-muted-foreground">
                This is how your text will look with the selected font family.
              </p>
            </div>
          </div>
        </div>

        <Separator />

        {/* Spacing */}
        <div>
          <div className="flex items-center space-x-2 mb-4">
            <Spacing className="h-4 w-4" />
            <h3 className="font-semibold">Spacing</h3>
          </div>

          <div>
            <Label htmlFor="spacing">Layout Density</Label>
            <Select value={styles.spacing} onValueChange={(value) => updateStyle("spacing", value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {spacingOptions.map((spacing) => (
                  <SelectItem key={spacing.value} value={spacing.value}>
                    {spacing.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <Separator />

        {/* Saved Palettes */}
        {savedPalettes.length > 0 && (
          <div>
            <h4 className="font-medium mb-3">Saved Palettes</h4>
            <div className="space-y-2">
              {savedPalettes.map((palette) => (
                <Card
                  key={palette.id}
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => onUpdateStyles(palette)}
                >
                  <CardContent className="p-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className="flex space-x-1">
                          <div className="w-3 h-3 rounded" style={{ backgroundColor: palette.primaryColor }} />
                          <div className="w-3 h-3 rounded" style={{ backgroundColor: palette.secondaryColor }} />
                          <div className="w-3 h-3 rounded" style={{ backgroundColor: palette.accentColor }} />
                        </div>
                        <span className="text-sm font-medium">{palette.name}</span>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {palette.fontFamily}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        <Separator />

        {/* Actions */}
        <div className="space-y-2">
          <Button onClick={savePalette} className="w-full">
            <Save className="mr-2 h-4 w-4" />
            Save Current Palette
          </Button>

          <Button onClick={resetToDefaults} variant="outline" className="w-full">
            <RotateCcw className="mr-2 h-4 w-4" />
            Reset to Defaults
          </Button>
        </div>

        {/* Preview */}
        <div>
          <div className="flex items-center space-x-2 mb-3">
            <Eye className="h-4 w-4" />
            <h4 className="font-medium">Style Preview</h4>
          </div>

          <Card>
            <CardContent className="p-4" style={{ fontFamily: styles.fontFamily }}>
              <div className="p-4 rounded-lg text-white mb-3" style={{ backgroundColor: styles.primaryColor }}>
                <h3 className="font-bold">Primary Color</h3>
                <p className="text-sm opacity-90">Main brand color</p>
              </div>

              <div className="p-4 rounded-lg text-white mb-3" style={{ backgroundColor: styles.secondaryColor }}>
                <h3 className="font-bold">Secondary Color</h3>
                <p className="text-sm opacity-90">Supporting color</p>
              </div>

              <div className="p-4 rounded-lg text-white" style={{ backgroundColor: styles.accentColor }}>
                <h3 className="font-bold">Accent Color</h3>
                <p className="text-sm opacity-90">Call-to-action color</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </ScrollArea>
  )
}
