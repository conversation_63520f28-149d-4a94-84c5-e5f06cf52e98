"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Layout, Type, Star, MessageSquare, CreditCard, Mail, Plus, Search } from "lucide-react"

interface EditorSidebarProps {
  landingPage: any
  onUpdateLandingPage: (landingPage: any) => void
}

const componentCategories = [
  {
    name: "Layout",
    icon: Layout,
    components: [
      {
        type: "hero",
        name: "Hero Section",
        description: "Eye-catching header with title and CTA",
        icon: Layout,
        defaultProps: {
          title: "Your Amazing Headline",
          subtitle: "A compelling subtitle that explains your value proposition",
          buttonText: "Get Started",
          backgroundImage: "/landing-page-template-1.png",
        },
      },
      {
        type: "features",
        name: "Feature Grid",
        description: "Showcase your key features",
        icon: Star,
        defaultProps: {
          title: "Amazing Features",
          subtitle: "Everything you need to succeed",
          features: [
            { icon: "zap", title: "Fast", description: "Lightning-fast performance" },
            { icon: "shield", title: "Secure", description: "Bank-level security" },
            { icon: "heart", title: "Reliable", description: "99.9% uptime guarantee" },
          ],
        },
      },
    ],
  },
  {
    name: "Content",
    icon: Type,
    components: [
      {
        type: "testimonials",
        name: "Testimonials",
        description: "Customer reviews and social proof",
        icon: MessageSquare,
        defaultProps: {
          title: "What Our Customers Say",
          testimonials: [
            {
              content: "This product has transformed our business completely.",
              name: "John Doe",
              role: "CEO, Company Inc.",
            },
            {
              content: "Outstanding service and incredible results.",
              name: "Jane Smith",
              role: "Marketing Director",
            },
          ],
        },
      },
      {
        type: "pricing",
        name: "Pricing Table",
        description: "Display your pricing plans",
        icon: CreditCard,
        defaultProps: {
          title: "Choose Your Plan",
          plans: [
            {
              name: "Basic",
              price: "$9",
              period: "month",
              features: ["Feature 1", "Feature 2", "Feature 3"],
            },
            {
              name: "Pro",
              price: "$19",
              period: "month",
              features: ["Everything in Basic", "Feature 4", "Feature 5"],
              popular: true,
            },
          ],
        },
      },
    ],
  },
  {
    name: "Conversion",
    icon: Mail,
    components: [
      {
        type: "cta",
        name: "Call to Action",
        description: "Drive conversions with compelling CTAs",
        icon: Mail,
        defaultProps: {
          title: "Ready to Get Started?",
          subtitle: "Join thousands of satisfied customers today",
          buttonText: "Get Started",
          secondaryButtonText: "Learn More",
        },
      },
      {
        type: "contact",
        name: "Contact Form",
        description: "Capture leads with a contact form",
        icon: Mail,
        defaultProps: {
          title: "Get in Touch",
          subtitle: "We'd love to hear from you",
          fields: ["name", "email", "message"],
        },
      },
    ],
  },
]

export function EditorSidebar({ landingPage, onUpdateLandingPage }: EditorSidebarProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)

  const addComponent = (componentType: string, defaultProps: any) => {
    const newComponent = {
      id: `${componentType}-${Date.now()}`,
      type: componentType,
      props: defaultProps,
    }

    onUpdateLandingPage({
      ...landingPage,
      components: [...landingPage.components, newComponent],
    })
  }

  const filteredCategories = componentCategories
    .map((category) => ({
      ...category,
      components: category.components.filter(
        (component) =>
          component.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          component.description.toLowerCase().includes(searchTerm.toLowerCase()),
      ),
    }))
    .filter((category) => category.components.length > 0)

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b">
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search components..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-4 space-y-4">
          {filteredCategories.map((category) => (
            <div key={category.name}>
              <div className="flex items-center space-x-2 mb-3">
                <category.icon className="h-4 w-4" />
                <h3 className="font-semibold text-sm">{category.name}</h3>
              </div>

              <div className="space-y-2">
                {category.components.map((component) => (
                  <Card
                    key={component.type}
                    className="cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => addComponent(component.type, component.defaultProps)}
                  >
                    <CardHeader className="pb-2">
                      <div className="flex items-center space-x-2">
                        <component.icon className="h-4 w-4" />
                        <CardTitle className="text-sm">{component.name}</CardTitle>
                      </div>
                      <CardDescription className="text-xs">{component.description}</CardDescription>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <Button size="sm" className="w-full">
                        <Plus className="mr-2 h-3 w-3" />
                        Add Component
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {category !== filteredCategories[filteredCategories.length - 1] && <Separator className="mt-4" />}
            </div>
          ))}
        </div>
      </ScrollArea>

      {landingPage.components.length > 0 && (
        <>
          <Separator />
          <div className="p-4">
            <h3 className="font-semibold text-sm mb-3">Page Structure</h3>
            <div className="space-y-2">
              {landingPage.components.map((component: any, index: number) => (
                <div key={component.id} className="flex items-center justify-between p-2 bg-muted rounded-md">
                  <span className="text-sm">{component.type}</span>
                  <Button variant="ghost" size="sm">
                    <Type className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  )
}
