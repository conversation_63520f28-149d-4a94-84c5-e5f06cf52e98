"use client"

import React, { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Trash2, Plus, ArrowUp, ArrowDown } from "lucide-react"
import { locales } from "./locales"
import { useLocalization } from "@/src/localization/functions/client"

export type EditableOption = {
    id: string
    text?: string
    audio?: string
    image?: string
    comment?: string
    correct?: boolean
}

export type OptionsEditorProps = {
    options: EditableOption[]
    onChange: (options: EditableOption[]) => void
    multipleCorrect?: boolean
}

const generateId = () => Math.random().toString(36).substring(2, 9)

type OptionType = "text" | "audio" | "image"

export const OptionsEditor: React.FC<OptionsEditorProps> = ({
    options,
    onChange,
    multipleCorrect = false,
}) => {
    const { t } = useLocalization("options-editor", locales)
    const [optionType, setOptionType] = useState<OptionType>("text")
    const [showComment, setShowComment] = useState(true)
    const [shuffleAnswers, setShuffleAnswers] = useState(false)

    const textAreaRefs = useRef<Record<string, HTMLTextAreaElement | null>>({})
    
    const autoResizeTextarea = (el: HTMLTextAreaElement | null) => {
        if (!el) return
        el.style.height = "auto"  // reset height
        el.style.height = el.scrollHeight + "px"  // set to scrollHeight
    }


    // Refs to each option DOM element
    const optionRefs = useRef<Record<string, HTMLDivElement | null>>({})

    // When optionType changes, reset options accordingly
    useEffect(() => {
        const resetOptions = options.map((opt) => {
            const correct = opt.correct ?? false
            switch (optionType) {
                case "text":
                    return { id: opt.id, text: opt.text || "", comment: opt.comment, correct }
                case "audio":
                    return { id: opt.id, audio: opt.audio || "", comment: opt.comment, correct }
                case "image":
                    return { id: opt.id, image: opt.image || "", comment: opt.comment, correct }
                default:
                    return opt
            }
        })
        onChange(resetOptions)
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [optionType])

    // Capture positions for FLIP animation
    const capturePositions = () => {
        const newPositions: Record<string, DOMRect> = {}
        options.forEach((opt) => {
            const el = optionRefs.current[opt.id]
            if (el) {
                newPositions[opt.id] = el.getBoundingClientRect()
            }
        })
        return newPositions
    }

    // FLIP animation on reorder
    const animateReorder = (oldPositions: Record<string, DOMRect>, newPositions: Record<string, DOMRect>) => {
        options.forEach((opt) => {
            const el = optionRefs.current[opt.id]
            if (!el) return

            const oldPos = oldPositions[opt.id]
            const newPos = newPositions[opt.id]
            if (!oldPos || !newPos) return

            const deltaY = oldPos.top - newPos.top
            if (deltaY) {
                el.style.transition = "none"
                el.style.transform = `translateY(${deltaY}px)`

                // Force reflow
                el.getBoundingClientRect()

                el.style.transition = "transform 300ms ease"
                el.style.transform = ""

                const cleanup = () => {
                    el.style.transition = ""
                    el.removeEventListener("transitionend", cleanup)
                }
                el.addEventListener("transitionend", cleanup)
            }
        })
    }

    const moveOption = (index: number, direction: "up" | "down") => {
        const newIndex = direction === "up" ? index - 1 : index + 1
        if (newIndex < 0 || newIndex >= options.length) return

        const oldPositions = capturePositions()

        const newOptions = [...options]
        const temp = newOptions[index]
        newOptions[index] = newOptions[newIndex]
        newOptions[newIndex] = temp

        onChange(newOptions)

        requestAnimationFrame(() => {
            const newPositions = capturePositions()
            animateReorder(oldPositions, newPositions)
        })
    }

    const handleAddOption = () => {
        const newOption: EditableOption =
            optionType === "text"
                ? { id: generateId(), text: "", correct: false }
                : optionType === "audio"
                    ? { id: generateId(), audio: "", correct: false }
                    : { id: generateId(), image: "", correct: false }
        onChange([...options, newOption])
    }

    const handleRemoveOption = (id: string) => {
        onChange(options.filter((opt) => opt.id !== id))
    }

    const handleOptionChange = (
        id: string,
        field: keyof EditableOption,
        value: string
    ) => {
        onChange(
            options.map((opt) =>
                opt.id === id ? { ...opt, [field]: value } : opt
            )
        )
    }

    const handleSelectCorrect = (id: string) => {
        if (multipleCorrect) {
            onChange(
                options.map(opt =>
                    opt.id === id ? { ...opt, correct: !opt.correct } : opt
                )
            )
        } else {
            onChange(
                options.map(opt => ({
                    ...opt,
                    correct: opt.id === id
                }))
            )
        }
    }

    return (
        <div className="space-y-4">
            {/* Option type selector */}
            <div>
                <label className="block mb-1 font-semibold">{t("optionTypeLabel")}</label>
                <select
                    className="border rounded p-2"
                    value={optionType}
                    onChange={(e) => setOptionType(e.target.value as OptionType)}
                >
                    <option value="text">{t("optionTypeText")}</option>
                    <option value="audio">{t("optionTypeAudio")}</option>
                    <option value="image">{t("optionTypeImage")}</option>
                </select>
            </div>

            {/* Show comment toggle */}
            <div>
                <label className="inline-flex items-center gap-2 cursor-pointer">
                    <input
                        type="checkbox"
                        checked={showComment}
                        onChange={() => setShowComment(!showComment)}
                    />
                    <span>{t("showCommentField")}</span>
                </label>
            </div>

            {/* Shuffle answers toggle */}
            <div>
                <label className="inline-flex items-center gap-2 cursor-pointer">
                    <input
                        type="checkbox"
                        checked={shuffleAnswers}
                        onChange={() => setShuffleAnswers(!shuffleAnswers)}
                    />
                    <span>{t("shuffleAnswers")}</span>
                </label>
            </div>

            {/* Options list */}
            {options.map((option, index) => (
                <div
                    key={option.id}
                    ref={el => {
                        optionRefs.current[option.id] = el
                    }}
                    className="p-4 border rounded-md space-y-2 relative bg-white flex items-start gap-4"
                    style={{ willChange: "transform" }}
                >
                    {/* Correct option input: checkbox or radio */}
                    <input
                        type={multipleCorrect ? "checkbox" : "radio"}
                        name={multipleCorrect ? undefined : "correctOption"}
                        checked={!!option.correct}
                        onChange={() => handleSelectCorrect(option.id)}
                        className="mt-1"
                        aria-label={t("selectCorrectOption", { index: index + 1 })}
                    />

                    <div className="flex-1">
                        <div className="absolute right-2 top-2 flex gap-1">
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => moveOption(index, "up")}
                                disabled={index === 0}
                                aria-label={t("moveOptionUp", { index: index + 1 })}
                            >
                                <ArrowUp className="w-4 h-4" />
                            </Button>
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => moveOption(index, "down")}
                                disabled={index === options.length - 1}
                                aria-label={t("moveOptionDown", { index: index + 1 })}
                            >
                                <ArrowDown className="w-4 h-4" />
                            </Button>
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleRemoveOption(option.id)}
                                aria-label={t("removeOption", { index: index + 1 })}
                            >
                                <Trash2 className="w-4 h-4 text-red-500" />
                            </Button>
                        </div>

                        <div className="flex items-center gap-2 mb-1">
                            <span className="font-semibold">
                                {t("optionLabel")} {String.fromCharCode(65 + index)}
                            </span>
                        </div>

                        {optionType === "text" && (
                            <Textarea
                                placeholder={t("textPlaceholder")}
                                value={option.text || ""}
                                ref={el => {
                                    textAreaRefs.current[option.id] = el
                                    autoResizeTextarea(el) // Auto resize on mount
                                }}
                                onChange={(e) => {
                                    handleOptionChange(option.id, "text", e.target.value)
                                    autoResizeTextarea(e.target)
                                }}
                                style={{ overflow: "hidden", resize: "none" }}
                            />
                        )}

                        {optionType === "audio" && (
                            <Input
                                placeholder={t("audioPlaceholder")}
                                value={option.audio || ""}
                                onChange={(e) => handleOptionChange(option.id, "audio", e.target.value)}
                            />
                        )}

                        {optionType === "image" && (
                            <Input
                                placeholder={t("imagePlaceholder")}
                                value={option.image || ""}
                                onChange={(e) => handleOptionChange(option.id, "image", e.target.value)}
                            />
                        )}

                        {showComment && (
                            <Textarea
                                placeholder={t("commentPlaceholder")}
                                value={option.comment || ""}
                                onChange={(e) => handleOptionChange(option.id, "comment", e.target.value)}
                            />
                        )}
                    </div>
                </div>
            ))}

            <div className="pt-2">
                <Button type="button" variant="outline" onClick={handleAddOption}>
                    <Plus className="w-4 h-4 mr-2" />
                    {t("addOption")}
                </Button>
            </div>
        </div>
    )
}
