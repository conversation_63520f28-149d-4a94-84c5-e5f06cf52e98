"use client"

import React, { useMemo } from "react"
import { BaseOption } from "@/src/app/test-session/[id]/model"
import TextOption from "../TextOption"
import AudioOption from "../AudioOption"

export type RenderableOption = BaseOption & {
  displayKey: string
}

type OptionsComponentProps = {
  options: BaseOption[]
  selectedId?: string
  randomize?: boolean
  onSelect: (id: string) => void
}

// Fisher–Yates shuffle
const shuffleArray = <T,>(array: T[]): T[] => {
  const arr = [...array]
  for (let i = arr.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[arr[i], arr[j]] = [arr[j], arr[i]]
  }
  return arr
}

const OptionsComponent: React.FC<OptionsComponentProps> = ({
  options,
  selectedId,
  randomize = false,
  onSelect,
}) => {
  const displayOptions: RenderableOption[] = useMemo(() => {
    const storageKey = `shuffled-options-${options.map(o => o.id).join("-")}`

    let finalOptions: BaseOption[] = options

    if (randomize && typeof window !== "undefined") {
      const cached = sessionStorage.getItem(storageKey)
      if (cached) {
        const idOrder = JSON.parse(cached) as string[]
        finalOptions = idOrder
          .map(id => options.find(o => o.id === id))
          .filter((o): o is BaseOption => Boolean(o))
      } else {
        const shuffled = shuffleArray(options)
        sessionStorage.setItem(
          storageKey,
          JSON.stringify(shuffled.map(o => o.id))
        )
        finalOptions = shuffled
      }
    }

    return finalOptions.map((option, index) => ({
      ...option,
      displayKey: String.fromCharCode(65 + index),
    }))
  }, [options, randomize])

  return (
    <>
      {displayOptions.map((option) => (
        <div
          key={option.id}
          className={`flex items-start gap-4 p-4 rounded-lg cursor-pointer transition-colors ${
            selectedId === option.id
              ? "bg-green-100 border border-green-300"
              : "bg-gray-50 hover:bg-gray-100"
          }`}
          onClick={() => onSelect(option.id)}
        >
          <div
            className={`w-10 h-10 rounded-full flex items-center justify-center font-medium flex-shrink-0 ${
              selectedId === option.id
                ? "bg-green-500 text-white"
                : "bg-gray-200 text-gray-700"
            }`}
          >
            {option.displayKey}
          </div>
          <div className="flex flex-col gap-2 flex-grow">
            {option.text && <TextOption content={option.text} />}
            {option.audio && <AudioOption src={option.audio} />}
          </div>
        </div>
      ))}
    </>
  )
}

export default OptionsComponent
