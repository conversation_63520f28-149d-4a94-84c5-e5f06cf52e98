"use client";

import type React from "react";

import { useState, useRef } from "react";
import { Upload, X, AlertCircle, CheckCircle2, FileText } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";

import { useLocalization } from "@/src/localization/functions/client"
import en from "@/src/app/dashboard/question-editor/locales/en.json"

interface QuizOption {
  id: string;
  text: string;
}

interface QuizQuestion {
  id: number;
  type: "multipleChoice" | "singleChoice";
  question: string;
  tag: string[];
  options: QuizOption[];
  correctAnswers: string[];
  competency: string;
  notes: string;
  media?: string;
}

function validateQuestions(data: any) {
  return { success: true, data, errors: "" };
}

interface BulkInsertDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onImport: (questions: QuizQuestion[]) => void;
}

export function BulkInsertDialog({
  open,
  onOpenChange,
  onImport,
}: BulkInsertDialogProps) {
  const [activeTab, setActiveTab] = useState("paste");
  const [jsonText, setJsonText] = useState("");
  const [fileName, setFileName] = useState<string | null>(null);
  const [validationState, setValidationState] = useState<
    "idle" | "validating" | "success" | "error"
  >("idle");
  const [validationErrors, setValidationErrors] = useState<any>(null);
  const [validQuestions, setValidQuestions] = useState<QuizQuestion[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { t } = useLocalization("public-test", { en });


  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
    
      setJsonText("");
      setFileName(null);
      setValidationState("idle");
      setValidationErrors(null);
      setValidQuestions([]);
      setActiveTab("paste");
    }
    onOpenChange(newOpen);
  };


  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setFileName(file.name);
    const reader = new FileReader();

    reader.onload = (event) => {
      const content = event.target?.result as string;
      setJsonText(content);
      validateJson(content);
    };

    reader.onerror = () => {
      setValidationState("error");
      setValidationErrors({ _errors: ["Failed to read file"] });
    };

    reader.readAsText(file);
  };


  const validateJson = (json: string) => {
    setValidationState("validating");

    try {
    
      const parsedData = JSON.parse(json);

    
      const result = validateQuestions(parsedData);

      if (result.success && result.data) {
        setValidQuestions(result.data);
        setValidationState("success");
        setValidationErrors(null);
      } else {
        setValidationState("error");
        setValidationErrors(result.errors);
      }
    } catch (error) {
      setValidationState("error");
      setValidationErrors({ _errors: ["Invalid JSON format"] });
    }
  };


  const handleJsonChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newText = e.target.value;
    setJsonText(newText);

  
    if (!newText.trim()) {
      setValidationState("idle");
      setValidationErrors(null);
      return;
    }
  };


  const handleValidate = () => {
    if (!jsonText.trim()) {
      setValidationState("error");
      setValidationErrors({ _errors: ["Please enter JSON data"] });
      return;
    }

    validateJson(jsonText);
  };


  const handleImport = () => {
    onImport(validQuestions);
    handleOpenChange(false);
  };


  const getQuestionTypeCounts = () => {
    const counts: Record<string, number> = {};

    validQuestions.forEach((question) => {
      counts[question.type] = (counts[question.type] || 0) + 1;
    });

    return Object.entries(counts).map(([type, count]) => ({ type, count }));
  };


  const renderValidationStatus = () => {
    switch (validationState) {
      case "idle":
        return null;
      case "validating":
        return (
          <Alert className="mt-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Validating</AlertTitle>
            <AlertDescription>
              <Progress value={50} className="h-2 mt-2" />
            </AlertDescription>
          </Alert>
        );
      case "success":
        return (
          <Alert className="mt-4 border-green-500 text-green-700">
            <CheckCircle2 className="h-4 w-4 text-green-500" />
            <AlertTitle>Validation Successful</AlertTitle>
            <AlertDescription>
              Found {validQuestions.length} valid questions ready to import.
            </AlertDescription>
          </Alert>
        );
      case "error":
        return (
          <Alert className="mt-4 border-red-500 text-red-700">
            <AlertCircle className="h-4 w-4 text-red-500" />
            <AlertTitle>Validation Failed</AlertTitle>
            <AlertDescription>
              <div className="mt-2">
                {validationErrors?._errors?.map(
                  (error: string, index: number) => (
                    <div key={index} className="text-sm">
                      {error}
                    </div>
                  )
                )}
                {validationErrors && !validationErrors._errors && (
                  <ScrollArea className="h-40 rounded border p-2 bg-red-50">
                    <pre className="text-xs whitespace-pre-wrap">
                      {JSON.stringify(validationErrors, null, 2)}
                    </pre>
                  </ScrollArea>
                )}
              </div>
            </AlertDescription>
          </Alert>
        );
      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{t("bulkInsert.title")}</DialogTitle>
          <DialogDescription>{t("bulkInsert.description")}</DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="paste">{t("bulkInsert.pasteJSON")}</TabsTrigger>
            <TabsTrigger value="upload">
              {t("bulkInsert.uploadFile")}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="paste" className="mt-4">
            <Textarea
              placeholder={t("bulkInsert.pastePlaceholder")}
              className="min-h-[200px] font-mono text-sm"
              value={jsonText}
              onChange={handleJsonChange}
            />
            <Button className="mt-2" onClick={handleValidate}>
              {t("bulkInsert.validate")}
            </Button>
          </TabsContent>

          <TabsContent value="upload" className="mt-4">
            <div className="border-2 border-dashed rounded-md p-6 text-center">
              {fileName ? (
                <div className="flex items-center justify-center space-x-2">
                  <FileText className="h-8 w-8 text-blue-500" />
                  <div>
                    <p className="text-sm font-medium">{fileName}</p>
                    <p className="text-xs text-gray-500">
                      {(jsonText.length / 1024).toFixed(2)} KB
                    </p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setFileName(null);
                      setJsonText("");
                      setValidationState("idle");
                      if (fileInputRef.current) {
                        fileInputRef.current.value = "";
                      }
                    }}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ) : (
                <>
                  <Upload className="h-8 w-8 mx-auto text-gray-400" />
                  <p className="mt-2 text-sm font-medium">
                    {t("bulkInsert.dragAndDrop")}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    {t("bulkInsert.fileRequirements")}
                  </p>
                  <Button
                    variant="outline"
                    className="mt-4"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    {t("bulkInsert.selectFile")}
                  </Button>
                  <input
                    type="file"
                    ref={fileInputRef}
                    className="hidden"
                    accept=".json"
                    onChange={handleFileUpload}
                  />
                </>
              )}
            </div>
            {fileName && (
              <Button className="mt-2" onClick={handleValidate}>
                {t("bulkInsert.validate")}
              </Button>
            )}
          </TabsContent>
        </Tabs>

        {renderValidationStatus()}

        {validationState === "success" && (
          <div className="mt-4 border rounded-md p-4">
            <h3 className="text-sm font-medium mb-2">
              {t("bulkInsert.importSummary")}
            </h3>
            <div className="flex flex-wrap gap-2 mb-3">
              {getQuestionTypeCounts().map(({ type, count }) => (
                <Badge key={type} variant="secondary">
                  {t(`questionTypes.${type}`)}: {count}
                </Badge>
              ))}
            </div>
            <p className="text-sm text-gray-500">
              {t("bulkInsert.totalQuestions")}: {validQuestions.length}
            </p>
          </div>
        )}

        <DialogFooter className="mt-4">
          <Button variant="outline" onClick={() => handleOpenChange(false)}>
            {t("common.cancel")}
          </Button>
          <Button
            disabled={validationState !== "success"}
            onClick={handleImport}
          >
            {t("bulkInsert.import")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
