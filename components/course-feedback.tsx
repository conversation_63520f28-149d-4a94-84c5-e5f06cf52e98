"use client"

import { useState } from "react"
import { Star, Send } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"

interface CourseFeedbackProps {
  courseId: string
}

export function CourseFeedback({ courseId }: CourseFeedbackProps) {
  const [rating, setRating] = useState(0)
  const [hoveredRating, setHoveredRating] = useState(0)
  const [feedback, setFeedback] = useState("")
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleSubmit = () => {
    if (rating === 0 || !feedback.trim()) return

    // In a real app, this would send the feedback to the server
    console.log({ courseId, rating, feedback })

    setIsSubmitted(true)
  }

  if (isSubmitted) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Your Feedback</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <div className="flex justify-center mb-2">
              {[1, 2, 3, 4, 5].map((star) => (
                <Star
                  key={star}
                  className={`h-6 w-6 ${star <= rating ? "fill-primary text-primary" : "text-muted-foreground"}`}
                />
              ))}
            </div>
            <p className="text-muted-foreground mb-4">
              Thank you for your feedback! Your input helps improve this course.
            </p>
            <Button variant="outline" onClick={() => setIsSubmitted(false)}>
              Edit Feedback
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Rate This Course</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <div className="flex justify-center mb-2">
              {[1, 2, 3, 4, 5].map((star) => (
                <Star
                  key={star}
                  className={`h-6 w-6 cursor-pointer ${
                    star <= (hoveredRating || rating) ? "fill-primary text-primary" : "text-muted-foreground"
                  }`}
                  onClick={() => setRating(star)}
                  onMouseEnter={() => setHoveredRating(star)}
                  onMouseLeave={() => setHoveredRating(0)}
                />
              ))}
            </div>
            <p className="text-center text-sm text-muted-foreground">
              {rating === 0
                ? "Click to rate this course"
                : `You've rated this course ${rating} ${rating === 1 ? "star" : "stars"}`}
            </p>
          </div>
          <div className="space-y-2">
            <label htmlFor="feedback" className="text-sm font-medium">
              Your Feedback
            </label>
            <Textarea
              id="feedback"
              placeholder="Share your experience with this course..."
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              className="min-h-[100px]"
            />
          </div>
          <Button className="w-full" onClick={handleSubmit} disabled={rating === 0 || !feedback.trim()}>
            <Send className="mr-2 h-4 w-4" />
            Submit Feedback
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
