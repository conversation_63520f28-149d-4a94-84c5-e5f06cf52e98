"use client"

import type { ReactNode } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CheckCircle2, Star } from "lucide-react"

interface ComingSoonOverlayProps {
  title: string
  description: string
  icon: ReactNode
  features: string[]
  regularPrice: string
  specialPrice: string
  ctaText: string
  videoSrc?: string
  imageSrc?: string
  imageAlt?: string
}

export function ComingSoonOverlay({
  title,
  description,
  icon,
  features,
  regularPrice,
  specialPrice,
  ctaText,
  videoSrc,
  imageSrc,
  imageAlt = "Feature preview",
}: ComingSoonOverlayProps) {
  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl grid md:grid-cols-2 gap-6">
        <Card className="border-2 border-primary/20 shadow-lg">
          <CardHeader>
            <div className="flex justify-between items-start">
              <div className="flex items-center gap-2">
                {icon}
                <div>
                  <CardTitle className="text-2xl">{title}</CardTitle>
                  <CardDescription className="text-base mt-1">{description}</CardDescription>
                </div>
              </div>
              <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
                Coming Soon
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-medium mb-2">Unlock these powerful features:</h3>
              <ul className="space-y-2">
                {features.map((feature, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <CheckCircle2 className="h-5 w-5 text-green-500 shrink-0 mt-0.5" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="bg-muted/50 p-4 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-1">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span className="font-medium">Pre-Launch Special Offer</span>
                </div>
                <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-100">
                  50% OFF
                </Badge>
              </div>
              <div className="flex items-baseline gap-2">
                <span className="text-2xl font-bold">{specialPrice}</span>
                <span className="text-muted-foreground line-through">{regularPrice}</span>
                <span className="text-sm text-muted-foreground">/month</span>
              </div>
              <p className="text-sm text-muted-foreground mt-1">Lock in lifetime access at this special price</p>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col">
            <Button className="w-full" size="lg">
              {ctaText}
            </Button>
            <p className="text-xs text-center text-muted-foreground mt-2">
              No credit card required until launch. Get early access when we go live.
            </p>
          </CardFooter>
        </Card>

        <div className="hidden md:flex flex-col justify-center">
          {videoSrc ? (
            <div className="rounded-lg overflow-hidden border shadow-lg">
              <video className="w-full aspect-video object-cover" controls poster={imageSrc}>
                <source src={videoSrc} type="video/mp4" />
                Your browser does not support the video tag.
              </video>
            </div>
          ) : imageSrc ? (
            <div className="rounded-lg overflow-hidden border shadow-lg">
              <img src={imageSrc || "/placeholder.svg"} alt={imageAlt} className="w-full aspect-video object-cover" />
            </div>
          ) : null}
        </div>
      </div>
    </div>
  )
}
