import type React from "react"
export const Chart = ({ children }: { children: React.ReactNode }) => {
  return <div className="w-full">{children}</div>
}

export const ChartContainer = ({ children }: { children: React.ReactNode }) => {
  return <div className="relative">{children}</div>
}

export const ChartLegend = () => {
  return <div></div>
}

export const ChartTooltip = () => {
  return <div></div>
}

export const ChartTooltipContent = () => {
  return <div></div>
}

export const ChartTooltipItem = () => {
  return <div></div>
}
