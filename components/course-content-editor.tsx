"use client"

import { useState } from "react"
import { VideoIcon, Plus, Trash2, GripVertical, Edit, Grid, List } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"

interface Video {
  id: string
  title: string
  url: string
  duration: string
  description?: string
}

interface Section {
  id: string
  title: string
  description: string // Add description field
  videos: Video[]
}

interface CourseContentEditorProps {
  sections: Section[]
}

export function CourseContentEditor({ sections: initialSections }: CourseContentEditorProps) {
  const [sections, setSections] = useState<Section[]>(initialSections)
  const [editingVideo, setEditingVideo] = useState<Video | null>(null)
  const [editingSection, setEditingSection] = useState<string | null>(null)
  const [videoDialogOpen, setVideoDialogOpen] = useState(false)
  const [sectionDialogOpen, setSectionDialogOpen] = useState(false)
  const [newSectionTitle, setNewSectionTitle] = useState("")
  const [newSectionDescription, setNewSectionDescription] = useState("") // Add state for section description
  const [viewMode, setViewMode] = useState<"list" | "grid">("list") // Add view mode state
  const [newVideoData, setNewVideoData] = useState({
    title: "",
    url: "",
    duration: "",
    description: "",
  })

  const addSection = () => {
    if (!newSectionTitle.trim()) return

    const newSection: Section = {
      id: `section-${Date.now()}`,
      title: newSectionTitle,
      description: newSectionDescription, // Add description to new section
      videos: [],
    }

    setSections([...sections, newSection])
    setNewSectionTitle("")
    setNewSectionDescription("") // Reset description input
    setSectionDialogOpen(false)
  }

  const updateSection = (sectionId: string, title: string, description?: string) => {
    setSections(
      sections.map((section) => {
        if (section.id === sectionId) {
          return {
            ...section,
            title,
            description: description !== undefined ? description : section.description,
          }
        }
        return section
      }),
    )
    setEditingSection(null)
  }

  const deleteSection = (sectionId: string) => {
    setSections(sections.filter((section) => section.id !== sectionId))
  }

  const addVideo = (sectionId: string) => {
    if (!newVideoData.title.trim() || !newVideoData.url.trim()) return

    const newVideo: Video = {
      id: `video-${Date.now()}`,
      title: newVideoData.title,
      url: newVideoData.url,
      duration: newVideoData.duration || "0:00",
      description: newVideoData.description,
    }

    setSections(
      sections.map((section) =>
        section.id === sectionId ? { ...section, videos: [...section.videos, newVideo] } : section,
      ),
    )

    setNewVideoData({ title: "", url: "", duration: "", description: "" })
    setVideoDialogOpen(false)
  }

  const updateVideo = (sectionId: string, videoId: string, videoData: Partial<Video>) => {
    setSections(
      sections.map((section) =>
        section.id === sectionId
          ? {
              ...section,
              videos: section.videos.map((video) => (video.id === videoId ? { ...video, ...videoData } : video)),
            }
          : section,
      ),
    )
    setEditingVideo(null)
    setVideoDialogOpen(false)
  }

  const deleteVideo = (sectionId: string, videoId: string) => {
    setSections(
      sections.map((section) =>
        section.id === sectionId
          ? { ...section, videos: section.videos.filter((video) => video.id !== videoId) }
          : section,
      ),
    )
  }

  function getYoutubeId(url: string) {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/
    const match = url.match(regExp)
    return match && match[2].length === 11 ? match[2] : null
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Course Content</CardTitle>
          <div className="flex items-center gap-2">
            <div className="flex items-center border rounded-md overflow-hidden">
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                className="rounded-none px-3"
                onClick={() => setViewMode("list")}
              >
                <List className="h-4 w-4 mr-1" />
                List
              </Button>
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="sm"
                className="rounded-none px-3"
                onClick={() => setViewMode("grid")}
              >
                <Grid className="h-4 w-4 mr-1" />
                Grid
              </Button>
            </div>
            <Dialog open={sectionDialogOpen} onOpenChange={setSectionDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Section
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add New Section</DialogTitle>
                  <DialogDescription>Create a new section to organize your course content.</DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="section-title">Section Title</Label>
                    <Input
                      id="section-title"
                      placeholder="e.g., Introduction, Advanced Topics"
                      value={newSectionTitle}
                      onChange={(e) => setNewSectionTitle(e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="section-description">Section Description</Label>
                    <Textarea
                      id="section-description"
                      placeholder="Describe what students will learn in this section"
                      value={newSectionDescription}
                      onChange={(e) => setNewSectionDescription(e.target.value)}
                      className="min-h-[80px]"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setSectionDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={addSection}>Add Section</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          {sections.length === 0 ? (
            <div className="text-center py-12 text-muted-foreground">
              <p>No sections added yet. Click "Add Section" to get started.</p>
            </div>
          ) : (
            <Accordion type="multiple" className="w-full">
              {sections.map((section) => (
                <AccordionItem key={section.id} value={section.id} className="border rounded-md mb-4 px-2">
                  <div className="flex items-center justify-between py-4">
                    <div className="flex items-center flex-1">
                      <GripVertical className="h-5 w-5 text-muted-foreground mr-2 cursor-move" />
                      {editingSection === section.id ? (
                        <div className="flex-1 flex flex-col gap-2">
                          <div className="flex items-center gap-2">
                            <Input
                              value={section.title}
                              onChange={(e) => updateSection(section.id, e.target.value)}
                              className="h-8"
                            />
                            <Button size="sm" onClick={() => setEditingSection(null)}>
                              Save
                            </Button>
                          </div>
                          <Textarea
                            value={section.description || ""}
                            onChange={(e) => updateSection(section.id, section.title, e.target.value)}
                            placeholder="Section description"
                            className="text-sm min-h-[60px]"
                          />
                        </div>
                      ) : (
                        <AccordionTrigger className="hover:no-underline flex-1">
                          <div className="text-left">
                            <span className="font-medium">{section.title}</span>
                            {section.description && (
                              <p className="text-xs text-muted-foreground mt-1 line-clamp-2">{section.description}</p>
                            )}
                          </div>
                        </AccordionTrigger>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={(e) => {
                          e.stopPropagation()
                          setEditingSection(section.id)
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={(e) => {
                          e.stopPropagation()
                          deleteSection(section.id)
                        }}
                      >
                        <Trash2 className="h-4 w-4 text-destructive" />
                      </Button>
                    </div>
                  </div>
                  <AccordionContent>
                    <div className="space-y-4 pt-2 pb-4">
                      {section.videos.length > 0 ? (
                        viewMode === "list" ? (
                          // List view
                          section.videos.map((video, index) => (
                            <div key={video.id} className="flex items-center gap-3 p-3 rounded-md border bg-background">
                              <div className="flex items-center gap-2 flex-1">
                                <GripVertical className="h-5 w-5 text-muted-foreground cursor-move" />
                                <VideoIcon className="h-4 w-4 text-muted-foreground" />
                                <div className="flex-1">
                                  <p className="font-medium">{video.title}</p>
                                  <p className="text-xs text-muted-foreground">{video.url}</p>
                                </div>
                                <div className="flex items-center text-xs text-muted-foreground">{video.duration}</div>
                              </div>
                              <div className="flex items-center gap-1">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => {
                                    setEditingVideo(video)
                                    setNewVideoData({
                                      title: video.title,
                                      url: video.url,
                                      duration: video.duration,
                                      description: video.description || "",
                                    })
                                    setVideoDialogOpen(true)
                                  }}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button variant="ghost" size="icon" onClick={() => deleteVideo(section.id, video.id)}>
                                  <Trash2 className="h-4 w-4 text-destructive" />
                                </Button>
                              </div>
                            </div>
                          ))
                        ) : (
                          // Grid view
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {section.videos.map((video) => (
                              <div key={video.id} className="border rounded-md overflow-hidden bg-background">
                                <div className="aspect-video bg-muted relative flex items-center justify-center">
                                  {video.url.includes("youtube") ? (
                                    <div className="w-full h-full bg-black">
                                      <iframe
                                        src={`https://www.youtube.com/embed/${getYoutubeId(video.url)}`}
                                        className="w-full h-full"
                                        allowFullScreen
                                      ></iframe>
                                    </div>
                                  ) : (
                                    <div className="flex flex-col items-center justify-center">
                                      <VideoIcon className="h-10 w-10 text-muted-foreground" />
                                      <span className="text-xs text-muted-foreground mt-2">Video Preview</span>
                                    </div>
                                  )}
                                </div>
                                <div className="p-3">
                                  <h4 className="font-medium text-sm">{video.title}</h4>
                                  {video.description && (
                                    <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                                      {video.description}
                                    </p>
                                  )}
                                  <p className="text-xs text-muted-foreground mt-1">{video.duration}</p>
                                  <div className="flex items-center justify-end gap-1 mt-2">
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="h-7 w-7"
                                      onClick={() => {
                                        setEditingVideo(video)
                                        setNewVideoData({
                                          title: video.title,
                                          url: video.url,
                                          duration: video.duration,
                                          description: video.description || "",
                                        })
                                        setVideoDialogOpen(true)
                                      }}
                                    >
                                      <Edit className="h-3 w-3" />
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="h-7 w-7"
                                      onClick={() => deleteVideo(section.id, video.id)}
                                    >
                                      <Trash2 className="h-3 w-3 text-destructive" />
                                    </Button>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        )
                      ) : (
                        <p className="text-sm text-muted-foreground text-center py-4">
                          No videos added to this section yet.
                        </p>
                      )}

                      <Dialog open={videoDialogOpen} onOpenChange={setVideoDialogOpen}>
                        <DialogTrigger asChild>
                          <Button variant="outline" className="w-full">
                            <Plus className="mr-2 h-4 w-4" />
                            Add Video
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>{editingVideo ? "Edit Video" : "Add New Video"}</DialogTitle>
                            <DialogDescription>
                              {editingVideo ? "Update the details of your video." : "Add a new video to this section."}
                            </DialogDescription>
                          </DialogHeader>
                          <div className="space-y-4 py-4">
                            <div className="space-y-2">
                              <Label htmlFor="video-title">Video Title</Label>
                              <Input
                                id="video-title"
                                placeholder="Enter video title"
                                value={newVideoData.title}
                                onChange={(e) => setNewVideoData({ ...newVideoData, title: e.target.value })}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="video-description">Video Description</Label>
                              <Textarea
                                id="video-description"
                                placeholder="Describe what students will learn in this video..."
                                value={newVideoData.description || ""}
                                onChange={(e) => setNewVideoData({ ...newVideoData, description: e.target.value })}
                                className="min-h-[100px]"
                              />
                              <p className="text-xs text-muted-foreground">
                                You can use basic formatting like **bold** and *italic*
                              </p>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="video-url">Video URL</Label>
                              <Input
                                id="video-url"
                                placeholder="e.g., https://youtube.com/watch?v=..."
                                value={newVideoData.url}
                                onChange={(e) => setNewVideoData({ ...newVideoData, url: e.target.value })}
                              />
                              <p className="text-xs text-muted-foreground">Paste a YouTube or Vimeo URL</p>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="video-duration">Duration (optional)</Label>
                              <Input
                                id="video-duration"
                                placeholder="e.g., 12:34"
                                value={newVideoData.duration}
                                onChange={(e) => setNewVideoData({ ...newVideoData, duration: e.target.value })}
                              />
                            </div>
                          </div>
                          <DialogFooter>
                            <Button
                              variant="outline"
                              onClick={() => {
                                setVideoDialogOpen(false)
                                setEditingVideo(null)
                                setNewVideoData({ title: "", url: "", duration: "", description: "" })
                              }}
                            >
                              Cancel
                            </Button>
                            <Button
                              onClick={() => {
                                if (editingVideo) {
                                  updateVideo(section.id, editingVideo.id, newVideoData)
                                } else {
                                  addVideo(section.id)
                                }
                              }}
                            >
                              {editingVideo ? "Update Video" : "Add Video"}
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
