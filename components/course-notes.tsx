"use client"

import { useState } from "react"
import { StickyNote, Clock, Edit, Trash2, Save, X, Plus } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"

interface CourseNotesProps {
  videoId: string
}

export function CourseNotes({ videoId }: CourseNotesProps) {
  const [activeTab, setActiveTab] = useState("all")
  const [newNoteContent, setNewNoteContent] = useState("")
  const [newNoteTitle, setNewNoteTitle] = useState("")
  const [isAddingNote, setIsAddingNote] = useState(false)
  const [editingNoteId, setEditingNoteId] = useState<string | null>(null)

  // Mock data for notes
  const initialNotes = [
    {
      id: "note-1",
      title: "useReducer vs useState",
      content:
        "useReducer is better for complex state logic, while useState is simpler for basic state management. Remember the reducer pattern from Redux!",
      videoTimestamp: "12:45",
      createdAt: "2 days ago",
      updatedAt: "2 days ago",
    },
    {
      id: "note-2",
      title: "Custom Hooks Best Practices",
      content:
        "1. Start with 'use' prefix\n2. Extract reusable logic\n3. Keep them focused on a single responsibility\n4. Return values, not just functions",
      videoTimestamp: "22:15",
      createdAt: "1 day ago",
      updatedAt: "1 day ago",
    },
    {
      id: "note-3",
      title: "Performance Optimization Checklist",
      content:
        "- Use React.memo for expensive components\n- useMemo for expensive calculations\n- useCallback for functions passed to child components\n- Code splitting with React.lazy\n- Virtualization for long lists",
      videoTimestamp: "15:10",
      createdAt: "5 hours ago",
      updatedAt: "5 hours ago",
    },
  ]

  const [notes, setNotes] = useState(initialNotes)
  const [editedNoteContent, setEditedNoteContent] = useState("")
  const [editedNoteTitle, setEditedNoteTitle] = useState("")

  const addNote = () => {
    if (!newNoteTitle.trim() || !newNoteContent.trim()) return

    const newNote = {
      id: `note-${Date.now()}`,
      title: newNoteTitle,
      content: newNoteContent,
      videoTimestamp: "10:30", // This would be the current video timestamp
      createdAt: "Just now",
      updatedAt: "Just now",
    }

    setNotes([newNote, ...notes])
    setNewNoteTitle("")
    setNewNoteContent("")
    setIsAddingNote(false)
  }

  const startEditingNote = (note: (typeof notes)[0]) => {
    setEditingNoteId(note.id)
    setEditedNoteTitle(note.title)
    setEditedNoteContent(note.content)
  }

  const saveEditedNote = (noteId: string) => {
    if (!editedNoteTitle.trim() || !editedNoteContent.trim()) return

    setNotes(
      notes.map((note) =>
        note.id === noteId
          ? {
              ...note,
              title: editedNoteTitle,
              content: editedNoteContent,
              updatedAt: "Just now",
            }
          : note,
      ),
    )
    setEditingNoteId(null)
  }

  const deleteNote = (noteId: string) => {
    setNotes(notes.filter((note) => note.id !== noteId))
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <StickyNote className="mr-2 h-5 w-5" />
            My Notes
          </CardTitle>
          <Button onClick={() => setIsAddingNote(true)} disabled={isAddingNote}>
            <Plus className="mr-2 h-4 w-4" />
            Add Note
          </Button>
        </div>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList>
            <TabsTrigger value="all">All Notes</TabsTrigger>
            <TabsTrigger value="this-video">This Video</TabsTrigger>
            <TabsTrigger value="this-section">This Section</TabsTrigger>
          </TabsList>
        </Tabs>
      </CardHeader>
      <CardContent>
        {isAddingNote && (
          <div className="mb-6 border rounded-lg p-4 space-y-3">
            <Input
              placeholder="Note title"
              value={newNoteTitle}
              onChange={(e) => setNewNoteTitle(e.target.value)}
              className="font-medium"
            />
            <div className="flex items-center text-xs text-muted-foreground mb-2">
              <Clock className="h-3 w-3 mr-1" />
              <span>At timestamp: 10:30</span>
            </div>
            <Textarea
              placeholder="Write your note here..."
              value={newNoteContent}
              onChange={(e) => setNewNoteContent(e.target.value)}
              className="min-h-[100px]"
            />
            <div className="flex justify-end gap-2">
              <Button variant="outline" size="sm" onClick={() => setIsAddingNote(false)}>
                Cancel
              </Button>
              <Button size="sm" onClick={addNote} disabled={!newNoteTitle.trim() || !newNoteContent.trim()}>
                Save Note
              </Button>
            </div>
          </div>
        )}

        <div className="space-y-4">
          {notes.length === 0 ? (
            <div className="text-center py-12 text-muted-foreground">
              <p>No notes yet. Click "Add Note" to create your first note.</p>
            </div>
          ) : (
            notes.map((note) => (
              <div key={note.id} className="border rounded-lg p-4 space-y-3">
                {editingNoteId === note.id ? (
                  <>
                    <Input
                      value={editedNoteTitle}
                      onChange={(e) => setEditedNoteTitle(e.target.value)}
                      className="font-medium"
                    />
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Clock className="h-3 w-3 mr-1" />
                      <span>At timestamp: {note.videoTimestamp}</span>
                    </div>
                    <Textarea
                      value={editedNoteContent}
                      onChange={(e) => setEditedNoteContent(e.target.value)}
                      className="min-h-[100px]"
                    />
                    <div className="flex justify-end gap-2">
                      <Button variant="outline" size="sm" onClick={() => setEditingNoteId(null)}>
                        <X className="h-4 w-4 mr-1" />
                        Cancel
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => saveEditedNote(note.id)}
                        disabled={!editedNoteTitle.trim() || !editedNoteContent.trim()}
                      >
                        <Save className="h-4 w-4 mr-1" />
                        Save
                      </Button>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="flex justify-between items-start">
                      <h3 className="font-medium">{note.title}</h3>
                      <div className="flex gap-1">
                        <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => startEditingNote(note)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => deleteNote(note.id)}>
                          <Trash2 className="h-4 w-4 text-destructive" />
                        </Button>
                      </div>
                    </div>
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Clock className="h-3 w-3 mr-1" />
                      <span>At timestamp: {note.videoTimestamp}</span>
                      <span className="mx-2">•</span>
                      <span>Updated {note.updatedAt}</span>
                    </div>
                    <div className="text-sm whitespace-pre-line">{note.content}</div>
                    <Button variant="outline" size="sm" className="w-full">
                      Jump to Timestamp
                    </Button>
                  </>
                )}
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  )
}
